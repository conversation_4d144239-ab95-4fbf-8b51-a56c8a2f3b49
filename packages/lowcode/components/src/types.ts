/**
 * 组件定义接口
 */
export interface ComponentDefinition<P = any> {
  category: string; // 组件分类
  // 数据绑定配置
  dataBindings?: {
    // 支持绑定的属性
    bindableProps: string[];

    // 数据源配置
    dataSources?: Array<{
      // 其他配置
      config?: Record<string, any>;
      type: 'api' | 'expression' | 'variable';
    }>;

    // 事件处理
    eventHandlers?: Record<
      string,
      Array<{
        // 动作配置
        config: Record<string, any>;
        type: 'callApi' | 'navigate' | 'setData';
      }>
    >;
  };
  // 默认属性
  defaultProps: P;
  // 设计时特有配置（不影响实际渲染）
  designerConfig?: {
    // 设计时的辅助信息
    guidelines?: any;

    // 设计时的特殊处理
    handlers?: any;

    // 设计时的交互限制
    restrictions?: any;
  };
  icon: string; // 组件图标

  // 是否可以包含子组件
  isContainer?: boolean;

  label: string; // 组件显示名称

  // 属性编辑器配置
  propsSchema: PropSchema[];

  // 组件渲染信息
  renderer: {
    // 组件名称（Vue组件或自定义组件）
    component: any;

    // 可选的渲染配置
    options?: Record<string, any>;
  };

  // 样式编辑器配置
  styleSchema?: StyleSchema[];

  // 组件基本信息
  type: string; // 组件类型标识
}

/**
 * 属性编辑器配置
 */
export interface PropSchema {
  description?: string;
  key: string;
  label: string;
  options?: Array<{ label: string; value: string }>;
  type:
    | 'array'
    | 'color'
    | 'complex'
    | 'input'
    | 'number'
    | 'select'
    | 'switch'
    | 'textarea';
}

/**
 * 样式编辑器配置
 */
export interface StyleSchema {
  key: string;
  label: string;
  options?: Array<{ label: string; value: string }>;
  type: 'color' | 'input' | 'number' | 'select';
}

/**
 * 组件实例接口
 */
export interface Component {
  children?: Component[];
  id: string;
  props?: Record<string, any>;
  style?: Record<string, any>;
  type: string;
}

/**
 * 组件类型
 */
export type ComponentType =
  | 'button'
  | 'container'
  | 'image'
  | 'input'
  | 'table'
  | 'text';

/**
 * 属性配置项
 */
export interface PropConfig {
  description?: string;
  key: string;
  label: string;
  options?: Array<{ label: string; value: string }>;
  type:
    | 'array'
    | 'color'
    | 'complex'
    | 'input'
    | 'number'
    | 'select'
    | 'switch'
    | 'textarea';
}

/**
 * 组件类别
 */
export enum ComponentCategory {
  BASIC = 'basic',
  BUSINESS = 'business',
  DATA = 'data',
  FORM = 'form',
  LAYOUT = 'layout',
}

/**
 * 组件类别配置
 */
export interface CategoryConfig {
  key: string;
  label: string;
}

/**
 * 组件类别列表
 */
export const COMPONENT_CATEGORIES: CategoryConfig[] = [
  { key: ComponentCategory.BASIC, label: '基础组件' },
  { key: ComponentCategory.FORM, label: '表单组件' },
  { key: ComponentCategory.LAYOUT, label: '布局组件' },
  { key: ComponentCategory.DATA, label: '数据展示' },
  { key: ComponentCategory.BUSINESS, label: '业务组件' },
];

/**
 * 组件渲染信息
 */
export interface ComponentRenderer {
  // 组件名称（Vue组件或自定义组件）
  component: any;

  // 可选的渲染配置
  options?: Record<string, any>;
}

/**
 * 项目布局模式
 */
export enum ProjectLayoutMode {
  SINGLE_PAGE = 'single-page',
  TABBAR = 'tabbar',
  CUSTOM = 'custom',
}

/**
 * Tab配置
 */
export interface TabConfig {
  id: string;
  name: string;
  title: string;
  icon?: string;
  pageId: string;
  badge?: string | number;
}

/**
 * TabBar配置
 */
export interface TabbarConfig {
  position: 'bottom' | 'top';
  fixed: boolean;
  tabs: TabConfig[];
}

/**
 * 页面配置
 */
export interface PageConfig {
  id: string;
  title: string;
  path: string;
  components: Component[];
  showInTabbar?: boolean;
}

/**
 * 项目配置
 */
export interface ProjectConfig {
  id: string;
  name: string;
  layoutMode: ProjectLayoutMode;
  tabbarConfig?: TabbarConfig;
  pages: PageConfig[];
}
