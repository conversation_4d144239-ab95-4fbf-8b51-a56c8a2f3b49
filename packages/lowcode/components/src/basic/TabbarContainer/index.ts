import TabbarContainerVue from './index.vue';

export const TabbarContainerComponent = {
  category: 'basic',
  defaultProps: {
    activeTabId: 'tab-1',
    allowAddTab: true,
    allowDragTab: true,
    allowRemoveTab: true,
    border: true,
    designMode: false,
    editable: true,
    fixed: true,
    height: 50,
    modelValue: [
      {
        content: [],
        icon: '',
        id: 'tab-1',
        title: '首页',
      },
      {
        content: [],
        icon: '',
        id: 'tab-2',
        title: '分类',
      },
      {
        content: [],
        icon: '',
        id: 'tab-3',
        title: '我的',
      },
    ],
    safeAreaInsetBottom: true,
    zIndex: 1,
  },
  icon: 'layout',
  label: 'Tabbar容器',
  propsSchema: [
    {
      itemSchema: [
        { key: 'id', label: '标识', type: 'input' },
        { key: 'title', label: '标题', type: 'input' },
        { key: 'icon', label: '图标', type: 'input' },
        {
          key: 'route',
          label: '页面路径',
          optionsProvider: 'getRegisteredPages',
          type: 'select',
        },
        { key: 'badge', label: '徽标', type: 'input' },
        { key: 'dot', label: '红点', type: 'switch' },
      ],
      key: 'modelValue',
      label: 'Tab项',
      type: 'array',
    },
    {
      key: 'activeTabId',
      label: '当前选中Tab',
      type: 'input',
    },
    {
      key: 'border',
      label: '显示边框',
      type: 'switch',
    },
    {
      key: 'fixed',
      label: '固定在底部',
      type: 'switch',
    },
    {
      key: 'safeAreaInsetBottom',
      label: '安全区适配',
      type: 'switch',
    },
    {
      key: 'allowAddTab',
      label: '允许添加Tab',
      type: 'switch',
    },
    {
      key: 'allowRemoveTab',
      label: '允许删除Tab',
      type: 'switch',
    },
    {
      key: 'allowDragTab',
      label: '允许拖拽Tab',
      type: 'switch',
    },
    {
      key: 'editable',
      label: '编辑模式',
      type: 'switch',
    },
    {
      key: 'height',
      label: 'Tabbar高度',
      type: 'number',
    },
    {
      key: 'zIndex',
      label: 'z-index',
      type: 'number',
    },
  ],
  renderer: {
    component: TabbarContainerVue,
    options: {
      designMode: true,
    },
  },
  type: 'TabbarContainer',
};

export default TabbarContainerComponent;
