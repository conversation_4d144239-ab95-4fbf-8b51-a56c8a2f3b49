<script lang="ts" setup>
import type { PropType } from 'vue';

import { computed, ref, watch } from 'vue';

export interface TabItem {
  /**
   * 徽标内容
   */
  badge?: number | string;
  /**
   * Tab内容
   */
  content: any[];
  /**
   * 是否显示徽标小红点
   */
  dot?: boolean;
  /**
   * 图标名称或图片链接
   */
  icon?: string;
  /**
   * 唯一标识
   */
  id: string;
  /**
   * 对应的页面路径
   */
  route?: string;
  /**
   * 标题
   */
  title: string;
}

const props = defineProps({
  /**
   * 当前选中的Tab ID
   */
  activeTabId: {
    default: '',
    type: String,
  },
  /**
   * 是否允许添加Tab
   */
  allowAddTab: {
    default: true,
    type: Boolean,
  },
  /**
   * 是否允许拖拽排序Tab
   */
  allowDragTab: {
    default: true,
    type: Boolean,
  },
  /**
   * 是否允许删除Tab
   */
  allowRemoveTab: {
    default: true,
    type: Boolean,
  },
  /**
   * 是否显示外边框
   */
  border: {
    default: true,
    type: Boolean,
  },
  /**
   * 是否处于设计器环境
   */
  designMode: {
    default: false,
    type: Boolean,
  },
  /**
   * 是否处于编辑模式
   */
  editable: {
    default: true,
    type: Boolean,
  },
  /**
   * 是否固定在底部
   */
  fixed: {
    default: true,
    type: Boolean,
  },
  /**
   * Tabbar高度
   */
  height: {
    default: 50,
    type: Number,
  },
  /**
   * Tab列表
   */
  modelValue: {
    default: () => [],
    type: Array as PropType<TabItem[]>,
  },
  /**
   * 是否开启底部安全区适配
   */
  safeAreaInsetBottom: {
    default: true,
    type: Boolean,
  },
  /**
   * 元素的z-index
   */
  zIndex: {
    default: 1,
    type: Number,
  },
});

const emit = defineEmits([
  'update:modelValue',
  'update:activeTabId',
  'tabAdded',
  'tabRemoved',
  'tabSwitched',
  'navigate',
]);

// 内部数据
const internalTabs = ref<TabItem[]>([...props.modelValue]);
const currentTabId = ref(props.activeTabId || props.modelValue[0]?.id || '');

// 监听外部传入的 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    internalTabs.value = [...newVal];

    // 如果没有激活的Tab但有Tab项，则激活第一个
    if (!currentTabId.value && newVal.length > 0) {
      currentTabId.value = newVal[0].id;
      emit('update:activeTabId', currentTabId.value);
    }
  },
  { deep: true },
);

// 监听外部传入的 activeTabId 变化
watch(
  () => props.activeTabId,
  (newVal) => {
    if (newVal && newVal !== currentTabId.value) {
      currentTabId.value = newVal;
    }
  },
);

// 监听内部数据变化，更新父组件
watch(
  internalTabs,
  (newVal) => {
    emit('update:modelValue', newVal);
  },
  { deep: true },
);

// 检测是否在低代码设计器中运行
const isInDesigner = computed(() => {
  return (
    props.designMode ||
    window.location.href.includes('designer') ||
    window.location.href.includes('editor')
  );
});

// 切换到指定Tab
function switchToTab(tabId: string) {
  if (tabId === currentTabId.value) return;

  currentTabId.value = tabId;
  emit('update:activeTabId', tabId);
  emit('tabSwitched', tabId);

  // 查找对应的Tab和路由
  const selectedTab = internalTabs.value.find((tab) => tab.id === tabId);
  if (selectedTab && selectedTab.route) {
    // 发出导航事件
    emit('navigate', selectedTab.route, selectedTab);
  }
}

// 添加新Tab
function onAddTab() {
  const newTab: TabItem = {
    content: [],
    id: `tab-${Date.now()}`,
    title: `标签 ${internalTabs.value.length + 1}`,
  };

  internalTabs.value.push(newTab);

  // 自动切换到新Tab
  switchToTab(newTab.id);

  emit('tabAdded', newTab);
}

// 删除Tab
function onRemoveTab(tabId: string, event: Event) {
  // 阻止事件冒泡，避免触发Tab切换
  event.stopPropagation();

  const tabIndex = internalTabs.value.findIndex((tab) => tab.id === tabId);
  if (tabIndex === -1) return;

  const tabToRemove = internalTabs.value[tabIndex];

  // 如果删除的是当前激活的Tab，则切换到相邻Tab
  if (currentTabId.value === tabId && internalTabs.value.length > 1) {
    const newActiveIndex = Math.min(tabIndex, internalTabs.value.length - 2);
    const newActiveTab = internalTabs.value[newActiveIndex];
    switchToTab(newActiveTab.id);
  }

  // 删除Tab
  internalTabs.value.splice(tabIndex, 1);

  emit('tabRemoved', tabToRemove);
}

// 处理Tab拖拽开始
function onTabDragStart(tab: TabItem, event: DragEvent) {
  if (!props.allowDragTab || !props.editable || !event.dataTransfer) return;

  // 设置拖拽数据
  event.dataTransfer.setData('application/tab', JSON.stringify(tab));
  event.dataTransfer.effectAllowed = 'move';
}

// 处理Tab拖拽放置
function onTabDrop(targetIndex: number, event: DragEvent) {
  if (!props.allowDragTab || !props.editable || !event.dataTransfer) return;

  // 获取拖拽的Tab
  const tabData = event.dataTransfer.getData('application/tab');
  if (!tabData) return;

  try {
    const draggedTab = JSON.parse(tabData) as TabItem;

    // 查找源Tab索引
    const sourceIndex = internalTabs.value.findIndex(
      (tab) => tab.id === draggedTab.id,
    );
    if (sourceIndex === -1 || sourceIndex === targetIndex) return;

    // 移除源Tab
    const [removed] = internalTabs.value.splice(sourceIndex, 1);

    // 插入到目标位置
    internalTabs.value.splice(targetIndex, 0, removed);
  } catch (error) {
    console.error('Invalid tab data', error);
  }
}

// 处理内容区域拖拽放置
function onContentDragOver(event: DragEvent) {
  // 阻止默认行为以允许放置
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move';
  }
}
</script>

<template>
  <div
    class="tabbar-container"
    :class="{
      'tabbar-container--fixed': fixed && !isInDesigner,
      'tabbar-container--border': border,
      'tabbar-container--in-designer': isInDesigner,
      'tabbar-container--safe-area': safeAreaInsetBottom,
    }"
    :style="{ zIndex }"
  >
    <!-- 内容区域 -->
    <div class="tabbar-container__content">
      <div
        v-for="tab in internalTabs"
        :key="`content-${tab.id}`"
        class="tabbar-container__tab-content"
        :class="{
          'tabbar-container__tab-content--active': tab.id === currentTabId,
        }"
        @dragover="onContentDragOver"
      >
        <slot :name="`tab-content-${tab.id}`" :tab="tab" :content="tab.content">
          <!-- 默认内容渲染 -->
          <div
            v-if="tab.content && tab.content.length > 0"
            class="tabbar-container__items"
          >
            <div
              v-for="item in tab.content"
              :key="item.id"
              class="tabbar-container__item"
            >
              {{ item.type || item.label || item.id }}
            </div>
          </div>
          <div v-else class="tabbar-container__empty">
            <p>{{ isInDesigner ? '拖拽组件到此Tab' : '暂无内容' }}</p>
          </div>
        </slot>
      </div>
    </div>

    <!-- 底部固定Tabbar -->
    <div class="tabbar-container__footer" :style="{ height: `${height}px` }">
      <div
        v-for="(tab, index) in internalTabs"
        :key="tab.id"
        class="tabbar-container__tab"
        :class="{
          'tabbar-container__tab--active': tab.id === currentTabId,
          'tabbar-container__tab--draggable': allowDragTab && editable,
        }"
        draggable="true"
        @click="switchToTab(tab.id)"
        @dragstart="onTabDragStart(tab, $event)"
        @dragover.prevent
        @drop="onTabDrop(index, $event)"
      >
        <div class="tabbar-container__tab-icon" v-if="tab.icon">
          <img :src="tab.icon" :alt="tab.title" />
        </div>
        <div class="tabbar-container__tab-title">
          {{ tab.title }}
        </div>
        <div v-if="tab.badge" class="tabbar-container__tab-badge">
          {{ tab.badge }}
        </div>
        <div v-else-if="tab.dot" class="tabbar-container__tab-dot"></div>
        <button
          v-if="allowRemoveTab && editable && internalTabs.length > 1"
          class="tabbar-container__tab-close"
          @click="onRemoveTab(tab.id, $event)"
        >
          ×
        </button>
      </div>

      <!-- 添加Tab按钮 -->
      <button
        v-if="allowAddTab && editable"
        class="tabbar-container__add-tab"
        @click="onAddTab"
      >
        +
      </button>
    </div>

    <!-- 在设计器中显示绑定的页面提示 -->
    <div
      v-if="isInDesigner && currentTabId"
      class="tabbar-container__page-hint"
    >
      <div
        v-if="internalTabs.find((t) => t.id === currentTabId)?.route"
        class="tabbar-container__bound-page"
      >
        已绑定页面: {{ internalTabs.find((t) => t.id === currentTabId)?.route }}
      </div>
      <div v-else class="tabbar-container__no-bound-page">未绑定页面路径</div>
    </div>
  </div>
</template>

<style scoped>
.tabbar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
}

.tabbar-container--fixed {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: auto;
}

.tabbar-container--border {
  border: 1px solid #ebedf0;
}

.tabbar-container__content {
  flex: 1;
  overflow-y: auto;
  position: relative;
}

.tabbar-container__tab-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  padding: 16px;
  box-sizing: border-box;
}

.tabbar-container__tab-content--active {
  display: block;
}

.tabbar-container__footer {
  display: flex;
  height: 50px;
  background-color: #fff;
  border-top: 1px solid #ebedf0;
  z-index: 1;
}

.tabbar-container--safe-area .tabbar-container__footer {
  padding-bottom: env(safe-area-inset-bottom);
}

.tabbar-container__tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  user-select: none;
  color: #646566;
  font-size: 12px;
  line-height: 1;
}

.tabbar-container__tab--active {
  color: #1989fa;
}

.tabbar-container__tab--draggable {
  cursor: move;
}

.tabbar-container__tab-icon {
  margin-bottom: 4px;
  font-size: 22px;
  height: 22px;
}

.tabbar-container__tab-icon img {
  width: 22px;
  height: 22px;
  display: block;
}

.tabbar-container__tab-title {
  margin-top: 2px;
}

.tabbar-container__tab-badge {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
  min-width: 16px;
  padding: 0 4px;
  color: #fff;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.4;
  text-align: center;
  background-color: #ee0a24;
  border-radius: 8px;
}

.tabbar-container__tab-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background-color: #ee0a24;
  border-radius: 100%;
  transform: translate(50%, -50%);
}

.tabbar-container__tab-close {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  line-height: 14px;
  text-align: center;
  border-radius: 50%;
  border: none;
  background-color: rgba(0, 0, 0, 0.1);
  color: #666;
  font-size: 12px;
  cursor: pointer;
  padding: 0;
}

.tabbar-container__tab-close:hover {
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
}

.tabbar-container__add-tab {
  width: 40px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
}

.tabbar-container__add-tab:hover {
  color: #1989fa;
}

.tabbar-container__items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tabbar-container__item {
  padding: 8px 12px;
  border: 1px solid #ebedf0;
  border-radius: 4px;
  background-color: #fff;
}

.tabbar-container__empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #969799;
  font-size: 14px;
  background-color: #f7f8fa;
  border-radius: 4px;
}

/* 在设计器中的特殊样式 */
.tabbar-container--in-designer {
  position: relative;
  height: 100%;
}

.tabbar-container--in-designer .tabbar-container__footer {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
}

.tabbar-container__page-hint {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  font-size: 12px;
  text-align: center;
  color: #999;
  background-color: #f5f5f5;
  border-top: 1px dashed #ddd;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 8px;
}

.tabbar-container__bound-page {
  color: #2196f3;
}

.tabbar-container__no-bound-page {
  color: #ff9800;
}
</style>
