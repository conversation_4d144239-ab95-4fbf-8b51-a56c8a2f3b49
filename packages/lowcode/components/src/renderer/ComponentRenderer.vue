<script setup lang="ts">
import { computed, inject } from 'vue';

import type { Component } from '../types';

interface ComponentRendererProps {
  component: Component;
}

const props = defineProps<ComponentRendererProps>();

// 从上下文中获取组件注册表
const componentRegistry = inject<Map<string, any>>('componentRegistry', new Map());

// 获取要渲染的组件
const renderComponent = computed(() => {
  return componentRegistry.get(props.component.type);
});

// 组件属性
const componentProps = computed(() => {
  return {
    ...props.component.props,
    style: props.component.style,
  };
});
</script>

<template>
  <component
    :is="renderComponent"
    v-if="renderComponent"
    v-bind="componentProps"
  >
    <!-- 递归渲染子组件 -->
    <ComponentRenderer
      v-for="child in component.children"
      :key="child.id"
      :component="child"
    />
  </component>
  <div v-else class="component-error">
    未知组件类型: {{ component.type }}
  </div>
</template>

<style scoped>
.component-error {
  padding: 8px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  color: #ff4d4f;
  font-size: 12px;
}
</style>
