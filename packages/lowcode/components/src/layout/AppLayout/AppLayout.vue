<script setup lang="ts">
import { computed, inject, ref, withDefaults, defineProps, defineEmits } from 'vue';

// 临时使用相对路径导入，避免循环依赖
import type { ProjectConfig } from '../../types';
import { ProjectLayoutMode } from '../../types';
import ComponentRenderer from '../../renderer/ComponentRenderer.vue';

import TabBarComponent from '../../basic/TabBar/index.vue';

interface AppLayoutProps {
  // 项目配置
  projectConfig: ProjectConfig;
  // 当前页面ID
  currentPageId: string;
  // 是否为设计器模式
  designMode?: boolean;
}

const props = withDefaults(defineProps<AppLayoutProps>(), {
  designMode: false,
});

const emit = defineEmits<{
  // Tab切换事件
  'tab-change': [tabName: string];
  // 页面导航事件
  'navigate': [path: string, pageId: string];
}>();

// 当前激活的Tab
const activeTab = ref<string>('');

// 初始化激活Tab
if (props.projectConfig.tabbarConfig?.tabs.length) {
  const currentTab = props.projectConfig.tabbarConfig.tabs.find(
    tab => tab.pageId === props.currentPageId
  );
  activeTab.value = currentTab?.name || props.projectConfig.tabbarConfig.tabs[0].name;
}

// 当前页面配置
const currentPage = computed(() => {
  return props.projectConfig.pages.find(p => p.id === props.currentPageId);
});

// 是否显示TabBar
const showTabbar = computed(() => {
  return props.projectConfig.layoutMode === ProjectLayoutMode.TABBAR && 
         props.projectConfig.tabbarConfig?.tabs.length;
});

// TabBar配置
const tabbarProps = computed(() => {
  if (!props.projectConfig.tabbarConfig) return null;
  
  return {
    modelValue: activeTab.value,
    tabs: props.projectConfig.tabbarConfig.tabs.map(tab => ({
      name: tab.name,
      title: tab.title,
      icon: tab.icon,
      path: props.projectConfig.pages.find(p => p.id === tab.pageId)?.path,
      badge: tab.badge,
      dot: tab.dot,
    })),
    fixed: props.projectConfig.tabbarConfig.fixed,
    designMode: props.designMode,
  };
});

// 处理Tab切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName;
  
  // 找到对应的页面ID
  const tab = props.projectConfig.tabbarConfig?.tabs.find(t => t.name === tabName);
  if (tab) {
    emit('tab-change', tab.pageId);
  }
};

// 处理导航
const handleNavigate = (path: string, tab: any) => {
  const pageId = props.projectConfig.tabbarConfig?.tabs.find(t => t.name === tab.name)?.pageId;
  if (pageId) {
    emit('navigate', path, pageId);
  }
};

// 页面容器样式
const pageContainerStyle = computed(() => {
  const style: Record<string, any> = {
    flex: 1,
    overflow: 'auto',
  };
  
  // 如果有TabBar且固定在底部，需要预留空间
  if (showTabbar.value && props.projectConfig.tabbarConfig?.position === 'bottom' && 
      props.projectConfig.tabbarConfig?.fixed && !props.designMode) {
    style.paddingBottom = '50px'; // TabBar高度
  }
  
  return style;
});
</script>

<template>
  <div class="app-layout" :class="{ 'design-mode': designMode }">
    <!-- TabBar在顶部的情况 -->
    <div 
      v-if="showTabbar && projectConfig.tabbarConfig?.position === 'top'"
      class="app-layout__tabbar app-layout__tabbar--top"
    >
      <TabBarComponent
        v-bind="tabbarProps"
        @change="handleTabChange"
        @navigate="handleNavigate"
      />
    </div>
    
    <!-- 页面内容区域 -->
    <div class="app-layout__content" :style="pageContainerStyle">
      <!-- 渲染当前页面的组件 -->
      <div v-if="currentPage" class="page-container">
        <ComponentRenderer
          v-for="component in currentPage.components"
          :key="component.id"
          :id="component.id"
          :type="component.type"
          :component-props="component.props"
          :style="component.style"
          :children="component.children"
        />
        
        <!-- 空页面提示 -->
        <div v-if="currentPage.components.length === 0" class="empty-page">
          <div class="empty-page__content">
            <div class="empty-page__icon">📱</div>
            <div class="empty-page__text">
              {{ designMode ? '拖拽组件到此处开始设计' : '页面内容为空' }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 页面不存在提示 -->
      <div v-else class="page-not-found">
        <div class="page-not-found__content">
          <div class="page-not-found__icon">❌</div>
          <div class="page-not-found__text">页面不存在</div>
        </div>
      </div>
    </div>
    
    <!-- TabBar在底部的情况 -->
    <div 
      v-if="showTabbar && projectConfig.tabbarConfig?.position === 'bottom'"
      class="app-layout__tabbar app-layout__tabbar--bottom"
    >
      <TabBarComponent
        v-bind="tabbarProps"
        @change="handleTabChange"
        @navigate="handleNavigate"
      />
    </div>
  </div>
</template>

<style scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.app-layout.design-mode {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}

.app-layout__tabbar {
  flex-shrink: 0;
  z-index: 1000;
}

.app-layout__tabbar--top {
  border-bottom: 1px solid #eee;
}

.app-layout__tabbar--bottom {
  border-top: 1px solid #eee;
}

.app-layout__content {
  flex: 1;
  overflow: auto;
  position: relative;
}

.page-container {
  min-height: 100%;
  position: relative;
}

.empty-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #999;
}

.empty-page__content {
  text-align: center;
}

.empty-page__icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-page__text {
  font-size: 14px;
}

.page-not-found {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ff4d4f;
}

.page-not-found__content {
  text-align: center;
}

.page-not-found__icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.page-not-found__text {
  font-size: 16px;
  font-weight: 500;
}

/* 设计器模式下的特殊样式 */
.design-mode .app-layout__tabbar {
  position: relative !important;
}

.design-mode .empty-page {
  border: 2px dashed #1890ff;
  border-radius: 8px;
  margin: 16px;
  background-color: #f0f8ff;
}

.design-mode .empty-page__text {
  color: #1890ff;
  font-weight: 500;
}
</style>
