/**
 * 低代码组件库入口文件
 */

// 导出所有组件
import { BasicComponents } from './basic';
import { BusinessComponents } from './business';
import { FormComponents } from './form';
import { LayoutComponents } from './layout';
import { yanshiComponents } from './yanshi';

import 'vant/lib/index.css'; // 1. 在包的入口处引入Vant样式，进行封装
// import { d } from './business'

// 导出基础组件
export * from './basic';

// 导出业务组件
export * from './business';

// 导出上下文
export * from './context';

// 导出域
export * from './domains';

// 导出表单组件
export * from './form';

// 导出布局组件
export * from './layout';

// 导出渲染器
export * from './renderer';

// 导出钩子
export * from './hooks';

// 导出类型
export * from './types';

// 导出工具函数
export * from './utils';

/**
 * 所有可用的低代码组件
 */
export const AllComponents = [
  // 基础组件
  ...BasicComponents,

  // 表单组件
  ...FormComponents,

  // 布局组件
  ...LayoutComponents,

  // 业务组件
  ...BusinessComponents,

  // 演示组件
  ...yanshiComponents,
];

/**
 * 注册所有组件
 * @param setupFn 设置函数
 */
export function registerAllComponents(setupFn: (components: any[]) => void) {
  setupFn(AllComponents);
}
