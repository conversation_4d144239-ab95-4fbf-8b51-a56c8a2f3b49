import type { PageConfig, ProjectConfig, ProjectLayoutMode, TabConfig } from './types';

import { reactive, ref } from 'vue';

/**
 * 项目管理器
 * 负责管理项目配置、页面和TabBar的关系
 */
export class ProjectManager {
  // 当前项目配置
  private projectConfig = ref<ProjectConfig | null>(null);
  
  // 当前编辑的页面ID
  private currentPageId = ref<string>('');

  constructor() {
    // 初始化默认项目
    this.initDefaultProject();
  }

  /**
   * 初始化默认项目
   */
  private initDefaultProject() {
    const defaultProject: ProjectConfig = {
      id: 'default-project',
      name: '默认项目',
      layoutMode: ProjectLayoutMode.SINGLE_PAGE,
      pages: [
        {
          id: 'home',
          title: '首页',
          path: '/pages/home',
          components: [],
          showInTabbar: false,
        },
      ],
    };
    
    this.setProjectConfig(defaultProject);
    this.setCurrentPageId('home');
  }

  /**
   * 设置项目配置
   */
  setProjectConfig(config: ProjectConfig) {
    this.projectConfig.value = config;
  }

  /**
   * 获取项目配置
   */
  getProjectConfig(): ProjectConfig | null {
    return this.projectConfig.value;
  }

  /**
   * 设置布局模式
   */
  setLayoutMode(mode: ProjectLayoutMode) {
    if (!this.projectConfig.value) return;
    
    this.projectConfig.value.layoutMode = mode;
    
    // 如果切换到TabBar模式，初始化TabBar配置
    if (mode === ProjectLayoutMode.TABBAR && !this.projectConfig.value.tabbarConfig) {
      this.initTabbarConfig();
    }
    
    // 如果切换到单页面模式，清除TabBar配置
    if (mode === ProjectLayoutMode.SINGLE_PAGE) {
      this.projectConfig.value.tabbarConfig = undefined;
      // 将所有页面的showInTabbar设为false
      this.projectConfig.value.pages.forEach(page => {
        page.showInTabbar = false;
      });
    }
  }

  /**
   * 初始化TabBar配置
   */
  private initTabbarConfig() {
    if (!this.projectConfig.value) return;
    
    // 获取前4个页面作为默认Tab
    const tabPages = this.projectConfig.value.pages.slice(0, 4);
    
    this.projectConfig.value.tabbarConfig = {
      position: 'bottom',
      fixed: true,
      tabs: tabPages.map((page, index) => ({
        id: `tab-${page.id}`,
        name: page.id,
        title: page.title,
        pageId: page.id,
        icon: this.getDefaultTabIcon(index),
      })),
    };
    
    // 标记这些页面在TabBar中显示
    tabPages.forEach(page => {
      page.showInTabbar = true;
    });
  }

  /**
   * 获取默认Tab图标
   */
  private getDefaultTabIcon(index: number): string {
    const icons = ['home-o', 'search', 'shopping-cart-o', 'user-o'];
    return icons[index] || 'apps-o';
  }

  /**
   * 添加页面
   */
  addPage(page: Omit<PageConfig, 'components'>): PageConfig {
    if (!this.projectConfig.value) throw new Error('项目配置不存在');
    
    const newPage: PageConfig = {
      ...page,
      components: [],
    };
    
    this.projectConfig.value.pages.push(newPage);
    return newPage;
  }

  /**
   * 删除页面
   */
  deletePage(pageId: string): boolean {
    if (!this.projectConfig.value) return false;
    
    const pageIndex = this.projectConfig.value.pages.findIndex(p => p.id === pageId);
    if (pageIndex === -1) return false;
    
    // 删除页面
    this.projectConfig.value.pages.splice(pageIndex, 1);
    
    // 如果是TabBar模式，同时删除对应的Tab
    if (this.projectConfig.value.layoutMode === ProjectLayoutMode.TABBAR && 
        this.projectConfig.value.tabbarConfig) {
      const tabIndex = this.projectConfig.value.tabbarConfig.tabs.findIndex(t => t.pageId === pageId);
      if (tabIndex !== -1) {
        this.projectConfig.value.tabbarConfig.tabs.splice(tabIndex, 1);
      }
    }
    
    // 如果删除的是当前页面，切换到第一个页面
    if (this.currentPageId.value === pageId && this.projectConfig.value.pages.length > 0) {
      this.setCurrentPageId(this.projectConfig.value.pages[0].id);
    }
    
    return true;
  }

  /**
   * 添加Tab
   */
  addTab(tab: Omit<TabConfig, 'id'>): TabConfig | null {
    if (!this.projectConfig.value?.tabbarConfig) return null;
    
    const newTab: TabConfig = {
      ...tab,
      id: `tab-${Date.now()}`,
    };
    
    this.projectConfig.value.tabbarConfig.tabs.push(newTab);
    
    // 标记对应页面在TabBar中显示
    const page = this.projectConfig.value.pages.find(p => p.id === tab.pageId);
    if (page) {
      page.showInTabbar = true;
    }
    
    return newTab;
  }

  /**
   * 删除Tab
   */
  deleteTab(tabId: string): boolean {
    if (!this.projectConfig.value?.tabbarConfig) return false;
    
    const tabIndex = this.projectConfig.value.tabbarConfig.tabs.findIndex(t => t.id === tabId);
    if (tabIndex === -1) return false;
    
    const tab = this.projectConfig.value.tabbarConfig.tabs[tabIndex];
    
    // 删除Tab
    this.projectConfig.value.tabbarConfig.tabs.splice(tabIndex, 1);
    
    // 标记对应页面不在TabBar中显示
    const page = this.projectConfig.value.pages.find(p => p.id === tab.pageId);
    if (page) {
      page.showInTabbar = false;
    }
    
    return true;
  }

  /**
   * 更新Tab与页面的绑定关系
   */
  updateTabPageBinding(tabId: string, pageId: string): boolean {
    if (!this.projectConfig.value?.tabbarConfig) return false;
    
    const tab = this.projectConfig.value.tabbarConfig.tabs.find(t => t.id === tabId);
    const page = this.projectConfig.value.pages.find(p => p.id === pageId);
    
    if (!tab || !page) return false;
    
    // 取消原页面的TabBar显示
    const oldPage = this.projectConfig.value.pages.find(p => p.id === tab.pageId);
    if (oldPage) {
      oldPage.showInTabbar = false;
    }
    
    // 更新绑定关系
    tab.pageId = pageId;
    page.showInTabbar = true;
    
    return true;
  }

  /**
   * 获取当前页面ID
   */
  getCurrentPageId(): string {
    return this.currentPageId.value;
  }

  /**
   * 设置当前页面ID
   */
  setCurrentPageId(pageId: string) {
    this.currentPageId.value = pageId;
  }

  /**
   * 获取当前页面配置
   */
  getCurrentPage(): PageConfig | null {
    if (!this.projectConfig.value) return null;
    return this.projectConfig.value.pages.find(p => p.id === this.currentPageId.value) || null;
  }

  /**
   * 获取TabBar中的页面列表
   */
  getTabbarPages(): PageConfig[] {
    if (!this.projectConfig.value) return [];
    return this.projectConfig.value.pages.filter(p => p.showInTabbar);
  }

  /**
   * 获取非TabBar页面列表
   */
  getNonTabbarPages(): PageConfig[] {
    if (!this.projectConfig.value) return [];
    return this.projectConfig.value.pages.filter(p => !p.showInTabbar);
  }

  /**
   * 检查是否为TabBar模式
   */
  isTabbarMode(): boolean {
    return this.projectConfig.value?.layoutMode === ProjectLayoutMode.TABBAR;
  }

  /**
   * 获取可用于绑定的页面列表（排除已绑定的页面）
   */
  getAvailablePagesForBinding(): PageConfig[] {
    if (!this.projectConfig.value) return [];
    
    const boundPageIds = this.projectConfig.value.tabbarConfig?.tabs.map(t => t.pageId) || [];
    return this.projectConfig.value.pages.filter(p => !boundPageIds.includes(p.id));
  }
}

// 全局项目管理器实例
export const projectManager = new ProjectManager();
