/**
 * 项目管理相关类型定义
 */

/**
 * 项目布局模式
 */
export enum ProjectLayoutMode {
  SINGLE_PAGE = 'single-page',
  TABBAR = 'tabbar', 
  CUSTOM = 'custom',
}

/**
 * Tab配置
 */
export interface TabConfig {
  id: string;
  name: string;
  title: string;
  icon?: string;
  pageId: string;
  badge?: string | number;
}

/**
 * TabBar配置
 */
export interface TabbarConfig {
  position: 'bottom' | 'top';
  fixed: boolean;
  tabs: TabConfig[];
}

/**
 * 页面配置
 */
export interface PageConfig {
  id: string;
  title: string;
  path: string;
  components: any[]; // 使用any[]避免循环依赖
  showInTabbar?: boolean;
}

/**
 * 项目配置
 */
export interface ProjectConfig {
  id: string;
  name: string;
  layoutMode: ProjectLayoutMode;
  tabbarConfig?: TabbarConfig;
  pages: PageConfig[];
}
