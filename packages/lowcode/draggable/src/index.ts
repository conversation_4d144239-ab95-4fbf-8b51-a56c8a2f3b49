import type { App } from 'vue';

import EditorDraggable from './adapters/EditorDraggable.vue';
import ContainerDraggable from './components/ContainerDraggable.vue';
import TabbarContainer from './components/TabbarContainer.vue';
import VDraggable from './core/Draggable.vue';

import 'animate.css'; // 导入 animate.css

// 导出适配器
export { useAnimation } from './adapters/animation-adapter';
export type {
  AnimationDirection,
  AnimationOptions,
  AnimationType,
} from './adapters/animation-adapter';
export { useContainerDrag } from './adapters/container-adapter';
export { useLowcodeDrag } from './adapters/lowcode-adapter';

export { useTabbarDrag } from './adapters/tabbar-adapter';

export type { TabItem } from './adapters/tabbar-adapter';
// 导出组合式API
export * from './composables';
// 导出类型
export * from './types';

// 导出组件
export { ContainerDraggable, EditorDraggable, TabbarContainer, VDraggable };

// Vue插件
export default {
  install(app: App) {
    app.component('VDraggable', VDraggable);
    app.component('EditorDraggable', EditorDraggable);
    app.component('ContainerDraggable', ContainerDraggable);
    app.component('TabbarContainer', TabbarContainer);
  },
};
