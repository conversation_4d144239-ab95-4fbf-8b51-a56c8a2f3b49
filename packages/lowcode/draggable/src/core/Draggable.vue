<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref, useSlots, watch } from 'vue';

import Sortable from 'sortablejs';

// 组件属性
const props = withDefaults(
  defineProps<{
    // 动画持续时间
    animation?: number;
    // 选中项目的类名
    chosenClass?: string;
    // 自定义克隆函数
    clone?: (original: any) => any;
    // 是否启用拖拽
    enabled?: boolean;
    // 禁用项目选择器
    filter?: string;
    // 拖动时应用的类名
    ghostClass?: string;
    // 分组名称（用于拖拽项跨容器移动）
    group?:
      | string
      | {
          name: string;
          pull?:
            | 'clone'
            | ((to: any, from: any) => boolean)
            | boolean
            | string[];
          put?: ((to: any, from: any) => boolean) | boolean | string[];
        };
    // 拖拽句柄选择器
    handle?: string;
    // 是否为嵌套容器
    isNested?: boolean;
    // 组项目的唯一标识属性名
    itemKey?: string;
    // 最大嵌套层级
    maxNestingLevel?: number;
    // 列表数据
    modelValue?: any[];
    // 嵌套层级
    nestingLevel?: number;
    // 自动滚动距离
    scrollSensitivity?: number;
  }>(),
  {
    animation: 150,
    enabled: true,
    isNested: false,
    itemKey: 'id',
    maxNestingLevel: 5,
    modelValue: () => [],
    nestingLevel: 0,
  },
);

// 组件事件
const emit = defineEmits<{
  // 添加新项目事件
  add: [event: any];
  // 改变事件（任何变化）
  change: [event: any];
  // 选择项目事件
  choose: [event: any];
  // 放置事件
  end: [event: any];
  // 拖动中事件
  move: [event: any];
  // 移除项目事件
  remove: [event: any];
  // 排序事件
  sort: [event: any];
  // 开始拖动事件
  start: [event: any];
  // 取消选择事件
  unchoose: [event: any];
  // 更新顺序事件
  update: [event: any];
  // 更新列表数据
  'update:modelValue': [value: any[]];
}>();

// 模板引用
const rootEl = ref<HTMLElement | null>(null);
// Sortable实例
let sortableInstance: null | Sortable = null;

// 判断是否为空列表
const isEmpty = computed(() => props.modelValue.length === 0);
// 插槽内容
const slots = useSlots();

// 初始化Sortable实例
function initSortable() {
  if (!rootEl.value) return;

  // 获取子节点列表
  const el = rootEl.value;

  // 创建Sortable实例
  sortableInstance = new Sortable(el, {
    // 动画持续时间
    animation: props.animation,
    // 选中类名
    chosenClass: props.chosenClass,
    // 数据绑定
    dataIdAttr: 'data-id',
    // 启用拖拽
    disabled: !props.enabled,
    // 禁用项目
    filter: props.filter,
    // 占位符类名
    ghostClass: props.ghostClass,
    // 分组
    group: props.group,
    // 拖拽句柄
    handle: props.handle,
    onAdd(evt) {
      const { item, newIndex } = evt;
      const itemEl = item as HTMLElement;
      const itemId = itemEl.dataset.id;

      console.log('Draggable onAdd:', {
        draggable_component: (evt.from as any)?.__draggable_component,
        draggable_context: (evt.from as any)?.__draggable_context,
        evt,
        from: evt.from,
        item,
        itemId,
        newIndex,
        pullMode: evt.pullMode,
        to: evt.to,
      });

      // 处理添加项目
      const newList = [...props.modelValue];

      // 如果是从另一个列表克隆过来的
      if (
        evt.pullMode === 'clone' &&
        evt.from &&
        (evt.from as any).__draggable_component
      ) {
        const sourceItem = (evt.from as any).__draggable_component;
        let clonedItem;

        // 使用自定义克隆函数或默认克隆
        if (props.clone && typeof props.clone === 'function') {
          clonedItem = props.clone(sourceItem);
        } else {
          clonedItem = { ...sourceItem };
          // 确保生成新ID避免冲突
          if (clonedItem.id) {
            clonedItem.id = `${clonedItem.id}_${Date.now()}`;
          }
        }

        newList.splice(newIndex || 0, 0, clonedItem);
      } else if (itemId && evt.from && (evt.from as any).__draggable_context) {
        // 从另一个列表移动过来
        const sourceList = (evt.from as any).__draggable_context;
        const movedItem = sourceList.find(
          (item: any) => item[props.itemKey] === itemId,
        );
        if (movedItem) {
          newList.splice(newIndex || 0, 0, movedItem);
        }
      } else {
        // 处理从其他类型的拖拽源（如Ant Design Vue的Draggable）
        try {
          // 尝试从拖拽元素的数据属性中获取组件信息
          const componentData = itemEl.dataset.component;
          if (componentData) {
            const parsedData = JSON.parse(componentData);
            newList.splice(newIndex || 0, 0, parsedData);
          } else if (itemEl.textContent) {
            // 如果没有数据属性，尝试创建一个简单的组件
            const newComponent = {
              id: `component_${Date.now()}`,
              label: itemEl.textContent.trim(),
              props: {},
              style: {},
              type: 'text',
            };
            newList.splice(newIndex || 0, 0, newComponent);
          }
        } catch (error) {
          console.error('Failed to add component:', error);
        }
      }

      emit('update:modelValue', newList);
      emit('add', { ...evt, item: newList[newIndex] });
      emit('change', { item: newList[newIndex], newIndex, type: 'add' });
    },

    onChoose(evt) {
      emit('choose', evt);
    },
    onEnd(evt) {
      emit('end', evt);
    },
    onMove(evt) {
      emit('move', evt);
      return true; // 允许移动
    },
    onRemove(evt) {
      const { oldIndex } = evt;

      // 处理移除项目
      const newList = [...props.modelValue];
      const removedItem = newList.splice(oldIndex, 1)[0];

      emit('update:modelValue', newList);
      emit('remove', { ...evt, item: removedItem });
      emit('change', { item: removedItem, oldIndex, type: 'remove' });
    },
    onSort(evt) {
      emit('sort', evt);
    },
    // 事件处理
    onStart(evt) {
      emit('start', evt);
    },
    onUnchoose(evt) {
      emit('unchoose', evt);
    },
    onUpdate(evt) {
      const { newIndex, oldIndex } = evt;

      // 处理更新顺序
      const newList = [...props.modelValue];
      const movedItem = newList.splice(oldIndex, 1)[0];
      newList.splice(newIndex, 0, movedItem);

      emit('update:modelValue', newList);
      emit('update', { ...evt, item: movedItem });
      emit('change', { item: movedItem, newIndex, oldIndex, type: 'update' });
    },
    // 滚动灵敏度
    scrollSensitivity: props.scrollSensitivity,
  });

  // 存储上下文，用于跨列表拖拽
  el.__draggable_context = props.modelValue;
}

// 销毁Sortable实例
function destroySortable() {
  if (sortableInstance) {
    sortableInstance.destroy();
    sortableInstance = null;
  }
}

// 刷新Sortable实例
function refreshSortable() {
  destroySortable();
  initSortable();
}

// 监听启用状态变化
watch(
  () => props.enabled,
  (newVal) => {
    if (sortableInstance) {
      sortableInstance.option('disabled', !newVal);
    }
  },
);

// 组件挂载后初始化
onMounted(() => {
  initSortable();
});

// 组件卸载前清理
onUnmounted(() => {
  destroySortable();
});

// 暴露组件方法
defineExpose({
  disable: () => {
    if (sortableInstance) {
      sortableInstance.option('disabled', true);
    }
  },
  enable: () => {
    if (sortableInstance) {
      sortableInstance.option('disabled', false);
    }
  },
  refresh: refreshSortable,
});
</script>

<template>
  <div
    ref="rootEl"
    class="v-draggable"
    :class="[
      {
        'v-draggable--disabled': !enabled,
        'v-draggable--nested': isNested,
        [ghostClass]: !!ghostClass,
      },
    ]"
  >
    <slot></slot>
    <div v-if="isEmpty && !isNested" class="v-draggable__empty">
      <slot name="empty">
        <div class="v-draggable__empty-text">拖拽元素到这里</div>
      </slot>
    </div>
  </div>
</template>

<style>
.v-draggable {
  min-height: 20px;
  position: relative;
}

.v-draggable--disabled {
  opacity: 0.6;
  pointer-events: none;
}

.v-draggable__empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  border: 2px dashed #ddd;
  border-radius: 4px;
  background-color: #fafafa;
}

.v-draggable__empty-text {
  color: #999;
  font-size: 14px;
}

.v-draggable .sortable-ghost {
  opacity: 0.4;
  background-color: #f0f0f0;
}

.v-draggable .sortable-chosen {
  background-color: #f5f5f5;
}

.v-draggable .sortable-drag {
  opacity: 0.8;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
</style>
