<script lang="ts" setup>
import type { TabItem as BaseTabItem } from '../adapters/tabbar-adapter';
import type { DraggableItemInfo } from '../types';

import { computed, ref, watch } from 'vue';

import { useTabbarDrag } from '../adapters/tabbar-adapter';

// 扩展TabItem接口，添加route属性
interface TabItem extends BaseTabItem {
  badge?: number | string;
  dot?: boolean;
  route?: string;
}

// 页面信息接口
interface PageInfo {
  id: string;
  path: string;
  title: string;
}

// 组件属性
const props = withDefaults(
  defineProps<{
    // 当前选中的Tab ID
    activeTabId: string;
    // 是否允许添加Tab
    allowAddTab?: boolean;
    // 是否允许拖拽排序Tab
    allowDragTab?: boolean;
    // 是否允许删除Tab
    allowRemoveTab?: boolean;
    // 是否处于设计器环境
    designMode: boolean;
    // 是否处于编辑模式
    editable?: boolean;
    // 占位符类名
    ghostClass?: string;
    // 拖拽分组名称
    groupName?: string;
    // 拖拽句柄
    handle?: string;
    // 最大嵌套层级
    maxNestingLevel?: number;
    // Tab列表
    modelValue: TabItem[];
    // 当前嵌套层级
    nestingLevel?: number;
    // 页面列表，用于绑定页面
    pageList: PageInfo[];
    // 选中项ID
    selectedId?: string;
    // 是否显示组件控件
    showControls?: boolean;
  }>(),
  {
    activeTabId: '',
    allowAddTab: true,
    allowDragTab: true,
    allowRemoveTab: true,
    designMode: true,
    editable: true,
    ghostClass: 'ghost',
    groupName: 'nested-tabs',
    handle: '',
    maxNestingLevel: 5,
    nestingLevel: 0,
    pageList: () => [],
    selectedId: '',
    showControls: true,
  },
);

// 组件事件
const emit = defineEmits<{
  // 添加组件事件
  'item-added': [item: DraggableItemInfo, tabId: string];
  // 删除组件事件
  'item-deleted': [item: DraggableItemInfo, tabId: string];
  // 添加Tab事件
  tabAdded: [tab: TabItem];
  // 删除Tab事件
  tabRemoved: [tab: TabItem];
  // 切换Tab事件
  tabSwitched: [tabId: string];
  // 更新当前选中的Tab ID
  'update:activeTabId': [value: string];
  // 更新Tab列表
  'update:modelValue': [value: TabItem[]];
}>();

// 内部数据
const internalTabs = ref<TabItem[]>([...props.modelValue]);
const showPageBindingModal = ref(false);
const currentBindingTabId = ref('');
const selectedPageId = ref('');

// 使用Tabbar拖拽适配器
const {
  activeTabId,
  createTab,
  handleTabContentDrop,
  handleTabDrag,
  removeTab,
  switchToTab,
} = useTabbarDrag({
  onTabSwitch: (tabId) => {
    emit('tabSwitched', tabId);
    emit('update:activeTabId', tabId);
  },
});

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    internalTabs.value = [...newVal];

    // 如果没有激活的Tab但有Tab项，则激活第一个
    if (activeTabId.value === null && newVal.length > 0) {
      switchToTab(newVal[0].id);
    }
  },
  { deep: true, immediate: true },
);

// 监听外部传入的 activeTabId 变化
watch(
  () => props.activeTabId,
  (newVal) => {
    if (newVal && newVal !== activeTabId.value) {
      switchToTab(newVal);
    }
  },
);

// 监听内部数据变化，更新父组件
watch(
  internalTabs,
  (newVal) => {
    emit('update:modelValue', newVal);
  },
  { deep: true },
);

// 处理Tab内容拖拽
const contentDropHandlers = handleTabContentDrop(
  internalTabs.value,
  (newTabs) => {
    internalTabs.value = newTabs;
  },
);

// 处理Tab项拖拽排序
const tabDragHandlers = handleTabDrag(internalTabs.value, (newTabs) => {
  internalTabs.value = newTabs;
});

// 添加新Tab
function onAddTab() {
  const newTab = createTab(
    internalTabs.value,
    `标签 ${internalTabs.value.length + 1}`,
    (newTabs) => {
      internalTabs.value = newTabs;
    },
  );

  emit('tabAdded', newTab);
}

// 删除Tab
function onRemoveTab(tabId: string, event: DragEvent) {
  event.stopPropagation();

  const tabToRemove = internalTabs.value.find((tab) => tab.id === tabId);

  if (tabToRemove) {
    removeTab(internalTabs.value, tabId, (newTabs) => {
      internalTabs.value = newTabs;
    });

    emit('tabRemoved', tabToRemove);
  }
}

// 处理Tab拖拽开始
function onTabDragStart(tab: TabItem, event: DragEvent) {
  if (props.allowDragTab && props.editable) {
    tabDragHandlers.onDragStart(tab, event);
  }
}

// 处理Tab拖拽放置
function onTabDrop(event: DragEvent, index: number) {
  if (props.allowDragTab && props.editable) {
    tabDragHandlers.onDrop(event, index);
  }
}

// 处理内容区域拖拽放置
function onContentDrop(event: DragEvent, tabId: string) {
  if (props.editable) {
    contentDropHandlers.onDrop(event, tabId);
  }
}

// 打开页面绑定模态框
function openPageBindingModal(tabId: string) {
  currentBindingTabId.value = tabId;
  const tab = internalTabs.value.find((t) => t.id === tabId);
  selectedPageId.value = tab?.route || '';
  showPageBindingModal.value = true;
}

// 绑定页面
function bindPage() {
  if (!currentBindingTabId.value) return;

  const tabIndex = internalTabs.value.findIndex(
    (tab) => tab.id === currentBindingTabId.value,
  );
  if (tabIndex === -1) return;

  // 更新Tab的route属性
  internalTabs.value[tabIndex] = {
    ...internalTabs.value[tabIndex],
    route: selectedPageId.value,
  };

  // 关闭模态框
  showPageBindingModal.value = false;
  currentBindingTabId.value = '';
  selectedPageId.value = '';

  // 通知父组件
  emit('update:modelValue', internalTabs.value);
}

// 获取页面标题
function getPageTitle(pageId: string) {
  const page = props.pageList.find((p) => p.id === pageId);
  return page ? page.title : pageId;
}

// 获取当前活跃的Tab
const activeTab = computed(() => {
  return internalTabs.value.find((tab) => tab.id === activeTabId.value) || null;
});
</script>

<template>
  <div class="tabbar-container">
    <!-- Tab栏 -->
    <div class="tabbar-container__header">
      <div
        v-for="(tab, index) in internalTabs"
        :key="tab.id"
        class="tabbar-container__tab"
        :class="{
          'tabbar-container__tab--active': tab.id === activeTabId,
          'tabbar-container__tab--draggable': allowDragTab && editable,
        }"
        draggable="true"
        @click="switchToTab(tab.id)"
        @dragstart="onTabDragStart(tab, $event)"
        @dragover.prevent
        @drop="onTabDrop($event, index)"
      >
        <span class="tabbar-container__tab-title">{{ tab.title }}</span>
        <button
          v-if="allowRemoveTab && editable && internalTabs.length > 1"
          class="tabbar-container__tab-close"
          @click.stop="onRemoveTab(tab.id, $event)"
        >
          ×
        </button>
      </div>

      <!-- 添加Tab按钮 -->
      <button
        v-if="allowAddTab && editable"
        class="tabbar-container__add-tab"
        @click="onAddTab"
      >
        +
      </button>
    </div>

    <!-- 内容区域 -->
    <div class="tabbar-container__content">
      <div
        v-for="tab in internalTabs"
        :key="`content-${tab.id}`"
        class="tabbar-container__tab-content"
        :class="{
          'tabbar-container__tab-content--active': tab.id === activeTabId,
        }"
        @dragover.prevent="contentDropHandlers.onDragOver($event, tab.id)"
        @drop="onContentDrop($event, tab.id)"
      >
        <!-- 这里可以放置Tab内容的渲染逻辑 -->
        <slot :name="`tab-content-${tab.id}`" :tab="tab" :content="tab.content">
          <!-- 默认内容渲染 -->
          <div
            v-if="tab.content && tab.content.length > 0"
            class="tabbar-container__items"
          >
            <div
              v-for="item in tab.content"
              :key="item.id"
              class="tabbar-container__item"
              :class="{
                'tabbar-container__item--selected': item.id === selectedId,
              }"
            >
              {{ item.type }} - {{ item.label || item.id }}
            </div>
          </div>
          <div v-else class="tabbar-container__empty">
            <p>拖拽组件到此Tab</p>
          </div>
        </slot>
      </div>
    </div>

    <!-- 设计模式下显示页面绑定信息 -->
    <div v-if="designMode" class="tabbar-container__page-binding">
      <div v-if="tab.route" class="bound-page">
        已绑定页面: {{ getPageTitle(tab.route) }}
        <button class="binding-btn" @click.stop="openPageBindingModal(tab.id)">
          修改
        </button>
      </div>
      <div v-else class="no-bound-page">
        未绑定页面
        <button class="binding-btn" @click.stop="openPageBindingModal(tab.id)">
          绑定页面
        </button>
      </div>
    </div>

    <!-- 页面绑定模态框 (仅在设计模式下显示) -->
    <div v-if="designMode && showPageBindingModal" class="page-binding-modal">
      <div class="modal-content">
        <h3>绑定页面</h3>
        <p>
          为标签 "{{
            internalTabs.find((t) => t.id === currentBindingTabId)?.title
          }}" 选择要绑定的页面:
        </p>

        <div class="page-list">
          <div
            v-for="page in props.pageList"
            :key="page.id"
            class="page-item"
            :class="{ selected: selectedPageId === page.id }"
            @click="selectedPageId = page.id"
          >
            {{ page.title }}
            <span class="page-path">{{ page.path }}</span>
          </div>
        </div>

        <div class="modal-actions">
          <button class="cancel-btn" @click="showPageBindingModal = false">
            取消
          </button>
          <button class="confirm-btn" @click="bindPage">确认绑定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.tabbar-container {
  display: flex;
  flex-direction: column;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}

.tabbar-container__header {
  display: flex;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
  overflow-x: auto;
  white-space: nowrap;
}

.tabbar-container__tab {
  padding: 8px 16px;
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  border-right: 1px solid #e8e8e8;
  background-color: #f5f5f5;
  transition: background-color 0.2s;
}

.tabbar-container__tab--active {
  background-color: #fff;
  border-bottom: 2px solid #1890ff;
  margin-bottom: -1px;
}

.tabbar-container__tab--draggable {
  cursor: move;
}

.tabbar-container__tab-title {
  margin-right: 8px;
}

.tabbar-container__tab-close {
  width: 16px;
  height: 16px;
  line-height: 14px;
  text-align: center;
  border-radius: 50%;
  border: none;
  background-color: transparent;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  color: #999;
}

.tabbar-container__tab-close:hover {
  background-color: #e6e6e6;
  color: #666;
}

.tabbar-container__add-tab {
  padding: 8px 12px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #999;
}

.tabbar-container__add-tab:hover {
  color: #1890ff;
}

.tabbar-container__content {
  flex: 1;
  position: relative;
  min-height: 100px;
}

.tabbar-container__tab-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 16px;
  display: none;
}

.tabbar-container__tab-content--active {
  display: block;
}

.tabbar-container__items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tabbar-container__item {
  padding: 8px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fff;
}

.tabbar-container__item--selected {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.tabbar-container__empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  color: #999;
}

/* 页面绑定信息 */
.tabbar-container__page-binding {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px;
  background-color: #f5f5f5;
  border-top: 1px dashed #ddd;
  font-size: 12px;
  display: flex;
  justify-content: center;
}

.bound-page {
  color: #2196f3;
}

.no-bound-page {
  color: #ff9800;
}

.binding-btn {
  margin-left: 8px;
  padding: 2px 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 12px;
  cursor: pointer;
}

.binding-btn:hover {
  background-color: #f0f0f0;
}

/* 页面绑定模态框 */
.page-binding-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 400px;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  color: #333;
}

.page-list {
  max-height: 300px;
  overflow-y: auto;
  margin: 16px 0;
  border: 1px solid #eee;
  border-radius: 4px;
}

.page-item {
  padding: 10px 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
}

.page-item:last-child {
  border-bottom: none;
}

.page-item:hover {
  background-color: #f9f9f9;
}

.page-item.selected {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.page-path {
  color: #999;
  font-size: 12px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn,
.confirm-btn {
  padding: 6px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  margin-right: 8px;
}

.confirm-btn {
  background-color: #1890ff;
  border: 1px solid #1890ff;
  color: #fff;
}

.cancel-btn:hover {
  background-color: #fafafa;
}

.confirm-btn:hover {
  background-color: #40a9ff;
}
</style>
