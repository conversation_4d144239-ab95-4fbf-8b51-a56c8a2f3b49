import type { DraggableItemInfo } from '../types';

import { useNestedDraggable } from '../composables/useNestedDraggable';

/**
 * 容器拖拽适配器 - 专门处理容器嵌套场景
 *
 * @param options 配置选项
 * @returns 容器拖拽相关方法和属性
 */
export function useContainerDrag(
  options: {
    containerTypes?: string[];
    maxNestingLevel?: number;
    onNestingLevelExceeded?: () => void;
  } = {},
) {
  const {
    containerTypes = ['container', 'grid', 'column', 'row', 'TabBarContainer'],
    maxNestingLevel = 5,
    onNestingLevelExceeded,
  } = options;

  // 使用嵌套拖拽核心逻辑
  const {
    canNest,
    findNestedItem,
    getNestedDepth,
    isContainer,
    selectedId,
    selectItem,
  } = useNestedDraggable(containerTypes, maxNestingLevel);

  /**
   * 确定放置区域
   * @param event 拖拽事件
   * @param containers 容器列表
   * @returns 目标容器信息
   */
  function determineDropZone(
    event: DragEvent,
    containers: DraggableItemInfo[],
  ): null | {
    container: DraggableItemInfo;
    position: 'after' | 'before' | 'inside';
  } {
    if (!event.target || containers.length === 0) return null;

    // 获取鼠标位置
    const mouseY = event.clientY;

    // 查找目标元素
    const targetEl = event.target as HTMLElement;
    const containerEl = findContainerElement(targetEl);

    if (!containerEl) return null;

    // 获取容器ID
    const containerId = containerEl.dataset.id;
    if (!containerId) return null;

    // 查找容器对象
    const containerInfo = findNestedItem(containers, containerId);
    if (!containerInfo) return null;

    const { item: container } = containerInfo;

    // 计算放置位置
    const rect = containerEl.getBoundingClientRect();
    const threshold = rect.height * 0.25; // 25% 阈值

    let position: 'after' | 'before' | 'inside';

    if (mouseY < rect.top + threshold) {
      position = 'before';
    } else if (mouseY > rect.bottom - threshold) {
      position = 'after';
    } else {
      position = 'inside';

      // 检查嵌套级别
      const nestingLevel = getNestedDepth(containers, containerId);
      if (nestingLevel >= 0 && !canNest(nestingLevel)) {
        // 超出最大嵌套深度
        onNestingLevelExceeded?.();
        return null;
      }
    }

    return { container, position };
  }

  /**
   * 查找容器元素
   * @param element 当前元素
   * @returns 容器元素
   */
  function findContainerElement(element: HTMLElement): HTMLElement | null {
    // 最多向上查找5层父元素
    let current: HTMLElement | null = element;
    let depth = 0;

    while (current && depth < 5) {
      if (current.dataset.container === 'true') {
        return current;
      }
      current = current.parentElement;
      depth++;
    }

    return null;
  }

  /**
   * 验证嵌套是否合法
   * @param draggedItem 被拖拽的项
   * @param targetContainer 目标容器
   * @returns 是否允许嵌套
   */
  function validateNesting(
    draggedItem: DraggableItemInfo,
    targetContainer: DraggableItemInfo,
  ): boolean {
    // 防止自己嵌套自己
    if (draggedItem.id === targetContainer.id) {
      return false;
    }

    // 防止循环嵌套 (容器A包含容器B，然后尝试将A拖入B)
    if (isContainer(draggedItem) && draggedItem.children) {
      const isTargetInsideDragged = findNestedItemInChildren(
        draggedItem.children,
        targetContainer.id,
      );

      if (isTargetInsideDragged) {
        return false;
      }
    }

    return true;
  }

  /**
   * 在子项中查找特定ID的项
   * @param children 子项列表
   * @param id 要查找的ID
   * @returns 是否找到
   */
  function findNestedItemInChildren(
    children: DraggableItemInfo[],
    id: string,
  ): boolean {
    for (const child of children) {
      if (child.id === id) {
        return true;
      }

      if (
        isContainer(child) &&
        child.children &&
        child.children.length > 0 &&
        findNestedItemInChildren(child.children, id)
      ) {
        return true;
      }
    }

    return false;
  }

  /**
   * 处理容器内拖拽
   * @param containers 容器列表
   * @param onUpdate 更新回调
   * @returns 拖拽处理方法
   */
  function handleContainerDrag(
    containers: DraggableItemInfo[],
    onUpdate: (newContainers: DraggableItemInfo[]) => void,
  ) {
    return {
      onDragOver: (event: DragEvent) => {
        // 阻止默认行为以允许放置
        event.preventDefault();

        // 确定放置区域并高亮显示
        const dropZone = determineDropZone(event, containers);

        // 这里可以添加高亮逻辑
      },

      onDragStart: (item: DraggableItemInfo, event: DragEvent) => {
        // 选中当前项
        selectItem(item.id);

        // 设置拖拽数据
        if (event.dataTransfer) {
          event.dataTransfer.setData('application/json', JSON.stringify(item));
        }
      },

      onDrop: (event: DragEvent) => {
        event.preventDefault();

        // 获取拖拽的项
        const itemData = event.dataTransfer?.getData('application/json');
        if (!itemData) return;

        try {
          const draggedItem = JSON.parse(itemData) as DraggableItemInfo;
          const dropZone = determineDropZone(event, containers);

          if (!dropZone) return;

          const { container, position } = dropZone;

          // 验证嵌套
          if (
            position === 'inside' &&
            !validateNesting(draggedItem, container)
          ) {
            return;
          }

          // 处理放置逻辑
          const newContainers = [...containers];

          // 这里需要实现具体的放置逻辑，根据position决定如何放置
          // ...

          // 更新容器列表
          onUpdate(newContainers);
        } catch (error) {
          console.error('Invalid drag data', error);
        }
      },
    };
  }

  return {
    canNest,
    determineDropZone,
    handleContainerDrag,
    isContainer,
    selectedId,
    selectItem,
    validateNesting,
  };
}
