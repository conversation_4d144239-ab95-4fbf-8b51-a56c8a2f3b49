<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

import VDraggable from '../core/Draggable.vue';

interface DraggableItem {
  [key: string]: any;
}

// 定义组件属性，与 vuedraggable 兼容
const props = defineProps({
  // 动画持续时间
  animation: {
    default: 150,
    type: Number,
  },
  // 选中项目的类名
  chosenClass: {
    default: undefined,
    type: String,
  },
  // 克隆函数
  clone: {
    default: undefined,
    type: Function as unknown as () => (original: any) => any,
  },
  // 启用拖拽
  disabled: {
    default: false,
    type: Boolean,
  },
  // 过滤器
  filter: {
    default: undefined,
    type: String,
  },
  // 拖动时应用的类名
  ghostClass: {
    default: undefined,
    type: String,
  },
  // 分组名称
  group: {
    default: undefined,
    type: [String, Object] as any,
  },
  // 拖拽句柄
  handle: {
    default: undefined,
    type: String,
  },
  // 项目唯一标识
  itemKey: {
    default: 'id',
    type: String,
  },
  // 列表数据
  modelValue: {
    default: () => [],
    type: Array as () => DraggableItem[],
  },
  // 是否允许排序
  sort: {
    default: true,
    type: Boolean,
  },
});

// 定义事件
const emit = defineEmits([
  'update:modelValue',
  'start',
  'add',
  'remove',
  'update',
  'end',
  'choose',
  'unchoose',
  'sort',
  'change',
  'move',
]);

// 计算属性：启用状态
const enabled = computed(() => !props.disabled);

// 内部列表状态
const internalList = ref<DraggableItem[]>([...props.modelValue]);

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    internalList.value = [...newVal];
  },
  { deep: true },
);

// 事件处理函数
function handleStart(event: any) {
  emit('start', event);
}

function handleEnd(event: any) {
  emit('end', event);
}

function handleAdd(event: any) {
  // 如果从 Ant Design Vue 的 Draggable 拖拽过来，需要特殊处理
  if (event.item && !event.item.__draggable_context && props.clone) {
    // 尝试从 DOM 元素中提取原始数据
    try {
      const originalItem =
        event.item.originalItem || event.item.__draggable_component;
      if (originalItem) {
        const clonedItem = props.clone(originalItem);
        if (clonedItem) {
          // 手动更新内部列表
          const newList = [...internalList.value];
          if (event.newIndex !== undefined) {
            newList.splice(event.newIndex, 0, clonedItem);
            internalList.value = newList;
            emit('update:modelValue', newList);
          }
        }
      }
    } catch (error) {
      console.error('Failed to handle dragged item:', error);
    }
  }

  emit('add', event);
}

function handleRemove(event: any) {
  emit('remove', event);
}

function handleUpdate(event: any) {
  emit('update', event);
}

function handleChoose(event: any) {
  emit('choose', event);
}

function handleUnchoose(event: any) {
  emit('unchoose', event);
}

function handleSort(event: any) {
  emit('sort', event);
}

function handleChange(event: any) {
  emit('change', event);
}

function handleMove(event: any) {
  emit('move', event);
}

function handleUpdateModelValue(value: DraggableItem[]) {
  internalList.value = value;
  emit('update:modelValue', value);
}
</script>

<template>
  <div class="vue-draggable-adapter">
    <VDraggable
      v-model="internalList"
      :group="group"
      :item-key="itemKey"
      :enabled="enabled"
      :handle="handle"
      :animation="animation"
      :ghost-class="ghostClass"
      :chosen-class="chosenClass"
      :filter="filter"
      :clone="clone"
      @start="handleStart"
      @end="handleEnd"
      @add="handleAdd"
      @remove="handleRemove"
      @update="handleUpdate"
      @choose="handleChoose"
      @unchoose="handleUnchoose"
      @sort="handleSort"
      @change="handleChange"
      @move="handleMove"
      @update:model-value="handleUpdateModelValue"
    >
      <!-- 渲染项目 -->
      <template v-for="(item, index) in internalList" :key="item[itemKey]">
        <div :data-id="item[itemKey]" class="vue-draggable-item">
          <slot name="item" :element="item" :index="index"></slot>
        </div>
      </template>

      <!-- 空状态插槽 -->
      <template v-if="internalList.length === 0">
        <slot name="empty"></slot>
      </template>
    </VDraggable>
  </div>
</template>

<style>
.vue-draggable-adapter {
  width: 100%;
  min-height: 20px;
}

.vue-draggable-item {
  width: 100%;
}
</style>
