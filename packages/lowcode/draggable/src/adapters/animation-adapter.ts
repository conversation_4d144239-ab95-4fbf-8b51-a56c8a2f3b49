/**
 * 动画类型
 */
export type AnimationType = 'bounce' | 'fade' | 'none' | 'slide' | 'zoom';

/**
 * 动画方向
 */
export type AnimationDirection =
  | 'down'
  | 'in'
  | 'left'
  | 'out'
  | 'right'
  | 'up';

/**
 * 动画配置
 */
export interface AnimationOptions {
  /** 动画延迟时间(ms) */
  delay?: number;
  /** 动画方向 */
  direction?: AnimationDirection;
  /** 动画持续时间(ms) */
  duration?: number;
  /** 是否无限重复 */
  infinite?: boolean;
  /** 动画重复次数 */
  repeat?: number;
  /** 动画类型 */
  type?: AnimationType;
}

/**
 * 生成CSS类名
 * @param options 动画配置
 * @returns CSS类名
 */
function generateClassName(options: AnimationOptions): string {
  const { direction = 'in', type = 'fade' } = options;

  if (type === 'none') {
    return '';
  }

  let className = 'animate__animated';

  // 添加动画类型
  switch (type) {
    case 'bounce': {
      className += ' animate__bounce';
      break;
    }
    case 'fade': {
      className +=
        direction === 'in' ? ' animate__fadeIn' : ' animate__fadeOut';
      break;
    }
    case 'slide': {
      switch (direction) {
        case 'down': {
          className += ' animate__slideInDown';
          break;
        }
        case 'left': {
          className += ' animate__slideInLeft';
          break;
        }
        case 'out': {
          className += ' animate__slideOutDown';
          break;
        }
        case 'right': {
          className += ' animate__slideInRight';
          break;
        }
        case 'up': {
          className += ' animate__slideInUp';
          break;
        }
        default: {
          className += ' animate__slideInDown';
        }
      }
      break;
    }
    case 'zoom': {
      className +=
        direction === 'in' ? ' animate__zoomIn' : ' animate__zoomOut';
      break;
    }
  }

  return className;
}

/**
 * 生成CSS样式
 * @param options 动画配置
 * @returns CSS样式对象
 */
function generateStyle(options: AnimationOptions): Record<string, string> {
  const { delay = 0, duration = 300, infinite = false, repeat = 1 } = options;

  return {
    '--animate-delay': `${delay}ms`,
    '--animate-duration': `${duration}ms`,
    '--animate-repeat': infinite ? 'infinite' : `${repeat}`,
  } as Record<string, string>;
}

/**
 * 应用动画到元素
 * @param element 目标元素
 * @param options 动画配置
 * @param callback 动画完成回调
 */
function applyAnimation(
  element: HTMLElement,
  options: AnimationOptions,
  callback?: () => void,
): void {
  if (!element || options.type === 'none') {
    if (callback) callback();
    return;
  }

  const className = generateClassName(options);
  const style = generateStyle(options);

  // 应用样式
  Object.entries(style).forEach(([key, value]) => {
    element.style.setProperty(key, value);
  });

  // 添加动画类
  element.classList.add(...className.split(' '));

  // 监听动画结束
  const handleAnimationEnd = () => {
    // 移除动画类
    element.classList.remove(...className.split(' '));

    // 移除样式
    Object.keys(style).forEach((key) => {
      element.style.removeProperty(key);
    });

    // 移除事件监听
    element.removeEventListener('animationend', handleAnimationEnd);

    // 执行回调
    if (callback) callback();
  };

  element.addEventListener('animationend', handleAnimationEnd);
}

/**
 * 动画适配器
 * 提供拖拽过程中的动画效果
 *
 * @param options 动画配置
 * @returns 动画相关方法
 */
export function useAnimation(defaultOptions: AnimationOptions = {}) {
  /**
   * 为拖拽项添加动画
   * @param element 拖拽项元素
   * @param options 动画配置
   * @param callback 动画完成回调
   */
  function animateDragItem(
    element: HTMLElement,
    options: Partial<AnimationOptions> = {},
    callback?: () => void,
  ) {
    applyAnimation(element, { ...defaultOptions, ...options }, callback);
  }

  /**
   * 为放置区域添加动画
   * @param element 放置区域元素
   * @param options 动画配置
   * @param callback 动画完成回调
   */
  function animateDropZone(
    element: HTMLElement,
    options: Partial<AnimationOptions> = {},
    callback?: () => void,
  ) {
    applyAnimation(element, { ...defaultOptions, ...options }, callback);
  }

  /**
   * 为容器添加动画
   * @param element 容器元素
   * @param options 动画配置
   * @param callback 动画完成回调
   */
  function animateContainer(
    element: HTMLElement,
    options: Partial<AnimationOptions> = {},
    callback?: () => void,
  ) {
    applyAnimation(element, { ...defaultOptions, ...options }, callback);
  }

  return {
    animateContainer,
    animateDragItem,
    animateDropZone,
    generateClassName,
    generateStyle,
  };
}
