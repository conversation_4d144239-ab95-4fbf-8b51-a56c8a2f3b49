import type { DraggableItemInfo } from '../types';
import type { TabItem } from './tabbar-adapter';

import { ref } from 'vue';

import { useContainerDrag } from './container-adapter';
import { useTabbarDrag } from './tabbar-adapter';

/**
 * 低代码平台适配器
 * 将通用拖拽能力转换为低代码平台需要的特定行为
 *
 * @param options 配置选项
 * @returns 低代码平台适配方法和属性
 */
export function useLowcodeDrag(
  options: {
    containerTypes?: string[];
    maxNestingLevel?: number;
    onSchemaChange?: (schema: any) => void;
  } = {},
) {
  const { containerTypes, maxNestingLevel = 5, onSchemaChange } = options;

  // 当前选中的组件ID
  const selectedId = ref<null | string>(null);

  // 使用容器拖拽适配器
  const containerDrag = useContainerDrag({
    containerTypes,
    maxNestingLevel,
    onNestingLevelExceeded: () => {
      // 使用允许的 console 方法
      console.warn(`已达到最大嵌套深度: ${maxNestingLevel}`);
    },
  });

  // 使用Tabbar拖拽适配器
  const tabbarDrag = useTabbarDrag({
    onTabSwitch: (_tabId) => {
      // 移除控制台日志
      // 使用下划线前缀表示有意未使用的参数
    },
  });

  /**
   * 处理从组件面板拖入画布
   * @param schema 当前Schema
   * @param componentType 组件类型
   * @param defaultProps 默认属性
   * @returns 拖拽处理方法
   */
  function handlePanelDrop(
    schema: any,
    componentType: string,
    defaultProps: Record<string, any> = {},
  ) {
    return {
      onDragEnd: (_event: DragEvent) => {
        // 拖拽结束处理
        // 使用下划线前缀表示有意未使用的参数
      },

      onDragStart: (event: DragEvent) => {
        // 设置拖拽数据
        if (event.dataTransfer) {
          const componentInfo: DraggableItemInfo = {
            id: `${componentType}-${Date.now()}`,
            props: { ...defaultProps },
            type: componentType,
            children: [],
          };

          event.dataTransfer.setData(
            'application/json',
            JSON.stringify(componentInfo),
          );
          event.dataTransfer.effectAllowed = 'copy';
        }
      },
    };
  }

  /**
   * 处理画布内组件拖拽
   * @param schema 当前Schema
   * @returns 拖拽处理方法
   */
  function handleCanvasDrag(schema: any) {
    return {
      onDragOver: (event: DragEvent) => {
        // 阻止默认行为以允许放置
        event.preventDefault();
        // 安全地访问 dataTransfer
        if (event.dataTransfer) {
          event.dataTransfer.dropEffect = 'move';
        }
      },

      onDragStart: (item: DraggableItemInfo, event: DragEvent) => {
        // 选中当前项
        selectedId.value = item.id;

        // 设置拖拽数据
        if (event.dataTransfer) {
          event.dataTransfer.setData('application/json', JSON.stringify(item));
          event.dataTransfer.effectAllowed = 'move';
        }
      },

      onDrop: (event: DragEvent) => {
        event.preventDefault();

        // 获取拖拽的项
        const itemData = event.dataTransfer?.getData('application/json');
        if (!itemData) return;

        try {
          const draggedItem = JSON.parse(itemData) as DraggableItemInfo;

          // 处理放置逻辑
          // 这里需要根据实际的Schema结构来实现
          const updatedSchema = { ...schema, draggedItem };

          // 通知Schema变化
          onSchemaChange?.(updatedSchema);
        } catch (error) {
          console.error('Invalid drag data', error);
        }
      },
    };
  }

  /**
   * 处理Tab布局容器
   * @param tabs Tab列表
   * @param onUpdate 更新回调
   * @returns Tab拖拽相关方法
   */
  function handleTabContainer(
    tabs: TabItem[],
    onUpdate: (newTabs: TabItem[]) => void,
  ) {
    return {
      // 当前激活的Tab
      activeTabId: tabbarDrag.activeTabId,

      // Tab内容区域拖放
      contentDrop: tabbarDrag.handleTabContentDrop(tabs, onUpdate),

      // Tab操作方法
      createTab: (title: string) => tabbarDrag.createTab(tabs, title, onUpdate),
      removeTab: (tabId: string) => tabbarDrag.removeTab(tabs, tabId, onUpdate),
      switchToTab: tabbarDrag.switchToTab,

      // Tab项拖拽排序
      tabDrag: tabbarDrag.handleTabDrag(tabs, onUpdate),
    };
  }

  /**
   * 处理嵌套容器
   * @param containers 容器列表
   * @param onUpdate 更新回调
   * @returns 容器拖拽相关方法
   */
  function handleNestedContainer(
    containers: DraggableItemInfo[],
    onUpdate: (newContainers: DraggableItemInfo[]) => void,
  ) {
    return {
      // 容器拖拽处理
      containerDrag: containerDrag.handleContainerDrag(containers, onUpdate),

      // 容器相关方法
      isContainer: containerDrag.isContainer,
      selectedId: containerDrag.selectedId,

      // 选中状态
      selectItem: containerDrag.selectItem,
      validateNesting: containerDrag.validateNesting,
    };
  }

  return {
    handleCanvasDrag,
    handleNestedContainer,
    handlePanelDrop,
    handleTabContainer,
    selectedId,
  };
}
