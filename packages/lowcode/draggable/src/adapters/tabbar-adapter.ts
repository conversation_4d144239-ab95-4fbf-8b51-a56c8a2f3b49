import type { DraggableItemInfo } from '../types';

import { ref } from 'vue';

/**
 * Tab项数据结构
 */
export interface TabItem {
  content: DraggableItemInfo[];
  icon?: string;
  id: string;
  title: string;
}

/**
 * Tabbar拖拽适配器 - 专门处理Tab布局场景
 *
 * @param options 配置选项
 * @returns Tabbar拖拽相关方法和属性
 */
export function useTabbarDrag(
  options: {
    onTabSwitch?: (tabId: string) => void;
  } = {},
) {
  const { onTabSwitch } = options;

  // 当前激活的Tab ID
  const activeTabId = ref<null | string>(null);

  /**
   * 切换到指定Tab
   * @param tabId Tab ID
   */
  function switchToTab(tabId: string) {
    activeTabId.value = tabId;
    onTabSwitch?.(tabId);
  }

  /**
   * 处理Tab内容区域的拖放
   * @param tabs Tab列表
   * @param onUpdate 更新回调
   * @returns 拖拽处理方法
   */
  function handleTabContentDrop(
    tabs: TabItem[],
    onUpdate: (newTabs: TabItem[]) => void,
  ) {
    return {
      onDragOver: (event: DragEvent, tabId: string) => {
        // 阻止默认行为以允许放置
        event.preventDefault();

        // 自动切换到目标Tab
        if (activeTabId.value !== tabId) {
          switchToTab(tabId);
        }
      },

      onDrop: (event: DragEvent, tabId: string) => {
        event.preventDefault();

        // 获取拖拽的项
        const itemData = event.dataTransfer?.getData('application/json');
        if (!itemData) return;

        try {
          const draggedItem = JSON.parse(itemData) as DraggableItemInfo;

          // 查找目标Tab
          const tabIndex = tabs.findIndex((tab) => tab.id === tabId);
          if (tabIndex === -1) return;

          // 创建新的Tab列表
          const newTabs = [...tabs];

          // 添加项目到目标Tab
          newTabs[tabIndex] = {
            ...newTabs[tabIndex],
            content: [...newTabs[tabIndex].content, draggedItem],
          };

          // 更新Tab列表
          onUpdate(newTabs);
        } catch (error) {
          console.error('Invalid drag data', error);
        }
      },
    };
  }

  /**
   * 处理Tab项的拖拽排序
   * @param tabs Tab列表
   * @param onUpdate 更新回调
   * @returns 拖拽处理方法
   */
  function handleTabDrag(
    tabs: TabItem[],
    onUpdate: (newTabs: TabItem[]) => void,
  ) {
    return {
      onDragOver: (event: DragEvent) => {
        // 阻止默认行为以允许放置
        event.preventDefault();
      },

      onDragStart: (tab: TabItem, event: DragEvent) => {
        // 设置拖拽数据
        if (event.dataTransfer) {
          event.dataTransfer.setData('application/tab', JSON.stringify(tab));
        }
      },

      onDrop: (event: DragEvent, targetIndex: number) => {
        event.preventDefault();

        // 获取拖拽的Tab
        const tabData = event.dataTransfer?.getData('application/tab');
        if (!tabData) return;

        try {
          const draggedTab = JSON.parse(tabData) as TabItem;

          // 查找源Tab索引
          const sourceIndex = tabs.findIndex((tab) => tab.id === draggedTab.id);
          if (sourceIndex === -1 || sourceIndex === targetIndex) return;

          // 创建新的Tab列表
          const newTabs = [...tabs];

          // 移除源Tab
          const [removed] = newTabs.splice(sourceIndex, 1);

          // 插入到目标位置
          newTabs.splice(targetIndex, 0, removed);

          // 更新Tab列表
          onUpdate(newTabs);

          // 保持当前Tab激活
          switchToTab(draggedTab.id);
        } catch (error) {
          console.error('Invalid tab data', error);
        }
      },
    };
  }

  /**
   * 创建新Tab
   * @param tabs 当前Tab列表
   * @param title 新Tab标题
   * @param onUpdate 更新回调
   */
  function createTab(
    tabs: TabItem[],
    title: string,
    onUpdate: (newTabs: TabItem[]) => void,
  ) {
    const newTab: TabItem = {
      content: [],
      id: `tab-${Date.now()}`,
      title,
    };

    const newTabs = [...tabs, newTab];
    onUpdate(newTabs);

    // 自动切换到新Tab
    switchToTab(newTab.id);

    return newTab;
  }

  /**
   * 删除Tab
   * @param tabs 当前Tab列表
   * @param tabId 要删除的Tab ID
   * @param onUpdate 更新回调
   */
  function removeTab(
    tabs: TabItem[],
    tabId: string,
    onUpdate: (newTabs: TabItem[]) => void,
  ) {
    const tabIndex = tabs.findIndex((tab) => tab.id === tabId);
    if (tabIndex === -1) return;

    const newTabs = [...tabs];
    newTabs.splice(tabIndex, 1);

    // 如果删除的是当前激活的Tab，则切换到相邻Tab
    if (activeTabId.value === tabId && newTabs.length > 0) {
      const newActiveIndex = Math.min(tabIndex, newTabs.length - 1);
      switchToTab(newTabs[newActiveIndex].id);
    }

    onUpdate(newTabs);
  }

  return {
    activeTabId,
    createTab,
    handleTabContentDrop,
    handleTabDrag,
    removeTab,
    switchToTab,
  };
}
