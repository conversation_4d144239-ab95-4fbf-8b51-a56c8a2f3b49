#!/bin/bash

# 版本号参数和构建模式参数
VERSION=$1     # 版本号
BUILD_MODE=$2  # 构建模式：build(默认)或no-build

# 检查参数
if [ -z "$VERSION" ]; then
  echo "用法: ./scripts/publish-dist.sh <版本号> [build|no-build]"
  echo "例子: ./scripts/publish-dist.sh v1.0.0       # 默认构建模式"
  echo "      ./scripts/publish-dist.sh v1.0.0 build    # 显式指定构建模式"
  echo "      ./scripts/publish-dist.sh v1.0.0 no-build # 不构建，只添加版本号并推送"
  exit 1
fi

# 如果没有指定构建模式，默认为构建模式
if [ -z "$BUILD_MODE" ]; then
  BUILD_MODE="build"
fi

# 确保在项目根目录
cd "$(dirname "$0")/.."

# 2. 准备部署包目录
DEPLOY_DIR="deploy-packages"
mkdir -p "$DEPLOY_DIR"

# 3. 如果是首次运行，初始化 git 仓库
if [ ! -d "$DEPLOY_DIR/.git" ]; then
  cd "$DEPLOY_DIR"
  git init
  git remote add origin https://gitee.com/moshenwl/iot-network-card-dist.git
  echo "# IOT Network Card Deployment Packages (标准版和高级版) $VERSION" > README.md
  git add README.md
  git commit -m "chore: 初始化部署包仓库"
  cd ..
fi

# 4. 准备版本目录
VERSION_DIR="$DEPLOY_DIR/versions/$VERSION"

# 如果是构建模式，则执行构建过程
if [ "$BUILD_MODE" = "build" ]; then
  # 清理所有旧版本目录
  echo "清理所有旧版本目录"
  rm -rf "$DEPLOY_DIR/versions"
  mkdir -p "$DEPLOY_DIR/versions"

  # 创建版本目录及子目录
  mkdir -p "$VERSION_DIR/standard"
  mkdir -p "$VERSION_DIR/premium"

  # 5. 构建并复制标准版
  echo "开始构建标准版..."
  pnpm build
  echo "复制标准版构建文件到: $VERSION_DIR/standard"
  cp -r dist/* "$VERSION_DIR/standard/"

  # 6. 构建并复制高级版
  echo "开始构建高级版..."
  pnpm build:premium
  echo "复制高级版构建文件到: $VERSION_DIR/premium"
  cp -r dist/* "$VERSION_DIR/premium/"
else
  echo "使用no-build模式，跳过构建过程"

  # 确保版本目录存在
  if [ ! -d "$VERSION_DIR" ]; then
    echo "错误: 版本目录 $VERSION_DIR 不存在，无法使用no-build模式"
    echo "请先使用构建模式创建此版本，或者检查版本号是否正确"
    exit 1
  fi
fi

# 7. 提交部署包
cd "$DEPLOY_DIR"

# 更新软链接到最新版本
if [ -L "latest" ]; then
  echo "更新软链接 latest"
  rm -f "latest"
fi
ln -s "versions/$VERSION" "latest"

# 添加所有更改
git add .

# 提交更改
git commit -m "feat(deploy): 更新版本 $VERSION 的部署包（标准版和高级版）"

# 删除已存在的标签（如果有）
if git tag | grep -q "^$VERSION$"; then
  echo "删除已存在的标签: $VERSION"
  git tag -d "$VERSION"
  git push origin ":$VERSION" || true
fi

# 创建新的版本标签
git tag -a "$VERSION" -m "部署包 $VERSION（标准版和高级版）"

# 推送到远程仓库
echo "推送更改到远程仓库..."
git push origin master --force
git push origin "$VERSION" --force

echo "部署包发布完成！"
echo "版本: $VERSION"
echo "模式: ${BUILD_MODE}"
if [ "$BUILD_MODE" = "build" ]; then
  echo "目录结构:"
  echo "  - $VERSION_DIR/standard  (标准版)"
  echo "  - $VERSION_DIR/premium   (高级版)"
fi
echo "仓库地址: https://gitee.com/moshenwl/iot-network-card-dist"
