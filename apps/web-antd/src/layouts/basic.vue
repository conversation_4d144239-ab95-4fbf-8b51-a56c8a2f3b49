<script lang="ts" setup>
import type { NotificationItem } from '@vben/layouts';

import { computed, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationLoginExpiredModal } from '@vben/common-ui';
import { VBEN_DOC_URL } from '@vben/constants';
import { useWatermark } from '@vben/hooks';
import { BookOpenText, LucideBookKey, MdiAccount } from '@vben/icons';
import { BasicLayout, LockScreen, UserDropdown } from '@vben/layouts';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';
import { openWindow } from '@vben/utils';

import { message } from 'ant-design-vue';

import { $t } from '#/locales';
import { useAuthStore, useSiteStore } from '#/store';
import { getDomainName } from '#/utils/domain';
import LoginForm from '#/views/_core/authentication/login.vue';
import ChangePassword from '#/views/account/settings/components/change-password.vue';

const router = useRouter();
// 控制修改密码弹窗的显示
const modalVisible = ref(false);
const notifications = ref<NotificationItem[]>([
  {
    avatar: 'https://avatar.vercel.sh/vercel.svg?text=VB',
    date: '3小时前',
    isRead: true,
    message: '描述信息描述信息描述信息',
    title: '收到了 14 份新周报',
  },
  {
    avatar: 'https://avatar.vercel.sh/1',
    date: '刚刚',
    isRead: false,
    message: '描述信息描述信息描述信息',
    title: '朱偏右 回复了你',
  },
  {
    avatar: 'https://avatar.vercel.sh/1',
    date: '2024-01-01',
    isRead: false,
    message: '描述信息描述信息描述信息',
    title: '曲丽丽 评论了你',
  },
  {
    avatar: 'https://avatar.vercel.sh/satori',
    date: '1天前',
    isRead: false,
    message: '描述信息描述信息描述信息',
    title: '代办提醒',
  },
]);

const userStore = useUserStore();
const siteStore = useSiteStore();
const authStore = useAuthStore();
const accessStore = useAccessStore();
const { destroyWatermark, updateWatermark } = useWatermark();

// 显示修改密码弹窗
const showModal = () => {
  modalVisible.value = true;
};

// 密码修改成功的处理函数
const handlePasswordSuccess = () => {
  message.success('密码修改成功');
};

// 密码修改失败的处理函数
const handlePasswordError = (errorMsg: string) => {
  console.error('密码修改失败:', errorMsg);
};

// 取消修改密码的处理函数
const handlePasswordCancel = () => {
  console.log('取消修改密码');
};

const showDot = computed(() =>
  notifications.value.some((item) => !item.isRead),
);

const menus = computed(() => [
  {
    handler: () => {
      openWindow(VBEN_DOC_URL, {
        target: '_blank',
      });
    },
    icon: BookOpenText,
    text: $t('ui.widgets.document'),
  },
  // 修改密码
  {
    handler: () => {
      // alert('修改密码');
      showModal();
    },
    icon: LucideBookKey,
    text: '修改密码',
  },
  // 个人中心
  {
    handler: () => {
      router.push('/account/settings');
    },
    icon: MdiAccount,
    text: '个人中心',
  },
  // {
  //   handler: () => {
  //     openWindow(VBEN_GITHUB_URL, {
  //       target: '_blank',
  //     });
  //   },
  //   icon: MdiGithub,
  //   text: 'GitHub',
  // },
  // {
  //   handler: () => {
  //     openWindow(`${VBEN_GITHUB_URL}/issues`, {
  //       target: '_blank',
  //     });
  //   },
  //   icon: CircleHelp,
  //   text: $t('ui.widgets.qa'),
  // },
]);

const avatar = computed(() => {
  return userStore.userInfo?.avatar ?? preferences.app.defaultAvatar;
});

async function handleLogout() {
  await authStore.logout(false);
}

function handleNoticeClear() {
  notifications.value = [];
}

function handleMakeAll() {
  notifications.value.forEach((item) => (item.isRead = true));
}
watch(
  () => preferences.app.watermark,
  async (enable) => {
    if (enable) {
      await updateWatermark({
        content: `${userStore.userInfo?.userName}`,
      });
    } else {
      destroyWatermark();
    }
  },
  {
    immediate: true,
  },
);

onMounted(async () => {
  // 从环境变量中获取默认域名
  // const defaultDomain = import.meta.env.VITE_APP_DEFAULT_DOMAIN;
  const domainName = getDomainName();
  await siteStore.fetchSiteSettings(domainName);
});
</script>

<template>
  <BasicLayout
    :config="siteStore.copyright"
    :settings="siteStore.settings"
    @clear-preferences-and-logout="handleLogout"
  >
    <template #user-dropdown>
      <UserDropdown
        :avatar
        :menus
        :text="userStore.userInfo?.userAccount"
        :description="`用户佣金:${userStore.userInfo?.userWage}`"
        :tag-text="`${userStore.userInfo?.authority === 1 ? '管理员' : '代理'}`"
        @logout="handleLogout"
      />
    </template>
    <template #notification>
      <Notification
        :dot="showDot"
        :notifications="notifications"
        @clear="handleNoticeClear"
        @make-all="handleMakeAll"
      />
    </template>
    <template #extra>
      <AuthenticationLoginExpiredModal
        v-model:open="accessStore.loginExpired"
        :avatar
      >
        <LoginForm />
      </AuthenticationLoginExpiredModal>
    </template>
    <template #lock-screen>
      <LockScreen :avatar @to-login="handleLogout" />
    </template>
  </BasicLayout>
  <!-- 使用改造后的ChangePassword组件 -->
  <ChangePassword
    v-model="modalVisible"
    title="修改密码"
    @success="handlePasswordSuccess"
    @error="handlePasswordError"
    @cancel="handlePasswordCancel"
  />
</template>
