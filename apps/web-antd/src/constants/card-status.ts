// 卡片状态码 1未知  2待激活 3已激活 4已停机 5预销号 6已销号 7已拆机  8测试期 9沉默期 10库存 11已过户 12 异常
export const statusOptions = [
  { label: '未知', value: 1, color: 'default' },
  { label: '待激活', value: 2, color: 'warning' },
  { label: '已激活', value: 3, color: 'success' },
  { label: '已停机', value: 4, color: 'error' },
  { label: '预销号', value: 5, color: 'warning' },
  { label: '已销号', value: 6, color: 'error' },
  { label: '已拆机', value: 7, color: 'error' },
  { label: '测试期', value: 8, color: 'processing' },
  { label: '沉默期', value: 9, color: 'default' },
  { label: '库存', value: 10, color: 'default' },
  { label: '已过户', value: 11, color: 'default' },
  { label: '异常', value: 12, color: 'error' },
];

// 订单状态选项
export const OrderStateOptions = [
  { label: '等待递交', value: 1, color: 'warning' },
  { label: '成功', value: 2, color: 'success' },
  { label: '失败', value: 3, color: 'default' },
  { label: '待支付', value: 4, color: 'error' },
  { label: '已过期', value: 5, color: 'processing' },
  { label: '已退款', value: 6, color: 'default' },
];

// 实名状态选项
export const realNameOptions = [
  { label: '未知', value: 0, color: 'default' },
  { label: '未实名', value: 1, color: 'warning' },
  { label: '已实名', value: 2, color: 'success' },
  { label: '实名失败', value: 3, color: 'error' },
];
// 实名状态选项
export const realNameOptions2 = [
  { label: '未知', value: 0, color: 'default' },
  { label: '未实名', value: 1, color: 'warning' },
  { label: '已实名', value: 2, color: 'success' },
  { label: '无需实名', value: 3, color: 'error' },
];

// 激活状态选项
export const FirstStatusOptions = [
  { label: '未激活', value: 1, color: 'default' },
  { label: '已激活', value: 2, color: 'success' },
  { label: '激活失败', value: 3, color: 'error' },
];

// 生效类型选项
export const EffectiveTypeOptions = [
  { label: '立即生效', value: 1 },
  { label: '次月生效', value: 2 },
  { label: '客户自选', value: 3 },
];

// 支付方式选项
export const PaymentOptions = [
  { label: '未支付', value: 0 },
  { label: '余额支付', value: 1 },
  { label: '批量充值', value: 2 },
  { label: '后台单冲', value: 3 },
  { label: '接口递交', value: 4 },
  { label: '微信JSAPI', value: 5 },
  { label: '支付宝当面付', value: 6 },
  { label: '点卡充值', value: 7 },
  { label: '自动订购', value: 8 },
  { label: '汇付', value: 9 },
  { label: '火脸', value: 10 },
  { label: '斗拱', value: 11 },
  { label: '小马哥', value: 12 },
];

// 退款类型选项
export const RefundTypeOptions = [
  { label: '不退金额', value: '0' },
  { label: '原路退回', value: '1' },
  { label: '退回余额', value: '2' },
  { label: '抹除佣金', value: '3' },
];

// 补单类型选项
export const ReplenishTypeOptions = [
  { label: '只修改状态', value: 0 },
  { label: '不提交api', value: 1 },
  { label: '提交api', value: 2 },
];

// 网速接入
export const NetSpeedOptions = [
  { label: '不支持', value: 0 },
  { label: '2G', value: 1 },
  { label: '3G', value: 2 },
  { label: '4G', value: 3 },
  { label: '5G', value: 4 },
  { label: 'NB', value: 6 },
];

// "change_card": 1, //换卡类型 1强制换卡 2非强制换卡 3关闭
export const ChangeCardOptions = [
  { label: '强制换卡', value: 1, color: 'error' },
  { label: '非强制换卡', value: 2, color: 'warning' },
  { label: '关闭', value: 3, color: 'default' },
];

// 冻结状态选项
export const FrozenStatusOptions = [
  { label: '已冻结', value: 1, color: 'error' },
  { label: '未冻结', value: 2, color: 'success' },
];

// APN状态选项
export const ApnStatusOptions = [
  { label: '正常', value: 1, color: 'success' },
  { label: '断网', value: 2, color: 'error' },
];

// 系统状态选项
export const SystemStatusOptions = [
  { label: '正常', value: 1, color: 'success' },
  { label: '停用', value: 2, color: 'error' },
];

// 在线状态选项
export const OnlineStatusOptions = [
  { label: '在线', value: 1, color: 'success' },
  { label: '离线', value: 2, color: 'error' },
  { label: '不支持', value: 3, color: 'default' },
];
