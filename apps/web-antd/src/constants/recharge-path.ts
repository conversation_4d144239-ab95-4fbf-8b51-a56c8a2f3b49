// 应用版本类型: standard(标准版) 或 premium(旗舰版)
const versionType = import.meta.env.VITE_VERSION_TYPE;

// type 1是链接 2是小程序
const premium = [
  { label: '充值端app', value: 1, path: 'app', type: 1 },
  { label: '微信小程序1', value: 2, path: '', type: 2 },
  { label: '充值端app_sw', value: 3, path: 'app_sw', type: 1 },
  { label: '设备端device_aw', value: 4, path: 'device_aw', type: 1 },
];

const standard = [
  { label: '充值端appAn', value: 1, path: 'appAn', type: 1 },
  { label: '设备端appDevice', value: 2, path: 'deviceAn', type: 1 },
];

export const rechargePathOptions =
  versionType === 'premium' ? premium : standard;
