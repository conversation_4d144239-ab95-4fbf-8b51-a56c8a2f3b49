// 网络切换
export const networkSwitch = [
  { label: '不支持', value: 1 },
  { label: '支持', value: 2 },
  { label: '虚假', value: 3 },
  { label: '内置卡切换', value: 4 },
];

// 电量显示
export const powerDisplay = [
  { label: '不支持', value: 1 },
  { label: '支持', value: 2 },
];

// 信号显示
export const signaling = [
  { label: '不支持', value: 1 },
  { label: '支持', value: 2 },
];

// 名称修改
export const wifiName = [
  { label: '不支持', value: 1 },
  { label: '支持', value: 2 },
];

// 密码修改
export const wifiPwd = [
  { label: '不支持', value: 1 },
  { label: '支持', value: 2 },
];

// 隐藏状态
export const wifiStatus = [
  { label: '不支持', value: 1 },
  { label: '支持', value: 2 },
];

// 重置设备
export const restoreFactory = [
  { label: '不支持', value: 1 },
  { label: '支持', value: 2 },
];

// 重启设备
export const restart = [
  { label: '不支持', value: 1 },
  { label: '支持', value: 2 },
];

// 清除缓存
export const clearCache = [
  { label: '不支持', value: 1 },
  { label: '支持', value: 2 },
];

// 卡槽网络
export const cardSlotNetwork = [
  { label: '单网', value: 1 },
  { label: '双网', value: 2 },
  { label: '三网', value: 3 },
  { label: '四网', value: 4 },
];

// 网络运营商选项
export const networkOptions = [
  { label: '未知', value: 0 },
  { label: '中国电信', value: 1 },
  { label: '中国联通', value: 2 },
  { label: '中国移动', value: 3 },
  { label: '中国广电', value: 4 },
];

// "status": 1, //设备状态 1未知 2待激活 3已激活 4已停机
export const deviceStatusOptions = [
  { label: '未知', value: 1, color: 'default' },
  { label: '待激活', value: 2, color: 'warning' },
  { label: '已激活', value: 3, color: 'success' },
  { label: '已停机', value: 4, color: 'error' },
];
