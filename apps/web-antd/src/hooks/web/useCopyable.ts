import { h } from 'vue';

import { useClipboard } from '@vueuse/core';
import { message } from 'ant-design-vue';

export function useCopyable() {
  const { copy: vueuseCopy } = useClipboard();

  // 新增：直接使用原生Clipboard API
  const copyWithNativeAPI = async (text: string): Promise<boolean> => {
    if (!navigator.clipboard) return false;
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      console.warn('Native clipboard API failed:', error);
      return false;
    }
  };

  const fallbackCopyToClipboard = async (text: string): Promise<boolean> => {
    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // 将文本区域添加到页面但保持不可见
    textArea.style.position = 'fixed';
    textArea.style.left = '-9999px';
    textArea.style.top = '0';
    document.body.append(textArea);

    try {
      // 选择文本
      textArea.select();
      textArea.setSelectionRange(0, textArea.value.length);

      // 尝试执行复制命令
      const successful = document.execCommand('copy');
      textArea.remove();
      return successful;
    } catch {
      textArea.remove();
      return false;
    }
  };

  const handleCopy = async (text: string) => {
    if (!text || text === '-') return;

    let success = false;

    // 第一步：尝试使用原生API (尝试两次)
    for (let i = 0; i < 2 && !success; i++) {
      success = await copyWithNativeAPI(text);
      if (success) break;
      // 短暂延迟后再次尝试
      if (i < 1) await new Promise((r) => setTimeout(r, 50));
    }

    // 第二步：尝试VueUse的方法
    if (!success) {
      try {
        await vueuseCopy(text);
        success = true;
      } catch (error) {
        console.warn('VueUse clipboard failed:', error);
      }
    }

    // 第三步：尝试fallback方法
    if (!success) {
      try {
        success = await fallbackCopyToClipboard(text);
      } catch (error) {
        console.warn('Fallback method failed:', error);
      }
    }

    // 结果反馈
    if (success) {
      message.success('复制成功');
    } else {
      message.error('复制失败，请尝试手动复制');
      console.error('所有复制方法均失败');
    }
  };

  const createCopyableCell = (text: any, formatter?: (val: any) => string) => {
    const content = formatter ? formatter(text) : text || '-';

    return h(
      'div',
      {
        class: 'copyable-cell',
        onClick: () => handleCopy(content),
        style: {
          cursor: 'pointer',
          userSelect: 'text', // 允许用户手动选择文本
        },
        title: '点击复制', // 添加提示
      },
      content,
    );
  };

  return {
    handleCopy,
    createCopyableCell,
  };
}
