import type { PictureCategory, PictureItem } from '#/api/core/picture';

import { computed, h, ref } from 'vue';

import { Mail<PERSON>heck, MdiPlus, MdiUpload } from '@vben/icons';

import {
  Button,
  Dropdown,
  Form,
  Image,
  Input,
  message,
  Modal,
  Pagination,
  Spin,
  Upload,
} from 'ant-design-vue';

import {
  createPictureCategory,
  getPictureCategories,
  getPictureList,
  uploadFile,
} from '#/api/core/picture';
import { getFileUrl } from '#/utils/file';

// 配置message的全局z-index
message.config({
  maxCount: 1,
  top: '24px',
  duration: 2,
  getContainer: () => document.body,
});

interface ImageItem {
  id: string;
  src: string;
  category: string;
}

interface Category {
  id: string;
  name: string;
}

interface ImagePreviewModalOptions {
  initialPage?: number;
  pageSize?: number;
  title?: string;
  /**
   * 是否启用多选模式
   * @default false
   */
  multiple?: boolean;
  /**
   * 多选模式下的最大选择数量
   * @default 9
   */
  maxCount?: number;
  /**
   * 预选中的图片ID列表
   */
  preselectedIds?: (number | string)[];
  /**
   * 单选模式下的选择回调
   * 与 onSelectMultiple 互斥，优先使用 onSelectMultiple
   */
  onSelect?: (selectedImage: ImageItem) => void;
  /**
   * 多选模式下的选择回调
   */
  onSelectMultiple?: (selectedImages: ImageItem[]) => void;
  modalProps?: {
    zIndex?: number;
  };
}

// 清理缓存
function clearCache(
  cache: Record<string, { images: ImageItem[]; total: number }>,
) {
  // 安全地清除缓存对象中的所有键
  Object.keys(cache).forEach((key) => {
    // 使用设置为空对象的方式而不是删除
    cache[key] = { images: [], total: 0 };
  });
}

export function useImagePreviewModal() {
  const visible = ref(false);
  const currentPage = ref(1);
  const pageSize = ref(8);
  const total = ref(0);
  const images = ref<ImageItem[]>([]);
  const categories = ref<Category[]>([]);
  const modalTitle = ref('');
  const selectedImage = ref<ImageItem | null>(null);
  // 多选模式的选中图片数组
  const selectedImages = ref<ImageItem[]>([]);
  const activeCategory = ref('');
  // 多选相关配置
  const isMultipleMode = ref(false);
  const maxSelectCount = ref(9);

  const loading = ref(false);
  const uploadLoading = ref(false);

  let onSelectCallback: ((image: ImageItem) => void) | undefined;
  let onSelectMultipleCallback: ((images: ImageItem[]) => void) | undefined;

  // 添加缓存状态
  const categoryCache = ref<
    Record<string, { images: ImageItem[]; total: number }>
  >({});

  // 添加分类相关状态
  const showAddCategory = ref(false);
  const categoryName = ref('');
  const addCategoryLoading = ref(false);

  // 添加显示数量限制
  const MAX_VISIBLE_CATEGORIES = 5;

  const options = ref<ImagePreviewModalOptions>();

  // 是否已经达到最大选择数量 - 用于UI显示和逻辑控制
  const _isMaxSelected = computed(
    () =>
      isMultipleMode.value &&
      selectedImages.value.length >= maxSelectCount.value,
  );

  const fetchCategories = async () => {
    try {
      const categoriesRes = await getPictureCategories({
        page: 1,
        pageSize: 100_000,
      });
      categories.value = [
        { id: '全部', name: '全部' },
        ...categoriesRes.data.rows.map((item: PictureCategory) => ({
          id: item.id.toString(),
          name: item.name,
        })),
      ];
      if (!activeCategory.value) {
        activeCategory.value = '全部';
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };

  const fetchImages = async () => {
    const cacheKey = `${activeCategory.value}-${currentPage.value}-${pageSize.value}`;

    // 如果有缓存，先显示缓存数据
    if (categoryCache.value[cacheKey]) {
      images.value = categoryCache.value[cacheKey].images;
      total.value = categoryCache.value[cacheKey].total;
    }

    try {
      loading.value = true;
      const picturesRes = await getPictureList({
        page: currentPage.value,
        pageSize: pageSize.value,
        name_id: activeCategory.value === '全部' ? '' : activeCategory.value,
      });

      const newImages = picturesRes.data.rows.map((item: PictureItem) => ({
        id: item.id.toString(),
        src: item.file_path,
        category: item.name_id || '',
      }));

      // 更新缓存和显示数据
      categoryCache.value[cacheKey] = {
        images: newImages,
        total: picturesRes.data.total,
      };
      images.value = newImages;
      total.value = picturesRes.data.total;
    } catch (error) {
      console.error('Failed to fetch images:', error);
      // 如果请求失败但有缓存，保持缓存数据显示
      if (!categoryCache.value[cacheKey]) {
        images.value = [];
        total.value = 0;
      }
    } finally {
      loading.value = false;
    }
  };

  // 处理预选功能
  const handlePreselection = (ids: (number | string)[]) => {
    if (ids.length === 0) return;

    if (isMultipleMode.value) {
      // 多选模式：筛选出所有匹配ID的图片并添加到选中列表
      const preselectedImages = images.value.filter(
        (img) =>
          ids.includes(img.id.toString()) || ids.includes(Number(img.id)),
      );

      if (preselectedImages.length > 0) {
        selectedImages.value = preselectedImages;
      }
    } else {
      // 单选模式：筛选第一个匹配ID的图片
      const firstMatch = images.value.find(
        (img) =>
          ids.includes(img.id.toString()) || ids.includes(Number(img.id)),
      );

      if (firstMatch) {
        selectedImage.value = firstMatch;
      }
    }
  };

  const showModal = async (modalOptions: ImagePreviewModalOptions) => {
    options.value = modalOptions;
    pageSize.value = options.value?.pageSize || 12;
    modalTitle.value = options.value?.title || '图片预览';

    // 设置多选模式
    isMultipleMode.value = options.value?.multiple === true;
    maxSelectCount.value = options.value?.maxCount || 9;

    // 重置选中状态
    selectedImage.value = null;
    selectedImages.value = [];

    // 设置回调
    onSelectCallback = options.value?.onSelect;
    onSelectMultipleCallback = options.value?.onSelectMultiple;

    // 清空已选状态
    selectedImage.value = null;
    selectedImages.value = [];

    visible.value = true;
    await fetchCategories();
    await fetchImages();

    // 加载完图片后处理预选
    if (options.value?.preselectedIds?.length) {
      handlePreselection(options.value.preselectedIds);
    }
  };

  const hideModal = () => {
    visible.value = false;
    clearCache(categoryCache.value);
  };

  const handlePageChange = (page: number) => {
    currentPage.value = page;
    fetchImages();
  };

  // 处理图片选择 - 支持单选和多选
  const handleImageSelect = (image: ImageItem) => {
    if (isMultipleMode.value) {
      // 多选模式
      const index = selectedImages.value.findIndex(
        (img) => img.id === image.id,
      );

      if (index === -1) {
        // 未选中，且未达到最大选择数量，则添加
        if (selectedImages.value.length < maxSelectCount.value) {
          selectedImages.value = [...selectedImages.value, image];
        } else {
          message.warning(`最多只能选择${maxSelectCount.value}张图片`);
        }
      } else {
        // 已选中，则移除
        selectedImages.value = selectedImages.value.filter(
          (img) => img.id !== image.id,
        );
      }
    } else {
      // 单选模式
      selectedImage.value = image;
    }
  };

  // 检查图片是否已选中
  const isImageSelected = (image: ImageItem) => {
    return isMultipleMode.value
      ? selectedImages.value.some((img) => img.id === image.id)
      : selectedImage.value?.id === image.id;
  };

  const handleConfirm = () => {
    if (isMultipleMode.value) {
      // 多选模式
      if (selectedImages.value.length > 0 && onSelectMultipleCallback) {
        onSelectMultipleCallback(selectedImages.value);
        hideModal();
      } else if (selectedImages.value.length === 0) {
        message.warning('请至少选择一张图片');
      }
    } else {
      // 单选模式
      if (selectedImage.value && onSelectCallback) {
        onSelectCallback(selectedImage.value);
        hideModal();
      } else {
        message.warning('请选择一张图片');
      }
    }
  };

  const handleUpload = async (file: File) => {
    if (!activeCategory.value || activeCategory.value === '全部') {
      message.warning('请先选择一个分类');
      return;
    }

    uploadLoading.value = true;
    try {
      const res = await uploadFile(file, activeCategory.value);
      if (res.code === 1) {
        message.success('上传成功');
        await fetchImages(); // 刷新图片列表
      } else {
        throw new Error(res.msg || '上传失败');
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '上传失败');
    } finally {
      uploadLoading.value = false;
    }
  };

  // 处理分类切换
  const handleCategoryChange = (categoryId: string) => {
    if (loading.value || activeCategory.value === categoryId) return;

    activeCategory.value = categoryId;
    currentPage.value = 1;

    // 切换分类时不清空已选择的图片
    // selectedImage.value = null;
    // selectedImages.value = [];

    // 检查是否有缓存
    const cacheKey = `${categoryId}-1-${pageSize.value}`;
    if (categoryCache.value[cacheKey]) {
      images.value = categoryCache.value[cacheKey].images;
      total.value = categoryCache.value[cacheKey].total;
    }

    fetchImages();
  };

  // 处理添加分类
  const handleAddCategory = async () => {
    if (!categoryName.value.trim()) {
      message.warning('请输入分类名称');
      return;
    }

    addCategoryLoading.value = true;
    try {
      const res = await createPictureCategory(categoryName.value.trim());
      if (res.code === 1) {
        message.success('添加成功');
        showAddCategory.value = false;
        categoryName.value = '';
        await fetchCategories(); // 刷新分类列表
      } else {
        throw new Error(res.msg || '添加失败');
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '添加失败');
    } finally {
      addCategoryLoading.value = false;
    }
  };

  // 添加分类表单内容
  const _renderAddCategoryForm = () => {
    return h(
      'div',
      {
        class: 'flex flex-col gap-2 p-2',
        style: {
          minWidth: '200px',
        },
      },
      [
        h(Input, {
          value: categoryName.value,
          'onUpdate:value': (val: string) => (categoryName.value = val),
          placeholder: '请输入分类名称',
          onPressEnter: handleAddCategory,
        }),
        h(
          'div',
          {
            class: 'flex justify-end gap-2 mt-2',
          },
          [
            h(
              Button,
              {
                size: 'small',
                onClick: () => {
                  showAddCategory.value = false;
                  categoryName.value = '';
                },
              },
              '取消',
            ),
            h(
              Button,
              {
                type: 'primary',
                size: 'small',
                loading: addCategoryLoading.value,
                onClick: handleAddCategory,
              },
              '确定',
            ),
          ],
        ),
      ],
    );
  };

  // 添加分类弹窗
  const addCategoryModal = () => {
    return h(
      Modal,
      {
        visible: showAddCategory.value,
        title: '添加分类',
        confirmLoading: addCategoryLoading.value,
        onOk: handleAddCategory,
        onCancel: () => {
          showAddCategory.value = false;
          categoryName.value = '';
        },
        zIndex: (options?.value?.modalProps?.zIndex || 1000) + 10,
      },
      {
        default: () =>
          h(
            Form,
            {
              labelCol: { span: 6 },
              wrapperCol: { span: 16 },
            },
            () =>
              h(
                Form.Item,
                {
                  label: '分类名称',
                  required: true,
                },
                () =>
                  h(Input, {
                    value: categoryName.value,
                    'onUpdate:value': (val: string) =>
                      (categoryName.value = val),
                    placeholder: '请输入分类名称',
                  }),
              ),
          ),
      },
    );
  };

  const ImagePreviewModal = () => {
    // 渲染分类菜单
    const renderCategories = () => {
      const visibleCategories = categories.value.slice(
        0,
        MAX_VISIBLE_CATEGORIES,
      );
      const hiddenCategories = categories.value.slice(MAX_VISIBLE_CATEGORIES);

      return [
        ...visibleCategories.map((category) =>
          h(
            'div',
            {
              class: `px-3 py-1 cursor-pointer whitespace-nowrap ${activeCategory.value === category.id ? 'text-primary border-b-2 border-primary' : 'text-gray-600'}`,
              onClick: () => handleCategoryChange(category.id),
            },
            category.name,
          ),
        ),
        // 如果有更多分类，显示下拉菜单
        hiddenCategories.length > 0 &&
          h(
            Dropdown,
            {
              trigger: ['click'],
              placement: 'bottomRight',
              overlayStyle: {
                zIndex: (options?.value?.modalProps?.zIndex || 1000) + 1,
              },
            },
            {
              default: () =>
                h(
                  'div',
                  {
                    class:
                      'px-3 py-1 cursor-pointer text-gray-600 whitespace-nowrap',
                  },
                  '更多',
                ),
              overlay: () =>
                h(
                  'div',
                  {
                    class:
                      'bg-white rounded shadow-lg py-1 max-h-60 overflow-y-auto',
                  },
                  hiddenCategories.map((category) =>
                    h(
                      'div',
                      {
                        class: `px-4 py-2 cursor-pointer hover:bg-gray-100 ${activeCategory.value === category.id ? 'text-primary' : 'text-gray-600'}`,
                        onClick: () => handleCategoryChange(category.id),
                      },
                      category.name,
                    ),
                  ),
                ),
            },
          ),
      ];
    };

    // 显示额外的模式信息
    const renderModeInfo = () => {
      if (isMultipleMode.value) {
        return h(
          'div',
          {
            class: 'text-sm ml-3 text-gray-500',
          },
          `已选择 ${selectedImages.value.length}/${maxSelectCount.value} 张图片`,
        );
      }
      return null;
    };

    return h(
      Modal,
      {
        visible: visible.value,
        title: [h('span', {}, modalTitle.value), renderModeInfo()],
        width: '80%',
        style: { top: '5%', maxHeight: '90vh' },
        onCancel: hideModal,
        onOk: handleConfirm,
        okButtonProps: {
          disabled: isMultipleMode.value
            ? selectedImages.value.length === 0
            : !selectedImage.value,
        },
        okText: isMultipleMode.value ? '确认选择' : '确定',
        class: 'image-preview-modal',
        zIndex: options?.value?.modalProps?.zIndex,
      },
      {
        default: () =>
          h('div', { class: 'relative' }, [
            h(
              'div',
              {
                class:
                  'flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-4',
              },
              [
                h(
                  'div',
                  {
                    class:
                      'flex flex-col sm:flex-row items-start sm:items-center gap-4',
                    style: 'min-width: 0',
                  },
                  [
                    h(
                      'div',
                      {
                        class:
                          'w-full sm:w-auto flex flex-wrap gap-2 border-b border-gray-200',
                      },
                      renderCategories(),
                    ),
                    // 添加分类按钮
                    h(
                      Button,
                      {
                        type: 'link',
                        class: 'flex items-center',
                        onClick: () => (showAddCategory.value = true),
                      },
                      () => [h(MdiPlus), '添加分类'],
                    ),
                    // 添加分类弹窗
                    addCategoryModal(),
                  ],
                ),
                h(
                  Upload,
                  {
                    accept: 'image/*',
                    showUploadList: false,
                    beforeUpload: (file) => {
                      handleUpload(file);
                      return false;
                    },
                  },
                  () => [
                    h(
                      Button,
                      {
                        type: 'primary',
                        loading: uploadLoading.value,
                        class:
                          'flex items-center w-full sm:w-auto justify-center',
                        disabled:
                          !activeCategory.value ||
                          activeCategory.value === '全部',
                      },
                      () => [h(MdiUpload), '上传图片'],
                    ),
                  ],
                ),
              ],
            ),
            h(
              'div',
              {
                class:
                  'relative grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 p-3 max-h-[60vh] overflow-y-auto',
                style: loading.value
                  ? 'opacity: 0.6; pointer-events: none; transition: all 0.3s ease-in-out'
                  : 'opacity: 1; transition: all 0.3s ease-in-out',
              },
              [
                loading.value &&
                  h(
                    'div',
                    {
                      class:
                        'absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 backdrop-blur-sm z-50',
                    },
                    h(Spin),
                  ),
                ...images.value.map((image) =>
                  h(
                    'div',
                    {
                      class: `relative cursor-pointer transition-all duration-300 aspect-square ${isImageSelected(image) ? 'ring-2 ring-primary scale-[1.02]' : ''}`,
                      onClick: () => handleImageSelect(image),
                    },
                    [
                      h('div', {
                        class: 'absolute inset-0 bg-gray-100 animate-pulse',
                        style: {
                          zIndex: 1,
                        },
                      }),
                      h(Image, {
                        src: getFileUrl(image.src),
                        alt: '',
                        preview: false,
                        class:
                          'w-full h-full object-contain rounded transition-all duration-300 hover:scale-105',
                        loading: 'lazy',
                        style: {
                          position: 'relative',
                          zIndex: 2,
                        },
                      }),
                      // 多选模式下显示选择标记
                      isMultipleMode.value &&
                        isImageSelected(image) &&
                        h(
                          'div',
                          {
                            class:
                              'absolute top-2 right-2 bg-primary rounded-full p-1 shadow-lg z-10',
                          },
                          h(MailCheck, {
                            class: 'text-white',
                            style: 'font-size: 16px;',
                          }),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            h(
              'div',
              {
                class: 'flex justify-center mt-4',
              },
              h(Pagination, {
                current: currentPage.value,
                pageSize: pageSize.value,
                total: total.value,
                onChange: handlePageChange,
                showQuickJumper: true,
                showSizeChanger: true,
                pageSizeOptions: ['8', '12', '16', '24'],
                onShowSizeChange: (current, size) => {
                  pageSize.value = size;
                  currentPage.value = 1;
                  fetchImages();
                },
              }),
            ),
          ]),
      },
    );
  };

  return {
    showModal,
    hideModal,
    ImagePreviewModal,
  };
}
