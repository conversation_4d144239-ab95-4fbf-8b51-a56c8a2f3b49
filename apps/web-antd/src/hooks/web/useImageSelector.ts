import { computed, h, ref } from 'vue';

import {
  MdiDelete,
  MdiImage,
  MdiPlus,
  MdiRefresh,
  MdiUpload,
} from '@vben/icons';

import { Button, Image, message, Upload } from 'ant-design-vue';

import { getFileUrl } from '#/utils/file';

import { useImagePreviewModal } from './useImagePreviewModal';

// 添加图片对象类型定义
export interface ImageObject {
  id?: number | string;
  src: string;
  [key: string]: any;
}

export interface UseImageSelectorOptions {
  /**
   * 默认图片URL
   */
  defaultImageUrl?: string;

  /**
   * 默认图片URL数组（多选模式下使用）
   */
  defaultImageUrls?: string[];

  /**
   * 默认图片ID数组（多选模式下使用）
   * 仅在 mode='gallery' 时有效
   */
  defaultImageIds?: (number | string)[];

  /**
   * 选择模式: 'local' - 本地文件, 'gallery' - 图库
   * @default 'local'
   */
  mode?: 'gallery' | 'local';

  /**
   * 是否支持多选
   * @default false
   */
  multiple?: boolean;

  /**
   * 多图模式下的最大图片数量
   * @default 9
   */
  maxCount?: number;

  /**
   * 选择图片后的回调
   */
  onImageChange?: (imageUrl: string, isLocalFile?: boolean) => void;

  /**
   * 多选模式下，图片列表变更回调
   */
  onImagesChange?: (imageUrls: string[]) => void;

  /**
   * 选择图库图片后的增强回调，提供完整图片对象
   * 仅在 mode='gallery' 时有效
   */
  onGalleryImageSelect?: (imageObject: ImageObject) => void;

  /**
   * 多选模式下，图库图片列表变更回调
   */
  onGalleryImagesSelect?: (imageObjects: ImageObject[]) => void;

  /**
   * 本地文件限制类型
   * @default ['image/jpeg', 'image/png', 'image/gif']
   */
  acceptTypes?: string[];

  /**
   * 图片预览尺寸
   */
  previewSize?: number;
}

export function useImageSelector(options: UseImageSelectorOptions = {}) {
  // 默认选项
  const defaultOptions: Required<UseImageSelectorOptions> = {
    defaultImageUrl: '',
    defaultImageUrls: [],
    defaultImageIds: [],
    mode: 'local',
    multiple: false,
    maxCount: 9,
    onImageChange: () => {},
    onImagesChange: () => {},
    onGalleryImageSelect: () => {},
    onGalleryImagesSelect: () => {},
    acceptTypes: ['image/jpeg', 'image/png', 'image/gif'],
    previewSize: 100,
  };

  // 合并选项
  const mergedOptions = { ...defaultOptions, ...options };

  // 从图库选择的Hook
  const { showModal: showGalleryModal, ImagePreviewModal } =
    useImagePreviewModal();

  // 当前选中的图片URL（单选模式）
  const imageUrl = ref(mergedOptions.defaultImageUrl);

  // 当前选中的图片URL数组（多选模式）
  const getDefaultImageUrls = (): string[] => {
    if (!mergedOptions.multiple) return [];
    if (mergedOptions.defaultImageUrls.length > 0)
      return mergedOptions.defaultImageUrls;
    if (mergedOptions.defaultImageUrl) return [mergedOptions.defaultImageUrl];
    return [];
  };
  const imageUrls = ref<string[]>(getDefaultImageUrls());

  // 存储ID列表（用于预选）
  const selectedIds = ref<(number | string)[]>(
    mergedOptions.defaultImageIds || [],
  );

  // 标记是否为本地文件
  const isLocalFile = ref(false);

  // 存储完整图片对象的ref (仅gallery模式使用)
  const selectedImageObject = ref<ImageObject | null>(null);

  // 存储完整图片对象数组（多选模式）
  const selectedImageObjects = ref<ImageObject[]>([]);

  // 文件类型限制
  const accept = computed(() => mergedOptions.acceptTypes.join(','));

  // 是否有图片（单选模式）
  const hasImage = computed(() => !!imageUrl.value);

  // 是否有图片（多选模式）
  const hasImages = computed(() => imageUrls.value.length > 0);

  // 是否达到最大图片数量限制
  const isMaxImages = computed(
    () =>
      mergedOptions.multiple &&
      imageUrls.value.length >= mergedOptions.maxCount,
  );

  // 完整的图片URL（处理相对路径）- 单选模式
  const fullImageUrl = computed(() => {
    if (!imageUrl.value) return '';

    // 如果是本地文件（blob:或data:开头）则直接返回
    if (
      isLocalFile.value ||
      imageUrl.value.startsWith('blob:') ||
      imageUrl.value.startsWith('data:')
    ) {
      return imageUrl.value;
    }

    // 如果是绝对路径（http或https开头）则直接返回
    if (imageUrl.value.startsWith('http')) {
      return imageUrl.value;
    }

    // 其他情况视为服务器相对路径，使用工具函数拼接完整URL
    return getFileUrl(imageUrl.value);
  });

  // 获取完整图片URL - 处理多选模式下的URL
  const getFullImageUrl = (url: string) => {
    if (!url) return '';

    // 如果是本地文件（blob:或data:开头）则直接返回
    if (url.startsWith('blob:') || url.startsWith('data:')) {
      return url;
    }

    // 如果是绝对路径（http或https开头）则直接返回
    if (url.startsWith('http')) {
      return url;
    }

    // 其他情况视为服务器相对路径，使用工具函数拼接完整URL
    return getFileUrl(url);
  };

  // 完整图片URL数组 - 多选模式
  const fullImageUrls = computed(() =>
    imageUrls.value.map((url) => getFullImageUrl(url)),
  );

  // 外部更新预选图片ID数组
  const updateSelectedIds = (ids: (number | string)[]) => {
    selectedIds.value = ids;
  };

  // 处理本地文件选择
  const handleLocalFile = (file: File) => {
    if (!mergedOptions.acceptTypes.includes(file.type)) {
      message.error(`不支持的文件类型: ${file.type}`);
      return false;
    }

    // 创建本地文件URL预览
    const localUrl = URL.createObjectURL(file);

    if (mergedOptions.multiple) {
      // 多图模式下，添加到图片列表
      if (imageUrls.value.length >= mergedOptions.maxCount) {
        message.warning(`最多只能上传${mergedOptions.maxCount}张图片`);
        return false;
      }

      imageUrls.value = [...imageUrls.value, localUrl];
      mergedOptions.onImagesChange(imageUrls.value);
    } else {
      // 单图模式
      imageUrl.value = localUrl;
      isLocalFile.value = true;
      selectedImageObject.value = null; // 清除之前可能选择的图库图片
      mergedOptions.onImageChange(localUrl, true);
    }

    return false; // 阻止默认上传行为
  };

  // 从图库选择图片
  const selectFromGallery = (
    overrideOptions?: Partial<UseImageSelectorOptions>,
  ) => {
    // 合并覆盖选项
    const selectOptions = overrideOptions
      ? { ...mergedOptions, ...overrideOptions }
      : mergedOptions;

    // 如果多选模式已达到最大数量，提示并返回
    if (
      selectOptions.multiple &&
      imageUrls.value.length >= selectOptions.maxCount
    ) {
      message.warning(`最多只能选择${selectOptions.maxCount}张图片`);
      return;
    }

    showGalleryModal({
      title: selectOptions.multiple ? '选择多张图片' : '选择图片',
      // 传递multiple和maxCount参数
      multiple: selectOptions.multiple,
      maxCount: selectOptions.maxCount,
      // 传递预选ID列表 - 新增这行实现预选功能
      preselectedIds: selectedIds.value,
      onSelect: (image) => {
        if (selectOptions.multiple) {
          // 多选模式：添加到列表
          const exists = imageUrls.value.includes(image.src);
          if (!exists) {
            imageUrls.value = [...imageUrls.value, image.src];
            selectedImageObjects.value = [...selectedImageObjects.value, image];

            // 更新已选ID列表
            if (image.id && !selectedIds.value.includes(image.id)) {
              selectedIds.value = [...selectedIds.value, image.id];
            }

            // 调用多图回调
            selectOptions.onImagesChange(imageUrls.value);
            if (selectOptions.mode === 'gallery') {
              selectOptions.onGalleryImagesSelect(selectedImageObjects.value);
            }
          }
        } else {
          // 单选模式：替换当前图片
          imageUrl.value = image.src;
          isLocalFile.value = false;
          // 保存完整的图片对象
          selectedImageObject.value = image;

          // 更新已选ID
          if (image.id) {
            selectedIds.value = [image.id];
          }

          // 调用标准回调
          selectOptions.onImageChange(image.src, false);
          // 调用增强回调，提供完整图片对象
          if (selectOptions.mode === 'gallery') {
            selectOptions.onGalleryImageSelect(image);
          }
        }
      },
      // 添加多选回调
      onSelectMultiple: (images) => {
        if (!selectOptions.multiple) return;

        // 更新图片URL列表和对象列表
        const newUrls = images.map((img) => img.src);
        imageUrls.value = newUrls;
        selectedImageObjects.value = images;

        // 更新已选ID列表
        selectedIds.value = images
          .map((img) => img.id)
          .filter((id): id is number | string => id !== undefined);

        // 调用回调
        selectOptions.onImagesChange(newUrls);
        if (selectOptions.mode === 'gallery') {
          selectOptions.onGalleryImagesSelect(images);
        }
      },
      modalProps: {
        zIndex: 2000,
      },
    });
  };

  // 清除图片 - 单选模式
  const clearImage = () => {
    imageUrl.value = '';
    isLocalFile.value = false;
    selectedImageObject.value = null; // 清除图片对象
    mergedOptions.onImageChange('', false);
  };

  // 移除指定图片 - 多选模式
  const removeImage = (index: number) => {
    if (index < 0 || index >= imageUrls.value.length) return;

    // 创建新数组，移除指定索引的图片
    const newUrls = [...imageUrls.value];
    newUrls.splice(index, 1);
    imageUrls.value = newUrls;

    // 同时更新对象数组
    const newObjects = [...selectedImageObjects.value];
    newObjects.splice(index, 1);
    selectedImageObjects.value = newObjects;

    // 触发回调
    mergedOptions.onImagesChange(imageUrls.value);
    if (mergedOptions.mode === 'gallery') {
      mergedOptions.onGalleryImagesSelect(selectedImageObjects.value);
    }
  };

  // 清空所有图片 - 多选模式
  const clearAllImages = () => {
    imageUrls.value = [];
    selectedImageObjects.value = [];
    mergedOptions.onImagesChange([]);
    if (mergedOptions.mode === 'gallery') {
      mergedOptions.onGalleryImagesSelect([]);
    }
  };

  // 渲染本地文件选择按钮 - 保留供内部使用
  const renderLocalSelector = () => {
    return h(
      Upload,
      {
        accept: accept.value,
        showUploadList: false,
        beforeUpload: handleLocalFile,
        multiple: false,
      },
      {
        default: () =>
          h(
            Button,
            {
              icon: h(MdiUpload),
              type: 'primary',
            },
            '选择本地图片',
          ),
      },
    );
  };

  // 渲染图库选择按钮 - 保留供内部使用
  const renderGallerySelector = () => {
    return h(
      Button,
      {
        onClick: () => selectFromGallery(),
        icon: h(MdiImage),
        type: 'primary',
        class: 'flex items-center justify-center',
      },
      '从图库选择',
    );
  };

  // 渲染图片选择器组件
  const renderImageSelector = (
    overrideOptions?: Partial<UseImageSelectorOptions>,
  ) => {
    // 合并覆盖选项
    const renderOptions = overrideOptions
      ? { ...mergedOptions, ...overrideOptions }
      : mergedOptions;

    return renderOptions.multiple
      ? // 多图模式
        h('div', { class: 'multi-image-selector-container' }, [
          h('div', { class: 'flex flex-wrap gap-3' }, [
            // 现有图片列表
            ...imageUrls.value.map((url, index) =>
              h(
                'div',
                {
                  class:
                    'relative w-24 h-24 rounded-lg overflow-hidden border border-gray-200 hover:border-primary transition-colors duration-300',
                  style: 'box-shadow: 0 2px 8px rgba(0,0,0,0.08)',
                },
                [
                  // 图片 - 使用Image组件的内置预览功能
                  h(Image, {
                    src: getFullImageUrl(url),
                    alt: `图片预览${index + 1}`,
                    class: 'w-full h-full object-cover',
                    preview: true,
                  }),

                  // 删除按钮 - 右上角
                  h(
                    'div',
                    {
                      class:
                        'absolute top-1 right-1 bg-black/70 rounded-full p-1 text-white cursor-pointer hover:text-red-400 hover:bg-black/90 transition-colors shadow-md z-10',
                      onClick: (e) => {
                        e.stopPropagation(); // 阻止事件冒泡
                        removeImage(index);
                      },
                    },
                    [h(MdiDelete, { style: 'font-size: 16px;' })],
                  ),
                ],
              ),
            ),

            // 添加图片按钮 (如果未达到最大数量)
            !(
              renderOptions.multiple &&
              imageUrls.value.length >= renderOptions.maxCount
            ) &&
              h(
                'div',
                {
                  class:
                    'w-24 h-24 rounded-lg border-2 border-dashed border-gray-300 hover:border-primary cursor-pointer transition-colors duration-300 flex flex-col items-center justify-center gap-2',
                  onClick: () => {
                    if (renderOptions.mode === 'local') {
                      const fileInput = document.querySelector(
                        '.multi-image-selector-container .ant-upload input[type="file"]',
                      );
                      if (fileInput) {
                        (fileInput as HTMLElement).click();
                      }
                    } else {
                      // 调用选择函数时传递临时覆盖配置
                      selectFromGallery(overrideOptions);
                    }
                  },
                },
                [
                  h(MdiPlus, {
                    class: 'text-gray-400 text-2xl',
                  }),
                  h(
                    'span',
                    {
                      class: 'text-gray-400 text-sm',
                    },
                    '添加图片',
                  ),
                  // 隐藏的上传组件
                  renderOptions.mode === 'local' &&
                    h(
                      Upload,
                      {
                        accept: accept.value,
                        showUploadList: false,
                        beforeUpload: handleLocalFile,
                        multiple: false,
                        class: 'hidden',
                      },
                      {
                        default: () => [],
                      },
                    ),
                ],
              ),
          ]),

          // 显示图片数量限制信息
          h(
            'div',
            {
              class: 'mt-2 text-xs text-gray-500',
            },
            `${imageUrls.value.length}/${renderOptions.maxCount} 张图片`,
          ),
        ])
      : // 单图模式
        h(
          'div',
          { class: 'image-selector-container flex items-center gap-4' },
          [
            // 有图片时
            hasImage.value
              ? h(
                  'div',
                  {
                    class:
                      'relative w-24 h-24 rounded-lg overflow-hidden border border-gray-200 hover:border-primary transition-colors duration-300',
                    style: 'box-shadow: 0 2px 8px rgba(0,0,0,0.08)',
                  },
                  [
                    // 图片 - 使用Image组件的内置预览功能
                    h(Image, {
                      src: fullImageUrl.value,
                      alt: '图片预览',
                      class: 'w-full h-full object-cover',
                      preview: true,
                    }),

                    // 更换按钮 - 左下角
                    h(
                      'div',
                      {
                        class:
                          'absolute bottom-1 left-1 bg-black/70 rounded-full p-1 text-white cursor-pointer hover:text-blue-400 hover:bg-black/90 transition-colors shadow-md z-10',
                        onClick: (e) => {
                          e.stopPropagation(); // 阻止事件冒泡，避免触发预览
                          if (renderOptions.mode === 'local') {
                            const fileInput = document.querySelector(
                              '.image-selector-container .ant-upload input[type="file"]',
                            );
                            if (fileInput) {
                              (fileInput as HTMLElement).click();
                            }
                          } else {
                            // 调用选择函数时传递临时覆盖配置
                            selectFromGallery(overrideOptions);
                          }
                        },
                      },
                      [h(MdiRefresh, { style: 'font-size: 16px;' })],
                    ),

                    // 删除按钮 - 右上角
                    h(
                      'div',
                      {
                        class:
                          'absolute top-1 right-1 bg-black/70 rounded-full p-1 text-white cursor-pointer hover:text-red-400 hover:bg-black/90 transition-colors shadow-md z-10',
                        onClick: (e) => {
                          e.stopPropagation(); // 阻止事件冒泡
                          clearImage();
                        },
                      },
                      [h(MdiDelete, { style: 'font-size: 16px;' })],
                    ),
                  ],
                )
              : h(
                  'div',
                  {
                    class:
                      'w-24 h-24 rounded-lg border-2 border-dashed border-gray-300 hover:border-primary cursor-pointer transition-colors duration-300 flex flex-col items-center justify-center gap-2',
                    onClick: () => {
                      if (renderOptions.mode === 'local') {
                        const fileInput = document.querySelector(
                          '.image-selector-container .ant-upload input[type="file"]',
                        );
                        if (fileInput) {
                          (fileInput as HTMLElement).click();
                        }
                      } else {
                        // 调用选择函数时传递临时覆盖配置
                        selectFromGallery(overrideOptions);
                      }
                    },
                  },
                  [
                    h(MdiImage, {
                      class: 'text-gray-400 text-2xl',
                    }),
                    h(
                      'span',
                      {
                        class: 'text-gray-400 text-sm',
                      },
                      '上传图片',
                    ),
                    // 隐藏的上传组件
                    renderOptions.mode === 'local' &&
                      h(
                        Upload,
                        {
                          accept: accept.value,
                          showUploadList: false,
                          beforeUpload: handleLocalFile,
                          multiple: false,
                          class: 'hidden',
                        },
                        {
                          default: () => [],
                        },
                      ),
                  ],
                ),
          ],
        );
  };

  return {
    // 单图模式
    imageUrl,
    isLocalFile,
    fullImageUrl,
    hasImage,
    clearImage,
    selectedImageObject,

    // 多图模式
    imageUrls,
    fullImageUrls,
    hasImages,
    isMaxImages,
    removeImage,
    clearAllImages,
    selectedImageObjects,

    // 预选ID相关
    selectedIds,
    updateSelectedIds,

    // 共用方法
    handleLocalFile,
    selectFromGallery,
    renderImageSelector,
    ImagePreviewModal,
    renderLocalSelector,
    renderGallerySelector,
  };
}
