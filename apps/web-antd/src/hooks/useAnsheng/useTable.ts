import type { UseTableOptions } from './types';

import { computed, reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

/**
 * 表格数据管理hook
 * 处理表格数据的加载、分页、搜索和单元格编辑等功能
 */
export function useTable<
  T extends Record<string, any>,
  P = Record<string, any>,
>(options: UseTableOptions<T, P>) {
  const {
    api,
    defaultParams = {},
    defaultPage = 1,
    defaultPageSize = 10,
    beforeFetch,
    afterFetch,
    tableType = 'ant',
    onSave,
    columns,
    virtualScroll = false,
    virtualScrollOptions = {},
  } = options;

  // 状态定义
  const loading = ref(false);
  const searchParams = reactive<Record<string, any>>({
    ...defaultParams,
    page: defaultPage,
    pageSize: defaultPageSize,
  });
  const tableData = ref<T[]>([]);
  const total = ref(0);

  // 编辑相关状态
  const editableColumns = ref<string[]>([]);
  const editLoading = ref(false);

  // 分页配置
  const pagination = reactive({
    current: defaultPage,
    pageSize: defaultPageSize,
    total: 0,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 虚拟滚动配置
  const virtualConfig = computed(() => {
    if (!virtualScroll) return undefined;

    return {
      scrollY: virtualScrollOptions.scrollY || 400,
      itemHeight: virtualScrollOptions.itemHeight || 54,
      threshold: virtualScrollOptions.threshold || 100,
      ...virtualScrollOptions,
    };
  });

  // 获取列表数据
  const getList = async () => {
    try {
      loading.value = true;
      let params = {
        page: searchParams.page,
        pageSize: searchParams.pageSize,
        ...searchParams,
      };

      if (virtualScroll && !searchParams.pageSize) {
        params.pageSize = 50;
      }

      if (beforeFetch) {
        params = beforeFetch(params);
      }

      const { data } = await api(params as P);

      if (afterFetch) {
        const processedData = afterFetch(data);
        tableData.value = processedData.rows;
        total.value = processedData.total;
      } else {
        tableData.value = data.rows;
        total.value = data.total;
      }

      pagination.total = total.value;
    } catch (error) {
      console.error('获取列表数据失败:', error);
      message.error('获取列表数据失败');
      tableData.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  };

  // 搜索处理
  const handleSearch = () => {
    pagination.current = 1;
    searchParams.page = 1;
    getList();
  };

  // 修改重置处理
  const handleReset = () => {
    Object.keys(searchParams).forEach((key) => {
      if (key !== 'page' && key !== 'pageSize') {
        delete searchParams[key];
      }
    });

    if (defaultParams) {
      Object.keys(defaultParams).forEach((key) => {
        searchParams[key] = defaultParams[key];
      });
    }

    pagination.current = 1;
    searchParams.page = 1;
    getList();
  };

  // 表格变化处理
  const handleTableChange = (pag: any, filters: any = {}, sorter: any = {}) => {
    // 检查是否需要重置页码（当筛选器或排序器改变时）
    const shouldResetPage = !pag || (pag && pag.current === 1);

    if (pag) {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      searchParams.page = pag.current;
      searchParams.pageSize = pag.pageSize;
    }

    // 如果排序器改变，重置页码到第一页
    let sortChanged = false;
    if (sorter && sorter.field) {
      sortChanged =
        searchParams.sortField !== sorter.field ||
        searchParams.sortOrder !== sorter.order;
      searchParams.sortField = sorter.field;
      searchParams.sortOrder = sorter.order;
    } else if (searchParams.sortField || searchParams.sortOrder) {
      sortChanged = true;
      delete searchParams.sortField;
      delete searchParams.sortOrder;
    }

    // 如果筛选器改变，重置页码到第一页
    let filterChanged = false;
    if (filters) {
      Object.keys(filters).forEach((key) => {
        const hasFilter = filters[key] && filters[key].length > 0;
        const hadFilter =
          searchParams[key as keyof typeof searchParams] &&
          Array.isArray(searchParams[key as keyof typeof searchParams]) &&
          searchParams[key as keyof typeof searchParams].length > 0;

        // 检测筛选器是否发生变化
        if (hasFilter !== hadFilter) {
          filterChanged = true;
        } else if (hasFilter && hadFilter) {
          // 比较数组内容是否一致
          const prevFilters = searchParams[
            key as keyof typeof searchParams
          ] as any[];
          const newFilters = filters[key] as any[];
          if (prevFilters.length === newFilters.length) {
            // 简单比较是否包含相同元素
            filterChanged =
              prevFilters.some((f: any) => !newFilters.includes(f)) ||
              newFilters.some((f: any) => !prevFilters.includes(f));
          } else {
            filterChanged = true;
          }
        }

        if (hasFilter) {
          searchParams[key as keyof typeof searchParams] = filters[key];
        } else {
          delete searchParams[key as keyof typeof searchParams];
        }
      });
    }

    // 如果排序或筛选发生变化，重置页码
    if ((sortChanged || filterChanged) && !shouldResetPage) {
      pagination.current = 1;
      searchParams.page = 1;
    }

    getList();
  };

  // 处理保存
  const handleCellSave = async (record: any, key: string, value: any) => {
    if (!onSave) return;

    try {
      if (typeof onSave === 'function') {
        await onSave(record, value, key);
      } else {
        const saveFunction = onSave[key] || onSave.default;
        if (!saveFunction) {
          throw new Error('未找到对应的保存方法');
        }
        await saveFunction(record, value, key);
      }
      message.success('保存成功');
      getList();
    } catch (error) {
      console.error('保存失败:', error);
      message.error(error instanceof Error ? error.message : '保存失败');
    }
  };

  // 设置可编辑列
  const setEditableColumns = (columns: string[]) => {
    editableColumns.value = columns;
  };

  // 更新搜索参数
  const updateSearchParams = (params: Record<string, any>) => {
    Object.keys(params).forEach((key) => {
      searchParams[key] = params[key];
    });
  };

  // 虚拟滚动数据懒加载处理
  const handleVirtualScroll = async (e: any) => {
    if (!virtualScroll) return;

    const { scrollTop, scrollHeight, clientHeight } = e.target;
    const threshold = virtualConfig.value?.threshold || 100;

    if (scrollHeight - scrollTop - clientHeight < threshold) {
      if (tableData.value.length >= total.value) return;

      if (loading.value) return;

      try {
        loading.value = true;
        const nextPage =
          Math.ceil(tableData.value.length / pagination.pageSize) + 1;

        const params = {
          ...searchParams,
          page: nextPage,
          pageSize: pagination.pageSize,
        };

        if (beforeFetch) {
          const processedParams = beforeFetch(params);
          Object.assign(params, processedParams);
        }

        const { data } = await api(params as P);

        let newRows;
        if (afterFetch) {
          const processedData = afterFetch(data);
          newRows = processedData.rows;
        } else {
          newRows = data.rows;
        }

        tableData.value = [...tableData.value, ...newRows];
      } catch (error) {
        console.error('加载更多数据失败:', error);
      } finally {
        loading.value = false;
      }
    }
  };

  return {
    loading,
    searchParams,
    tableData,
    total,
    pagination,
    editableColumns,
    editLoading,
    columns,
    virtualConfig,
    virtualScroll,

    getList,
    handleSearch,
    handleReset,
    handleTableChange,
    handleCellSave,
    setEditableColumns,
    updateSearchParams,
    handleVirtualScroll,
  };
}
