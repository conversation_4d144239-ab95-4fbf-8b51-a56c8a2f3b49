import type { ModalInteractionState } from './types';

import { nextTick, onMounted, ref, watch } from 'vue';

import { FULLSCREEN_CLASS, FULLSCREEN_WRAP_CLASS } from './types';
import { sleep } from './utils';

/**
 * 模态框交互管理hook
 * 处理模态框的拖拽和全屏显示等交互功能
 */
export function useModalInteractions(
  options: {
    draggable?: boolean;
    fullscreenable?: boolean;
    width?: number | string;
  } = {},
): ModalInteractionState {
  // 模态框引用
  const modalRef = ref<HTMLElement | null>(null);
  const headerRef = ref<HTMLElement | null>(null);
  const wrapperRef = ref<HTMLElement | null>(null);

  // 交互状态
  const isFullscreen = ref(false);
  const isDragging = ref(false);
  // 是否启用拖拽
  const shouldDraggable = ref(Boolean(options.draggable));

  // 初始化拖拽
  const initDrag = () => {
    if (!headerRef.value || !modalRef.value || !shouldDraggable.value) return;

    // 拖拽状态
    let dragging = false;
    let startX = 0;
    let startY = 0;
    let initialLeft = 0;
    let initialTop = 0;

    // 开始拖拽
    const onMouseDown = (e: MouseEvent) => {
      if (isFullscreen.value) return;
      if (
        e.target &&
        (e.target as HTMLElement).classList.contains('modal-fullscreen-btn')
      )
        return;

      dragging = true;
      isDragging.value = true;
      startX = e.clientX;
      startY = e.clientY;

      // 获取当前位置
      const transformValue = modalRef.value!.style.transform;
      const translateMatch = transformValue.match(
        /translate\(([^,]+)px,\s*([^)]+)px\)/,
      );

      initialLeft = translateMatch ? Number.parseFloat(translateMatch[1]) : 0;
      initialTop = translateMatch ? Number.parseFloat(translateMatch[2]) : 0;

      // 添加拖拽样式
      modalRef.value!.classList.add('modal-dragging', 'no-transition');

      // 添加全局事件监听
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    };

    // 拖拽中
    const onMouseMove = (e: MouseEvent) => {
      if (!dragging) return;

      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      // 应用新位置
      modalRef.value!.style.transform = `translate(${initialLeft + deltaX}px, ${initialTop + deltaY}px)`;
    };

    // 结束拖拽
    const onMouseUp = () => {
      dragging = false;
      isDragging.value = false;

      // 移除拖拽样式
      if (modalRef.value) {
        modalRef.value.classList.remove('modal-dragging', 'no-transition');
      }

      // 移除全局事件监听
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    // 设置鼠标样式
    headerRef.value.style.cursor = 'move';

    // 添加事件监听
    headerRef.value.addEventListener('mousedown', onMouseDown);

    // 返回清理函数
    return () => {
      if (headerRef.value) {
        headerRef.value.removeEventListener('mousedown', onMouseDown);
      }
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };
  };

  /**
   * 重置模态框位置
   */
  const resetPosition = () => {
    if (!modalRef.value) return;

    modalRef.value.style.transform = '';
    modalRef.value.style.left = '';
    modalRef.value.style.top = '';
  };

  /**
   * 应用全屏样式
   */
  const applyFullscreenStyles = (isFullscreen: boolean) => {
    if (!modalRef.value || !wrapperRef.value) return;

    if (isFullscreen) {
      // 添加全屏样式
      modalRef.value.classList.add(FULLSCREEN_CLASS);
      wrapperRef.value.classList.add(FULLSCREEN_WRAP_CLASS);

      // 设置全屏样式
      modalRef.value.style.width = '100%';
      modalRef.value.style.height = '100vh';
      modalRef.value.style.maxHeight = '100vh';
      modalRef.value.style.top = '0';
      modalRef.value.style.padding = '0';
      modalRef.value.style.margin = '0';

      // 内容容器样式
      const bodyElement = modalRef.value.querySelector('.ant-modal-body');
      if (bodyElement) {
        (bodyElement as HTMLElement).style.height = 'calc(100vh - 160px)';
        (bodyElement as HTMLElement).style.overflow = 'hidden';
      }

      // 重置位置
      resetPosition();
    } else {
      // 移除全屏样式
      modalRef.value.classList.remove(FULLSCREEN_CLASS);
      wrapperRef.value.classList.remove(FULLSCREEN_WRAP_CLASS);

      // 恢复普通样式
      modalRef.value.style.width =
        typeof options.width === 'number'
          ? `${options.width}px`
          : options.width || '900px';
      modalRef.value.style.height = '';
      modalRef.value.style.maxHeight = '';
      modalRef.value.style.top = '';
      modalRef.value.style.padding = '';
      modalRef.value.style.margin = '';

      // 内容容器样式
      const bodyElement = modalRef.value.querySelector('.ant-modal-body');
      if (bodyElement) {
        (bodyElement as HTMLElement).style.height = '';
        (bodyElement as HTMLElement).style.overflow = '';
      }
    }
  };

  /**
   * 初始化模态框元素引用
   */
  // 将这个修改应用到 useModalInteractions.ts 的 initDomRefs 函数中
  const initDomRefs = async () => {
    // 等待Modal动画完成后再获取元素
    await sleep(100);

    // 找到最新打开的modal元素（通常是z-index最高的）
    const modals = document.querySelectorAll('.ant-modal');
    const modalElement = modals[modals.length - 1] as HTMLElement; // 获取最后一个，通常是最新打开的
    modalRef.value = modalElement;

    // 在找到的模态框内部查找头部元素
    const headerElement = modalElement?.querySelector(
      '.ant-modal-header',
    ) as HTMLElement;
    headerRef.value = headerElement;

    // 找到对应的包装元素
    const wrapperElement = modalElement?.closest(
      '.ant-modal-wrap',
    ) as HTMLElement;
    wrapperRef.value = wrapperElement;

    // 如果启用了拖拽且找到了元素，则立即初始化拖拽
    if (shouldDraggable.value && modalElement && headerElement) {
      initDrag();
    }

    // 如果当前是全屏状态，应用全屏样式
    if (isFullscreen.value) {
      applyFullscreenStyles(true);
    }
  };

  /**
   * 切换全屏状态
   */
  const toggleFullscreen = () => {
    if (options.fullscreenable === false) return;
    isFullscreen.value = !isFullscreen.value;
  };

  // 监听全屏状态，动态更新拖拽启用状态
  watch(
    () => isFullscreen.value,
    (val) => {
      shouldDraggable.value = Boolean(options.draggable) && !val;

      // 全屏状态变化时，应用样式修改
      nextTick(() => {
        applyFullscreenStyles(val);
      });
    },
    { immediate: true },
  );

  /**
   * 初始化模态框
   */
  const initModal = async (visible: boolean) => {
    if (visible) {
      await nextTick();
      await initDomRefs();
    } else {
      // 弹窗关闭时，重置全屏状态
      isFullscreen.value = false;
      resetPosition();
    }
  };

  // 在组件挂载时添加全屏样式
  onMounted(() => {
    // 如果没有已经添加的样式，则添加
    if (!document.querySelector('#form-modal-fullscreen-styles')) {
      const styleElement = document.createElement('style');
      styleElement.id = 'form-modal-fullscreen-styles';
      styleElement.textContent = `
        .${FULLSCREEN_CLASS} {
          width: 100% !important;
          height: 100vh !important;
          top: 0 !important;
          padding: 0 !important;
          margin: 0 !important;
          max-width: 100% !important;
          transform: none !important;
        }

        .${FULLSCREEN_WRAP_CLASS} {
          overflow: hidden !important;
          display: flex !important;
          justify-content: center !important;
          align-items: flex-start !important;
        }

        .${FULLSCREEN_WRAP_CLASS} .ant-modal {
          padding: 0 !important;
          margin: 0 !important;
        }

        .${FULLSCREEN_CLASS} .ant-modal-content {
          height: 100vh !important;
          border-radius: 0 !important;
        }

        .${FULLSCREEN_CLASS} .channel-form-container {
          height: calc(100vh - 160px) !important;
          max-height: calc(100vh - 160px) !important;
          overflow-y: auto !important;
        }

        .modal-dragging {
          transition: none !important;
        }

        .no-transition {
          transition: none !important;
        }
      `;
      document.head.append(styleElement);
    }
  });

  return {
    isFullscreen,
    isDragging,
    toggleFullscreen,
    resetPosition,
    initModal,
  };
}
