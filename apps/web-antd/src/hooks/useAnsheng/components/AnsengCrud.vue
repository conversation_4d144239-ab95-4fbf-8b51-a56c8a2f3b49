<script lang="ts" setup>
import type { UseAnsengOptions } from '../types';

import { computed, defineComponent, onMounted, ref, useSlots } from 'vue';

import { Button, Card, Space } from 'ant-design-vue';

import {
  BasicTable,
  DetailModal,
  SearchToolbar,
  useAnsheng,
} from '#/hooks/useAnsheng';

interface Props {
  /**
   * useAnsheng 配置项
   */
  options: UseAnsengOptions<any, any>;

  /**
   * 是否显示搜索栏
   */
  showSearch?: boolean;

  /**
   * 是否显示卡片包装器
   */
  showCard?: boolean;

  /**
   * 是否显示批量操作区域
   */
  showBatchActions?: boolean;

  /**
   * 是否显示右上角列设置
   */
  showColumnSetting?: boolean;

  /**
   * 是否自动加载数据
   */
  autoLoad?: boolean;

  /**
   * 主键字段名
   */
  rowKey?: string;

  /**
   * 自定义渲染表单的函数
   */
  renderForm?: (form: any) => any;

  /**
   * 自定义批量操作按钮
   */
  batchButtons?: Array<{
    danger?: boolean;
    icon?: any;
    onClick: (keys: any[], rows: any[]) => void;
    text: string;
    type?: 'dashed' | 'default' | 'link' | 'primary' | 'text';
  }>;
}

const props = withDefaults(defineProps<Props>(), {
  showSearch: true,
  showCard: true,
  showBatchActions: false,
  showColumnSetting: true,
  autoLoad: true,
  rowKey: 'id',
  renderForm: () => undefined,
  batchButtons: () => [],
});

const emit = defineEmits<{
  error: [error: Error];
  load: [data: any[], total: number];
  selectionChange: [selectedRowKeys: any[], selectedRows: any[]];
}>();

// 使用 useAnsheng hook
const {
  // 状态和数据
  tableData,
  loading,
  pagination,
  actionButtons,

  // 方法
  handleTableChange,
  initialize,
  createItem,
  editItem,
  deleteItem,
  viewItem,

  // 搜索相关
  searchToolbarBind,
  handleSearch,
  handleReset,

  // 子hook
  detail,
  form,
  table,
} = useAnsheng(props.options);

// 批量操作
const selectedRowKeys = ref<(number | string)[]>([]);
const selectedRows = ref<any[]>([]);

// 处理表格选择变化
const handleSelectionChange = (rowKeys: (number | string)[], rows: any[]) => {
  selectedRowKeys.value = rowKeys;
  selectedRows.value = rows;
  emit('selectionChange', rowKeys, rows);
};

// 批量操作按钮
const batchButtonsConfig = computed(() => {
  return props.batchButtons || [];
});

// 创建表单组件
const formComponent = computed(() => {
  if (props.renderForm) {
    return props.renderForm(form);
  }

  // 默认使用 form.renderFormModal 方法
  return defineComponent({
    setup() {
      return () => form.renderFormModal();
    },
  });
});

// Get slots using the useSlots composable
const slots = useSlots();

// 获取表格的插槽
const tableSlots = computed(() => {
  if (!slots) return [];

  return Object.keys(slots).filter(
    (name) => name.startsWith('column-') || name.startsWith('header-'),
  );
});

// 初始化
onMounted(() => {
  if (props.autoLoad) {
    initialize();
  }
});

// 暴露方法和状态
defineExpose({
  // 状态和数据
  tableData,
  loading,
  pagination,
  selectedRowKeys,
  selectedRows,

  // 方法
  initialize,
  createItem,
  editItem,
  deleteItem,
  viewItem,
  handleSearch,
  handleReset,
  clearSelection: () => {
    selectedRowKeys.value = [];
    selectedRows.value = [];
  },

  // 子hook
  detail,
  form,
  table,
});
</script>

<template>
  <div class="ansheng-crud">
    <!-- 搜索工具栏 -->
    <div v-if="showSearch">
      <SearchToolbar
        v-bind="searchToolbarBind"
        @search="handleSearch"
        @reset="handleReset"
      />
    </div>

    <!-- 批量操作按钮 -->
    <div
      class="batch-actions mb-4"
      v-if="showBatchActions && selectedRowKeys.length > 0"
    >
      <div class="batch-info">已选择 {{ selectedRowKeys.length }} 项</div>
      <Space>
        <Button
          v-for="btn in batchButtonsConfig"
          :key="btn.text"
          :type="btn.type || 'default'"
          :danger="btn.danger"
          @click="() => btn.onClick(selectedRowKeys, selectedRows)"
        >
          <template #icon v-if="btn.icon">
            <component :is="btn.icon" />
          </template>
          {{ btn.text }}
        </Button>
      </Space>
    </div>

    <!-- 表格区域 -->
    <template v-if="showCard">
      <Card :bordered="false" :body-style="{ padding: 0 }" class="table-area">
        <template #title v-if="$slots.tableTitle">
          <slot name="tableTitle"></slot>
        </template>

        <template #extra v-if="$slots.tableExtra">
          <slot name="tableExtra"></slot>
        </template>

        <div class="table-header" v-if="$slots.tableHeader">
          <slot name="tableHeader"></slot>
        </div>

        <BasicTable
          :columns="table.columns || []"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          :show-index="true"
          :show-action="true"
          :action-buttons="actionButtons"
          :show-selection="showBatchActions"
          :row-key="rowKey"
          :show-column-setting="showColumnSetting"
          @selection-change="handleSelectionChange"
          @change="handleTableChange"
        >
          <template v-for="name in tableSlots" :key="name" #[name]="slotData">
            <slot :name="name" v-bind="slotData"></slot>
          </template>
        </BasicTable>
      </Card>
    </template>

    <template v-else>
      <div class="table-header" v-if="$slots.tableHeader">
        <slot name="tableHeader"></slot>
      </div>

      <BasicTable
        :columns="table.columns || []"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :show-index="true"
        :show-action="true"
        :action-buttons="actionButtons"
        :show-selection="showBatchActions"
        :row-key="rowKey"
        :show-column-setting="showColumnSetting"
        @selection-change="handleSelectionChange"
        @change="handleTableChange"
      >
        <template v-for="name in tableSlots" :key="name" #[name]="slotData">
          <slot :name="name" v-bind="slotData"></slot>
        </template>
      </BasicTable>
    </template>

    <!-- 表单模态框 -->
    <component :is="formComponent" />

    <!-- 详情模态框 -->
    <DetailModal
      v-if="detail"
      v-model:visible="detail.visible"
      :sections="detail.sections"
      :title="detail.title"
      :loading="loading"
    />
  </div>
</template>

<style lang="less" scoped>
.ansheng-crud {
  .mb-4 {
    margin-bottom: 16px;
  }

  .search-area {
    background-color: #fff;
  }

  .batch-actions {
    background-color: #f0f8ff;
    padding: 12px 16px;
    border-radius: 2px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .batch-info {
    font-weight: 500;
    color: #1890ff;
  }

  .table-area {
    background-color: #fff;
  }

  .table-header {
    padding: 16px 24px;
    background-color: #fff;
  }
}
</style>
