<script lang="ts" setup>
import type { TableColumnType, TableProps } from 'ant-design-vue';

import { computed, h, onMounted, onUnmounted, ref } from 'vue';

import { MdiCog, MdiDrag, MdiPin } from '@vben/icons';
import { useUserStore } from '@vben/stores';

import {
  Button,
  Drawer,
  Dropdown,
  Input,
  Menu,
  Pagination,
  Space,
  Switch,
  Table,
  Tag,
  Tooltip,
} from 'ant-design-vue';

import { checkDomainPermission } from '#/hooks/useAnsheng/domainConfig';

interface ActionButton {
  key: string;
  text: string;
  type?: 'danger' | 'dashed' | 'default' | 'link' | 'primary' | 'text';
  danger?: boolean;
  ghost?: boolean;
  disabled?: ((record: any) => boolean) | boolean;
  loading?: ((record: any) => boolean) | boolean;
  permission?: number;
  domainPermission?:
    | number
    | {
        authority?: number; // 1表示管理员权限
        domains?: string[]; // 允许的域名列表，不提供则使用全局配置
      };
  onClick: (record: any) => void;
  show?: ((record: any) => boolean) | boolean;
}

interface Props {
  columns: TableColumnType[];
  dataSource?: any[];
  showIndex?: boolean;
  showSelection?: boolean;
  showAction?: boolean;
  loading?: boolean;
  pagination?: false | TableProps['pagination'];
  rowSelection?: TableProps['rowSelection'];
  scroll?: TableProps['scroll'];
  rowKey?: ((record: any) => string) | string;
  actionColumn?: TableColumnType;
  actionButtons?: ActionButton[];
  statusOptions?: {
    [key: string]: Array<{
      color?: string;
      label: string;
      value: number | string;
    }>;
  };
  maxVisibleButtons?: number;
  editableColumns?: string[];
  onSave?: (record: any, key: string, value: any) => Promise<void>;
  // 控制操作按钮是否换行
  actionWrap?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  dataSource: () => [],
  showIndex: false,
  showSelection: false,
  showAction: false,
  loading: false,
  rowKey: 'id',
  scroll: () => ({ x: 'max-content' }),
  actionButtons: () => [],
  statusOptions: () => ({}),
  maxVisibleButtons: 2,
  pagination: false,
  rowSelection: () => ({}),
  actionColumn: () => ({}),
  editableColumns: () => [],
  onSave: () => Promise.resolve(),
  // 默认不换行
  actionWrap: false,
});

const emit = defineEmits<{
  cellSave: [record: any, key: string, value: any];
  change: [pagination: any, filters: any, sorter: any];
  selectionChange: [selectedRowKeys: any[], selectedRows: any[]];
  'update:selectedRowKeys': [keys: any[]];
}>();

// 添加计算属性处理分页配置，确保显示总条数
const tablePagination = computed(() => {
  if (!props.pagination) return false;
  return {
    ...props.pagination,
    showTotal: (total: number) => `共 ${total} 条`,
  };
});

// 窗口宽度
const windowWidth = ref(window.innerWidth);

const userStore = useUserStore();

// 添加响应式判断
const isMobile = computed(() => windowWidth.value <= 768);

// 选中的行 key
const selectedRowKeys = ref<any[]>([]);

// 检查权限
const checkPermission = (permission?: number) => {
  if (!permission) return true;
  const authority = userStore.userInfo?.authority;
  // 如果用户是管理员，直接返回 true
  if (authority === 1) return true;
  // 如果需要管理员权限，且用户不是管理员，返回 false
  return permission !== 1;
};

// 控制列显示和顺序
const columnsVisible = ref<Record<string, boolean>>({});
const columnsFrozen = ref<Record<string, boolean>>({});
const columnsOrder = ref<string[]>([]);

// 控制设置抽屉显示
const settingsVisible = ref(false);

// 备份原始设置，用于重置
const originalSettings = ref({
  visible: {} as Record<string, boolean>,
  frozen: {} as Record<string, boolean>,
  order: [] as string[],
});

// 初始化列显示状态和顺序
onMounted(() => {
  // 初始化列显示
  props.columns.forEach((column) => {
    if (column.dataIndex) {
      const dataIndex = column.dataIndex as string;
      columnsVisible.value[dataIndex] = true;
      columnsFrozen.value[dataIndex] = column.fixed === 'left';
    }
  });

  // 初始化列顺序
  columnsOrder.value = props.columns
    .filter((column) => column.dataIndex)
    .map((column) => column.dataIndex as string);

  // 保存原始设置
  saveOriginalSettings();
});

// 保存原始设置
const saveOriginalSettings = () => {
  originalSettings.value = {
    visible: { ...columnsVisible.value },
    frozen: { ...columnsFrozen.value },
    order: [...columnsOrder.value],
  };
};

// 重置设置
const resetSettings = () => {
  columnsVisible.value = { ...originalSettings.value.visible };
  columnsFrozen.value = { ...originalSettings.value.frozen };
  columnsOrder.value = [...originalSettings.value.order];
};

// 打开设置抽屉
const openSettings = () => {
  settingsVisible.value = true;
};

// 关闭设置抽屉
const closeSettings = () => {
  settingsVisible.value = false;
};

// 保存当前设置
const saveSettings = () => {
  saveOriginalSettings();
  closeSettings();
  // 这里可以添加保存到本地存储或发送到服务器的逻辑
};

// 添加计算属性来过滤列
const filteredColumns = computed(() => {
  return props.columns.filter((column) => column.dataIndex);
});

// 处理拖拽开始
const handleDragStart = (event: DragEvent, index: number) => {
  event.dataTransfer?.setData('text/plain', String(index));
};

// 处理列顺序变更
const handleDragEnd = (event: DragEvent) => {
  const sourceIndex = Number(event.dataTransfer?.getData('text/plain') || '0');
  const targetElement = event.target as HTMLElement;
  const targetItem = targetElement.closest('.column-settings-item');
  if (!targetItem) return;

  const targetIndex = Number(targetItem.dataset.index || '0');

  if (sourceIndex !== targetIndex) {
    const newOrder = [...columnsOrder.value];
    const [movedItem] = newOrder.splice(sourceIndex, 1);
    newOrder.splice(targetIndex, 0, movedItem);
    columnsOrder.value = newOrder;
  }
};

// 允许放置
const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
};

// 计算最终的列配置
const finalColumns = computed(() => {
  const columns: TableColumnType[] = [];

  // 添加序号列
  if (props.showIndex) {
    columns.push({
      title: '序号',
      width: 60,
      align: 'center',
      fixed: 'left',
      customRender: ({ index }) => index + 1,
    });
  }

  // 根据自定义顺序和可见性过滤和排序列
  const orderedVisibleColumns = columnsOrder.value
    .filter((dataIndex) => columnsVisible.value[dataIndex])
    .map((dataIndex) => {
      const column = props.columns.find((col) => col.dataIndex === dataIndex);
      if (column) {
        // 检查权限，如果没有权限则不显示该列
        if (!checkPermission(column.permission as number)) {
          return null;
        }

        // 检查域名权限 - 传递用户权限等级
        const authority = userStore.userInfo?.authority;
        if (!checkDomainPermission(column.domainPermission, authority)) {
          return null;
        }

        // 应用冻结设置
        return {
          ...column,
          fixed: columnsFrozen.value[dataIndex] ? 'left' : undefined,
        };
      }
      return null;
    })
    .filter(Boolean) as TableColumnType[];

  columns.push(...orderedVisibleColumns);

  // 在computed区域添加操作列宽度计算
  const actionColumnWidth = computed(() => {
    // 移动端固定宽度
    if (isMobile.value) return 60;

    // 自定义宽度优先
    if (props.actionColumn?.width) return props.actionColumn.width;

    // 根据列数自适应宽度
    const columnCount = props.columns.length;
    if (columnCount <= 2) {
      // 计算可见按钮数
      const visibleButtonCount = Math.min(
        props.actionButtons.filter((btn) => {
          if (btn.show === undefined) return true;
          return typeof btn.show === 'function'
            ? btn.show({}) // 使用空对象测试，仅估算可能显示的按钮数
            : btn.show;
        }).length,
        props.maxVisibleButtons,
      );

      // 根据按钮数计算宽度：每个按钮约占45px，更多按钮占50px
      const btnWidth = visibleButtonCount * 45;
      const moreWidth =
        props.actionButtons.length > props.maxVisibleButtons ? 50 : 0;

      return Math.min(120, btnWidth + moreWidth); // 最大不超过120px
    }

    // 默认宽度
    return 150;
  });

  // 添加操作列
  if (props.showAction && props.actionButtons?.length) {
    const isCompact = props.columns.length <= 2;
    columns.push({
      title: '操作',
      key: 'action',
      // fixed: 'right',
      fixed: isCompact ? undefined : 'right',
      // width: isMobile.value ? 60 : props.actionColumn?.width || 150,
      width: actionColumnWidth.value,
      align: 'center',
      customRender: ({ record }) => {
        // 获取用户权限等级
        const authority = userStore.userInfo?.authority;

        // 过滤掉没有权限的按钮
        const availableButtons = props.actionButtons?.filter(
          (btn) =>
            checkPermission(btn.permission) &&
            checkDomainPermission(btn.domainPermission, authority),
        );

        if (!availableButtons?.length) {
          return null;
        }

        // 移动端使用下拉菜单
        if (isMobile.value) {
          return renderMobileActions(availableButtons, record);
        }

        // 桌面端使用按钮组
        return renderDesktopActions(availableButtons, record);
      },
    });
  }

  return columns;
});

interface EditCell {
  rowKey: number | string;
  column: string;
  value: any;
}

const editingCell = ref<EditCell | null>(null);
const editingValue = ref<any>(null);

// 清理编辑状态
const cleanupEditState = () => {
  editingCell.value = null;
  editingValue.value = null;
  document.removeEventListener('click', handleOutsideClick);
};

// 保存编辑内容
const handleSave = async () => {
  if (!editingCell.value) return;

  const { rowKey, column, value: originalValue } = editingCell.value;
  // 如果值没有变化，直接清理编辑状态
  if (originalValue === editingValue.value) {
    cleanupEditState();
    return;
  }

  const record = props.dataSource?.find((item) => {
    const itemKey =
      typeof props.rowKey === 'function'
        ? props.rowKey(item)
        : item[props.rowKey];
    return itemKey === rowKey;
  });

  if (!record) return;

  try {
    if (props.onSave) {
      await props.onSave(record, column, editingValue.value);
    }
    emit('cellSave', record, column, editingValue.value);
  } catch (error) {
    console.error('Save failed:', error);
  } finally {
    cleanupEditState();
  }
};

// 处理外部点击
const handleOutsideClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  // 如果点击的是输入框本身或其父元素，不处理
  if (target.closest('.editable-cell-input')) return;

  handleSave();
  // 移除事件监听
  document.removeEventListener('click', handleOutsideClick);
};

// 取消编辑
const handleCancel = () => {
  cleanupEditState();
};

// 双击单元格处理
const handleCellDoubleClick = (record: any, columnOrName: any, value?: any) => {
  // 判断传入的是列对象还是列名
  let column: string;

  if (typeof columnOrName === 'object') {
    // 如果传入的是列对象
    const dataIndex = columnOrName.dataIndex as string;

    // 判断是否是操作列，如果是则不允许编辑
    if (
      columnOrName.key === 'action' ||
      !props.editableColumns?.includes(dataIndex)
    ) {
      return;
    }

    column = dataIndex;
    value = record[dataIndex];
  } else {
    // 如果传入的是列名
    column = columnOrName;

    // 判断该列是否可编辑
    if (!props.editableColumns?.includes(column)) return;
  }

  const rowKey =
    typeof props.rowKey === 'function'
      ? props.rowKey(record)
      : record[props.rowKey];

  // 设置当前编辑单元格
  editingCell.value = { rowKey, column, value };
  editingValue.value = value;

  // 添加点击监听，处理点击外部区域保存
  document.addEventListener('click', handleOutsideClick);
};

// 渲染可编辑单元格
const renderEditableCell = (text: any, record: any, column: string) => {
  const rowKey =
    typeof props.rowKey === 'function'
      ? props.rowKey(record)
      : record[props.rowKey];

  const isEditing =
    editingCell.value?.rowKey === rowKey &&
    editingCell.value?.column === column;

  if (isEditing) {
    return h('div', { class: 'editable-cell-input' }, [
      h(Input, {
        value: editingValue.value,
        'onUpdate:value': (val: any) => {
          editingValue.value = val;
        },
        onPressEnter: handleSave,
        onKeydown: (e: KeyboardEvent) => {
          if (e.key === 'Escape') {
            handleCancel();
          }
        },
        autoFocus: true,
      }),
    ]);
  }

  // 添加Tooltip提示
  return h(
    Tooltip,
    {
      title: '双击可编辑',
      mouseEnterDelay: 0.5,
    },
    {
      default: () =>
        h(
          'div',
          {
            class: 'editable-cell',
            onDblclick: () => handleCellDoubleClick(record, column, text),
          },
          text,
        ),
    },
  );
};

// 渲染移动端操作按钮
const renderMobileActions = (buttons: ActionButton[], record: any) => {
  // 过滤出可见的按钮
  const visibleButtons = buttons.filter((btn) => {
    if (btn.show === undefined) return true;
    return typeof btn.show === 'function' ? btn.show(record) : btn.show;
  });

  if (visibleButtons.length === 0) return null;

  return h(
    Dropdown,
    {
      trigger: ['click'],
      placement: 'bottomRight',
      overlayClassName: 'table-dropdown-overlay',
    },
    {
      default: () => h('a', { class: 'action-dropdown-trigger' }, '···'),
      overlay: () =>
        h(
          Menu,
          {},
          {
            default: () =>
              visibleButtons.map((btn) => {
                const isDisabled =
                  btn.disabled &&
                  (typeof btn.disabled === 'function'
                    ? btn.disabled(record)
                    : btn.disabled);

                return h(
                  Menu.Item,
                  {
                    key: btn.key,
                    disabled: isDisabled,
                    danger: btn.danger,
                  },
                  {
                    default: () =>
                      h(
                        'a',
                        {
                          onClick: (e: Event) => {
                            e.preventDefault();
                            if (!isDisabled) btn.onClick(record);
                          },
                        },
                        btn.text,
                      ),
                  },
                );
              }),
          },
        ),
    },
  );
};

// 渲染桌面端操作按钮
const renderDesktopActions = (buttons: ActionButton[], record: any) => {
  // 过滤出可见的按钮
  const visibleButtons = buttons.filter((btn) => {
    if (btn.show === undefined) return true;
    return typeof btn.show === 'function' ? btn.show(record) : btn.show;
  });

  if (visibleButtons.length === 0) return null;

  // 分为显示的按钮和放入下拉菜单的按钮
  const mainButtons = visibleButtons.slice(0, props.maxVisibleButtons);
  const dropdownButtons = visibleButtons.slice(props.maxVisibleButtons);

  // 渲染主按钮
  const mainButtonElements = mainButtons.map((btn) => {
    const isDisabled =
      btn.disabled &&
      (typeof btn.disabled === 'function'
        ? btn.disabled(record)
        : btn.disabled);

    const isLoading =
      btn.loading &&
      (typeof btn.loading === 'function' ? btn.loading(record) : btn.loading);

    return h(
      Button,
      {
        key: btn.key,
        type: btn.type || 'link',
        size: 'small',
        disabled: isDisabled,
        loading: isLoading,
        danger: btn.danger,
        ghost: btn.ghost,
        onClick: () => {
          if (!isDisabled && !isLoading) btn.onClick(record);
        },
      },
      { default: () => btn.text },
    );
  });

  // 如果只有主按钮，直接返回
  if (dropdownButtons.length === 0) {
    return h(
      Space,
      {
        wrap: props.actionWrap,
        class: props.actionWrap ? 'action-buttons-wrap' : '',
      },
      { default: () => mainButtonElements },
    );
  }

  // 有更多按钮，添加下拉菜单
  const dropdownElement = h(
    Dropdown,
    {
      trigger: ['hover'],
      placement: 'bottomRight',
      overlayClassName: 'table-dropdown-overlay',
    },
    {
      default: () =>
        h(
          Button,
          {
            type: 'link',
            size: 'small',
          },
          { default: () => '更多' },
        ),
      overlay: () =>
        h(
          Menu,
          {},
          {
            default: () =>
              dropdownButtons.map((btn) => {
                const isDisabled =
                  btn.disabled &&
                  (typeof btn.disabled === 'function'
                    ? btn.disabled(record)
                    : btn.disabled);

                return h(
                  Menu.Item,
                  {
                    key: btn.key,
                    disabled: isDisabled,
                    danger: btn.danger,
                  },
                  {
                    default: () =>
                      h(
                        'a',
                        {
                          onClick: (e: Event) => {
                            e.preventDefault();
                            if (!isDisabled) btn.onClick(record);
                          },
                        },
                        btn.text,
                      ),
                  },
                );
              }),
          },
        ),
    },
  );

  return h(
    Space,
    {
      wrap: props.actionWrap,
      class: props.actionWrap ? 'action-buttons-wrap' : '',
    },
    { default: () => [...mainButtonElements, dropdownElement] },
  );
};

// 处理行选择变化
const onSelectChange = (keys: any[], rows: any[]) => {
  selectedRowKeys.value = keys;
  emit('selectionChange', keys, rows);
  emit('update:selectedRowKeys', keys);
};

// 表格选择配置
const rowSelectionConfig = computed(() => {
  if (!props.showSelection) return undefined;

  return {
    selectedRowKeys: selectedRowKeys.value,
    onChange: onSelectChange,
    ...props.rowSelection,
  };
});

// 状态下拉选项格式化
const formatStatusText = (column: string, value: number | string) => {
  // 如果存在状态选项配置，则使用配置生成适当的标签
  if (props.statusOptions && props.statusOptions[column]) {
    const option = props.statusOptions[column].find(
      (item) => item.value === value,
    );
    if (option) {
      return h(
        Tag,
        {
          color: option.color,
        },
        { default: () => option.label },
      );
    }
  }
  return value;
};

// 创建统一的渲染单元格函数，替代原有的customRender
const renderCell = ({
  column,
  record,
  index,
}: {
  column: TableColumnType;
  index: number;
  record: any;
}) => {
  const dataIndex = column.dataIndex as string;
  const text = record[dataIndex];
  const rowKey =
    typeof props.rowKey === 'function'
      ? props.rowKey(record)
      : record[props.rowKey];

  // 检查是否是当前编辑的单元格
  const isEditing =
    editingCell.value?.rowKey === rowKey &&
    editingCell.value?.column === dataIndex;

  // 如果是操作列，不应该可编辑
  if (column.key === 'action') {
    return column.customRender
      ? column.customRender({
          text,
          record,
          index,
          column,
        })
      : text;
  }

  if (isEditing) {
    return h('div', { class: 'editable-cell-input' }, [
      h(Input, {
        value: editingValue.value,
        'onUpdate:value': (val: any) => {
          editingValue.value = val;
        },
        onPressEnter: handleSave,
        onKeydown: (e: KeyboardEvent) => {
          if (e.key === 'Escape') {
            handleCancel();
          }
        },
        autoFocus: true,
      }),
    ]);
  }

  // 处理自定义渲染
  if (column.customRender) {
    try {
      const customContent = column.customRender({
        text,
        record,
        index,
        column,
      });

      // 如果是可编辑列，添加双击事件和Tooltip
      if (props.editableColumns?.includes(dataIndex)) {
        return h(
          Tooltip,
          {
            title: '双击可编辑',
            mouseEnterDelay: 0.5,
          },
          {
            default: () =>
              h(
                'div',
                {
                  class: 'editable-cell',
                  onDblclick: () => handleCellDoubleClick(record, column),
                },
                [customContent],
              ),
          },
        );
      }
      return customContent;
    } catch (error) {
      console.error('Error in renderCell:', error);
      return text;
    }
  }

  // 处理状态标签
  if (
    props.statusOptions &&
    Object.keys(props.statusOptions).includes(dataIndex)
  ) {
    const statusContent = formatStatusText(dataIndex, text);

    // 如果是可编辑列，添加双击事件和Tooltip
    if (props.editableColumns?.includes(dataIndex)) {
      return h(
        Tooltip,
        {
          title: '双击可编辑',
          mouseEnterDelay: 0.5,
        },
        {
          default: () =>
            h(
              'div',
              {
                class: 'editable-cell',
                onDblclick: () => handleCellDoubleClick(record, column),
              },
              [statusContent],
            ),
        },
      );
    }
    return statusContent;
  }

  // 如果是可编辑列，添加双击事件和Tooltip
  if (props.editableColumns?.includes(dataIndex)) {
    return h(
      Tooltip,
      {
        title: '双击可编辑',
        mouseEnterDelay: 0.5,
      },
      {
        default: () =>
          h(
            'div',
            {
              class: 'editable-cell',
              onDblclick: () => handleCellDoubleClick(record, column),
            },
            [text],
          ),
      },
    );
  }

  return text;
};

// 自定义渲染函数，处理可编辑单元格和状态标签 (保持兼容)
const customRender = ({ text, record, column, index }: any) => {
  return renderCell({ column, record, index });
};

// 调整窗口大小
const handleWindowResize = () => {
  windowWidth.value = window.innerWidth;
};

// 添加全屏状态控制
const isFullscreen = ref(false);
const tableRef = ref<HTMLElement>();

// 处理全屏切换
const toggleFullscreen = () => {
  if (!tableRef.value) return;

  if (isFullscreen.value) {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  } else {
    if (tableRef.value.requestFullscreen) {
      tableRef.value.requestFullscreen();
    }
  }
};

// 监听窗口大小变化
onMounted(() => {
  window.addEventListener('resize', handleWindowResize);
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement;
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', handleWindowResize);
});

// // 添加刷新方法
// const handleRefresh = () => {
//   emit('change', pagination, {}, {});
// };

// 导出组件API
defineExpose({
  selectedRowKeys,
  setSelectedRowKeys: (keys: any[]) => {
    selectedRowKeys.value = keys;
  },
  clearSelection: () => {
    selectedRowKeys.value = [];
  },
});
</script>

<template>
  <div class="basic-table-wrapper" ref="tableRef">
    <!-- 简单的工具栏 -->
    <div class="table-header">
      <div class="table-title">
        <slot name="tableTitle"></slot>
      </div>
      <div class="table-actions">
        <!-- <Button class="action-button" @click="handleRefresh">
          <MdiRefresh />
        </Button> -->
        <!-- <Button class="action-button" @click="toggleFullscreen">
          <MdiFullscreen />
        </Button> -->
        <Button class="action-button" @click="openSettings">
          <MdiCog />
        </Button>
        <slot name="tableActions"></slot>
      </div>
    </div>

    <div class="basic-table">
      <Table
        :columns="finalColumns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="tablePagination"
        :row-key="rowKey"
        :row-selection="rowSelectionConfig"
        :scroll="scroll"
        @change="(...args) => emit('change', ...args)"
      >
        <!-- 添加自定义插槽 -->
        <template #headerCell="{ column }">
          <slot :name="`header-${column.dataIndex}`" :column="column">
            {{ column.title }}
          </slot>
        </template>

        <template #bodyCell="{ column, record, index }">
          <slot
            :name="`column-${column.dataIndex}`"
            :text="record[column.dataIndex]"
            :record="record"
            :index="index"
          >
            <component :is="() => renderCell({ column, record, index })" />
          </slot>
        </template>

        <!-- 添加分页自定义插槽 -->
        <template #pagination="paginationProps" v-if="tablePagination">
          <div class="table-pagination-wrapper">
            <div class="pagination-left-content">
              <slot name="paginationLeftContent"></slot>
            </div>
            <Pagination
              v-bind="{ ...tablePagination, ...paginationProps }"
              @change="
                (page, pageSize) => emit('change', { current: page, pageSize })
              "
            />
          </div>
        </template>
      </Table>
    </div>

    <!-- 列设置抽屉 -->
    <Drawer
      title="表格列设置"
      placement="right"
      :visible="settingsVisible"
      @close="closeSettings"
      :width="400"
      :closable="true"
      :mask-closable="true"
    >
      <div class="column-settings">
        <div class="column-settings-header">
          <div class="column-settings-title">调整列顺序和显示</div>
          <div class="column-settings-subtitle">
            拖动项目可调整表格中的显示顺序
          </div>
        </div>

        <div class="column-settings-scroll">
          <div
            v-for="(dataIndex, index) in columnsOrder"
            :key="dataIndex"
            class="column-settings-item"
            draggable="true"
            @dragstart="handleDragStart($event, index)"
            @dragover="handleDragOver"
            @drop="handleDragEnd($event)"
            :data-index="index"
          >
            <div class="column-settings-drag">
              <MdiDrag />
            </div>

            <div class="column-settings-content">
              <div class="column-settings-info">
                <Switch
                  :checked="columnsVisible[dataIndex]"
                  @change="(checked) => (columnsVisible[dataIndex] = checked)"
                  size="small"
                />
                <span class="column-settings-name">
                  {{
                    props.columns.find((col) => col.dataIndex === dataIndex)
                      ?.title
                  }}
                </span>
              </div>

              <Button
                type="text"
                size="small"
                :class="{
                  'column-frozen': columnsFrozen[dataIndex],
                }"
                @click="columnsFrozen[dataIndex] = !columnsFrozen[dataIndex]"
              >
                <MdiPin />
              </Button>
            </div>
          </div>
        </div>

        <div class="column-settings-footer">
          <Button @click="resetSettings">重置</Button>
        </div>
      </div>
    </Drawer>
  </div>
</template>

<style lang="less" scoped>
.basic-table-wrapper {
  width: 100%;
  background-color: hsl(var(--background-color));
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 4px;
  // border-bottom: 1px solid #f0f0f0;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: calc(var(--radius) - 2px);
}

// 列设置样式
.column-settings {
  display: flex;
  flex-direction: column;
  height: 100%;

  &-header {
    margin-bottom: 16px;
  }

  &-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
  }

  &-subtitle {
    color: #999;
    font-size: 12px;
  }

  &-scroll {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px;
    margin-bottom: 16px;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: hsl(var(--border-color));
      border-radius: 4px;
    }

    &::-webkit-scrollbar-track {
      background-color: hsl(var(--background-color));
    }
  }

  &-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin-bottom: 8px;
    border: 1px solid hsl(var(--border-color));
    border-radius: 4px;
    background-color: hsl(var(--background-color));
    cursor: move;
    transition: all 0.2s;

    &:hover {
      background-color: hsl(var(--background-color));
      border-color: hsl(var(--border-color));
    }

    &.dragging {
      opacity: 0.5;
      background-color: hsl(var(--background-color));
    }
  }

  &-drag {
    color: #bbb;
    margin-right: 8px;
    cursor: grab;
    display: flex;
    align-items: center;

    &:active {
      cursor: grabbing;
    }
  }

  &-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &-name {
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
  }

  &-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}

.column-frozen {
  color: #1890ff;
}

// 全屏样式
:fullscreen {
  .basic-table-wrapper {
    height: 100vh;
    display: flex;
    flex-direction: column;

    .basic-table {
      flex: 1;
      overflow: auto;
    }
  }
}

.editable-cell {
  padding: 5px 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s;

  &:hover {
    background-color: #f0f0f0;
  }
}

.editable-cell-input {
  padding: 0;
  margin: -3px 0;
}

.action-dropdown-trigger {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  font-size: 16px;
  font-weight: bold;
  color: #1890ff;
  border-radius: 4px;

  &:hover {
    background-color: #f0f0f0;
  }
}

:deep(.ant-dropdown-menu-item) {
  min-width: 100px;
}

:deep(.ant-table-small) {
  font-size: 14px;
}

:deep(.ant-table-cell) {
  &.ant-table-column-sort {
    background-color: #f0f7ff;
  }
}

:deep(.ant-table-row) {
  &:hover {
    .editable-cell {
      background-color: #f0f0f0;
      // color: hsl(var(--text-color));
      // background-color: hsl(var(--background-color));
    }
  }
}

// 移动端样式优化
@media (max-width: 768px) {
  :deep(.ant-table) {
    font-size: 12px;
  }

  :deep(.ant-table-cell) {
    padding: 8px 4px;
  }

  :deep(.ant-table-thead > tr > th) {
    padding: 8px 4px;
  }
}

// 操作按钮换行居中样式
:deep(.action-buttons-wrap) {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;

  .ant-btn {
    margin-bottom: 4px;
  }
}
</style>
