<script setup lang="ts">
import type { CustomButton } from '../types';

import { computed, h, onMounted, onUnmounted, ref, watch } from 'vue';

import { MdiChevronDown, MdiMagnify, MdiReload } from '@vben/icons';
import { useUserStore } from '@vben/stores';

import {
  Button,
  DatePicker,
  Input,
  InputNumber,
  Select,
  Space,
  Switch,
  Textarea,
} from 'ant-design-vue';

// 导入域名权限检查函数
import { checkDomainPermission } from '../domainConfig';

// 组件属性默认值
const props = withDefaults(defineProps<SearchToolbarProps>(), {
  loading: false,
  showExpand: true,
  showShortcut: true,
  advancedItems: () => [],
  customButtons: () => [],
  compact: false,
});

// 组件事件
const emit = defineEmits<{
  /** 重置事件 */
  reset: [];
  /** 搜索事件 */
  search: [resetPage?: boolean];
  /** 表单值更新事件 */
  'update:modelValue': [value: Record<string, any>];
}>();

// 添加 userStore 常量
const userStore = useUserStore();

/**
 * 搜索项配置接口
 * @interface SearchItemConfig
 */
export interface SearchItemConfig {
  /** 表单项标签 */
  label: string;
  /** 表单项字段名 */
  field: string;
  /** 表单控件类型 */
  component: keyof typeof componentMap;
  /** 栅格占比(可选) */
  span?: number;
  /** 权限控制，1表示需要管理员权限 */
  permission?: number;
  /** 域名权限控制 */
  domainPermission?:
    | number
    | {
        authority?: number; // 1表示管理员权限
        domains?: string[]; // 允许的域名列表，不提供则使用全局配置
      };
  /** 组件属性(可选) */
  props?: Record<string, any>;
  /** 选项数据(Select组件使用) */
  options?: SelectOption[];
  /** 远程数据配置 */
  remote?: {
    /** 远程数据接口函数 */
    api: ApiFunction;
    /** 是否启用缓存 */
    cache?: boolean;
    /** 缓存时间(毫秒) */
    cacheTime?: number;
    /** 防抖时间(毫秒) */
    debounceTime?: number;
    /** 接口参数 */
    params?: Record<string, any>;
    /** 数据转换函数 */
    transform?: (data: any) => SelectOption[];
  };
}

/**
 * 搜索组配置接口
 * @interface SearchGroup
 */
export interface SearchGroup {
  /** 分组标题(可选) */
  label?: string;
  /** 分组内的搜索项 */
  items: SearchItemConfig[];
}

/**
 * 组件属性接口
 * @interface SearchToolbarProps
 */
export interface SearchToolbarProps {
  /** 基础搜索项配置 */
  basicItems: SearchItemConfig[];
  /** 高级搜索项配置(可选) */
  advancedItems?: SearchGroup[];
  /** 搜索表单值 */
  modelValue: Record<string, any>;
  /** 加载状态 */
  loading?: boolean;
  /** 是否显示展开按钮 */
  showExpand?: boolean;
  /** 是否显示搜索按钮快捷键提示 */
  showShortcut?: boolean;
  /** 自定义按钮配置 */
  customButtons?: CustomButton[];
  /** 是否使用紧凑布局，紧凑布局时搜索项不会撑满整行 */
  compact?: boolean;
}

/**
 * 组件映射表 - 用于动态渲染表单控件
 * @description 可以根据需要添加更多组件
 */
const componentMap = {
  Input, // 输入框
  Select, // 下拉选择框
  RangePicker: DatePicker.RangePicker, // 日期范围选择器
  DatePicker, // 日期选择器
  TimePicker: DatePicker.TimePicker, // 时间选择器
  Textarea, // 输入框
  Switch, // 开关
  InputNumber, // 输入框
};

// 定义选项数据类型
export interface SelectOption {
  label: string;
  value: any;
  [key: string]: any;
}

// 定义接口函数类型
export type ApiFunction<T = any> = (params?: T) => Promise<SelectOption[]>;

/**
 * 展开/收起状态
 */
const isExpanded = ref(false);

/**
 * 切换展开状态
 */
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

/**
 * 搜索处理
 */
const handleSearch = () => {
  emit('search', true);
};

/**
 * 重置处理
 */
const handleReset = () => {
  emit('reset');
};

/**
 * 是否为移动端
 */
const _isMobile = computed(() => {
  return window.innerWidth <= 768;
});

// 移除全局事件监听
const searchWrapperRef = ref<HTMLDivElement>();

// 修改为本地事件监听
const handleKeydown = (e: KeyboardEvent) => {
  // 确保事件源在搜索组件内
  if (
    e.key === 'Enter' &&
    !e.isComposing &&
    searchWrapperRef.value?.contains(e.target as Node)
  ) {
    handleSearch();
  }
};

// 注册和移除事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});

/**
 * 获取实际组件
 * @param type 组件类型
 */
const getComponent = (type: keyof typeof componentMap) => {
  return componentMap[type];
};

// 缓存选项数据
const optionsCache = new Map<
  string,
  {
    data: SelectOption[];
    timestamp: number;
  }
>();

// 存储当前选项数据
const currentOptions = ref<Record<string, SelectOption[]>>({});

// 存储加载状态
const loadingStates = ref<Record<string, boolean>>({});

// 存储防抖定时器
const debounceTimers = new Map<string, NodeJS.Timeout>();

/**
 * 设置加载状态
 * @param field 字段名
 * @param loading 加载状态
 */
const setLoading = (field: string, loading: boolean) => {
  loadingStates.value[field] = loading;
};

/**
 * 获取加载状态
 * @param field 字段名
 */
const getLoading = (field: string) => {
  return loadingStates.value[field] || false;
};

/**
 * 获取缓存数据
 * @param key 缓存键
 * @param cacheTime 缓存时间
 */
const getCache = (key: string, cacheTime?: number) => {
  const cache = optionsCache.get(key);
  if (!cache) return null;

  if (cacheTime && Date.now() - cache.timestamp > cacheTime) {
    optionsCache.delete(key);
    return null;
  }

  return cache.data;
};

/**
 * 设置缓存数据
 * @param key 缓存键
 * @param data 缓存数据
 */
const setCache = (key: string, data: SelectOption[]) => {
  optionsCache.set(key, {
    data,
    timestamp: Date.now(),
  });
};

/**
 * 获取远程数据
 * @param item 搜索项配置
 */
const fetchRemoteData = async (item: SearchItemConfig) => {
  if (!item.remote) return;

  const { api, params, transform, cache, cacheTime, debounceTime } =
    item.remote;
  const cacheKey = `${item.field}_${JSON.stringify(params || {})}`;

  // 检查缓存
  if (cache) {
    const cachedData = getCache(cacheKey, cacheTime);
    if (cachedData) {
      currentOptions.value[item.field] = cachedData;
      return;
    }
  }

  // 防抖处理
  if (debounceTime) {
    if (debounceTimers.has(cacheKey)) {
      clearTimeout(debounceTimers.get(cacheKey));
    }

    return new Promise((resolve) => {
      debounceTimers.set(
        cacheKey,
        setTimeout(async () => {
          try {
            setLoading(item.field, true);
            const response = await api(params);
            const data = transform ? transform(response) : response;

            if (cache) {
              setCache(cacheKey, data);
            }

            currentOptions.value[item.field] = data;
            resolve(data);
          } catch (error) {
            console.error(`获取远程数据失败: ${item.field}`, error);
            currentOptions.value[item.field] = [];
          } finally {
            setLoading(item.field, false);
            debounceTimers.delete(cacheKey);
          }
        }, debounceTime),
      );
    });
  }

  // 直接请求
  try {
    setLoading(item.field, true);
    const response = await api(params);
    const data = transform ? transform(response) : response;

    if (cache) {
      setCache(cacheKey, data);
    }

    currentOptions.value[item.field] = data;
  } catch (error) {
    console.error(`获取远程数据失败: ${item.field}`, error);
    currentOptions.value[item.field] = [];
  } finally {
    setLoading(item.field, false);
  }
};

/**
 * 初始化选项数据
 * @param items 搜索项配置
 */
const initOptions = async (items: SearchItemConfig[]) => {
  for (const item of items) {
    if (item.component === 'Select') {
      if (item.options) {
        // 使用静态选项数据，确保存入 currentOptions 以便一致处理
        currentOptions.value[item.field] = item.options;
      } else if (item.remote) {
        // 使用远程数据
        await fetchRemoteData(item);
      }
    }
  }
};

// 监听展开状态变化，初始化高级搜索的选项数据
watch(isExpanded, async (expanded) => {
  if (expanded && props.advancedItems) {
    for (const group of props.advancedItems) {
      await initOptions(group.items);
    }
  }
});

// 组件挂载时初始化基础搜索的选项数据
onMounted(async () => {
  await initOptions(props.basicItems);
});

// 在模板中使用
const _renderSelect = (item: SearchItemConfig) => {
  const Component = getComponent(item.component) as any;
  const options = currentOptions.value[item.field] || [];
  const loading = getLoading(item.field);

  return h(Component, {
    ...item.props,
    loading,
    options,
    value: props.modelValue[item.field],
    'onUpdate:value': (value: any) => {
      emit('update:modelValue', {
        ...props.modelValue,
        [item.field]: value,
      });
    },
    // 支持搜索
    showSearch: true,
    // 支持多选
    mode: item.props?.mode || 'default',
    // 自定义搜索逻辑，同时搜索 label 和 value
    filterOption: (input: string, option: any) => {
      const label = option.label?.toString().toLowerCase() || '';
      const value = option.value?.toString().toLowerCase() || '';
      const searchText = input.toLowerCase();
      return label.includes(searchText) || value.includes(searchText);
    },
    // 远程搜索
    onSearch: async (value: string) => {
      if (item.remote) {
        await fetchRemoteData({
          ...item,
          remote: {
            ...item.remote,
            params: {
              ...item.remote.params,
              keyword: value,
            },
          },
        });
      }
    },
  });
};

// 添加权限检查函数
const checkPermission = (permission?: number) => {
  if (permission === undefined) return true;
  const authority = userStore.userInfo?.authority;
  // 如果用户是管理员，直接返回 true
  if (authority === 1) return true;
  // 如果需要管理员权限，且用户不是管理员，返回 false
  return permission !== 1;
};

// 过滤后的按钮列表
const filteredButtons = computed(() => {
  if (!props.customButtons?.length) return [];

  // 获取用户权限等级
  const authority = userStore.userInfo?.authority;

  return props.customButtons.filter(
    (btn) =>
      checkPermission(btn.permission) &&
      checkDomainPermission(btn.domainPermission, authority),
  );
});

// 添加过滤搜索项的计算属性
const filteredBasicItems = computed(() => {
  // 获取用户权限等级
  const authority = userStore.userInfo?.authority;

  return props.basicItems.filter(
    (item) =>
      checkPermission(item.permission) &&
      checkDomainPermission(item.domainPermission, authority),
  );
});

const filteredAdvancedItems = computed(() => {
  // 获取用户权限等级
  const authority = userStore.userInfo?.authority;

  return props.advancedItems.map((group) => ({
    ...group,
    items: group.items.filter(
      (item) =>
        checkPermission(item.permission) &&
        checkDomainPermission(item.domainPermission, authority),
    ),
  }));
});
</script>

<template>
  <div class="search-toolbar" ref="searchWrapperRef">
    <!-- 基础搜索区域 -->
    <div class="basic-search">
      <div class="search-row" :class="{ 'compact-mode': compact }">
        <div
          v-for="item in filteredBasicItems"
          :key="item.field"
          class="form-item custom-width"
          :style="item.span ? { '--search-item-width': `${item.span}px` } : {}"
        >
          <div class="form-label">{{ item.label }}</div>
          <component
            :is="getComponent(item.component)"
            :value="modelValue[item.field]"
            @update:value="
              (val) =>
                emit('update:modelValue', { ...modelValue, [item.field]: val })
            "
            v-bind="{
              ...item.props,
              showSearch: item.component === 'Select',
              optionFilterProp: 'label',
            }"
            :loading="item.component === 'Select' && getLoading(item.field)"
            @keyup.enter="handleSearch"
          >
            <!-- Select 组件的选项处理 -->
            <template v-if="item.component === 'Select'">
              <Select.Option
                v-for="option in item.options ||
                currentOptions[item.field] ||
                []"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              >
                {{ option.label }}
              </Select.Option>
            </template>
          </component>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-row">
        <Space>
          <Button
            type="primary"
            :loading="loading"
            @click="handleSearch"
            class="toolbar-button"
          >
            <span class="button-content">
              <MdiMagnify class="button-icon" />
              <span class="button-text">搜索</span>
              <span v-if="showShortcut" class="shortcut-tip">(Enter)</span>
            </span>
          </Button>
          <Button @click="handleReset" class="toolbar-button">
            <span class="button-content">
              <MdiReload class="button-icon" />
              <span class="button-text">重置</span>
            </span>
          </Button>
        </Space>
        <a
          v-if="showExpand && advancedItems?.length"
          class="expand-button"
          @click="toggleExpand"
        >
          {{ isExpanded ? '收起筛选' : '展开筛选' }}
          <MdiChevronDown :class="{ expanded: isExpanded }" />
        </a>
      </div>
    </div>

    <!-- 高级搜索区域 -->
    <div
      v-if="advancedItems?.length"
      class="advanced-search"
      :class="{ expanded: isExpanded }"
    >
      <div
        v-for="(group, index) in filteredAdvancedItems"
        :key="index"
        class="search-group"
      >
        <div v-if="group.label" class="group-title">{{ group.label }}</div>
        <div class="group-items" :class="{ 'compact-mode': compact }">
          <div
            v-for="item in group.items"
            :key="item.field"
            class="form-item custom-width"
            :style="
              item.span ? { '--search-item-width': `${item.span}px` } : {}
            "
          >
            <div class="form-label">{{ item.label }}</div>
            <component
              :is="getComponent(item.component)"
              :value="modelValue[item.field]"
              @update:value="
                (val) =>
                  emit('update:modelValue', {
                    ...modelValue,
                    [item.field]: val,
                  })
              "
              v-bind="{
                ...item.props,
                showSearch: item.component === 'Select',
                optionFilterProp: 'label',
              }"
              :loading="item.component === 'Select' && getLoading(item.field)"
              @keyup.enter="handleSearch"
            >
              <!-- Select 组件的选项处理 -->
              <template v-if="item.component === 'Select'">
                <Select.Option
                  v-for="option in item.options ||
                  currentOptions[item.field] ||
                  []"
                  :key="option.value"
                  :value="option.value"
                  :label="option.label"
                >
                  {{ option.label }}
                </Select.Option>
              </template>
            </component>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义按钮区域 -->
    <div v-if="filteredButtons.length > 0" class="pt-4">
      <Space class="flex w-full flex-wrap gap-2">
        <Button
          v-for="btn in filteredButtons"
          :key="btn.text"
          :type="btn.type"
          v-bind="btn.props"
          @click="btn.onClick"
          class="!flex min-w-[100px] flex-1 items-center justify-center md:min-w-[120px] md:flex-none"
        >
          <component v-if="btn.icon" :is="btn.icon" class="mr-1 text-[14px]" />
          <span>{{ btn.text }}</span>
        </Button>
      </Space>
    </div>
  </div>
</template>

<style lang="less" scoped>
.search-toolbar {
  background-color: var(--background-deep);
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;

  .basic-search {
    .search-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 16px;

      // 紧凑布局模式
      &.compact-mode {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;

        .form-item {
          flex: 0 0 auto;
          margin-right: 16px;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;

        &.compact-mode {
          flex-direction: column;

          .form-item {
            margin-right: 0;
            margin-bottom: 16px;
          }
        }
      }

      .form-item {
        display: flex;
        flex-direction: column;
        gap: 8px;

        &.custom-width {
          max-width: var(--search-item-width, auto);
          min-width: 200px;
        }

        .form-label {
          font-size: 14px;
          color: var(--text-color);
          white-space: nowrap;
        }

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker) {
          width: 100%;
        }
      }
    }

    .action-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;

      .toolbar-button {
        display: flex;
        align-items: center;
        gap: 4px;

        .button-content {
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .button-icon {
          font-size: 16px;
        }

        .button-text {
          margin-left: 4px;
        }

        .shortcut-tip {
          color: var(--text-color-secondary);
          font-size: 12px;
          margin-left: 4px;
        }
      }

      .expand-button {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--primary-color);
        cursor: pointer;
        font-size: 14px;

        &:hover {
          opacity: 0.8;
        }

        .expanded {
          transform: rotate(180deg);
        }
      }
    }
  }

  .advanced-search {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
    transition: all 0.3s ease;

    &.expanded {
      display: block;
      opacity: 1;
      transform: translateY(0);
    }

    &:not(.expanded) {
      display: none;
      opacity: 0;
      transform: translateY(-10px);
    }

    .search-group {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .group-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-color);
        margin-bottom: 12px;
        padding-left: 8px;
        border-left: 3px solid var(--primary-color);
      }

      .group-items {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        padding: 8px;
        background-color: var(--background-light);
        border-radius: 4px;

        // 紧凑布局模式
        &.compact-mode {
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;

          .form-item {
            flex: 0 0 auto;
            margin-right: 16px;

            &:last-child {
              margin-right: 0;
            }
          }
        }

        @media (max-width: 768px) {
          grid-template-columns: 1fr;

          &.compact-mode {
            flex-direction: column;

            .form-item {
              margin-right: 0;
              margin-bottom: 16px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }

        .form-item {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .form-label {
            font-size: 14px;
            color: var(--text-color);
            white-space: nowrap;
          }

          :deep(.ant-input),
          :deep(.ant-select),
          :deep(.ant-picker) {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
