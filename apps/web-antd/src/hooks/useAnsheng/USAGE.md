# useAnsheng Hooks 使用文档

## 1. 简介

`useAnsheng` 是一套高度封装的 Vue3 组合式 API 集合，专为简化后台管理系统中常见的 CRUD (增删改查) 操作而设计。它集成了表格、表单、搜索、详情等功能，提供了一致性的界面和交互体验。

### 主要功能

- **表格管理**：分页、排序、筛选、虚拟滚动
- **表单处理**：新增、编辑、验证
- **搜索功能**：基础搜索、高级搜索
- **详情展示**：结构化展示数据
- **批量操作**：批量删除、批量编辑等
- **内联编辑**：直接在表格中编辑数据

## 2. 基本用法

### 2.1 完整示例

```typescript
import { useAnsheng } from '#/hooks/useAnsheng';
import { onMounted } from 'vue';

// 在setup函数中使用
const {
  // 状态和数据
  formData,          // 表单数据
  tableData,         // 表格数据
  loading,           // 加载状态
  pagination,        // 分页配置
  actionButtons,     // 操作按钮

  // 方法
  handleTableChange, // 表格变化处理函数
  initialize,        // 初始化函数
  createItem,        // 创建项目函数
  editItem,          // 编辑项目函数
  deleteItem,        // 删除项目函数
  
  // 搜索相关
  searchToolbarBind, // 搜索工具栏绑定
  handleSearch,      // 搜索处理函数
  handleReset,       // 重置搜索处理函数

  // 子hook
  detail,            // 详情hook
  form,              // 表单hook
  table,             // 表格hook
} = useAnsheng({
  // 配置项
  formOptions: {
    // 表单配置...
  },
  tableOptions: {
    // 表格配置...
  },
  searchOptions: {
    // 搜索配置...
  },
  detailOptions: {
    // 详情配置...
  },
  deleteApi: (id) => api.delete(id),
  viewMode: 'form', // 'form' 或 'detail'
});

// 初始化数据
onMounted(() => {
  initialize();
});
```

### 2.2 在模板中使用

```vue
<template>
  <div>
    <!-- 搜索工具栏 -->
    <SearchToolbar v-bind="searchToolbarBind" />
    
    <!-- 表格 -->
    <BasicTable v-bind="tableBind" />
    
    <!-- 表单模态框 -->
    <component :is="formComponent" />
    
    <!-- 详情模态框 -->
    <component :is="detailComponent" />
  </div>
</template>

<script setup>
// 创建表单组件
const formComponent = form.renderFormModal([
  {
    title: '基本信息',
    fields: [
      { name: 'name', label: '名称', component: 'Input' },
      // 更多字段...
    ]
  }
]);

// 创建详情组件
const detailComponent = detail?.renderDetailModal([
  {
    title: '基本信息',
    items: [
      { label: '名称', field: 'name' },
      // 更多字段...
    ]
  }
]);
</script>
```

## 3. 配置项详解

### 3.1 表单配置 (formOptions)

```typescript
formOptions: {
  // 表单标题，可以是字符串或函数
  title: (isEdit: boolean) => isEdit ? '编辑' : '创建',
  
  // 表单宽度
  width: 700,
  
  // 表单默认值
  defaultValues: {
    name: '',
    age: 18,
    // ...其他字段
  },
  
  // 获取详情的API函数，用于编辑表单
  getDetail: (id: number) => Promise<any>,
  
  // 创建数据的API函数
  create: (data: any) => Promise<any>,
  
  // 更新数据的API函数
  update: (id: number, data: any) => Promise<any>,
  
  // 是否允许全屏
  fullscreenable: true,
  
  // 是否允许拖动
  draggable: true,
  
  // 成功回调
  onSuccess: () => void,
  
  // 取消回调
  onCancel: () => void,
  
  // 表单验证规则
  rules: {
    name: [{ required: true, message: '请输入名称' }],
  },
  
  // 简单布局配置
  simpleLayout: {
    containerClass: 'custom-form',
    inline: false,
    itemMargin: 16,
    padding: 24,
    showGroupTitle: true,
  },
  
  // 组件挂载时的回调函数
  onMounted: () => void,
  
  // 选项加载API配置
  selectApis: {
    userList: () => api.getUserList(),
  },
}
```

### 3.2 表格配置 (tableOptions)

```typescript
tableOptions: {
  // 获取列表数据的API
  api: (params: any) => Promise<{ rows: any[], total: number }>,
  
  // 表格列定义
  columns: [
    { title: '姓名', dataIndex: 'name', width: 100 },
    { title: '年龄', dataIndex: 'age', width: 80 },
    // ...其他列
  ],
  
  // 默认查询参数
  defaultParams: {
    status: 1,
  },
  
  // 默认页码
  defaultPage: 1,
  
  // 默认每页数量
  defaultPageSize: 10,
  
  // 操作按钮配置
  actionButtons: [
    {
      key: 'view',
      text: '查看',
      type: 'link',
      onClick: (record) => void,
    },
    {
      key: 'edit',
      text: '编辑',
      type: 'link',
      onClick: (record) => void,
    },
    {
      key: 'delete',
      text: '删除',
      type: 'link',
      danger: true,
      onClick: (record) => void,
    },
  ],
  
  // 操作按钮是否换行显示
  actionWrap: false,
  
  // 最大显示按钮数量
  maxVisibleButtons: 2,
  
  // 请求前参数处理函数
  beforeFetch: (params) => params,
  
  // 请求后数据处理函数
  afterFetch: (data) => ({ rows: data.list, total: data.total }),
  
  // 表格类型
  tableType: 'ant',
  
  // 单元格保存函数
  onSave: (record, value, key) => api.updateField(record.id, key, value),
  
  // 是否启用虚拟滚动
  virtualScroll: false,
  
  // 虚拟滚动配置
  virtualScrollOptions: {
    scrollY: 400,
    itemHeight: 54,
    threshold: 100,
  },
}
```

### 3.3 搜索配置 (searchOptions)

```typescript
searchOptions: {
  // 基础搜索项
  basicItems: [
    {
      field: 'keyword',
      label: '关键词',
      component: 'Input',
      props: {
        placeholder: '请输入关键词',
        allowClear: true,
      },
    },
    {
      field: 'status',
      label: '状态',
      component: 'Select',
      props: {
        placeholder: '请选择状态',
        allowClear: true,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
    // ...其他搜索项
  ],
  
  // 高级搜索项
  advancedItems: [
    {
      title: '基本信息',
      items: [
        // ...高级搜索项
      ],
    },
  ],
  
  // 自定义按钮
  customButtons: [
    {
      key: 'add',
      text: '新增',
      type: 'primary',
      onClick: () => void,
    },
    {
      key: 'export',
      text: '导出',
      onClick: () => void,
    },
  ],
  
  // 默认表单值
  defaultValues: {
    keyword: '',
    status: undefined,
  },
  
  // 表单提交前处理函数
  beforeSearch: (values) => true,
  
  // 表单重置后处理函数
  afterReset: () => void,
}
```

### 3.4 详情配置 (detailOptions)

```typescript
detailOptions: {
  // 详情区块
  sections: [
    {
      title: '基本信息',
      items: [
        { label: '姓名', field: 'name' },
        { label: '年龄', field: 'age' },
        { 
          label: '状态', 
          field: 'status',
          formatter: (value) => value === 1 ? '启用' : '禁用'
        },
      ],
    },
    // ...其他区块
  ],
  
  // 标题
  title: '详情',
  
  // 格式化器
  formatters: {
    date: (value) => dayjs(value).format('YYYY-MM-DD'),
  },
}
```

## 4. 核心 Hooks 详解

### 4.1 useTable

表格数据管理 hook，处理表格数据的加载、分页、搜索和单元格编辑等功能。

```typescript
const {
  loading,           // 加载状态
  tableData,         // 表格数据
  pagination,        // 分页配置
  searchParams,      // 搜索参数
  getList,           // 获取列表数据
  handleSearch,      // 搜索处理
  handleReset,       // 重置处理
  handleTableChange, // 表格变化处理
  handleCellSave,    // 单元格保存处理
  setEditableColumns,// 设置可编辑列
  updateSearchParams,// 更新搜索参数
  handleVirtualScroll,// 虚拟滚动处理
} = useTable(options);
```

### 4.2 useForm

表单弹窗管理 hook，提供完整的表单弹窗解决方案。

```typescript
const {
  visible,           // 弹窗可见状态
  loading,           // 加载状态
  isEdit,            // 是否编辑模式
  formData,          // 表单数据
  formRef,           // 表单引用
  modalTitle,        // 模态框标题
  show,              // 显示弹窗
  close,             // 关闭弹窗
  handleSubmit,      // 提交处理
  resetForm,         // 重置表单
  setFormData,       // 设置表单数据
  renderFormModal,   // 渲染表单模态框
} = useForm(options);
```

### 4.3 useDetail

详情弹窗管理 hook，用于展示数据详情。

```typescript
const {
  visible,           // 弹窗可见状态
  detailData,        // 详情数据
  title,             // 标题
  sections,          // 详情区块
  open,              // 打开详情
  close,             // 关闭详情
  renderDetailModal, // 渲染详情模态框
} = useDetail(options);
```

### 4.4 useCustomDetail

自定义详情 hook，提供更灵活的详情展示方式。

```typescript
const {
  visible,           // 弹窗可见状态
  detailData,        // 详情数据
  open,              // 打开详情
  close,             // 关闭详情
  renderDetailModal, // 渲染详情模态框
} = useCustomDetail();

// 使用示例
const detailComponent = renderDetailModal([
  {
    title: '基本信息',
    items: [
      { label: '姓名', field: 'name' },
      { label: '年龄', field: 'age' },
    ]
  }
]);
```

## 5. 表单字段配置

表单字段支持多种组件类型和配置项：

```typescript
{
  name: 'username',         // 字段名
  label: '用户名',          // 标签
  component: 'Input',       // 组件类型
  rules: [                  // 验证规则
    { required: true, message: '请输入用户名' }
  ],
  props: {                  // 组件属性
    placeholder: '请输入用户名',
    allowClear: true,
  },
  col: {                    // 栅格配置
    span: 12,
    xs: 24,
    sm: 12,
    md: 8,
  },
  labelCol: {               // 标签布局
    span: 6,
  },
  wrapperCol: {             // 内容布局
    span: 18,
  },
  options: [                // 选项（用于Select等）
    { label: '选项1', value: 1 },
    { label: '选项2', value: 2 },
  ],
  show: (formData) => formData.type === 1,  // 条件显示
  disabled: (formData) => formData.locked,  // 条件禁用
  remote: {                 // 远程加载选项
    api: () => api.getOptions(),
    params: { type: 1 },
    transform: (data) => data.map(item => ({
      label: item.name,
      value: item.id,
    })),
  },
  helpText: '帮助信息',     // 帮助文本
  extra: '额外信息',        // 额外信息
  render: (formData, field) => {  // 自定义渲染
    // 返回自定义组件
  },
}
```

## 6. 最佳实践

### 6.1 创建自定义 Hook

推荐创建自定义 Hook 来整合 useAnsheng 的功能，使代码更加模块化和可维护：

```typescript
// useCustomPage.ts
import { computed } from 'vue';
import { useAnsheng } from '#/hooks/useAnsheng';

export function useCustomPage() {
  const {
    tableData,
    loading,
    pagination,
    searchToolbarBind: origSearchToolbarBind,
    tableBind: origTableBind,
    handleTableChange,
    handleSearch,
    handleReset,
    table,
    form,
    detail,
  } = useAnsheng({
    // 配置项...
  });

  // 自定义逻辑...
  
  // 创建表单组件
  const formComponent = form.renderFormModal([
    // 表单配置...
  ]);
  
  // 创建详情组件
  const detailComponent = detail?.renderDetailModal([
    // 详情配置...
  ]);

  return {
    searchToolbarBind: computed(() => origSearchToolbarBind.value),
    tableBind: computed(() => ({
      ...origTableBind.value,
      onChange: handleTableChange,
    })),
    formComponent,
    detailComponent,
    // 其他需要暴露的属性和方法...
  };
}
```

### 6.2 分离配置

将表格列、搜索项等配置抽离到单独的配置文件中，使代码更加清晰：

```typescript
// config.ts
export const columns = [
  { title: '姓名', dataIndex: 'name' },
  // ...其他列
];

export const searchItems = [
  { field: 'keyword', label: '关键词', component: 'Input' },
  // ...其他搜索项
];

// 在自定义Hook中导入
import { columns, searchItems } from './config';
```

### 6.3 处理自定义操作

实现删除、导出等自定义操作：

```typescript
// 处理删除
function handleDelete(row) {
  Modal.confirm({
    title: '确定删除吗？',
    content: `名称：${row.name}`,
    onOk: async () => {
      try {
        const res = await deleteApi(row.id);
        if (res.code === 1) {
          message.success('删除成功');
          table.getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
}

// 处理导出
async function handleExport() {
  try {
    const res = await exportApi();
    await handleFileDownload(res, `导出文件_${dayjs().format('YYYY-MM-DD')}.xlsx`);
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
}
```

## 7. 常见问题与解决方案

### 7.1 表单数据不更新

确保在编辑模式下正确设置表单数据：

```typescript
// 正确方式
form.show(record.id); // 通过ID加载
// 或
form.show(record); // 直接传入记录对象
```

### 7.2 表格数据不刷新

确保在操作成功后调用 `table.getList()`：

```typescript
onSuccess: () => {
  table.getList();
}
```

### 7.3 自定义表单验证

添加自定义验证规则：

```typescript
rules: {
  name: [
    { required: true, message: '请输入名称' },
    { validator: async (rule, value) => {
      if (value && value.length < 3) {
        throw new Error('名称长度不能小于3');
      }
    }}
  ]
}
```

### 7.4 表单字段联动

使用 `show` 和 `disabled` 属性实现字段联动：

```typescript
fields: [
  {
    name: 'type',
    label: '类型',
    component: 'Select',
    options: [
      { label: '类型1', value: 1 },
      { label: '类型2', value: 2 },
    ],
  },
  {
    name: 'subType',
    label: '子类型',
    component: 'Select',
    show: (formData) => formData.type === 1,
    options: [
      { label: '子类型1', value: 1 },
      { label: '子类型2', value: 2 },
    ],
  }
]
```

## 8. 进阶用法

### 8.1 虚拟滚动

处理大量数据时，可以启用虚拟滚动：

```typescript
tableOptions: {
  // ...其他配置
  virtualScroll: true,
  virtualScrollOptions: {
    scrollY: 400,
    itemHeight: 54,
    threshold: 100,
  },
}
```

### 8.2 内联编辑

实现表格内直接编辑：

```typescript
tableOptions: {
  // ...其他配置
  editableColumns: ['name', 'age', 'email'],
  onSave: async (record, column, value) => {
    return await api.updateField(record.id, column, value);
  },
}
```

### 8.3 自定义表单布局

使用 `simpleLayout` 配置自定义表单布局：

```typescript
formOptions: {
  // ...其他配置
  simpleLayout: {
    containerClass: 'custom-form',
    inline: true,
    itemMargin: 16,
    padding: 24,
    showGroupTitle: false,
  },
}
```

### 8.4 远程搜索

实现下拉选择的远程搜索：

```typescript
fields: [
  {
    name: 'userId',
    label: '用户',
    component: 'Select',
    remote: {
      api: (params) => api.searchUsers(params),
      params: { status: 1 },
      transform: (data) => data.map(item => ({
        label: item.name,
        value: item.id,
      })),
    },
  }
]
```

## 9. 总结

useAnsheng 提供了一套完整的 CRUD 解决方案，通过简单的配置即可实现复杂的后台管理功能。合理使用它可以大大提高开发效率，减少重复代码，保持界面和交互的一致性。

关键步骤：

1. 定义配置（表单、表格、搜索、详情）
2. 调用 useAnsheng 获取状态和方法
3. 在模板中使用相应的组件
4. 实现自定义操作和逻辑

希望本文档能帮助您更好地理解和使用 useAnsheng Hook！ 
