/**
 * useForm 桥接文件
 * 保持原有API不变，但内部实现重定向到模块化结构
 */
import type { UseFormOptions } from './types';

// 导入模块化实现
import { createForm } from './form/create';

/**
 * 表单弹窗管理Hook
 * 提供完整的表单弹窗解决方案，包括表单状态管理、模态框交互、选项加载等功能
 *
 * 注意: 此函数的实现已重构为更模块化的结构，但API保持不变以确保兼容性
 */
export function useForm<T extends Record<string, any>>(
  options: UseFormOptions<T>,
) {
  // 调用重构后的模块化实现
  return createForm<T>(options);
}

// 重新导出原始类型，确保类型兼容性
export * from './types';
