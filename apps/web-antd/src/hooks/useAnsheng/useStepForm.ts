import type { FormInstance } from 'ant-design-vue';

import type { DynamicFormGroup } from './useDynamicForm';

import { computed, h, reactive, ref, watch } from 'vue';

import { Button, Card, Form, message, Space, Steps } from 'ant-design-vue';

const Step = Steps.Step;
const FormItem = Form.Item;

// 步骤配置
export interface FormStep {
  title: string;
  description?: string;
  icon?: any;
  status?: 'error' | 'finish' | 'process' | 'wait';
  subTitle?: string;
  fields: DynamicFormGroup[] | string[]; // 字段列表或表单分组
  validator?: (formData: any) => boolean | Promise<boolean>; // 步骤验证函数
}

// 步骤表单配置
export interface StepFormOptions {
  steps: FormStep[];
  defaultValues?: Record<string, any>;
  onFinish?: (values: any) => Promise<void> | void;
  onCancel?: () => void;
  formItemLayout?: {
    labelCol?: { span: number };
    wrapperCol?: { span: number };
  };
  stepsDirection?: 'horizontal' | 'vertical';
  submitText?: string;
  cancelText?: string;
}

/**
 * 分步骤表单Hook
 * 支持多步骤表单向导，包括步骤导航、验证、提交等功能
 */
export function useStepForm(options: StepFormOptions) {
  // 表单数据和状态
  const formRef = ref<FormInstance>();
  const formData = reactive<Record<string, any>>(options.defaultValues || {});
  const currentStep = ref(0);
  const submitting = ref(false);
  const stepsStatus = ref<('error' | 'finish' | 'process' | 'wait')[]>(
    options.steps.map((_, index) => (index === 0 ? 'process' : 'wait')),
  );

  // 计算属性
  const isFirstStep = computed(() => currentStep.value === 0);
  const isLastStep = computed(
    () => currentStep.value === options.steps.length - 1,
  );
  const currentStepConfig = computed(() => options.steps[currentStep.value]);

  // 当前步骤字段
  const currentFields = computed(() => {
    const step = options.steps[currentStep.value];
    return step.fields;
  });

  // 监听步骤变化，更新步骤状态
  watch(currentStep, (newStep) => {
    stepsStatus.value = options.steps.map((_, index) => {
      if (index < newStep) return 'finish';
      if (index === newStep) return 'process';
      return 'wait';
    });
  });

  // 下一步
  const nextStep = async () => {
    try {
      // 验证当前步骤
      await formRef.value?.validate();

      // 自定义验证
      const validator = currentStepConfig.value.validator;
      if (validator) {
        const isValid = await validator(formData);
        if (!isValid) {
          stepsStatus.value[currentStep.value] = 'error';
          return;
        }
      }

      // 更新当前步骤状态
      stepsStatus.value[currentStep.value] = 'finish';

      // 如果是最后一步，提交表单
      if (isLastStep.value) {
        await handleFinish();
        return;
      }

      // 前进到下一步
      currentStep.value++;
      stepsStatus.value[currentStep.value] = 'process';
    } catch (error) {
      console.error('表单验证失败:', error);
      stepsStatus.value[currentStep.value] = 'error';
    }
  };

  // 上一步
  const prevStep = () => {
    if (currentStep.value > 0) {
      stepsStatus.value[currentStep.value] = 'wait';
      currentStep.value--;
      stepsStatus.value[currentStep.value] = 'process';
    }
  };

  // 跳转到指定步骤
  const goToStep = (stepIndex: number) => {
    // 只允许跳转到已完成的步骤
    if (stepIndex < currentStep.value) {
      currentStep.value = stepIndex;
      stepsStatus.value = options.steps.map((_, index) => {
        if (index < stepIndex) return 'finish';
        if (index === stepIndex) return 'process';
        return 'wait';
      });
    }
  };

  // 完成表单
  const handleFinish = async () => {
    try {
      submitting.value = true;

      // 调用完成回调
      if (options.onFinish) {
        await options.onFinish(formData);
      }

      message.success('提交成功');
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error('提交失败');
      stepsStatus.value[currentStep.value] = 'error';
    } finally {
      submitting.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    options.onCancel?.();
  };

  // 重置表单
  const resetForm = () => {
    // 重置表单数据
    formRef.value?.resetFields();
    Object.keys(formData).forEach((key) => {
      delete formData[key];
    });

    if (options.defaultValues) {
      Object.assign(formData, options.defaultValues);
    }

    // 重置步骤状态
    currentStep.value = 0;
    stepsStatus.value = options.steps.map((_, index) =>
      index === 0 ? 'process' : 'wait',
    );
  };

  // 渲染步骤导航
  const renderSteps = () => {
    return h(
      Steps,
      {
        current: currentStep.value,
        direction: options.stepsDirection || 'horizontal',
        class: 'mb-6',
      },
      {
        default: () =>
          options.steps.map((step, index) =>
            h(Step, {
              title: step.title,
              description: step.description,
              status: stepsStatus.value[index],
              icon: step.icon,
              subTitle: step.subTitle,
              onClick: () => goToStep(index),
            }),
          ),
      },
    );
  };

  // 渲染表单
  const renderStepForm = (renderStepContent: (step: FormStep) => any) => {
    return h(
      Card,
      {
        class: 'step-form-card',
        bodyStyle: { padding: '24px' },
      },
      {
        default: () => [
          // 步骤导航
          renderSteps(),

          // 表单主体
          h(
            Form,
            {
              ref: formRef,
              model: formData,
              labelCol: options.formItemLayout?.labelCol || { span: 6 },
              wrapperCol: options.formItemLayout?.wrapperCol || { span: 16 },
            },
            {
              default: () => [
                // 当前步骤内容
                renderStepContent(currentStepConfig.value),

                // 步骤操作按钮
                h('div', { class: 'step-form-actions mt-6 flex justify-end' }, [
                  h(Space, null, {
                    default: () => [
                      // 取消按钮
                      h(
                        Button,
                        {
                          onClick: handleCancel,
                        },
                        () => options.cancelText || '取消',
                      ),

                      // 上一步按钮
                      !isFirstStep.value &&
                        h(
                          Button,
                          {
                            onClick: prevStep,
                          },
                          () => '上一步',
                        ),

                      // 下一步/提交按钮
                      h(
                        Button,
                        {
                          type: 'primary',
                          loading: submitting.value,
                          onClick: nextStep,
                        },
                        () =>
                          isLastStep.value
                            ? options.submitText || '提交'
                            : '下一步',
                      ),
                    ],
                  }),
                ]),
              ],
            },
          ),
        ],
      },
    );
  };

  return {
    formRef,
    formData,
    currentStep,
    stepsStatus,
    submitting,
    isFirstStep,
    isLastStep,
    currentStepConfig,
    currentFields,

    // 方法
    nextStep,
    prevStep,
    goToStep,
    handleFinish,
    handleCancel,
    resetForm,

    // 渲染方法
    renderSteps,
    renderStepForm,

    // 工具方法
    setFormData: (data: Record<string, any>) => {
      Object.assign(formData, data);
    },

    // 表单验证
    validateStep: async (stepIndex: number) => {
      try {
        await formRef.value?.validate(
          Array.isArray(options.steps[stepIndex].fields) &&
            typeof options.steps[stepIndex].fields[0] === 'string'
            ? (options.steps[stepIndex].fields as string[])
            : undefined,
        );
        return true;
      } catch {
        return false;
      }
    },

    validateForm: async () => {
      try {
        await formRef.value?.validate();
        return true;
      } catch {
        return false;
      }
    },
  };
}
