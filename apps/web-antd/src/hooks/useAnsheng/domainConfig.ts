/**
 * 域名权限配置文件
 * 提供全局域名配置和域名匹配检查功能
 */

// 默认域名配置
export const defaultDomains = [
  // 生产环境域名
  'ml.liangaiyun.com',
  '*.your-domain.com',
  // 测试环境域名
  'test.your-domain.com',
  'test-admin.your-domain.com',
  // 开发环境域名
  'localhost',
  '127.0.0.1',
  // 添加更多允许的域名
];

// 全局配置
export const domainConfig = {
  // 默认允许的域名列表
  allowedDomains: [...defaultDomains],
  // 是否在开发环境下禁用域名检查
  disableInDev: false,
};

/**
 * 更新域名配置
 * @param options 配置选项
 */
export function configureDomains(options: {
  allowedDomains?: string[];
  disableInDev?: boolean;
}) {
  if (options.allowedDomains) {
    domainConfig.allowedDomains = options.allowedDomains;
  }

  if (options.disableInDev !== undefined) {
    domainConfig.disableInDev = options.disableInDev;
  }
}

/**
 * 检查是否为开发环境
 */
export function isDevelopment(): boolean {
  return (
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname.includes('.local')
  );
}

/**
 * 检查域名是否匹配
 * @param domain 要检查的域名
 * @param currentDomain 当前域名
 * @returns 是否匹配
 */
export function isDomainMatch(domain: string, currentDomain: string): boolean {
  // 支持通配符匹配，例如 *.example.com
  if (domain.startsWith('*.')) {
    const suffix = domain.slice(1); // 从 '*.example.com' 得到 '.example.com'
    return currentDomain.endsWith(suffix);
  }
  return currentDomain === domain;
}

/**
 * 检查域名权限
 * @param config 域名权限配置
 * @param userAuthority 用户权限等级
 * @returns 是否有权限
 */
export function checkDomainPermission(
  config?: number | { authority?: number; domains?: string[] },
  userAuthority?: number,
): boolean {
  // 未设置权限，所有域名可见
  if (config === undefined) return true;

  // 获取当前域名
  const currentDomain = window.location.hostname;

  // 获取需要检查的域名列表
  const domainsToCheck =
    typeof config === 'number'
      ? domainConfig.allowedDomains
      : config.domains || domainConfig.allowedDomains;

  // 检查域名是否匹配
  const isDev = isDevelopment();
  const domainMatches =
    isDev && domainConfig.disableInDev
      ? true
      : domainsToCheck.some((domain) => isDomainMatch(domain, currentDomain));

  // 域名不匹配，返回true，使配置项可见，不进行后续权限检查
  if (!domainMatches) {
    return true;
  }

  // 域名匹配后，才进行权限检查

  // 处理数字形式的权限值
  if (typeof config === 'number') {
    const requiredAuthority = config;

    // 如果用户是管理员或不需要管理员权限，允许访问
    return userAuthority === 1 || requiredAuthority !== 1;
  }

  // 处理对象形式的权限值
  const requiredAuthority = config.authority || 0;

  // 如果用户是管理员或不需要管理员权限，允许访问
  return userAuthority === 1 || requiredAuthority !== 1;
}
