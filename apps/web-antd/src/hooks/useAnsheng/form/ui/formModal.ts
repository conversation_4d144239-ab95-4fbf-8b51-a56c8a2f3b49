// form/ui/formModal.ts
// 表单模态框管理

import type { FormModalOptions } from '../core/types';

import { computed, h, ref } from 'vue';

import { MdiFullscreen, MdiFullscreenExit } from '@vben/icons';

import { Mo<PERSON>, Spin } from 'ant-design-vue';

import { FULLSCREEN_CLASS, FULLSCREEN_WRAP_CLASS } from '../../types';

/**
 * 表单模态框Hook
 * 处理模态框的显示、关闭和交互
 */
export function useFormModal(
  options: FormModalOptions = {},
  deps: {
    formLayout: {
      isDragging: any;
      isFullscreen: any;
      toggleFullscreen: () => void;
    };
    formState: {
      clearFormData: () => void;
      formData: any;
      formRef: any;
      resetForm: () => void;
    };
    isEdit: any;
    loading: any;
  },
) {
  // 弹窗可见性
  const visible = ref(false);

  // 提交加载状态
  const confirmLoading = ref(false);

  // 计算标题
  const modalTitle = computed(() =>
    typeof options.title === 'function'
      ? options.title(deps.isEdit.value)
      : options.title || (deps.isEdit.value ? '编辑' : '新增'),
  );

  // 关闭弹窗
  const close = () => {
    visible.value = false;
    deps.isEdit.value = false;
    // 重置表单数据
    deps.formState.resetForm();
    // 清除验证
    deps.formState.formRef.value?.clearValidate();
    // 清除原始数据记录
    deps.formState.clearFormData();
    options.onCancel?.();
  };

  // 渲染全屏按钮
  const renderFullscreenButton = () => {
    return h(
      'div',
      {
        class: 'modal-fullscreen-btn cursor-pointer ml-4 flex items-center',
        onClick: (e: Event) => {
          e.stopPropagation(); // 阻止事件冒泡
          deps.formLayout.toggleFullscreen();
        },
        style: 'font-size: 16px;',
      },
      [
        deps.formLayout.isFullscreen.value
          ? h(MdiFullscreenExit)
          : h(MdiFullscreen),
      ],
    );
  };

  // 渲染模态框
  const renderModal = (
    content: any,
    width?: number,
    customConfig?: Record<string, any>,
  ) => {
    // Modal组件配置
    const modalProps = {
      visible: visible.value,
      'onUpdate:visible': (val: boolean) => {
        if (!val) close();
      },
      title: h(
        'div',
        {
          class: 'text-base font-medium flex items-center modal-title',
          style: options.draggable ? 'cursor: move;' : '',
        },
        [h('span', {}, modalTitle.value), renderFullscreenButton()],
      ),
      width: deps.formLayout.isFullscreen.value ? '100%' : width || 900,
      confirmLoading: confirmLoading.value,
      maskClosable: false,
      destroyOnClose: true,
      centered: !deps.formLayout.isFullscreen.value && !options.draggable,
      class: `common-form-modal ${deps.formLayout.isFullscreen.value ? FULLSCREEN_CLASS : ''} ${deps.formLayout.isDragging.value ? 'modal-dragging no-transition' : ''}`,
      bodyStyle: {
        padding: '12px 0',
        ...(deps.formLayout.isFullscreen.value
          ? {
              height: 'calc(100vh - 160px)',
              overflow: 'hidden',
            }
          : {}),
      },
      style: deps.formLayout.isFullscreen.value
        ? {
            top: '0',
            paddingBottom: '0',
            height: '100vh',
            maxHeight: '100vh',
            overflow: 'hidden',
            margin: '0',
          }
        : {},
      wrapClassName: `channel-form-modal-wrap ${deps.formLayout.isFullscreen.value ? FULLSCREEN_WRAP_CLASS : ''}`,
      // 合并自定义配置
      ...customConfig,
    };

    return h(Modal, modalProps, {
      default: () =>
        h(
          Spin,
          {
            spinning: deps.isEdit.value && deps.loading.value,
            tip: '加载中...',
            class: 'w-full h-full',
          },
          {
            default: () =>
              h(
                'div',
                {
                  class: `channel-form-container ${
                    deps.formLayout.isFullscreen.value
                      ? 'h-full overflow-y-auto'
                      : 'max-h-[calc(100vh-200px)] overflow-y-auto'
                  }`,
                  style: deps.formLayout.isFullscreen.value
                    ? 'height: calc(100vh - 160px); max-height: calc(100vh - 160px);'
                    : '',
                },
                content,
              ),
          },
        ),
    });
  };

  return {
    visible,
    modalTitle,
    close,
    confirmLoading,
    renderFullscreenButton,
    renderModal,
  };
}
