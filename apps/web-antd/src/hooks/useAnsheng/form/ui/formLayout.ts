// form/ui/formLayout.ts
// 表单布局和全屏功能

import type { FormLayoutOptions } from '../core/types';

import { onMounted, ref } from 'vue';

import { FULLSCREEN_CLASS, FULLSCREEN_WRAP_CLASS } from '../../types';

/**
 * 表单布局Hook
 * 管理表单布局、全屏和拖拽功能
 */
export function useFormLayout(options: FormLayoutOptions = {}) {
  // 全屏状态
  const isFullscreen = ref(false);
  // 拖动状态
  const isDragging = ref(false);

  // 模态框引用
  let modalElement: HTMLElement | null = null;
  let modalHeaderElement: HTMLElement | null = null;

  // 模态框位置
  const modalPosition = ref({ x: 0, y: 0 });

  // 切换全屏状态
  const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
    // 重置位置
    if (isFullscreen.value) {
      resetPosition();
    }
    // 应用全屏样式
    applyFullscreenStyles(isFullscreen.value);
  };

  // 应用全屏样式
  const applyFullscreenStyles = (isFullscreen: boolean) => {
    if (!modalElement) return;

    const wrapperElement = modalElement.closest(
      '.ant-modal-wrap',
    ) as HTMLElement;
    if (!wrapperElement) return;

    if (isFullscreen) {
      // 添加全屏样式类
      modalElement.classList.add('modal-fullscreen');
      wrapperElement.classList.add(FULLSCREEN_WRAP_CLASS);

      // 设置全屏样式
      modalElement.style.width = '100%';
      modalElement.style.height = '100vh';
      modalElement.style.maxHeight = '100vh';
      modalElement.style.top = '0';
      modalElement.style.padding = '0';
      modalElement.style.margin = '0';
      modalElement.style.transform = 'none';

      // 内容容器样式
      const bodyElement = modalElement.querySelector('.ant-modal-body');
      if (bodyElement) {
        (bodyElement as HTMLElement).style.height = 'calc(100vh - 160px)';
        (bodyElement as HTMLElement).style.overflow = 'hidden';
      }

      // 重置位置
      resetPosition();
    } else {
      // 移除全屏样式类
      modalElement.classList.remove('modal-fullscreen');
      wrapperElement.classList.remove(FULLSCREEN_WRAP_CLASS);

      // 恢复普通样式
      modalElement.style.width = '';
      modalElement.style.height = '';
      modalElement.style.maxHeight = '';
      modalElement.style.top = '';
      modalElement.style.padding = '';
      modalElement.style.margin = '';
      modalElement.style.transform = '';

      // 内容容器样式
      const bodyElement = modalElement.querySelector('.ant-modal-body');
      if (bodyElement) {
        (bodyElement as HTMLElement).style.height = '';
        (bodyElement as HTMLElement).style.overflow = '';
      }
    }
  };

  // 重置模态框位置
  const resetPosition = () => {
    modalPosition.value = { x: 0, y: 0 };
    if (modalElement) {
      modalElement.style.transform = '';
    }
  };

  // 处理鼠标按下事件
  const handleMouseDown = (e: MouseEvent) => {
    // 如果是全屏状态，不允许拖拽
    if (isFullscreen.value) return;

    // 排除全屏按钮区域
    if (
      e.target &&
      ((e.target as HTMLElement).classList.contains('modal-fullscreen-btn') ||
        (e.target as HTMLElement).closest('.modal-fullscreen-btn'))
    ) {
      return;
    }

    isDragging.value = true;
    const startX = e.clientX;
    const startY = e.clientY;

    // 获取当前位置
    const transformValue = modalElement!.style.transform;
    const translateMatch = transformValue.match(
      /translate\(([^,]+)px,\s*([^)]+)px\)/,
    );

    const startLeft = translateMatch ? Number.parseFloat(translateMatch[1]) : 0;
    const startTop = translateMatch ? Number.parseFloat(translateMatch[2]) : 0;

    // 添加拖拽样式
    modalElement!.classList.add('modal-dragging', 'no-transition');

    // 添加事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // 处理鼠标移动
    function handleMouseMove(e: MouseEvent) {
      if (!isDragging.value) return;

      // 计算新位置
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      // 更新位置
      const newLeft = startLeft + deltaX;
      const newTop = startTop + deltaY;
      modalPosition.value = { x: newLeft, y: newTop };
      modalElement!.style.transform = `translate(${newLeft}px, ${newTop}px)`;
    }

    // 处理鼠标释放
    function handleMouseUp() {
      isDragging.value = false;

      // 移除拖拽样式
      if (modalElement) {
        modalElement.classList.remove('modal-dragging', 'no-transition');
      }

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    }
  };

  // 初始化模态框
  const initModal = (visible: boolean) => {
    if (visible) {
      setTimeout(() => {
        // 找到最新打开的modal元素（通常是z-index最高的）
        const modals = document.querySelectorAll('.ant-modal');
        modalElement = modals[modals.length - 1] as HTMLElement; // 获取最后一个，通常是最新打开的
        if (!modalElement) return;

        // 如果不可拖动，直接返回
        if (!options.draggable) return;

        // 获取模态框头部
        modalHeaderElement = modalElement.querySelector('.ant-modal-header');
        if (!modalHeaderElement) return;

        // 设置鼠标样式
        modalHeaderElement.style.cursor = 'move';

        // 添加鼠标按下事件
        modalHeaderElement.addEventListener('mousedown', handleMouseDown);

        // 如果当前是全屏状态，应用全屏样式
        if (isFullscreen.value) {
          applyFullscreenStyles(true);
        }
      }, 100);
    } else {
      // 清理事件监听
      if (modalHeaderElement) {
        modalHeaderElement.removeEventListener('mousedown', handleMouseDown);
        modalHeaderElement.style.cursor = '';
      }
      modalElement = null;
      modalHeaderElement = null;
    }
  };

  // 获取表单布局配置
  const getFormLayoutConfig = () => {
    // 是否使用简洁布局
    const useSimpleLayout = Boolean(options.simpleLayout);

    // 解析简洁布局选项
    const simpleLayoutOptions =
      typeof options.simpleLayout === 'object' ? options.simpleLayout : {};

    // 内边距和间距
    const padding =
      simpleLayoutOptions.padding === undefined
        ? (useSimpleLayout
          ? 12
          : 24)
        : simpleLayoutOptions.padding;

    const itemMargin =
      simpleLayoutOptions.itemMargin === undefined
        ? (useSimpleLayout
          ? 16
          : 24)
        : simpleLayoutOptions.itemMargin;

    // 是否显示分组标题 (默认在简洁模式下不显示)
    const showGroupTitle = simpleLayoutOptions.showGroupTitle !== false;

    // 自定义容器类名
    const containerClass = simpleLayoutOptions.containerClass || '';

    return {
      useSimpleLayout,
      simpleLayoutOptions,
      padding,
      itemMargin,
      showGroupTitle,
      containerClass,
      formLayout: useSimpleLayout
        ? {
            // 根据simpleLayout.inline配置决定布局方式
            layout:
              typeof options.simpleLayout === 'object' &&
              options.simpleLayout.inline === true
                ? 'horizontal'
                : 'vertical',
            // 水平布局时的列宽配置
            labelCol:
              typeof options.simpleLayout === 'object' &&
              options.simpleLayout.inline === true
                ? { span: 6 }
                : undefined,
            wrapperCol:
              typeof options.simpleLayout === 'object' &&
              options.simpleLayout.inline === true
                ? { span: 18 }
                : undefined,
          }
        : {
            layout: 'horizontal',
            labelCol: { span: 6 },
            wrapperCol: { span: 18 },
          },
    };
  };

  // 在组件挂载时添加全屏样式
  onMounted(() => {
    // 如果没有已经添加的样式，则添加
    if (!document.querySelector('#form-modal-fullscreen-styles')) {
      const styleElement = document.createElement('style');
      styleElement.id = 'form-modal-fullscreen-styles';
      styleElement.textContent = `
        .${FULLSCREEN_CLASS}, .modal-fullscreen {
          width: 100% !important;
          height: 100vh !important;
          top: 0 !important;
          padding: 0 !important;
          margin: 0 !important;
          max-width: 100% !important;
          transform: none !important;
        }

        .${FULLSCREEN_WRAP_CLASS} {
          overflow: hidden !important;
          display: flex !important;
          justify-content: center !important;
          align-items: flex-start !important;
        }

        .${FULLSCREEN_WRAP_CLASS} .ant-modal {
          padding: 0 !important;
          margin: 0 !important;
        }

        .${FULLSCREEN_CLASS} .ant-modal-content,
        .modal-fullscreen .ant-modal-content {
          height: 100vh !important;
          border-radius: 0 !important;
        }

        .${FULLSCREEN_CLASS} .ant-modal-body,
        .modal-fullscreen .ant-modal-body {
          height: calc(100vh - 160px) !important;
          overflow: hidden !important;
        }
      `;
      document.head.append(styleElement);
    }
  });

  return {
    isFullscreen,
    isDragging,
    modalPosition,
    toggleFullscreen,
    resetPosition,
    initModal,
    getFormLayoutConfig,
  };
}
