// form/ui/formRender.ts
// 表单渲染模块

import type { VNode } from 'vue';

import type { FormFieldConfig, FormGroup } from '../../types';

import { h, isVNode, ref } from 'vue';

import {
  Checkbox,
  Col,
  DatePicker,
  Form,
  Image,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
  Switch,
  TimePicker,
  Upload,
} from 'ant-design-vue';

import UploadBox from '../../components/UploadBox.vue';

// 组件映射
const componentMap: Record<string, any> = {
  Input,
  InputNumber,
  Select,
  DatePicker,
  TimePicker,
  Switch,
  Radio,
  RadioGroup: Radio.Group,
  Checkbox,
  CheckboxGroup: Checkbox.Group,
  TextArea: Input.TextArea,
  Upload,
  UploadBox,
  Image,
};

/**
 * 处理输入字符串的空格
 * @param value 输入值
 * @param trimType 处理类型 - both：去除首尾空格，all：去除所有空格，none：不处理
 * @param isTextArea 是否为TextArea组件
 * @returns 处理后的字符串
 */
function handleInputTrim(
  value: string,
  trimType: 'all' | 'both' | 'none' = 'both',
  isTextArea = false,
): string {
  if (typeof value !== 'string') return value;

  // TextArea 需要特殊处理以保留换行符
  if (isTextArea) {
    switch (trimType) {
      case 'all': {
        // 对每一行分别处理所有空格，但保留换行
        return value
          .split('\n')
          .map((line) => line.replaceAll(/\s/g, ''))
          .join('\n');
      }
      case 'both': {
        // 对每一行分别处理首尾空格
        return value
          .split('\n')
          .map((line) => line.trim())
          .join('\n');
      }
      default: {
        // 'none'及其他情况
        return value;
      }
    }
  }

  // 普通Input处理
  switch (trimType) {
    case 'all': {
      return value.replaceAll(/\s/g, '');
    }
    case 'both': {
      return value.trim();
    }
    default: {
      // 'none'及其他情况
      return value;
    }
  }
}

/**
 * 表单渲染Hook
 * 负责表单UI的渲染
 */
export function useFormRender() {
  // 存储远程选项数据的映射
  const remoteFieldOptionsMap = ref<Record<string, any[]>>({});

  // 加载远程字段选项
  const loadRemoteFieldOptions = async (field: FormFieldConfig) => {
    if (!field.name || !field.remote || !field.remote.api) return;

    try {
      // 设置加载状态
      field.remote._loading = true;
      const response = await field.remote.api(field.remote.params);

      if (field.remote.transform) {
        const options = field.remote.transform(response);
        if (Array.isArray(options) && options.length > 0) {
          // 使用ref更新选项映射，确保UI更新
          remoteFieldOptionsMap.value[field.name] = options;
        }
      }
    } catch (error) {
      console.error(`加载${field.label}选项数据失败:`, error);
    } finally {
      // 清除加载状态
      if (field.remote) {
        field.remote._loading = false;
      }
    }
  };

  // 渲染声明式字段
  const renderFields = (
    fields: FormFieldConfig[] = [],
    formData: any,
    formRules: any,
    selectOptions?: Record<string, any[]>,
  ) => {
    if (!fields || fields.length === 0) return [];

    // 预加载所有远程字段选项
    fields.forEach((field) => {
      if (
        field.remote &&
        field.component === 'Select' &&
        field.name && // 如果还没有加载过此字段的选项，加载它
        !remoteFieldOptionsMap.value[field.name]
      ) {
        loadRemoteFieldOptions(field);
      }
    });

    return fields
      .map((field) => {
        // 合并全局规则和字段级别规则
        const fieldRules = field.rules || [];
        const globalRules =
          (formRules && field.name && formRules[field.name]) || [];
        const mergedRules = [...fieldRules, ...globalRules];

        // 处理条件显示
        const shouldShow =
          typeof field.show === 'function'
            ? field.show(formData)
            : field.show !== false;

        if (!shouldShow) return null;

        // 处理禁用状态
        const isDisabled =
          typeof field.disabled === 'function'
            ? field.disabled(formData)
            : field.disabled === true;

        // 获取对应的组件
        const Component = componentMap[field.component];
        if (!Component) {
          console.warn(`Component ${field.component} not found`);
          return null;
        }

        // 构建Col配置
        const colProps = field.col || { span: 24 };

        // 构建组件props
        const componentProps: Record<string, any> = {
          ...field.props,
          disabled: isDisabled,
        };

        // 只有当name存在时才设置value绑定
        if (field.name && typeof field.name === 'string') {
          // 特殊处理 Switch 组件，使用 checked 而不是 value
          if (field.component === 'Switch') {
            componentProps.checked = formData[field.name];
            componentProps['onUpdate:checked'] = (val: any) => {
              if (field.name && typeof field.name === 'string') {
                formData[field.name] = val;
              }
            };
          } else {
            componentProps.value = formData[field.name];

            // 根据组件类型设置不同的更新处理
            if (field.component === 'Input' || field.component === 'TextArea') {
              // 输入框处理空格
              const trimType = field.props?.trim || 'both';
              componentProps['onUpdate:value'] = (val: any) => {
                // 处理输入值中的空格问题
                const processedValue = handleInputTrim(
                  val,
                  trimType,
                  field.component === 'TextArea',
                );
                if (field.name && typeof field.name === 'string') {
                  formData[field.name] = processedValue;
                }
              };

              // 添加blur事件，在失去焦点时处理空格
              const originalBlur = componentProps.onBlur;
              componentProps.onBlur = (e: any) => {
                if (
                  field.name &&
                  typeof field.name === 'string' &&
                  typeof formData[field.name] === 'string'
                ) {
                  // 确保失去焦点时也应用空格处理
                  formData[field.name] = handleInputTrim(
                    formData[field.name],
                    trimType,
                    field.component === 'TextArea',
                  );
                }

                // 调用原始onBlur
                if (originalBlur) originalBlur(e);
              };
            } else {
              // 其他组件使用默认更新
              componentProps['onUpdate:value'] = (val: any) => {
                if (field.name && typeof field.name === 'string') {
                  formData[field.name] = val;
                }
              };
            }
          }
        }

        // 特殊处理UploadBox组件
        if (field.component === 'UploadBox' && field.name) {
          // 重写UploadBox的事件处理，确保与表单集成
          componentProps.onChange = (info: any) => {
            // 如果有原始onChange处理函数，先调用它
            if (field.props?.onChange) {
              field.props.onChange(info);
            }

            // 如果上传了文件，则更新表单值
            if (info.fileList && info.fileList.length > 0) {
              const file = info.file.originFileObj || info.file;
              formData[field.name as string] = file;
            } else {
              // 如果移除了文件，则清空表单值
              formData[field.name as string] = undefined;
            }
          };

          // 如果定义了onUpload，需要确保它能正确更新表单值
          if (field.props?.onUpload) {
            const originalOnUpload = field.props.onUpload;
            componentProps.onUpload = (file: File) => {
              // 直接更新表单值
              formData[field.name as string] = file;

              // 返回原始onUpload的结果
              return originalOnUpload(file);
            };
          }

          // 如果定义了onRemove，需要确保它能正确清空表单值
          if (field.props?.onRemove) {
            const originalOnRemove = field.props.onRemove;
            componentProps.onRemove = (file: any) => {
              // 清空表单值
              formData[field.name as string] = undefined;

              // 调用原始onRemove
              if (originalOnRemove) {
                return originalOnRemove(file);
              }
            };
          }
        }

        // 处理远程数据加载
        if (field.remote && field.component === 'Select' && field.name) {
          // 使用ref读取，确保响应式更新
          componentProps.options =
            remoteFieldOptionsMap.value[field.name] || [];

          // 添加这一行：设置loading状态
          componentProps.loading = field.remote._loading === true;

          // 如果没有选项且没有开始加载，立即加载
          if (
            componentProps.options.length === 0 &&
            field.remote &&
            !field.remote._loading &&
            field.remote
          ) {
            field.remote._loading = true;
            loadRemoteFieldOptions(field).finally(() => {
              if (field.remote) {
                field.remote._loading = false;
              }
            });
          }
        }
        // 特殊处理Select组件的options
        else if (field.component === 'Select' && field.options) {
          componentProps.options = field.options;
        } else if (
          field.component === 'Select' &&
          selectOptions &&
          field.name &&
          selectOptions[field.name]
        ) {
          componentProps.options = selectOptions[field.name];
        }

        // 处理帮助文本
        const helpText =
          typeof field.helpText === 'function'
            ? field.helpText(formData)
            : field.helpText;

        // 处理额外信息
        const extra =
          typeof field.extra === 'function'
            ? field.extra(formData)
            : field.extra;

        // 创建Form.Item属性
        const formItemProps: Record<string, any> = {
          label: field.label,
        };

        // 只有当name存在时才添加到formItem
        if (field.name) {
          formItemProps.name = field.name;
          formItemProps.rules = mergedRules;
        }

        if (helpText) {
          formItemProps.help = helpText;
        }

        if (extra) {
          formItemProps.extra = extra;
        }

        // 添加对 labelCol 和 wrapperCol 的支持
        if (field.labelCol) {
          formItemProps.labelCol = field.labelCol;
        }

        if (field.wrapperCol) {
          formItemProps.wrapperCol = field.wrapperCol;
        }

        // 使用Form.Item包装
        return h(Col, colProps, [
          h(Form.Item, formItemProps, () => {
            // 如果有自定义render函数，使用它
            if (field.render) {
              return field.render(formData, field);
            }
            // 否则使用默认组件渲染
            return h(Component, componentProps);
          }),
        ]);
      })
      .filter(Boolean); // 过滤掉null
  };

  // 渲染表单组
  const renderFormGroups = (
    groups: FormGroup[],
    formData: any,
    formRules: any,
    selectOptions: any,
    layoutConfig: any,
  ) => {
    if (!groups || groups.length === 0) return null;

    // 渲染表单组
    return h(
      'div',
      {
        class: `form-groups ${layoutConfig.useSimpleLayout ? 'simple-form-content' : ''}`,
      },
      groups
        .map((group) => {
          // 检查组可见性
          const isVisible = !group.visible || group.visible(formData);
          if (!isVisible) return null;

          // 组标题
          const titleNode =
            !layoutConfig.useSimpleLayout ||
            (typeof layoutConfig.useSimpleLayout === 'object' &&
              layoutConfig.useSimpleLayout.showGroupTitle !== false)
              ? h(
                  'div',
                  {
                    // class: 'form-group-title mb-2 text-base font-medium',
                    class: 'text-base font-medium mb-5 pb-2.5 border-gray-100',
                  },
                  group.title,
                )
              : null;

          // 组内容
          let contentNodes;

          // 增强版渲染逻辑，支持混合模式
          if (group.fields && group.fields.length > 0) {
            // 检查是否有直接的VNode节点
            const directNodes = group.fields.filter((field) =>
              isVNode(field),
            ) as VNode[];
            const configFields = group.fields.filter(
              (field) => !isVNode(field),
            ) as FormFieldConfig[];

            // 如果有直接的VNode节点，使用混合渲染模式
            if (directNodes.length > 0) {
              // 所有节点（包括配置字段和直接VNode）都在同一个Row内
              const rowChildren: any[] = [];

              // 1. 添加配置字段生成的节点
              if (configFields.length > 0) {
                rowChildren.push(
                  ...renderFields(
                    configFields,
                    formData,
                    formRules,
                    selectOptions,
                  ),
                );
              }

              // 2. 添加直接的VNode节点（它们已经是Col包装过的）
              rowChildren.push(...directNodes);

              // 将所有内容包装在一个Row中，保持栅格布局
              contentNodes = h(
                Row,
                {
                  gutter: 16,
                  class: 'form-group-content',
                },
                rowChildren.filter(Boolean),
              );
            } else {
              // 常规声明式配置渲染
              contentNodes = h(
                Row,
                {
                  gutter: 16,
                  class: 'form-group-content',
                },
                renderFields(configFields, formData, formRules, selectOptions),
              );
            }
          } else if (group.content) {
            // 使用render函数
            const contentResult = group.content(formData, selectOptions);

            // 判断内容是否已经包含Row组件
            const hasRow =
              Array.isArray(contentResult) &&
              contentResult.some(
                (node) =>
                  node &&
                  node.type &&
                  (node.type === Row ||
                    (typeof node.type === 'object' &&
                      node.type.name === 'Row')),
              );

            // 使用三元运算符代替if语句
            contentNodes = h(
              'div',
              { class: 'form-group-content' },
              hasRow ? contentResult : h(Row, { gutter: 16 }, contentResult),
            );
          }

          // 完整的组
          return h(
            'div',
            {
              // class: 'form-group mb-4',
              class: 'px-6 mb-8',
            },
            [titleNode, contentNodes],
          );
        })
        .filter(Boolean),
    );
  };

  return {
    renderFields,
    renderFormGroups,
    remoteFieldOptionsMap: remoteFieldOptionsMap.value,
    loadRemoteFieldOptions,
  };
}
