# 表单模块重构文档

## 重构目标

1. 将原有的 `useForm.ts` 文件拆分为多个模块化组件
2. 保持原有API不变，确保向后兼容
3. 提高代码可维护性和可扩展性
4. 减少单文件代码量，便于理解和维护

## 模块结构

```
form/
├── core/               # 核心功能模块
│   ├── types.ts        # 类型定义
│   ├── formState.ts    # 表单状态管理
│   ├── formSubmit.ts   # 表单提交逻辑
│   └── formValidation.ts # 表单验证逻辑
├── ui/                 # UI相关模块
│   ├── formLayout.ts   # 表单布局和全屏功能
│   ├── formModal.ts    # 表单模态框管理
│   └── formRender.ts   # 表单渲染逻辑
├── data/               # 数据相关模块
│   ├── optionsLoader.ts # 选项数据加载
│   └── remoteData.ts   # 远程数据处理
├── index.ts            # 主入口，整合所有模块
└── README.md           # 文档说明
```

## 模块说明

### 核心模块 (core)

- **types.ts**: 定义表单相关的类型接口
- **formState.ts**: 管理表单数据状态、默认值和重置功能
- **formSubmit.ts**: 处理表单提交、防抖和错误处理
- **formValidation.ts**: 处理表单验证规则和验证逻辑

### UI模块 (ui)

- **formLayout.ts**: 管理表单布局、全屏和拖拽功能
- **formModal.ts**: 处理模态框的显示、关闭和交互
- **formRender.ts**: 负责表单字段和分组的渲染

### 数据模块 (data)

- **optionsLoader.ts**: 管理选项数据的加载和缓存
- **remoteData.ts**: 处理远程数据获取和详情加载

### 主入口 (index.ts)

整合所有模块，提供与原 `useForm` 完全兼容的API。

## 使用方法

重构后的用法与原来完全相同：

```typescript
import { useForm } from '@/hooks/useAnsheng';

const {
  formRef,
  formData,
  visible,
  loading,
  isEdit,
  show,
  close,
  renderFormModal,
} = useForm({
  title: (isEdit) => (isEdit ? '编辑项目' : '新增项目'),
  width: 900,
  getDetail: getDetail,
  create: createItem,
  update: updateItem,
  onSuccess: () => {
    refresh();
  },
  defaultValues: {
    status: 1,
    type: 1,
  },
});
```

## 扩展功能

重构后的模块化结构使得扩展功能更加容易：

1. 可以单独替换或扩展某个模块而不影响其他功能
2. 可以在不同项目中重用特定模块
3. 便于添加新功能或修改现有功能

## 维护说明

1. 所有对原 `useForm` 的功能修改应同步到对应的模块中
2. 新增功能应考虑放入最合适的模块，或创建新模块
3. 保持向后兼容，确保原有代码不会因为重构而失效 
