// form/index.ts
// 表单模块主入口

import type { UseFormOptions } from '../types';

import { createForm } from './create';

// 导出核心类型
export * from './core/types';

/**
 * 表单弹窗管理Hook
 * 提供完整的表单弹窗解决方案，包括表单状态管理、模态框交互、选项加载等功能
 *
 * 注意: 此函数的实现已重构为更模块化的结构，但API保持不变以确保兼容性
 */
export function useForm<T extends Record<string, any>>(
  options: UseFormOptions<T>,
) {
  // 调用重构后的模块化实现
  return createForm<T>(options);
}
