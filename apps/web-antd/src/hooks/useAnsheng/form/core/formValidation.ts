// form/core/formValidation.ts
// 表单验证模块

import type { FormInstance } from 'ant-design-vue';

// 使用与原始代码相同的类型定义方式
import type { FormRules } from '../../types';

import { ref } from 'vue';

/**
 * 表单验证Hook
 * 处理表单验证相关逻辑
 */
export function useFormValidation() {
  // 表单规则
  const formRules = ref<FormRules>({});

  // 设置表单规则
  const setRules = (rules: FormRules) => {
    formRules.value = rules;
  };

  // 添加单个规则
  const addRule = (field: string, rule: any | any[]) => {
    if (!formRules.value[field]) {
      formRules.value[field] = [];
    }
    formRules.value[field] = Array.isArray(rule)
      ? [...formRules.value[field], ...rule]
      : [...formRules.value[field], rule];
  };

  // 验证表单
  const validate = async (formRef: FormInstance): Promise<boolean> => {
    try {
      await formRef.validate();
      return true;
    } catch {
      return false;
    }
  };

  return {
    formRules,
    setRules,
    addRule,
    validate,
  };
}
