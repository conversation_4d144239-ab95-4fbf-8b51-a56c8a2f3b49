// form/core/formState.ts
// 表单状态管理模块

import type { FormInstance } from 'ant-design-vue';

import type { FormRules, FormStateOptions } from './types';

import { reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

/**
 * 表单状态管理Hook
 * 处理表单数据、验证规则和状态
 */
export function useFormState<T extends Record<string, any>>(
  options: FormStateOptions<T> = {},
) {
  // 表单引用和验证规则
  const formRef = ref<FormInstance>();
  const formRules = ref<FormRules>(options.rules || {});

  // 初始化表单数据
  const getInitialValues = (): Partial<T> => {
    if (typeof options.defaultValues === 'function') {
      return (options.defaultValues as () => Partial<T>)();
    } else if (options.defaultValues) {
      return { ...options.defaultValues };
    }
    return {};
  };

  const initialValues = getInitialValues();

  // 表单数据和原始数据 - 使用reactive而不是ref，与原实现保持一致
  const formData = reactive<T>({ ...initialValues } as T);
  const originalData = ref<Partial<T>>({});

  // 是否被修改
  const isModified = ref(false);

  // 设置表单规则
  const setRules = (rules: FormRules) => {
    formRules.value = rules;
  };

  // 添加单个规则
  const addRule = (field: string, rule: any | any[]) => {
    if (!formRules.value[field]) {
      formRules.value[field] = [];
    }

    formRules.value[field] = Array.isArray(rule)
      ? [...formRules.value[field], ...rule]
      : [...formRules.value[field], rule];
  };

  // 重置表单
  const resetForm = () => {
    // 重置为初始值
    Object.keys(formData).forEach((key) => {
      // 使用undefined赋值，避免删除动态计算的属性键的错误
      formData[key] = undefined;
    });

    // 应用默认值
    const newInitialValues = getInitialValues();

    Object.keys(newInitialValues).forEach((key) => {
      formData[key] = newInitialValues[key as keyof T];
    });

    // 重置表单验证状态
    formRef.value?.clearValidate();
  };

  // 设置表单数据
  const setFormData = (data: Partial<T>) => {
    Object.keys(data).forEach((key) => {
      formData[key] = data[key as keyof T];
    });
  };

  // 保存表单数据（同时记录原始数据）
  const saveFormData = (data: Partial<T>) => {
    setFormData(data);
    originalData.value = { ...data };
    isModified.value = false;
  };

  // 重置为原始数据
  const resetToOriginal = () => {
    if (Object.keys(originalData.value).length > 0) {
      setFormData(originalData.value);
      isModified.value = false;
    }
  };

  // 清除表单数据记录
  const clearFormData = () => {
    originalData.value = {};
    isModified.value = false;
  };

  // 查找字段对应的标签名
  const findFieldLabel = (fieldName: string): string | undefined => {
    // 尝试从DOM中查找对应字段的标签
    try {
      // 查找表单项元素
      const formItemElement = document
        .querySelector(
          `.ant-form-item-has-error [name="${fieldName}"], .ant-form-item-has-error #${fieldName}`,
        )
        ?.closest('.ant-form-item');

      // 从表单项中获取标签文本
      const labelElement = formItemElement?.querySelector(
        '.ant-form-item-label label',
      );
      if (labelElement) {
        // 移除可能的冒号和必填标记
        let labelText = labelElement.textContent || '';
        labelText = labelText.replace(/[:：*\s]+$/, '').trim();
        if (labelText) {
          return labelText;
        }
      }
    } catch (error) {
      console.error('查找字段标签失败:', error);
    }

    // 如果无法从DOM获取，返回格式化的字段名
    return fieldName
      .replaceAll(/([A-Z])/g, ' $1') // 在大写字母前添加空格
      .replace(/^./, (str) => str.toUpperCase()) // 首字母大写
      .trim();
  };

  // 验证表单
  const validate = async (): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      // 获取验证失败的字段名称列表
      const errorFields = (error as any)?.errorFields || [];

      if (errorFields.length > 0) {
        // 提取第一个错误字段和错误信息
        const firstError = errorFields[0];
        const fieldName = firstError.name?.[0];
        const errorMsg = firstError.errors?.[0] || '表单验证失败';

        // 显示错误提示，包含字段名称
        const fieldLabel = findFieldLabel(fieldName) || fieldName;
        message.error(`${fieldLabel}: ${errorMsg}`);
      } else {
        // 如果没有具体的错误字段信息，显示通用错误
        message.error('表单验证失败，请检查输入');
      }

      return false;
    }
  };

  return {
    formRef,
    formData,
    originalData,
    isModified,
    formRules,
    setRules,
    addRule,
    resetForm,
    setFormData,
    saveFormData,
    resetToOriginal,
    clearFormData,
    validate,
  };
}
