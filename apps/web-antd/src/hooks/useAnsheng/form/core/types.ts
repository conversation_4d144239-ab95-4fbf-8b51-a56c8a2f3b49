// form/core/types.ts
// 从原始types.ts文件导出必要的类型

import type {
  ApiResponse,
  FormFieldConfig,
  FormGroup,
  FormRules,
  UseFormOptions,
} from '../../types';

// 重新导出必要的类型
export type {
  ApiResponse,
  FormFieldConfig,
  FormGroup,
  FormRules,
  UseFormOptions,
};

// 表单状态相关类型
export interface FormStateOptions<T> {
  defaultValues?: (() => Partial<T>) | Partial<T>;
  rules?: FormRules;
}

// 表单提交相关类型
export interface FormSubmitOptions<T> {
  create?: (data: T) => Promise<ApiResponse>;
  update?: (id: number, data: T) => Promise<ApiResponse>;
  onSuccess?: () => Promise<void> | void;
}

// 表单布局相关类型
export interface FormLayoutOptions {
  width?: number;
  draggable?: boolean;
  fullscreenable?: boolean;
  simpleLayout?: boolean | Record<string, any>;
}

// 表单模态框相关类型
export interface FormModalOptions {
  title?: ((isEdit: boolean) => string) | string;
  onCancel?: () => void;
  onMounted?: () => void;
}

// 表单选项数据相关类型
export interface FormOptionsLoaderOptions {
  selectApis?: Record<string, () => Promise<any>>;
}

// 远程数据相关类型
export interface RemoteDataOptions<T> {
  getDetail?: (id: number) => Promise<ApiResponse<T>>;
}
