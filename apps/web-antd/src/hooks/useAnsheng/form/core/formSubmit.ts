// form/core/formSubmit.ts
// 表单提交逻辑模块

import type { FormSubmitOptions } from './types';

import { ref } from 'vue';

import { message } from 'ant-design-vue';

import { handleError, preventMultiSubmit } from '../../utils';

/**
 * 表单提交Hook
 * 处理表单提交、防抖和错误处理
 */
export function useFormSubmit<T extends Record<string, any>>(
  options: FormSubmitOptions<T>,
  deps?: {
    close?: () => void;
  },
) {
  // 加载状态
  const loading = ref(false);
  // 防抖锁
  const submitDebounce = ref(false);

  // 表单提交处理
  const handleSubmit = preventMultiSubmit(
    async (
      values: T,
      isEdit: boolean,
      id?: number,
      showErrorMessage = true,
    ) => {
      if (submitDebounce.value) return;

      try {
        submitDebounce.value = true;
        loading.value = true;

        let res;
        let successMsg = '';

        if (isEdit && id) {
          if (!options.update) throw new Error('未提供更新方法');

          res = await options.update(id, values);
          successMsg = '修改成功';
        } else {
          if (!options.create) throw new Error('未提供创建方法');

          res = await options.create(values);
          successMsg = '创建成功';
        }

        // 增加对res的安全检查
        if (!res) {
          throw new Error(isEdit ? '修改失败' : '创建失败');
        }

        if (res.code !== 1) {
          throw new Error(res.msg || (isEdit ? '修改失败' : '创建失败'));
        }

        message.success(res.msg || successMsg);

        // 确保在操作成功后调用 onSuccess
        if (options.onSuccess) {
          await options.onSuccess();
        }

        // 提交成功后关闭弹窗
        if (deps?.close) {
          deps.close();
        }

        return res;
      } catch (error) {
        const formattedError = handleError(
          error,
          isEdit ? '修改失败' : '创建失败',
        );

        // 添加错误消息去重逻辑
        const errorMsg = formattedError.message;
        const isUserCancelled = errorMsg.includes('用户取消');

        // 只有在showErrorMessage为true、不是用户取消、且消息未重复时才显示
        if (
          showErrorMessage &&
          !isUserCancelled &&
          (!window.__lastErrorMessage || window.__lastErrorMessage !== errorMsg)
        ) {
          // message.error(errorMsg);
          // 记录最后显示的错误消息
          window.__lastErrorMessage = errorMsg;
          // 5秒后清除记录，避免长期影响
          setTimeout(() => {
            window.__lastErrorMessage = '';
          }, 5000);
        }

        throw formattedError; // 向上抛出错误
      } finally {
        loading.value = false;
        // 延迟重置防抖锁
        setTimeout(() => {
          submitDebounce.value = false;
        }, 500);
      }
    },
  );

  return {
    loading,
    submitDebounce,
    handleSubmit,
  };
}

// 在文件顶部添加类型声明
declare global {
  interface Window {
    __lastErrorMessage?: string;
  }
}
