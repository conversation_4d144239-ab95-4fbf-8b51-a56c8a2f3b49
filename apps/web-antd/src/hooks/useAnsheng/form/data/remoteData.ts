// form/data/remoteData.ts
// 远程数据处理模块

import type { RemoteDataOptions } from '../core/types';

import { ref } from 'vue';

import { message } from 'ant-design-vue';

import { asyncOnce, handleError } from '../../utils';

/**
 * 远程数据处理Hook
 * 处理远程数据获取和详情加载
 */
export function useRemoteData<T extends Record<string, any>>(
  options: RemoteDataOptions<T> = {},
) {
  // 加载状态
  const loading = ref(false);
  // 当前ID
  const currentId = ref<number>();
  // 编辑状态
  const isEdit = ref(false);
  // 存储异步默认值
  const asyncDefaultValues = ref<Partial<T>>({} as Partial<T>);
  // 标记是否已应用异步默认值
  const hasAppliedAsyncDefaults = ref(false);

  // 加载详情的优化版本，使用asyncOnce确保不会并发调用
  const loadDetail = asyncOnce(async (id: number) => {
    if (!options.getDetail) return;

    try {
      loading.value = true;
      const res = await options.getDetail(id);
      if (res.code === 1 && res.data) {
        return res.data;
      }
      throw new Error(res.msg || '获取详情失败');
    } catch (error) {
      const formattedError = handleError(error, '加载失败');
      message.error(formattedError.message);
      throw formattedError;
    } finally {
      loading.value = false;
    }
  });

  /**
   * 设置异步默认值
   * 这个方法允许在表单初始化后设置默认值，而不会被resetForm覆盖
   */
  const setAsyncDefaultValues = (values: Partial<T>) => {
    // 保存异步默认值
    asyncDefaultValues.value = {
      ...asyncDefaultValues.value,
      ...values,
    };
    hasAppliedAsyncDefaults.value = true;
    return values;
  };

  return {
    loading,
    currentId,
    isEdit,
    asyncDefaultValues,
    hasAppliedAsyncDefaults,
    loadDetail,
    setAsyncDefaultValues,
  };
}
