// form/data/optionsLoader.ts
// 选项数据加载模块

import type { FormOptionsLoaderOptions } from '../core/types';

import { h, ref } from 'vue';

import { Select, Spin } from 'ant-design-vue';

/**
 * 选项数据加载Hook
 * 管理选项数据的加载和缓存
 */
export function useOptionsLoader(options: FormOptionsLoaderOptions = {}) {
  // 选项数据和加载状态
  const selectOptions = ref<Record<string, any[]>>({});
  const selectLoading = ref<Record<string, boolean>>({});
  const remoteLoading = ref<Record<string, boolean>>({});
  const searchKeywords = ref<Record<string, string>>({});

  // 加载选项数据
  const loadSelectOptions = async () => {
    if (!options.selectApis) return;

    const apis = options.selectApis;
    const keys = Object.keys(apis);
    if (keys.length === 0) return;

    // 并行加载所有选项
    await Promise.all(
      keys.map(async (key) => {
        try {
          selectLoading.value[key] = true;
          const res = await apis[key]();
          if (res && res.code === 1 && res.data) {
            selectOptions.value[key] = res.data;
          }
        } catch (error) {
          console.error(`加载${key}选项数据失败:`, error);
        } finally {
          selectLoading.value[key] = false;
        }
      }),
    );
  };

  // 处理远程搜索
  const handleRemoteSearch = (
    key: string,
    api: (keyword: string) => Promise<any>,
  ) => {
    return async (keyword: string) => {
      try {
        searchKeywords.value[key] = keyword;
        if (!keyword) return;

        remoteLoading.value[key] = true;
        const res = await api(keyword);
        if (res && res.code === 1 && res.data) {
          selectOptions.value[key] = res.data;
        }
      } catch (error) {
        console.error(`远程搜索${key}失败:`, error);
      } finally {
        remoteLoading.value[key] = false;
      }
    };
  };

  // 创建Select元素
  const createSelectElement = (
    key: string,
    props: Record<string, any> = {},
    formData?: any,
    fieldName?: string,
  ) => {
    const isLoading = selectLoading.value[key];
    const options = selectOptions.value[key] || [];

    const selectProps = {
      options,
      loading: isLoading,
      ...props,
    };

    // 如果提供了formData和fieldName，添加双向绑定
    if (formData && fieldName) {
      selectProps.value = formData[fieldName];
      selectProps['onUpdate:value'] = (val: any) => (formData[fieldName] = val);
    }

    return h(Select, selectProps);
  };

  // 创建远程搜索Select
  const createRemoteSelect = (
    key: string,
    api: (keyword: string) => Promise<any>,
    props: Record<string, any> = {},
    formData?: any,
    fieldName?: string,
  ) => {
    const isLoading = remoteLoading.value[key];
    const options = selectOptions.value[key] || [];

    const selectProps = {
      options,
      loading: isLoading,
      filterOption: false,
      showSearch: true,
      onSearch: handleRemoteSearch(key, api),
      ...props,
    };

    // 如果提供了formData和fieldName，添加双向绑定
    if (formData && fieldName) {
      selectProps.value = formData[fieldName];
      selectProps['onUpdate:value'] = (val: any) => (formData[fieldName] = val);
    }

    // 使用Spin包装，显示加载状态
    return h(
      Spin,
      { spinning: isLoading },
      {
        default: () => h(Select, selectProps),
      },
    );
  };

  return {
    selectOptions,
    selectLoading,
    remoteLoading,
    searchKeywords,
    loadSelectOptions,
    handleRemoteSearch,
    createSelectElement,
    createRemoteSelect,
  };
}
