// form/create.ts
// 表单工厂函数

import type { FormFieldConfig, FormGroup, UseFormOptions } from '../types';

import { h, onMounted, watch } from 'vue';

import { Form } from 'ant-design-vue';

import { useFieldVisibility } from '../useFieldVisibility';
import { useFormState } from './core/formState';
import { useFormSubmit } from './core/formSubmit';
import { useOptionsLoader } from './data/optionsLoader';
import { useRemoteData } from './data/remoteData';
import { useFormLayout } from './ui/formLayout';
import { useFormModal } from './ui/formModal';
import { useFormRender } from './ui/formRender';

/**
 * 创建表单
 * 整合所有模块，提供完整的表单功能
 */
export function createForm<T extends Record<string, any>>(
  options: UseFormOptions<T>,
) {
  // 使用远程数据管理
  const {
    loading,
    currentId,
    isEdit,
    asyncDefaultValues,
    hasAppliedAsyncDefaults,
    loadDetail,
    setAsyncDefaultValues,
  } = useRemoteData<T>(options);

  // 使用表单状态管理
  const {
    formRef,
    formData, // 注意：这里已经是reactive对象，不是ref
    formRules,
    setRules,
    addRule,
    resetForm: originalResetForm,
    setFormData,
    saveFormData,
    resetToOriginal,
    isModified,
    clearFormData,
    validate,
  } = useFormState<T>(options);

  // 使用字段可见性管理
  const {
    fieldVisibility,
    setFieldVisibility,
    updateAllFieldVisibility,
    renderField,
  } = useFieldVisibility(formData);

  // 重写resetForm函数，确保异步默认值不会被覆盖
  const resetForm = () => {
    // 先执行原始的resetForm
    originalResetForm();

    // 如果有异步默认值，应用它们
    if (Object.keys(asyncDefaultValues.value).length > 0) {
      Object.keys(asyncDefaultValues.value).forEach((key) => {
        const value = asyncDefaultValues.value[key as keyof T];
        if (value !== undefined) {
          // 直接设置到formData，不需要.value
          formData[key] = value;
        }
      });
      hasAppliedAsyncDefaults.value = true;
    }
  };

  // 使用表单布局管理
  const {
    isFullscreen,
    isDragging,
    toggleFullscreen,
    resetPosition,
    initModal,
    getFormLayoutConfig,
  } = useFormLayout({
    fullscreenable: options.fullscreenable,
    draggable: options.draggable,
    width: options.width,
    simpleLayout: options.simpleLayout,
  });

  // 使用选项数据管理
  const {
    selectOptions,
    selectLoading,
    remoteLoading,
    searchKeywords,
    loadSelectOptions,
    handleRemoteSearch,
    createSelectElement,
    createRemoteSelect,
  } = useOptionsLoader({
    // 使用配置中的selectApis或空对象
    selectApis: options.selectApis || {},
  });

  // 使用表单模态框管理
  const {
    visible,
    modalTitle,
    close,
    confirmLoading,
    renderFullscreenButton,
    renderModal,
  } = useFormModal(
    {
      title: options.title,
      onCancel: options.onCancel,
      // 确保onMounted存在
      onMounted: options.onMounted || (() => {}),
    },
    {
      formLayout: { isFullscreen, isDragging, toggleFullscreen },
      formState: { formData, clearFormData, resetForm, formRef },
      isEdit,
      loading,
    },
  );

  // 现在初始化表单提交逻辑，传入 close 函数
  const formSubmitResult = useFormSubmit<T>(options, { close });
  const handleSubmit = formSubmitResult.handleSubmit;
  const submitDebounce = formSubmitResult.submitDebounce;

  // 监听提交状态，更新confirmLoading
  watch(
    () => formSubmitResult.loading.value,
    (newVal) => {
      confirmLoading.value = newVal;
    },
  );

  // 使用表单渲染
  const {
    renderFields,
    renderFormGroups,
    remoteFieldOptionsMap,
    loadRemoteFieldOptions,
  } = useFormRender();

  // 监听modal打开状态，初始化DOM引用
  watch(
    () => visible.value,
    (v) => {
      initModal(v);
      if (!v) {
        // 弹窗关闭时，重置全屏状态
        isFullscreen.value = false;
      }
    },
  );

  // 打开弹窗
  const show = async (
    idOrRecord?: number | Partial<T>,
    groups?: FormGroup[],
  ) => {
    // 重置表单到默认状态
    resetForm();

    // 确保清除之前的验证状态
    formRef.value?.clearValidate();

    // 加载Select选项数据
    await loadSelectOptions();

    // 如果传入了表单组，预加载表单字段的远程选项
    if (groups && groups.length > 0) {
      // 从表单组中提取所有字段
      const fields: FormFieldConfig[] = [];
      groups.forEach((group) => {
        if (group.fields && group.fields.length > 0) {
          fields.push(...group.fields);
        }
      });

      // 预加载远程选项
      if (fields.length > 0) {
        try {
          // 找出所有需要远程加载选项的Select字段
          const remoteFields = fields.filter(
            (field) =>
              field.remote && field.component === 'Select' && field.name,
          );

          // 并行加载所有远程选项
          if (remoteFields.length > 0) {
            await Promise.all(
              remoteFields.map((field) => {
                // 直接调用loadRemoteFieldOptions方法加载选项
                if (loadRemoteFieldOptions) {
                  return loadRemoteFieldOptions(field);
                }
                return Promise.resolve();
              }),
            );
          }
        } catch (error) {
          console.error('预加载表单选项失败:', error);
        }
      }
    }

    // 然后再显示弹窗
    visible.value = true;

    if (idOrRecord === undefined) {
      // 处理新增情况
      isEdit.value = false;
      currentId.value = undefined;
    } else {
      isEdit.value = true;

      // 处理编辑情况
      if (typeof idOrRecord === 'object') {
        // 如果传入的是对象，直接使用
        const record = idOrRecord as Partial<T>;
        currentId.value = (record as any).id; // 保存ID用于后续更新操作
        saveFormData(record as T);
      } else {
        // 如果传入的是ID，通过getDetail获取数据
        currentId.value = idOrRecord;
        if (options.getDetail) {
          try {
            const data = await loadDetail(idOrRecord);
            if (data) {
              saveFormData(data);
              return data;
            }
          } catch (error) {
            close();
            throw error;
          }
        }
      }
    }
  };

  // 渲染表单弹窗
  const renderFormModal = (
    groups: FormGroup[],
    customConfig?: Record<string, any>,
  ) => {
    // 获取布局配置
    const layoutConfig = getFormLayoutConfig();

    // 创建表单内容
    const formContent = h(
      Form,
      {
        ref: formRef,
        model: formData, // 直接使用formData，不需要.value
        rules: formRules.value,
        onFinish: (values: Record<string, any>) => {
          const formValues = {} as any;
          Object.keys(values).forEach((key) => {
            formValues[key] = values[key];
          });
          return handleSubmit(formValues as T, isEdit.value, currentId.value);
        },
        class: `py-2 channel-form ${layoutConfig.useSimpleLayout ? 'simple-form' : ''}`,
        ...layoutConfig.formLayout,
      },
      () =>
        renderFormGroups(
          groups,
          formData, // 直接传递formData，不需要.value
          formRules.value,
          selectOptions.value,
          layoutConfig,
        ),
    );

    // 渲染模态框
    return renderModal(formContent, options.width, {
      onOk: () =>
        validate().then((valid) => {
          if (!valid) {
            // 验证失败，显示更具体的错误信息
            throw new Error('表单验证失败');
          }
          const formValues = {} as any;
          Object.keys(formData).forEach((key) => {
            formValues[key] = formData[key];
          });
          return handleSubmit(
            formValues as T,
            isEdit.value,
            currentId.value,
            true,
          );
        }),
      ...customConfig,
    });
  };

  // 添加CSS样式
  onMounted(() => {
    // 添加简洁表单样式
    if (options.simpleLayout) {
      const styleId = 'ansheng-simple-form-style';
      if (!document.querySelector(`#${styleId}`)) {
        const style = document.createElement('style');
        style.id = styleId;
        style.innerHTML = `
        /* 简洁表单样式调整 */
        .simple-form-modal .simple-form .ant-form-item {
          margin-bottom: 16px; /* 减少表单项间距 */
        }
        .simple-form-modal .simple-form .ant-form-item-label {
          padding-bottom: 4px; /* 减少标签与控件之间的间距 */
        }
        .simple-form-modal .simple-form-content {
          width: 100%;
          padding: 0 2px;
          margin: 0 auto;
        }
        .simple-form-modal .ant-form-item-control-input {
          width: 100%;
        }
        .simple-form-modal .ant-form-vertical .ant-form-item-label {
          padding: 0 0 4px; /* 减少垂直布局的标签底部间距 */
        }
        /* 调整按钮大小和边距 */
        .simple-form-modal .ant-modal-footer {
          padding: 10px 16px; /* 减小footer内边距 */
        }
        /* 调整内容区域内边距 */
        .simple-form-modal .ant-modal-body {
          padding-top: 8px;
          padding-bottom: 8px;
        }
      `;
        document.head.append(style);
      }
    }

    // 确保onMounted存在
    if (options.onMounted) {
      options.onMounted();
    }
  });

  // 初始化表单数据
  resetForm();

  return {
    visible,
    loading,
    isEdit,
    currentId,
    formRef,
    formData,
    formRules,
    isFullscreen,
    isDragging,
    selectOptions,
    selectLoading,
    remoteLoading,
    searchKeywords,
    show,
    close,
    setFormData,
    saveFormData,
    resetForm,
    resetToOriginal,
    setRules,
    addRule,
    validate,
    handleSubmit,
    toggleFullscreen,
    resetPosition,
    loadSelectOptions,
    handleRemoteSearch,
    createSelectElement,
    createRemoteSelect,
    renderFormModal,
    setAsyncDefaultValues,
    remoteFieldOptionsMap,
    confirmLoading,
    loadRemoteFieldOptions,
    // 字段可见性相关
    fieldVisibility,
    setFieldVisibility,
    updateAllFieldVisibility,
    renderField,
    isModified,
  };
}
