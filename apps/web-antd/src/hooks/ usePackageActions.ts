import { ref } from 'vue';

import { message, Modal } from 'ant-design-vue';

import { deleteCardPack } from '#/api/core/sim';

export function usePackageActions(onSuccess?: () => void) {
  const loading = ref(false);
  const modalVisible = ref(false);
  const currentRecord = ref<any>(null);

  // 添加套餐包
  const handleAddPackage = async () => {
    modalVisible.value = true;
    currentRecord.value = null;
  };

  // 修改套餐包
  const handleEditPackage = async (record: any) => {
    modalVisible.value = true;
    currentRecord.value = record;
  };

  // 删除套餐包
  const handleDeletePackage = async (record: any) => {
    try {
      Modal.confirm({
        title: '删除套餐包',
        content: `确定要删除${record.packageName}套餐包吗？`,
        onOk: async () => {
          loading.value = true;
          const res = await deleteCardPack(record.id);
          if (res.code === 1) {
            message.success('删除套餐包成功');
            onSuccess?.();
          } else {
            message.error(res.msg || '删除套餐包失败');
          }
        },
      });
    } catch (error) {
      console.error('删除套餐包失败:', error);
      message.error('删除套餐包失败');
    } finally {
      loading.value = false;
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      loading.value = true;
      if (currentRecord.value) {
        // TODO: 实现修改套餐包逻辑
        message.success('修改套餐包成功');
      } else {
        // TODO: 实现添加套餐包逻辑
        message.success('添加套餐包成功');
      }
      modalVisible.value = false;
      onSuccess?.();
    } catch (error) {
      console.error('操作失败:', error);
      message.error('操作失败');
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    modalVisible,
    currentRecord,
    handleAddPackage,
    handleEditPackage,
    handleDeletePackage,
    handleSubmit,
  };
}
