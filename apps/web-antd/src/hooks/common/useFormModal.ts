import type { FormInstance } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

import {
  computed,
  h,
  markRaw,
  nextTick,
  onMounted,
  reactive,
  ref,
  shallowRef,
  watch,
} from 'vue';

import { MdiFullscreen, MdiFullscreenExit } from '@vben/icons';

import { Form, message, Modal, Row, Select, Spin } from 'ant-design-vue';

import { useModalDrag } from './useModalDrag';

// 新增：Select选项的接口定义
export interface SelectOption {
  label: string;
  value: number | string;
  [key: string]: any;
}

// 新增：Select选项的映射
export type SelectOptionsMap = Record<string, SelectOption[]>;

// 新增：全局加载状态的映射
export type SelectLoadingMap = Record<string, boolean>;

// 新增：表单验证规则映射
export type FormRules = Record<string, Rule[]>;

// 新增：字段可见性条件
export interface FieldVisibility {
  visible?: (formData: any) => boolean;
  dependencies?: string[];
}

// 新增：扩展FormGroup接口，添加条件显示属性
export interface FormGroup {
  title: string;
  content: (formData: any, selectOptions?: SelectOptionsMap) => any;
  // 新增：条件显示
  visible?: (formData: any) => boolean;
}

// 新增：接口函数配置
export interface ApiConfig {
  api: (...args: any[]) => Promise<any>; // API函数
  params?: any; // 请求参数
  valueField?: string; // 值字段名，默认为'id'
  labelField?: string; // 标签字段名，默认为'name'
  transform?: (data: any[]) => SelectOption[]; // 自定义转换函数
  // 新增：远程搜索相关配置
  remote?: boolean; // 是否支持远程搜索
  searchField?: string; // 搜索字段名称，默认为'keyword'
  debounce?: number; // 防抖延迟时间，默认300ms
}

export interface UseFormModalOptions<T = any> {
  title?: ((isEdit: boolean) => string) | string;
  width?: number | string;
  onSuccess?: () => void;
  onCancel?: () => void;
  // 获取详情的方法
  getDetail?: (id: number) => Promise<{ code: number; data: T; msg: string }>;
  // 创建的方法
  create?: (params: any) => Promise<{ code: number; msg: string }>;
  // 更新的方法
  update?: (id: number, params: any) => Promise<{ code: number; msg: string }>;
  // 默认值
  defaultValues?: Partial<T>;
  // 新增：Select选项的API配置
  selectApis?: Record<string, ApiConfig>;
  onMounted?: () => void;
  // 新增：是否允许全屏
  fullscreenable?: boolean;
  // 新增：是否允许拖拽
  draggable?: boolean;
  // 新增：表单验证规则
  rules?: FormRules;
}

// 新增：全屏模式样式类名
const FULLSCREEN_CLASS = 'fullscreen-modal';
const FULLSCREEN_WRAP_CLASS = 'fullscreen-wrap';

export function useFormModal<T extends Record<string, any>>(
  options: UseFormModalOptions<T> = {},
) {
  const formRef = ref<FormInstance>();
  const visible = ref(false);
  const loading = ref(false);
  const isEdit = ref(false);
  const currentId = ref<number>();
  const formData = reactive<T>({} as T);
  // 新增：全屏状态
  const isFullscreen = ref(false);

  // 新增：模态框和标题引用
  const modalRef = ref<HTMLElement | null>();
  const headerRef = ref<HTMLElement | null>();
  const wrapperRef = ref<HTMLElement | null>();

  // 新增：使用shallowRef优化性能
  const selectOptions = shallowRef<SelectOptionsMap>({});
  const selectLoading = ref<SelectLoadingMap>({});

  // 新增：表单验证规则管理
  const formRules = ref<FormRules>(options.rules || {});

  // 新增：表单字段可见性控制
  const fieldVisibility = reactive<Record<string, boolean>>({});

  // 新增：防抖锁
  const submitDebounce = ref(false);

  // 新增：组件缓存
  const cachedComponents = shallowRef(new Map());

  // 控制拖拽是否启用
  const shouldDraggable = ref(Boolean(options.draggable));

  // 新增：远程搜索防抖计时器
  const searchTimers = shallowRef<Record<string, number>>({});

  // 新增：远程搜索loading状态
  const remoteLoading = ref<Record<string, boolean>>({});

  // 新增：搜索关键词
  const searchKeywords = ref<Record<string, string>>({});

  // 新增：更新所有字段可见性
  const updateAllFieldVisibility = () => {
    // 触发每个字段的重新计算，以更新可见性
    Object.keys(fieldVisibility).forEach((field) => {
      const temp = !!fieldVisibility[field]; // 转为布尔值确保类型安全
      // 延迟一帧来确保Vue检测到变化
      setTimeout(() => {
        fieldVisibility[field] = temp;
      }, 0);
    });
  };

  // 新增：使用计算属性优化标题渲染
  const modalTitle = computed(() =>
    typeof options.title === 'function'
      ? options.title(isEdit.value)
      : options.title || (isEdit.value ? '编辑' : '新增'),
  );

  // 使用新的拖拽hook
  const { isDragging, resetPosition, initDrag } = useModalDrag(
    modalRef,
    headerRef,
    {
      enabled: shouldDraggable,
      onDragStart: () => {
        // 开始拖拽时添加类
        if (modalRef.value) {
          modalRef.value.classList.add('modal-dragging', 'no-transition');
        }
      },
      onDragEnd: () => {
        // 结束拖拽时移除类
        if (modalRef.value) {
          modalRef.value.classList.remove('modal-dragging', 'no-transition');
        }
      },
    },
  );

  // 新增：应用全屏样式
  const applyFullscreenStyles = (isFullscreen: boolean) => {
    if (!modalRef.value || !wrapperRef.value) return;

    if (isFullscreen) {
      // 添加全屏样式
      modalRef.value.classList.add(FULLSCREEN_CLASS);
      wrapperRef.value.classList.add(FULLSCREEN_WRAP_CLASS);

      // 设置全屏样式
      modalRef.value.style.width = '100%';
      modalRef.value.style.height = '100vh';
      modalRef.value.style.maxHeight = '100vh';
      modalRef.value.style.top = '0';
      modalRef.value.style.padding = '0';
      modalRef.value.style.margin = '0';

      // 内容容器样式
      const bodyElement = modalRef.value.querySelector('.ant-modal-body');
      if (bodyElement) {
        (bodyElement as HTMLElement).style.height = 'calc(100vh - 160px)';
        (bodyElement as HTMLElement).style.overflow = 'hidden';
      }

      // 重置位置
      resetPosition();
    } else {
      // 移除全屏样式
      modalRef.value.classList.remove(FULLSCREEN_CLASS);
      wrapperRef.value.classList.remove(FULLSCREEN_WRAP_CLASS);

      // 恢复普通样式
      modalRef.value.style.width =
        typeof options.width === 'number'
          ? `${options.width}px`
          : options.width || '900px';
      modalRef.value.style.height = '';
      modalRef.value.style.maxHeight = '';
      modalRef.value.style.top = '';
      modalRef.value.style.padding = '';
      modalRef.value.style.margin = '';

      // 内容容器样式
      const bodyElement = modalRef.value.querySelector('.ant-modal-body');
      if (bodyElement) {
        (bodyElement as HTMLElement).style.height = '';
        (bodyElement as HTMLElement).style.overflow = '';
      }
    }
  };

  // 监听全屏状态，动态更新拖拽启用状态
  watch(
    () => isFullscreen.value,
    (val) => {
      shouldDraggable.value = Boolean(options.draggable) && !val;

      // 全屏状态变化时，应用样式修改
      nextTick(() => {
        applyFullscreenStyles(val);
      });
    },
    { immediate: true },
  );

  // 监听modal打开状态，获取实际DOM引用
  watch(
    () => visible.value,
    async (v) => {
      if (v) {
        await nextTick();
        // 等待Modal动画完成后再获取元素并初始化
        setTimeout(() => {
          // 找到实际的modal元素
          const modalElement = document.querySelector(
            '.ant-modal',
          ) as HTMLElement;
          modalRef.value = modalElement;

          // 找到头部元素作为拖拽触发区
          const headerElement = document.querySelector(
            '.ant-modal-header',
          ) as HTMLElement;
          headerRef.value = headerElement;

          // 找到包装元素
          const wrapperElement = document.querySelector(
            '.ant-modal-wrap',
          ) as HTMLElement;
          wrapperRef.value = wrapperElement;

          // 如果启用了拖拽且找到了元素，则立即初始化拖拽
          if (shouldDraggable.value && modalElement && headerElement) {
            initDrag();

            // 设置cursor样式，确保鼠标在标题上时显示为可拖拽状态
            headerElement.style.cursor = 'move';
          }

          // 如果当前是全屏状态，应用全屏样式
          if (isFullscreen.value) {
            applyFullscreenStyles(true);
          }
        }, 100);
      } else {
        // 弹窗关闭时，重置全屏状态
        isFullscreen.value = false;
      }
    },
  );

  // 切换全屏时处理位置
  watch(
    () => isFullscreen.value,
    (val) => {
      if (val) {
        // 全屏时重置位置
        resetPosition();
      }
    },
  );

  // 重置表单数据
  const resetFormData = () => {
    const defaultValues = options.defaultValues || {};
    Object.keys(formData).forEach((key) => {
      formData[key] = undefined;
    });
    Object.assign(formData, defaultValues);

    // 新增：更新字段可见性
    updateAllFieldVisibility();
  };

  // 设置表单数据
  const setFormData = (data: Partial<T>) => {
    resetFormData();
    Object.assign(formData, data);

    // 新增：更新字段可见性
    updateAllFieldVisibility();
  };

  // 新增：保存表单原始数据，用于重置或检查是否有修改
  const originalFormData = shallowRef<Partial<T>>({});

  // 新增：保存表单数据并记录原始值
  const saveFormData = (data: Partial<T>) => {
    // 清除现有数据但保留默认值
    resetFormData();
    // 设置新数据
    Object.assign(formData, data);
    // 记录原始数据（深拷贝）
    originalFormData.value = structuredClone(data);
    // 更新字段可见性
    updateAllFieldVisibility();
  };

  // 新增：重置为原始数据
  const resetToOriginal = () => {
    if (originalFormData.value) {
      // 深拷贝原始数据到表单
      Object.assign(formData, structuredClone(originalFormData.value));
      // 更新所有字段可见性
      updateAllFieldVisibility();
    }
  };

  // 新增：检查表单是否有修改
  const isFormModified = computed(() => {
    if (Object.keys(originalFormData.value).length === 0) return false;

    // 对比当前表单数据和原始数据
    return JSON.stringify(formData) !== JSON.stringify(originalFormData.value);
  });

  // 新增：清除表单数据（保留默认值）
  const clearFormData = () => {
    resetFormData();
    // 清空原始数据记录
    originalFormData.value = {};
  };

  // 新增：设置表单验证规则
  const setRules = (rules: FormRules) => {
    formRules.value = rules;
  };

  // 新增：添加单个字段验证规则
  const addRule = (field: string, rule: Rule | Rule[]) => {
    const rules = Array.isArray(rule) ? rule : [rule];
    if (!formRules.value[field]) {
      formRules.value[field] = [];
    }
    formRules.value[field].push(...rules);
  };

  // 新增：设置字段可见性
  const setFieldVisibility = (field: string, config: FieldVisibility) => {
    // 立即更新字段可见性
    fieldVisibility[field] = config.visible ? config.visible(formData) : true;

    // 如果存在依赖字段，则监听变化
    if (
      config.dependencies &&
      config.dependencies.length > 0 &&
      config.visible
    ) {
      const visibleFn = config.visible; // 保存引用避免类型检查问题
      config.dependencies.forEach((depField) => {
        watch(
          () => formData[depField],
          () => {
            fieldVisibility[field] = visibleFn(formData);
          },
        );
      });
    }
  };

  // 新增：创建组件缓存
  const getCachedComponent = (key: string, creator: () => any) => {
    if (!cachedComponents.value.has(key)) {
      cachedComponents.value.set(key, markRaw(creator()));
    }
    return cachedComponents.value.get(key);
  };

  // 关闭弹窗
  const close = () => {
    visible.value = false;
    isEdit.value = false;
    currentId.value = undefined;
    // 重置表单数据
    resetFormData();
    // 清空原始数据记录
    originalFormData.value = {};
    // 清除验证
    formRef.value?.clearValidate();
    // 新增：退出全屏
    isFullscreen.value = false;
    options.onCancel?.();
  };

  // 新增：切换全屏
  const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
    // 避免使用console.log，改为更合适的方式记录状态变化
    // console.log('切换全屏状态:', isFullscreen.value);
  };

  // 加载详情
  const loadDetail = async (id: number) => {
    if (!options.getDetail) return;

    try {
      loading.value = true;
      const res = await options.getDetail(id);
      if (res.code === 1 && res.data) {
        setFormData(res.data);
        return res.data;
      }
      throw new Error(res.msg || '获取详情失败');
    } catch (error) {
      message.error(error instanceof Error ? error.message : '加载失败');
      close();
    } finally {
      loading.value = false;
    }
  };

  // 新增：加载Select选项数据的函数
  const loadSelectOptions = async () => {
    if (!options.selectApis) return;

    const apis = options.selectApis;
    const keys = Object.keys(apis);

    if (keys.length === 0) return;

    try {
      // 并行加载所有Select选项
      await Promise.all(
        keys.map(async (key) => {
          const config = apis[key];
          if (!config || !config.api) return;

          try {
            selectLoading.value[key] = true;

            // 调用API函数获取数据
            const res = await config.api(config.params);

            if (res && res.code === 1 && res.data) {
              let options: SelectOption[];

              // 使用自定义转换函数或默认转换逻辑
              if (config.transform) {
                options = config.transform(res.data);
              } else {
                const valueField = config.valueField || 'id';
                const labelField = config.labelField || 'name';

                options = Array.isArray(res.data)
                  ? res.data.map((item: Record<string, any>) => ({
                      label: item[labelField],
                      value: item[valueField],
                      ...item, // 保留原始数据
                    }))
                  : [];
              }

              selectOptions.value = {
                ...selectOptions.value,
                [key]: options,
              };
            } else {
              console.error(`加载${key}选项失败:`, res);
              selectOptions.value = {
                ...selectOptions.value,
                [key]: [],
              };
            }
          } catch (error) {
            console.error(`加载${key}选项出错:`, error);
            selectOptions.value = {
              ...selectOptions.value,
              [key]: [],
            };
          } finally {
            selectLoading.value[key] = false;
          }
        }),
      );
    } catch (error) {
      console.error('加载Select选项出错:', error);
    }
  };

  // 修改：扩展的h函数，支持自动处理Select组件
  const createSelectElement = (key: string, props: any) => {
    // 检查是否有对应的选项数据
    const options = selectOptions.value[key] || [];
    const isLoading = selectLoading.value[key] || false;

    // 结合原始props和自动生成的options
    return h(Select, {
      ...props,
      loading: isLoading,
      options,
    });
  };

  // 新增：处理远程搜索
  const handleRemoteSearch = (key: string, value: string) => {
    const apiConfig = options.selectApis?.[key];
    if (!apiConfig || !apiConfig.remote) return;

    // 记录搜索关键词
    searchKeywords.value[key] = value;

    // 清除之前的计时器
    if (searchTimers.value[key]) {
      clearTimeout(searchTimers.value[key]);
    }

    // 设置防抖延迟，默认300ms
    const debounce = apiConfig.debounce ?? 300;

    // 创建新的计时器
    searchTimers.value[key] = window.setTimeout(async () => {
      // 设置加载状态
      remoteLoading.value[key] = true;

      try {
        // 构建搜索参数
        const searchParams = {
          ...apiConfig.params,
          [apiConfig.searchField || 'keyword']: value,
        };

        // 调用API
        const res = await apiConfig.api(searchParams);

        if (res && res.code === 1 && res.data) {
          let options: SelectOption[];

          // 使用自定义转换函数或默认转换逻辑
          if (apiConfig.transform) {
            options = apiConfig.transform(res.data);
          } else {
            const valueField = apiConfig.valueField || 'id';
            const labelField = apiConfig.labelField || 'name';

            options = Array.isArray(res.data)
              ? res.data.map((item: Record<string, any>) => ({
                  label: item[labelField],
                  value: item[valueField],
                  ...item, // 保留原始数据
                }))
              : [];
          }

          // 更新选项
          selectOptions.value = {
            ...selectOptions.value,
            [key]: options,
          };
        } else {
          console.error(`加载${key}选项失败:`, res);
          selectOptions.value = {
            ...selectOptions.value,
            [key]: [],
          };
        }
      } catch (error) {
        console.error(`远程搜索${key}选项出错:`, error);
        selectOptions.value = {
          ...selectOptions.value,
          [key]: [],
        };
      } finally {
        remoteLoading.value[key] = false;
      }
    }, debounce);
  };

  // 新增：创建远程搜索Select
  const createRemoteSelect = (key: string, props: any) => {
    // 获取API配置
    const apiConfig = options.selectApis?.[key];
    if (!apiConfig || !apiConfig.remote) {
      return createSelectElement(key, props);
    }

    // 合并传入的props和远程搜索功能
    const selectProps = {
      ...props,
      filterOption: false, // 禁用本地过滤
      showSearch: true, // 启用搜索
      loading: remoteLoading.value[key] || false,
      options: selectOptions.value[key] || [],
      onSearch: (value: string) => handleRemoteSearch(key, value),
      onFocus: () => {
        // 聚焦时加载初始数据
        if (
          !selectOptions.value[key] ||
          selectOptions.value[key].length === 0
        ) {
          handleRemoteSearch(key, '');
        }
      },
    };

    return h(Select, selectProps);
  };

  // 提交表单
  const handleSubmit = async (values: T) => {
    // 新增：防抖处理
    if (submitDebounce.value) return;

    try {
      submitDebounce.value = true;
      loading.value = true;

      // 添加调试日志，不使用structuredClone避免克隆不可序列化的对象
      console.error('提交表单数据:', {
        isEdit: isEdit.value,
        currentId: currentId.value,
        formData: values,
      });

      let res;
      if (isEdit.value && currentId.value) {
        if (!options.update) throw new Error('未提供更新方法');
        res = await options.update(currentId.value, values);
      } else {
        if (!options.create) throw new Error('未提供创建方法');
        res = await options.create(values);
      }

      if (res.code !== 1) {
        throw new Error(res.msg || (isEdit.value ? '修改失败' : '创建失败'));
      }

      // message.success(isEdit.value ? '修改成功' : '创建成功');
      message[res.code === 1 ? 'success' : 'error'](
        res.msg || (isEdit.value ? '修改成功' : '创建成功'),
      );
      visible.value = false;

      // 确保在操作成功后调用 onSuccess
      if (options.onSuccess) {
        await options.onSuccess();
      }
    } catch (error) {
      // message.error(error instanceof Error ? error.message : '操作失败');
      console.warn(error);
      throw error; // 向上抛出错误
    } finally {
      loading.value = false;
      // 新增：延迟重置防抖锁
      setTimeout(() => {
        submitDebounce.value = false;
      }, 500);
    }
  };

  // 打开弹窗
  const show = async (id?: number) => {
    resetFormData();
    visible.value = true;

    // 加载Select选项数据
    await loadSelectOptions();

    if (id) {
      isEdit.value = true;
      currentId.value = id;
      if (options.getDetail) {
        return await loadDetail(id);
      }
    } else {
      isEdit.value = false;
      currentId.value = undefined;
    }
  };

  // 新增：条件渲染字段
  const renderField = (
    field: string,
    component: any,
    visibility?: (formData: any) => boolean,
  ) => {
    // 如果设置了显示条件且结果为false，则不渲染
    if (visibility && !visibility(formData)) {
      return null;
    }

    // 检查字段是否在全局可见性配置中且为不可见
    if (field in fieldVisibility && !fieldVisibility[field]) {
      return null;
    }

    return component;
  };

  // 渲染表单分组
  const renderFormGroups = (groups: FormGroup[]) => {
    // 新增：过滤不可见的表单分组
    return groups
      .filter((group) => !group.visible || group.visible(formData))
      .map((group) =>
        h('div', { class: 'px-6 mb-8' }, [
          h(
            'div',
            {
              class: 'text-base font-medium mb-5 pb-2.5 border-gray-100',
            },
            group.title,
          ),
          h(Row, { gutter: 24 }, () =>
            group.content(formData, selectOptions.value),
          ),
        ]),
      );
  };

  // 渲染全屏按钮
  const renderFullscreenButton = () => {
    if (options.fullscreenable === false) return null;
    return h(
      'div',
      {
        class: 'modal-fullscreen-btn cursor-pointer ml-4 flex items-center',
        onClick: (e) => {
          e.stopPropagation(); // 阻止事件冒泡
          toggleFullscreen();
        },
        style: 'font-size: 16px;',
      },
      [isFullscreen.value ? h(MdiFullscreenExit) : h(MdiFullscreen)],
    );
  };

  // 渲染弹窗
  const renderFormModal = (groups: FormGroup[]) => {
    // Modal组件配置
    const modalProps = {
      visible: visible.value,
      'onUpdate:visible': (val: boolean) => {
        if (!val) close();
      },
      title: h(
        'div',
        {
          class: 'text-base font-medium flex items-center modal-title',
          style: options.draggable ? 'cursor: move;' : '',
        },
        [h('span', {}, modalTitle.value), renderFullscreenButton()],
      ),
      width: isFullscreen.value ? '100%' : options.width || 900,
      confirmLoading: loading.value,
      maskClosable: false,
      destroyOnClose: true,
      centered: !isFullscreen.value,
      class: `common-form-modal ${isFullscreen.value ? FULLSCREEN_CLASS : ''} ${isDragging.value ? 'modal-dragging no-transition' : ''}`,
      bodyStyle: {
        padding: '12px 0',
        ...(isFullscreen.value
          ? {
              height: 'calc(100vh - 160px)',
              overflow: 'hidden',
            }
          : {}),
      },
      style: isFullscreen.value
        ? {
            top: '0',
            paddingBottom: '0',
            height: '100vh',
            maxHeight: '100vh',
            overflow: 'hidden',
            margin: '0',
          }
        : {},
      onOk: () =>
        formRef.value?.validate().then(() => {
          const formValues = {} as any;
          Object.keys(formData).forEach((key) => {
            formValues[key] = formData[key];
          });
          return handleSubmit(formValues as T);
        }),
      wrapClassName: `channel-form-modal-wrap ${isFullscreen.value ? FULLSCREEN_WRAP_CLASS : ''}`,
    };

    return h(Modal, modalProps, {
      default: () =>
        h(
          Spin,
          {
            spinning: isEdit.value && loading.value,
            tip: '加载中...',
            class: 'w-full h-full',
          },
          {
            default: () =>
              h(
                'div',
                {
                  class: `channel-form-container ${
                    isFullscreen.value
                      ? 'h-full overflow-y-auto'
                      : 'max-h-[calc(100vh-200px)] overflow-y-auto'
                  }`,
                  style: isFullscreen.value
                    ? 'height: calc(100vh - 160px); max-height: calc(100vh - 160px);'
                    : '',
                },
                h(
                  Form,
                  {
                    ref: formRef,
                    model: formData,
                    rules: formRules.value, // 新增：使用统一管理的验证规则
                    onFinish: (values) => {
                      const formValues = {} as any;
                      Object.keys(values).forEach((key) => {
                        formValues[key] = values[key];
                      });
                      return handleSubmit(formValues as T);
                    },
                    layout: 'horizontal',
                    class: 'py-4 channel-form',
                    labelCol: { span: 6 },
                    wrapperCol: { span: 16 },
                  },
                  () => renderFormGroups(groups),
                ),
              ),
          },
        ),
    });
  };

  // 添加CSS样式
  onMounted(() => {
    // 如果没有已经添加的样式，则添加
    if (!document.querySelector('#form-modal-fullscreen-styles')) {
      const styleElement = document.createElement('style');
      styleElement.id = 'form-modal-fullscreen-styles';
      styleElement.textContent = `
        .${FULLSCREEN_CLASS} {
          width: 100% !important;
          height: 100vh !important;
          top: 0 !important;
          padding: 0 !important;
          margin: 0 !important;
          max-width: 100% !important;
          transform: none !important;
        }

        .${FULLSCREEN_WRAP_CLASS} {
          overflow: hidden !important;
          display: flex !important;
          justify-content: center !important;
          align-items: flex-start !important;
        }

        .${FULLSCREEN_WRAP_CLASS} .ant-modal {
          padding: 0 !important;
          margin: 0 !important;
        }

        .${FULLSCREEN_CLASS} .ant-modal-content {
          height: 100vh !important;
          border-radius: 0 !important;
        }

        .${FULLSCREEN_CLASS} .channel-form-container {
          height: calc(100vh - 160px) !important;
          max-height: calc(100vh - 160px) !important;
          overflow-y: auto !important;
        }
      `;
      document.head.append(styleElement);
    }
  });

  return {
    formRef,
    formData,
    visible,
    loading,
    isEdit,
    currentId,
    show,
    close,
    setFormData,
    renderFormModal,
    handleSubmit,
    // 新增：导出Select相关功能
    selectOptions,
    selectLoading,
    loadSelectOptions,
    createSelectElement,
    // 新增：导出远程搜索相关功能
    createRemoteSelect,
    remoteLoading,
    searchKeywords,
    // 新增：导出全屏相关功能
    isFullscreen,
    toggleFullscreen,
    // 新增：拖拽相关
    isDragging,
    resetPosition,
    // 新增：表单验证规则相关
    formRules,
    setRules,
    addRule,
    // 新增：字段条件显示相关
    fieldVisibility,
    setFieldVisibility,
    renderField,
    // 新增：性能优化相关
    getCachedComponent,
    // 新增：表单原始数据相关
    originalFormData,
    saveFormData,
    resetToOriginal,
    isFormModified,
    clearFormData,
  };
}
