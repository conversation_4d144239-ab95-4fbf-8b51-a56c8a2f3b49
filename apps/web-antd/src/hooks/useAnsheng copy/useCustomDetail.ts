// src/hooks/useAnsheng/useCustomDetail.ts
import { defineComponent, h, reactive, ref } from 'vue';

import { Modal, Spin } from 'ant-design-vue';

/**
 * 自定义详情hook，复制原来DetailModal的样式和布局
 */
export function useCustomDetail() {
  // 可见性控制
  const visible = ref(false);
  // 加载状态
  const loading = ref(false);
  // 当前详情数据
  const detailData = reactive<Record<string, any>>({});
  // 标题
  const title = ref('详情');

  // 打开详情
  const open = (data: Record<string, any>, customTitle?: string) => {
    // 深拷贝数据
    const safeData = JSON.parse(JSON.stringify(data));
    // 先重置再设置，确保完全刷新
    Object.keys(detailData).forEach((key) => {
      delete detailData[key];
    });
    // 更新数据
    Object.assign(detailData, safeData);
    // 设置标题
    if (customTitle) {
      title.value = customTitle;
    }
    // 显示模态框
    visible.value = true;
  };

  // 关闭详情
  const close = () => {
    visible.value = false;
  };

  // 渲染详情模态框 - 保持原始DetailModal的布局和样式
  const renderDetailModal = (
    sections: Array<{
      items: Array<{
        bold?: boolean;
        field?: string;
        formatter?: (value: any) => string;
        label: string;
      }>;
      title: string;
    }>,
  ) => {
    return defineComponent({
      setup() {
        return () =>
          h(
            Modal,
            {
              visible: visible.value,
              title: title.value,
              width: '800px',
              footer: null,
              bodyStyle: { maxHeight: '80vh', overflow: 'auto', padding: 0 },
              onCancel: close,
              'onUpdate:visible': (val: boolean) => {
                visible.value = val;
              },
              destroyOnClose: false,
              maskClosable: false,
            },
            {
              default: () =>
                h('div', { class: 'min-h-[400px]' }, [
                  h(
                    Spin,
                    {
                      spinning: loading.value,
                      class: 'h-full',
                    },
                    {
                      default: () =>
                        h(
                          'div',
                          { class: 'px-6 py-4' },
                          sections.map((section, sIndex) => {
                            // 格式化section中的items
                            const formattedItems = section.items.map((item) => {
                              let value = '';
                              if (item.field) {
                                const rawValue = detailData[item.field];
                                if (item.formatter) {
                                  value = item.formatter(rawValue);
                                } else {
                                  // 基础格式化
                                  if (
                                    rawValue === undefined ||
                                    rawValue === null
                                  ) {
                                    value = '';
                                  } else if (Array.isArray(rawValue)) {
                                    value = rawValue.join(', ');
                                  } else {
                                    value = String(rawValue);
                                  }
                                }
                              }

                              return {
                                ...item,
                                value,
                              };
                            });

                            return h(
                              'div',
                              {
                                key: sIndex,
                                class:
                                  sIndex < sections.length - 1 ? 'mb-6' : '',
                              },
                              [
                                // Section标题
                                h(
                                  'div',
                                  {
                                    class:
                                      'mb-3 flex items-center text-base font-medium',
                                  },
                                  [
                                    h('div', {
                                      class: 'bg-primary mr-2 h-4 w-1 rounded',
                                    }),
                                    section.title,
                                  ],
                                ),
                                // Section内容
                                h(
                                  'div',
                                  {
                                    class:
                                      'grid gap-x-12 gap-y-3 rounded-lg p-4 md:grid-cols-2',
                                  },
                                  formattedItems.map((item, itemIndex) =>
                                    h(
                                      'div',
                                      {
                                        key: itemIndex,
                                        class: 'flex items-start py-2',
                                      },
                                      [
                                        h(
                                          'span',
                                          { class: 'w-20 flex-shrink-0' },
                                          `${item.label}：`,
                                        ),
                                        h(
                                          'span',
                                          {
                                            class: [
                                              'flex-1',
                                              item.bold ? 'font-medium' : '',
                                            ],
                                          },
                                          item.value,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            );
                          }),
                        ),
                    },
                  ),
                ]),
            },
          );
      },
    });
  };

  // 加载详情数据的帮助方法
  const loadDetail = async (
    id: number | string,
    getDetailApi: (id: number | string) => Promise<any>,
  ) => {
    try {
      loading.value = true;
      const res = await getDetailApi(id);
      if (res.code === 1 && res.data) {
        open(res.data);
        return res.data;
      }
      return null;
    } catch (error) {
      console.error('加载详情失败:', error);
      return null;
    } finally {
      loading.value = false;
    }
  };

  return {
    visible,
    loading,
    detailData,
    title,
    open,
    close,
    renderDetailModal,
    loadDetail,
  };
}
