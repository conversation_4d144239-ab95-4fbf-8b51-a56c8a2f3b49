# useAnsheng Hooks 使用文档

## 简介

`useAnsheng` 是一套高度封装的 Vue3 组合式 API 集合，主要用于简化后台管理系统中常见的 CRUD (增删改查) 操作。它集成了表格、表单、搜索、详情等功能，提供了一致性的界面和交互体验。

主要功能：
- 表格管理 (分页、排序、筛选)
- 表单处理 (新增、编辑)
- 搜索功能 (基础搜索、高级搜索)
- 详情展示
- 批量操作
- 内联编辑

## 基本用法

```typescript
import { useAnsheng } from '#/hooks/useAnsheng';

// 基本使用
const {
  // 状态和数据
  formData,          // 表单数据
  tableData,         // 表格数据
  loading,           // 加载状态
  pagination,        // 分页配置
  actionButtons,     // 操作按钮

  // 方法
  handleTableChange, // 表格变化处理函数
  initialize,        // 初始化函数
  createItem,        // 创建项目函数

  // 搜索相关
  searchToolbarBind, // 搜索工具栏绑定
  handleSearch,      // 搜索处理函数
  handleReset,       // 重置搜索处理函数

  // 子hook
  detail,            // 详情hook
  form,              // 表单hook
  table,             // 表格hook
} = useAnsheng({
  // 配置项...
});
```

## 配置项

### 表单配置 (formOptions)

```typescript
formOptions: {
  // 表单标题，可以是字符串或函数
  title: (isEdit: boolean) => isEdit ? '编辑' : '创建',
  
  // 表单宽度
  width: 700,
  
  // 表单默认值
  defaultValues: {
    name: '',
    age: 18,
    // ...其他字段
  },
  
  // 获取详情的API函数，用于编辑表单
  getDetail: (id: number) => Promise<any>,
  
  // 创建数据的API函数
  create: (data: any) => Promise<any>,
  
  // 更新数据的API函数
  update: (id: number, data: any) => Promise<any>,
  
  // 是否允许全屏
  fullscreenable: true,
  
  // 是否允许拖动
  draggable: true,
  
  // 成功回调
  onSuccess: () => void,
}
```

### 表格配置 (tableOptions)

```typescript
tableOptions: {
  // 获取列表数据的API
  api: (params: any) => Promise<{ rows: any[], total: number }>,
  
  // 默认每页显示数量
  defaultPageSize: 10,
  
  // 列定义
  columns: [
    { title: '姓名', dataIndex: 'name', width: 100 },
    { title: '年龄', dataIndex: 'age', width: 80 },
    // ...其他列
  ],
  
  // 可编辑列（支持内联编辑）
  editableColumns: ['name', 'age', 'email'],
  
  // 保存编辑数据的函数
  onSave: async (record, column, value) => Promise<void>,
  
  // 操作按钮配置
  actionButtons: [
    {
      key: 'view',
      text: '查看',
      type: 'primary',
      onClick: (record) => void,
      icon: IconComponent,
    },
    // ...其他按钮
  ],
}
```

### 搜索配置 (searchOptions)

```typescript
searchOptions: {
  // 基础搜索项
  basicItems: [
    {
      field: 'keyword',
      component: 'Input',
      label: '关键词',
      span: 6,
    },
    // ...其他搜索项
  ],
  
  // 自定义按钮
  customButtons: [
    {
      text: '新增',
      icon: IconComponent,
      type: 'primary',
      onClick: () => void,
    },
    // ...其他按钮
  ],
}
```

### 基本用法

```typescript
import { useCustomDetail } from '#/hooks/useAnsheng';

const {
  visible,
  detailData,
  open,
  close,
  renderDetailModal,
} = useCustomDetail();

// 创建详情组件
const detailComponent = renderDetailModal([
  {
    title: '基本信息',
    items: [
      { label: '姓名', field: 'name' },
      { label: '年龄', field: 'age' },
      { 
        label: '状态', 
        field: 'status',
        formatter: (value) => value === 1 ? '启用' : '禁用'
      },
    ]
  }
]);
  ```

### 详情配置 (detailOptions)

```typescript
detailOptions: {
  // 详情分段
  sections: [
    {
      title: '基本信息',
      items: [
        { label: '姓名', field: 'name', value: '' },
        { label: '年龄', field: 'age', value: '' },
        // ...其他项
      ],
    },
    // ...其他分段
  ],
}
```

## 组件使用

### 搜索工具栏 (SearchToolbar)

```vue
<SearchToolbar
  v-bind="searchToolbarBind"
  @search="handleSearch"
  @reset="handleReset"
/>
```

### 表格 (BasicTable)

```vue
<BasicTable
  :columns="table.columns"
  :data-source="tableData"
  :loading="loading"
  :pagination="pagination"
  :show-index="true"
  :show-action="true"
  :action-buttons="actionButtons"
  :show-selection="true"
  @selection-change="handleSelectionChange"
  @change="handleTableChange"
/>
```

### 详情模态框 (DetailModal)

```vue
<DetailModal
  v-if="detail"
  v-model:visible="detail.visible"
  :sections="detail.sections"
  :title="detail.title"
  :loading="loading"
/>
```

### 表单模态框（使用renderFormModal方法）

```vue
<script>
// 创建表单模态框组件
const formComponent = defineComponent({
  setup() {
    return () =>
      form.renderFormModal([
        {
          title: '基本信息',
          content: (formData) => [
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '姓名',
                  name: 'name',
                  rules: [{ required: true, message: '请输入姓名' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入姓名',
                    value: formData.name,
                    'onUpdate:value': (val) => (formData.name = val),
                  }),
              ),
            ]),
            // ...其他表单项
          ],
        },
        // ...其他表单分段
      ]);
  },
});
</script>

<template>
  <component :is="formComponent" />
</template>
```

## 高级特性

### 批量操作

```typescript
// 表格选择状态
const selectedRowKeys = ref<(number | string)[]>([]);
const selectedRows = ref<any[]>([]);

// 处理表格选择变化
const handleSelectionChange = (rowKeys: (number | string)[], rows: any[]) => {
  selectedRowKeys.value = rowKeys;
  selectedRows.value = rows;
};

// 批量操作处理
const handleBatchAction = (action: string) => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一条记录');
    return;
  }

  switch (action) {
    case 'delete':
      // 处理批量删除
      break;
    case 'disable':
      // 处理批量禁用
      break;
    case 'enable':
      // 处理批量启用
      break;
  }

  // 操作完成后刷新表格
  table.getList();
  // 清空选择
  selectedRowKeys.value = [];
  selectedRows.value = [];
};
```

### 内联编辑

表格配置中可以设置可编辑列，并提供保存处理函数：

```typescript
tableOptions: {
  // ...
  editableColumns: ['name', 'age', 'email'],
  onSave: async (record, column, value) => {
    try {
      await apiService.updateField(record.id, column, value);
    } catch (error) {
      return Promise.reject(error);
    }
  },
  // ...
}
```

### 两种表单编辑模式

1. 通过API获取详情编辑

```typescript
// 打开编辑表单并从API获取详情数据
form.show(recordId);
```

2. 直接传值编辑

```typescript
// 打开一个空表单
form.show();
// 使用saveFormData保存数据
form.saveFormData(recordData);
// 设置为编辑模式
form.isEdit.value = true;
form.currentId.value = recordData.id;
```

## 完整示例

```vue
<script setup lang="ts">
import { defineComponent, h, ref } from 'vue';
import { useAnsheng, BasicTable, DetailModal, SearchToolbar } from '#/hooks/useAnsheng';
import { Button, Input, FormItem, Col, Select } from 'ant-design-vue';

// 模拟API服务
const mockService = {
  getList: async (params) => {
    // 模拟获取列表数据
    return { 
      code: 1, 
      data: { 
        rows: [], 
        total: 0 
      } 
    };
  },
  getDetail: async (id) => {
    // 模拟获取详情
    return { 
      code: 1, 
      data: {} 
    };
  },
  create: async (data) => {
    // 模拟创建
    return { code: 1 };
  },
  update: async (id, data) => {
    // 模拟更新
    return { code: 1 };
  },
  delete: async (id) => {
    // 模拟删除
    return { code: 1 };
  }
};

// 表格选择状态
const selectedRowKeys = ref([]);
const selectedRows = ref([]);

// 处理表格选择变化
const handleSelectionChange = (rowKeys, rows) => {
  selectedRowKeys.value = rowKeys;
  selectedRows.value = rows;
};

// 使用useAnsheng hook
const {
  formData,
  tableData,
  loading,
  pagination,
  actionButtons,
  handleTableChange,
  initialize,
  createItem,
  searchToolbarBind,
  handleSearch,
  handleReset,
  detail,
  form,
  table,
} = useAnsheng({
  formOptions: {
    title: (isEdit) => isEdit ? '编辑数据' : '创建数据',
    width: 700,
    defaultValues: { name: '', age: 18 },
    getDetail: mockService.getDetail,
    create: mockService.create,
    update: mockService.update,
    fullscreenable: true,
    draggable: true,
    onSuccess: () => {
      table.getList();
    },
  },
  tableOptions: {
    api: mockService.getList,
    defaultPageSize: 10,
    columns: [
      { title: '姓名', dataIndex: 'name', width: 100 },
      { title: '年龄', dataIndex: 'age', width: 80 },
      {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        customRender: ({ text }) => (text === 1 ? '启用' : '禁用'),
      },
    ],
    editableColumns: ['name', 'age'],
    onSave: async (record, column, value) => {
      await mockService.update(record.id, { [column]: value });
    },
    actionButtons: [
      {
        key: 'view',
        text: '查看',
        onClick: (record) => detail.open(record),
      },
      {
        key: 'edit',
        text: '编辑',
        onClick: (record) => form.show(record.id),
      },
      {
        key: 'delete',
        text: '删除',
        onClick: async (record) => {
          await mockService.delete(record.id);
          table.getList();
        },
        popConfirm: '确定要删除此记录吗？',
      },
    ],
  },
  searchOptions: {
    basicItems: [
      {
        field: 'keyword',
        component: 'Input',
        label: '关键词',
        span: 6,
      },
      {
        field: 'status',
        component: 'Select',
        label: '状态',
        span: 6,
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    ],
    customButtons: [
      {
        text: '新增',
        type: 'primary',
        onClick: () => createItem(),
      },
    ],
  },
  detailOptions: {
    sections: [
      {
        title: '基本信息',
        items: [
          { label: '姓名', field: 'name', value: '' },
          { label: '年龄', field: 'age', value: '' },
          {
            label: '状态',
            field: 'status',
            value: '',
            formatter: (val) => (val === 1 ? '启用' : '禁用'),
          },
        ],
      },
    ],
  },
  deleteApi: mockService.delete,
});

// 创建表单模态框组件
const formComponent = defineComponent({
  setup() {
    return () =>
      form.renderFormModal([
        {
          title: '基本信息',
          content: (formData) => [
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '姓名',
                  name: 'name',
                  rules: [{ required: true, message: '请输入姓名' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入姓名',
                    value: formData.name,
                    'onUpdate:value': (val) => (formData.name = val),
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '年龄',
                  name: 'age',
                  rules: [{ required: true, message: '请输入年龄' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入年龄',
                    value: formData.age,
                    'onUpdate:value': (val) => (formData.age = Number(val)),
                    type: 'number',
                  }),
              ),
            ]),
          ],
        },
      ]);
  },
});

// 页面加载时初始化数据
onMounted(() => {
  initialize();
});
</script>

<template>
  <div class="page-container">
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-bind="searchToolbarBind"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 批量操作按钮 -->
    <div class="batch-actions" v-if="selectedRowKeys.length > 0">
      <div class="batch-info">已选择 {{ selectedRowKeys.length }} 项</div>
      <Button type="primary" @click="handleBatchAction('enable')">批量启用</Button>
      <Button @click="handleBatchAction('disable')">批量禁用</Button>
      <Button danger @click="handleBatchAction('delete')">批量删除</Button>
    </div>

    <!-- 表格 -->
    <BasicTable
      :columns="table.columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      :show-index="true"
      :show-action="true"
      :action-buttons="actionButtons"
      :show-selection="true"
      @selection-change="handleSelectionChange"
      @change="handleTableChange"
    />

    <!-- 表单弹窗 -->
    <component :is="formComponent" />

    <!-- 详情弹窗 -->
    <DetailModal
      v-if="detail"
      v-model:visible="detail.visible"
      :sections="detail.sections"
      :title="detail.title"
      :loading="loading"
    />
  </div>
</template>
```

## 子hook详解

### table

`table` 子hook提供表格相关的功能和属性：

```typescript
const { table } = useAnsheng();

// 表格数据
table.data          // 表格数据
table.columns       // 表格列配置
table.loading       // 加载状态
table.pagination    // 分页配置
table.searchParams  // 搜索参数

// 方法
table.getList()     // 获取列表数据
table.reset()       // 重置表格
```

### form

`form` 子hook提供表单相关的功能和属性：

```typescript
const { form } = useAnsheng();

// 表单状态
form.visible        // 表单可见性
form.loading        // 加载状态
form.isEdit         // 是否为编辑模式
form.currentId      // 当前编辑的记录ID
form.formData       // 表单数据

// 方法
form.show(id?)      // 显示表单，有id则为编辑模式，无id则为新建模式
form.hide()         // 隐藏表单
form.reset()        // 重置表单
form.saveFormData(data) // 保存表单数据
form.renderFormModal(sections) // 渲染表单模态框
```

### detail

`detail` 子hook提供详情相关的功能和属性：

```typescript
const { detail } = useAnsheng();

// 详情状态
detail.visible      // 详情可见性
detail.title        // 详情标题
detail.sections     // 详情分段
detail.record       // 当前记录

// 方法
detail.open(record) // 打开详情
detail.close()      // 关闭详情
```

## 注意事项

1. 使用前确保已经引入相关组件和样式
2. 在表格列配置中，可以使用`customRender`来自定义列渲染
3. 在使用`BasicTable`组件时，如需显示选择框，需设置`:show-selection="true"`
4. 对于需要显示的操作按钮，配置`actionButtons`中的`onClick`方法
5. 表单提交前会自动进行验证，配置的`rules`会被应用
6. 对于详情显示，会自动使用`formatter`格式化显示值 

## 虚拟滚动功能

对于大数据量表格，useAnsheng提供了虚拟滚动支持，可以显著提升表格的渲染性能。

### 配置方式

```typescript
import { useAnsheng } from '#/hooks/useAnsheng';

const {
  // 状态和数据
  tableData,
  loading,
  virtualConfig,
  tableBind,
  
  // 方法
  handleTableChange,
  handleVirtualScroll,
} = useAnsheng({
  tableOptions: {
    // 启用虚拟滚动
    virtualScroll: true,
    
    // 虚拟滚动配置
    virtualScrollOptions: {
      // 滚动区域高度
      scrollY: 400,
      // 单行高度
      itemHeight: 54,
      // 触发加载更多的阈值（距离底部多少像素时加载）
      threshold: 100,
    },
    
    // 其他表格配置
    // ...
  },
  // 其他配置
  // ...
});
```

### 使用示例

```vue
<template>
  <div class="table-container">
    <BasicTable v-bind="tableBind" @change="handleTableChange" />
  </div>
</template>

<script setup>
import { useAnsheng } from '#/hooks/useAnsheng';
import { BasicTable } from '#/components';

const {
  tableBind,
  handleTableChange,
  initialize,
} = useAnsheng({
  tableOptions: {
    api: yourApi,
    columns: yourColumns,
    virtualScroll: true,
    virtualScrollOptions: {
      scrollY: 500,
      itemHeight: 54,
    },
  },
  // 其他配置...
});

// 初始化数据
initialize();
</script>

<style scoped>
.table-container {
  height: 500px;
  overflow: hidden;
}
</style>
```

### 工作原理

虚拟滚动功能会：

1. 禁用传统的分页控件，改为自动加载更多数据
2. 当用户滚动到表格底部时，自动加载下一页数据并追加到现有数据中
3. 只渲染可视区域的数据，大幅减少DOM节点数量
4. 支持自定义滚动区域高度、行高和加载阈值

### 注意事项

- 使用虚拟滚动时，需要确保表格容器有明确的高度
- 表格行高应保持一致，以确保虚拟滚动的准确性
- 建议在数据量超过1000条时启用虚拟滚动
- 虚拟滚动模式下会自动禁用表格的常规分页控件

## 动态表单功能

useAnsheng提供了强大的动态表单功能，可以通过配置生成表单，支持字段联动、校验、JSON Schema等特性。

### 基本用法

```typescript
import { useDynamicForm } from '#/hooks/useAnsheng';

const {
  formData,
  formRef,
  renderDynamicForm,
  handleSubmit,
  resetForm,
} = useDynamicForm({
  groups: [
    {
      title: '基本信息',
      description: '请填写基本信息',
      fields: [
        {
          field: 'name',
          label: '姓名',
          type: 'input',
          required: true,
          placeholder: '请输入姓名',
        },
        {
          field: 'age',
          label: '年龄',
          type: 'number',
          required: true,
        },
        {
          field: 'gender',
          label: '性别',
          type: 'select',
          options: [
            { label: '男', value: 'male' },
            { label: '女', value: 'female' },
          ],
        },
      ],
    },
    {
      title: '其他信息',
      fields: [
        {
          field: 'remark',
          label: '备注',
          type: 'textarea',
          span: 2, // 占用2列
        },
      ],
    },
  ],
  defaultValues: {
    name: '',
    age: 18,
    gender: 'male',
    remark: '',
  },
  api: {
    create: async (data) => {
      // 创建数据的API
      console.log('创建数据', data);
      return { code: 1, msg: '创建成功' };
    },
    update: async (id, data) => {
      // 更新数据的API
      console.log('更新数据', id, data);
      return { code: 1, msg: '更新成功' };
    },
  },
  onSuccess: () => {
    // 提交成功后的回调
    console.log('提交成功');
  },
});
```

### 在模板中使用

```vue
<template>
  <div class="dynamic-form-container">
    <component :is="renderDynamicForm()" />
    
    <div class="form-actions">
      <a-button @click="resetForm">重置</a-button>
      <a-button type="primary" @click="handleSubmit">提交</a-button>
    </div>
  </div>
</template>
```

### 字段联动

```typescript
useDynamicForm({
  groups: [
    {
      title: '联动表单',
      fields: [
        {
          field: 'type',
          label: '类型',
          type: 'select',
          options: [
            { label: '个人', value: 'personal' },
            { label: '企业', value: 'company' },
          ],
        },
        {
          field: 'companyName',
          label: '企业名称',
          type: 'input',
          // 只有当类型为企业时才显示
          visible: (formData) => formData.type === 'company',
          // 依赖项
          dependencies: ['type'],
        },
      ],
    },
  ],
});
```

### JSON Schema支持

```typescript
// 从Schema加载表单配置
const { loadSchemaConfig, renderDynamicForm } = useDynamicForm({
  groups: [], // 初始为空，将从Schema加载
  api: {
    getSchemaConfig: async () => {
      // 获取Schema的API
      return {
        title: '用户信息',
        type: 'object',
        properties: {
          name: {
            type: 'string',
            title: '姓名',
            minLength: 2,
          },
          age: {
            type: 'integer',
            title: '年龄',
            minimum: 18,
          },
          email: {
            type: 'string',
            title: '邮箱',
            format: 'email',
          },
        },
        required: ['name', 'age'],
      };
    },
  },
});

// 组件挂载时加载Schema
onMounted(() => {
  loadSchemaConfig();
});
```

## 分步表单功能

useAnsheng还提供了分步表单功能，支持多步骤表单向导，包括步骤导航、验证、提交等功能。

### 基本用法

```typescript
import { useStepForm } from '#/hooks/useAnsheng';

const {
  formData,
  currentStep,
  isFirstStep,
  isLastStep,
  nextStep,
  prevStep,
  renderStepForm,
} = useStepForm({
  steps: [
    {
      title: '基本信息',
      description: '填写基本信息',
      fields: ['name', 'age', 'gender'], // 第一步包含的字段
      // 自定义验证
      validator: async (formData) => {
        if (!formData.name) {
          message.error('请填写姓名');
          return false;
        }
        return true;
      },
    },
    {
      title: '联系方式',
      description: '填写联系方式',
      fields: ['phone', 'email', 'address'], // 第二步包含的字段
    },
    {
      title: '其他信息',
      description: '填写其他信息',
      fields: ['remark'], // 第三步包含的字段
    },
  ],
  defaultValues: {
    name: '',
    age: 18,
    gender: 'male',
    phone: '',
    email: '',
    address: '',
    remark: '',
  },
  onFinish: async (values) => {
    // 提交表单
    console.log('表单提交', values);
    // 这里可以调用API提交数据
  },
});
```

### 在模板中使用

```vue
<template>
  <div class="step-form-container">
    <!-- 渲染分步表单 -->
    <component :is="renderStepForm(renderStepContent)" />
  </div>
</template>

<script setup>
// 渲染每个步骤的内容
const renderStepContent = (step) => {
  // 根据当前步骤渲染不同的表单内容
  switch (currentStep.value) {
    case 0:
      return h('div', {}, [
        // 第一步的表单内容
        h(FormItem, { label: '姓名', name: 'name' }, () =>
          h(Input, {
            value: formData.name,
            'onUpdate:value': (val) => (formData.name = val),
          }),
        ),
        // ...其他字段
      ]);
    case 1:
      return h('div', {}, [
        // 第二步的表单内容
      ]);
    case 2:
      return h('div', {}, [
        // 第三步的表单内容
      ]);
    default:
      return null;
  }
};
</script>
```

## 权限控制功能

useAnsheng提供了权限控制功能，可以根据用户权限控制表格列、表单字段和操作按钮的显示。

### 基本用法

```typescript
import { useAnsengPermission } from '#/hooks/useAnsheng';

const {
  tableData,
  loading,
  pagination,
  formData,
  createItem,
  editItem,
  deleteItem,
  permissions, // 权限相关方法和状态
} = useAnsengPermission({
  // 常规useAnsheng配置
  formOptions: {
    // ...
  },
  tableOptions: {
    // ...
  },
  // 权限配置
  permissionOptions: {
    // 模块权限编码
    moduleCode: 'user',
    // 操作权限映射
    actionPermissions: {
      create: 'user:create',
      update: 'user:update',
      delete: 'user:delete',
      read: 'user:read',
      export: 'user:export',
    },
    // 字段权限映射
    fieldPermissions: {
      name: 'user:field:name',
      phone: 'user:field:phone',
      email: 'user:field:email',
    },
    // 管理员是否拥有所有权限
    adminHasAllPermissions: true,
  },
});
```

### 在模板中使用

```vue
<template>
  <div class="container">
    <!-- 根据权限显示按钮 -->
    <a-button v-if="permissions.canCreate" @click="createItem">新增</a-button>
    
    <!-- 也可以使用showIf辅助方法 -->
    <a-button v-if="permissions.showIf('export')" @click="exportData">导出</a-button>
    
    <!-- 表格将自动过滤列和操作按钮 -->
    <BasicTable
      :columns="table.columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      :action-buttons="actionButtons"
      @change="handleTableChange"
    />
    
    <!-- 表单也会自动过滤字段 -->
    <component :is="formComponent" />
  </div>
</template>
```

### 权限控制的原理

1. **列权限**：自动过滤用户无权查看的表格列
2. **字段权限**：自动过滤用户无权编辑的表单字段
3. **操作权限**：根据用户权限过滤操作按钮
4. **管理员权限**：可配置管理员是否拥有所有权限

使用这种方式，可以轻松实现基于权限的CRUD页面，无需手动编写大量条件判断代码。
