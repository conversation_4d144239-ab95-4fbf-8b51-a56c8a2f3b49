import type { FormInstance } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

import type { SelectOption } from './types';

import { h, reactive, ref } from 'vue';

import { MdiCloudUpload } from '@vben/icons';

import {
  Checkbox,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Radio,
  Select,
  Switch,
  Upload,
} from 'ant-design-vue';

const FormItem = Form.Item;

// 表单字段配置接口
export interface FormFieldConfig {
  field: string;
  label: string;
  type:
    | 'checkbox'
    | 'custom'
    | 'date'
    | 'datetime'
    | 'input'
    | 'number'
    | 'radio'
    | 'select'
    | 'switch'
    | 'textarea'
    | 'upload';
  required?: boolean;
  options?: SelectOption[];
  rules?: Rule[];
  props?: Record<string, any>;
  span?: number;
  defaultValue?: any;
  placeholder?: string;
  visible?: (formData: any) => boolean;
  render?: (formData: any, field: FormFieldConfig) => any;
  disabled?: ((formData: any) => boolean) | boolean;
  tooltip?: string;
  help?: string;
  dependencies?: string[]; // 依赖的其他字段，当这些字段变化时重新计算visible
}

// 表单组配置
export interface DynamicFormGroup {
  title: string;
  description?: string;
  collapsed?: boolean;
  fields: (FormFieldConfig | string)[];
  visible?: (formData: any) => boolean;
}

// 动态表单配置
export interface DynamicFormOptions {
  groups: DynamicFormGroup[];
  defaultValues?: Record<string, any>;
  labelCol?: { span: number };
  wrapperCol?: { span: number };
  api?: {
    create?: (data: any) => Promise<any>;
    getFormConfig?: () => Promise<{ groups: DynamicFormGroup[] }>;
    getSchemaConfig?: () => Promise<any>; // JSON Schema
    update?: (id: number, data: any) => Promise<any>;
  };
  transformRequest?: (data: any) => any;
  transformResponse?: (data: any) => any;
  validateBeforeSubmit?: (formData: any) => Promise<boolean>;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * 动态表单生成hook
 * 支持从配置自动生成表单，包括字段联动、校验、提交等功能
 */
export function useDynamicForm(options: DynamicFormOptions) {
  // 表单引用和数据
  const formRef = ref<FormInstance>();
  const formData = reactive<Record<string, any>>(options.defaultValues || {});
  const formGroups = ref<DynamicFormGroup[]>(options.groups || []);
  const loading = ref(false);
  const submitting = ref(false);

  // 加载表单配置
  const loadFormConfig = async () => {
    if (!options.api?.getFormConfig) return;

    try {
      loading.value = true;
      const { groups } = await options.api.getFormConfig();
      formGroups.value = groups;
    } catch (error) {
      console.error('获取表单配置失败:', error);
      message.error('加载表单配置失败');
    } finally {
      loading.value = false;
    }
  };

  // 从JSON Schema加载表单配置
  const loadSchemaConfig = async () => {
    if (!options.api?.getSchemaConfig) return;

    try {
      loading.value = true;
      const schema = await options.api.getSchemaConfig();
      const groups = convertJsonSchemaToFormConfig(schema);
      formGroups.value = groups;
    } catch (error) {
      console.error('获取Schema配置失败:', error);
      message.error('加载表单配置失败');
    } finally {
      loading.value = false;
    }
  };

  // 转换JSON Schema为表单配置
  const convertJsonSchemaToFormConfig = (schema: any): DynamicFormGroup[] => {
    if (!schema || !schema.properties) return [];

    // 简单实现：将所有属性放在一个分组中
    const fields: FormFieldConfig[] = [];

    Object.keys(schema.properties).forEach((key) => {
      const property = schema.properties[key];
      const required = schema.required?.includes(key) || false;

      let fieldType: FormFieldConfig['type'] = 'input';

      // 根据JSON Schema类型映射组件类型
      switch (property.type) {
        case 'array': {
          fieldType = property.items?.enum ? 'checkbox' : 'custom';
          break;
        }
        case 'boolean': {
          fieldType = 'switch';
          break;
        }
        case 'integer':
        case 'number': {
          fieldType = 'number';
          break;
        }
        case 'string': {
          if (property.format === 'date-time') {
            fieldType = 'datetime';
          } else if (property.format === 'date') {
            fieldType = 'date';
          } else if (property.enum) {
            fieldType = 'select';
          } else if (property.maxLength && property.maxLength > 100) {
            fieldType = 'textarea';
          } else {
            fieldType = 'input';
          }
          break;
        }
        default: {
          fieldType = 'input';
        }
      }

      // 构建表单字段配置
      const fieldConfig: FormFieldConfig = {
        field: key,
        label: property.title || key,
        type: fieldType,
        required,
        placeholder: property.description,
        defaultValue: property.default,
        props: {},
      };

      // 处理验证规则
      const rules: Rule[] = [];

      if (required) {
        rules.push({
          required: true,
          message: `请${fieldType === 'select' ? '选择' : '输入'}${fieldConfig.label}`,
        });
      }

      if (property.minLength) {
        rules.push({
          min: property.minLength,
          message: `最小长度为${property.minLength}`,
        });
      }

      if (property.maxLength) {
        rules.push({
          max: property.maxLength,
          message: `最大长度为${property.maxLength}`,
        });
      }

      if (property.pattern) {
        rules.push({
          pattern: new RegExp(property.pattern),
          message: property.description || '格式不正确',
        });
      }

      if (rules.length > 0) {
        fieldConfig.rules = rules;
      }

      // 处理选项
      if (property.enum && property.enumNames) {
        fieldConfig.options = property.enum.map(
          (value: any, index: number) => ({
            label: property.enumNames[index],
            value,
          }),
        );
      }

      fields.push(fieldConfig);
    });

    return [
      {
        title: schema.title || '表单数据',
        description: schema.description,
        fields,
      },
    ];
  };

  // 渲染表单字段
  const renderFormField = (field: FormFieldConfig) => {
    // 检查字段可见性
    if (field.visible && !field.visible(formData)) {
      return null;
    }

    // 检查禁用状态
    const isDisabled =
      typeof field.disabled === 'function'
        ? field.disabled(formData)
        : !!field.disabled;

    // 使用自定义渲染函数
    if (field.render) {
      return field.render(formData, field);
    }

    // 根据字段类型渲染不同组件
    switch (field.type) {
      case 'checkbox': {
        return h(Checkbox.Group, {
          value: formData[field.field],
          disabled: isDisabled,
          options: field.options || [],
          'onUpdate:value': (val) => (formData[field.field] = val),
          ...field.props,
        });
      }
      case 'date': {
        return h(DatePicker, {
          placeholder: field.placeholder || `请选择${field.label}`,
          value: formData[field.field],
          disabled: isDisabled,
          'onUpdate:value': (val) => (formData[field.field] = val),
          style: { width: '100%' },
          ...field.props,
        });
      }
      case 'datetime': {
        return h(DatePicker, {
          placeholder: field.placeholder || `请选择${field.label}`,
          value: formData[field.field],
          disabled: isDisabled,
          'onUpdate:value': (val) => (formData[field.field] = val),
          showTime: true,
          style: { width: '100%' },
          ...field.props,
        });
      }
      case 'input': {
        return h(Input, {
          placeholder: field.placeholder || `请输入${field.label}`,
          value: formData[field.field],
          disabled: isDisabled,
          'onUpdate:value': (val) => (formData[field.field] = val),
          ...field.props,
        });
      }
      case 'number': {
        return h(InputNumber, {
          placeholder: field.placeholder || `请输入${field.label}`,
          value: formData[field.field],
          disabled: isDisabled,
          'onUpdate:value': (val) => (formData[field.field] = val),
          style: { width: '100%' },
          ...field.props,
        });
      }
      case 'radio': {
        return h(Radio.Group, {
          value: formData[field.field],
          disabled: isDisabled,
          options: field.options || [],
          'onUpdate:value': (val) => (formData[field.field] = val),
          ...field.props,
        });
      }
      case 'select': {
        return h(Select, {
          placeholder: field.placeholder || `请选择${field.label}`,
          value: formData[field.field],
          disabled: isDisabled,
          options: field.options || [],
          'onUpdate:value': (val) => (formData[field.field] = val),
          ...field.props,
        });
      }
      case 'switch': {
        return h(Switch, {
          checked: formData[field.field],
          disabled: isDisabled,
          'onUpdate:checked': (val) => (formData[field.field] = val),
          ...field.props,
        });
      }
      case 'textarea': {
        return h(Input.TextArea, {
          placeholder: field.placeholder || `请输入${field.label}`,
          value: formData[field.field],
          disabled: isDisabled,
          'onUpdate:value': (val) => (formData[field.field] = val),
          rows: 4,
          ...field.props,
        });
      }
      case 'upload': {
        return h(
          Upload,
          {
            value: formData[field.field],
            disabled: isDisabled,
            'onUpdate:value': (val) => (formData[field.field] = val),
            ...field.props,
          },
          {
            default: () => [
              h(
                Button,
                {
                  type: 'dashed',
                  block: true,
                },
                {
                  default: () => [h(MdiCloudUpload), `上传${field.label}`],
                },
              ),
            ],
          },
        );
      }
      default: {
        return h(
          'div',
          { class: 'text-gray-400' },
          `不支持的字段类型: ${field.type}`,
        );
      }
    }
  };

  // 渲染表单项
  const renderFormItem = (field: FormFieldConfig) => {
    // 检查字段可见性
    if (field.visible && !field.visible(formData)) {
      return null;
    }

    return h(
      FormItem,
      {
        label: field.label,
        name: field.field,
        rules: field.rules,
        tooltip: field.tooltip,
        help: field.help,
      },
      () => renderFormField(field),
    );
  };

  // 渲染表单分组
  const renderFormGroup = (group: DynamicFormGroup) => {
    // 检查分组可见性
    if (group.visible && !group.visible(formData)) {
      return null;
    }

    // 处理字段列表，支持字符串引用和直接配置对象
    const fields = group.fields
      .map((field) => {
        if (typeof field === 'string') {
          // 查找字段配置
          const allFields = formGroups.value.flatMap((g) => g.fields);
          const fieldConfig = allFields.find(
            (f) => typeof f !== 'string' && f.field === field,
          );
          return fieldConfig as FormFieldConfig;
        }
        return field as FormFieldConfig;
      })
      .filter(Boolean);

    return h('div', { class: 'form-group mb-6' }, [
      // 分组标题
      h(
        'div',
        { class: 'text-base font-medium mb-4 pb-2 border-b border-gray-100' },
        group.title,
      ),

      // 分组描述
      group.description
        ? h('div', { class: 'text-sm text-gray-500 mb-4' }, group.description)
        : null,

      // 分组字段
      h(
        'div',
        { class: 'grid grid-cols-2 gap-4' },
        fields.map((field) => {
          if (!field) return null;

          return h(
            'div',
            {
              class: `col-span-${field.span || 1}`,
              style: {
                gridColumn: field.span ? `span ${field.span}` : undefined,
              },
            },
            [renderFormItem(field)],
          );
        }),
      ),
    ]);
  };

  // 渲染完整表单
  const renderDynamicForm = () => {
    return h(
      Form,
      {
        ref: formRef,
        model: formData,
        labelCol: options.labelCol || { span: 8 },
        wrapperCol: options.wrapperCol || { span: 16 },
        onFinish: handleSubmit,
      },
      {
        default: () => formGroups.value.map((group) => renderFormGroup(group)),
      },
    );
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      submitting.value = true;

      // 表单验证
      await formRef.value?.validate();

      // 自定义验证
      if (options.validateBeforeSubmit) {
        const isValid = await options.validateBeforeSubmit(formData);
        if (!isValid) return;
      }

      // 转换请求数据
      const requestData = options.transformRequest
        ? options.transformRequest(formData)
        : { ...formData };

      // 调用API
      if (options.api?.create) {
        await options.api.create(requestData);
        message.success('创建成功');
      } else if (options.api?.update && formData.id) {
        await options.api.update(formData.id, requestData);
        message.success('更新成功');
      }

      // 调用成功回调
      options.onSuccess?.();
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error('表单提交失败');
    } finally {
      submitting.value = false;
    }
  };

  // 重置表单
  const resetForm = () => {
    formRef.value?.resetFields();

    // 重置为默认值
    Object.keys(formData).forEach((key) => {
      delete formData[key];
    });

    if (options.defaultValues) {
      Object.assign(formData, options.defaultValues);
    }
  };

  // 初始化
  return {
    formRef,
    formData,
    formGroups,
    loading,
    submitting,

    // 方法
    loadFormConfig,
    loadSchemaConfig,
    renderFormField,
    renderFormItem,
    renderFormGroup,
    renderDynamicForm,
    handleSubmit,
    resetForm,

    // 工具方法
    setFormData: (data: Record<string, any>) => {
      Object.assign(formData, data);
    },

    // 表单验证
    validate: async () => {
      try {
        await formRef.value?.validate();
        return true;
      } catch {
        return false;
      }
    },

    // 获取表单数据
    getFormData: () => ({ ...formData }),
  };
}
