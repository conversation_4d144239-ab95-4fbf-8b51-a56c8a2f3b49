import type { FormInstance } from 'ant-design-vue';

import type { Ref } from 'vue';

import type { FormRules } from './types';

import { computed, reactive, ref } from 'vue';

import { message } from 'ant-design-vue';

import { deepClone } from './utils';

/**
 * 表单状态管理hook
 * 处理表单数据的状态管理、验证规则和数据操作
 */
export function useFormState<T extends Record<string, any>>(
  options: {
    defaultValues?: Partial<T>;
    rules?: FormRules;
  } = {},
): {
  addRule: (field: string, rule: any | any[]) => void;
  clearFormData: () => void;
  formData: T;
  formRef: Ref<FormInstance | undefined>;
  formRules: Ref<FormRules>;
  isModified: Ref<boolean>;
  originalData: Ref<Partial<T>>;
  resetForm: () => void;
  resetToOriginal: () => void;
  saveFormData: (data: Partial<T>) => void;
  setFormData: (data: Partial<T>) => void;
  setRules: (rules: FormRules) => void;
  validate: () => Promise<boolean>;
} {
  // 表单引用和验证规则
  const formRef = ref<FormInstance>();
  const formRules = ref<FormRules>(options.rules || {});

  // 表单数据
  const formData = reactive<T>({} as T);

  // 原始表单数据，用于比较是否有修改
  const originalData = ref<Partial<T>>({});

  // 表单是否被修改
  const isModified = computed(() => {
    if (Object.keys(originalData.value).length === 0) return false;

    // 通过JSON比较检查修改，性能比deepEqual好
    return JSON.stringify(formData) !== JSON.stringify(originalData.value);
  });

  // 设置表单验证规则
  const setRules = (rules: FormRules) => {
    formRules.value = rules;
  };

  // 添加单个字段验证规则
  const addRule = (field: string, rule: any | any[]) => {
    const rules = Array.isArray(rule) ? rule : [rule];
    if (!formRules.value[field]) {
      formRules.value[field] = [];
    }
    formRules.value[field].push(...rules);
  };

  // 重置表单数据
  const resetForm = () => {
    const defaultValues = options.defaultValues || {};
    Object.keys(formData).forEach((key) => {
      formData[key] = undefined;
    });
    Object.assign(formData, defaultValues);
  };

  // 设置表单数据
  const setFormData = (data: Partial<T>) => {
    resetForm();
    Object.assign(formData, data);
  };

  // 保存表单数据并记录原始值
  const saveFormData = (data: Partial<T>) => {
    // 清除现有数据但保留默认值
    resetForm();
    // 设置新数据
    Object.assign(formData, data);
    // 记录原始数据（深拷贝）
    originalData.value = deepClone(data);
  };

  // 重置为原始数据
  const resetToOriginal = () => {
    if (originalData.value && Object.keys(originalData.value).length > 0) {
      // 深拷贝原始数据到表单
      Object.assign(formData, deepClone(originalData.value));
    }
  };

  // 清除表单数据（保留默认值）
  const clearFormData = () => {
    resetForm();
    // 清空原始数据记录
    originalData.value = {};
  };

  // 验证表单
  const validate = async (): Promise<boolean> => {
    if (!formRef.value) {
      message.error('表单实例不存在');
      return false;
    }

    try {
      await formRef.value.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  };

  return {
    formRef,
    formData,
    originalData,
    isModified,
    formRules,
    setRules,
    addRule,
    resetForm,
    setFormData,
    saveFormData,
    resetToOriginal,
    clearFormData,
    validate,
  };
}
