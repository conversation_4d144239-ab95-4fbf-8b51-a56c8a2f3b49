import type { UseAnsengOptions } from './types';

import { computed, ref } from 'vue';

import { message } from 'ant-design-vue';

import { useUserPermission } from '#/hooks/useUserPermission';

import { useAnsheng } from './index';

/**
 * useAnsengPermission
 * 集成useUserPermission与useAnsheng hooks，提供权限控制功能
 */
export function useAnsengPermission<
  T extends Record<string, any>,
  P = Record<string, any>,
>(
  options: {
    // 权限配置
    permissionOptions?: {
      // 操作权限映射，例如：{ edit: 'user:update' }
      actionPermissions?: Record<string, string>;
      // 管理员是否拥有所有权限
      adminHasAllPermissions?: boolean;
      // 字段权限映射，例如：{ name: 'user:read:name' }
      fieldPermissions?: Record<string, string>;
      // 自定义权限检查函数
      hasPermission?: (permissionCode: string) => boolean;
      // 自定义管理员状态
      isAdmin?: boolean | { value: boolean };
      // 模块权限编码，用于检查权限
      moduleCode?: string;
    };
  } & UseAnsengOptions<T, P>,
) {
  // 获取用户权限信息
  const userPermission = useUserPermission();

  // 获取权限配置
  const permOptions = options.permissionOptions || {};
  const moduleCode = permOptions.moduleCode || '';
  const actionPermMap = permOptions.actionPermissions || {};
  const fieldPermMap = permOptions.fieldPermissions || {};
  const adminHasAll = permOptions.adminHasAllPermissions !== false; // 默认为true

  // 使用自定义的权限检查函数或默认的权限检查函数
  const hasPermission =
    permOptions.hasPermission || userPermission.hasPermission;

  // 使用自定义的管理员状态或默认的管理员状态
  const isAdmin =
    permOptions.isAdmin === undefined
      ? userPermission.isAdmin
      : typeof permOptions.isAdmin === 'boolean'
        ? ref(permOptions.isAdmin)
        : permOptions.isAdmin;

  // 检查操作权限
  const checkActionPermission = (action: string): boolean => {
    // 管理员默认拥有所有权限
    if (
      adminHasAll &&
      (typeof isAdmin === 'boolean' ? isAdmin : isAdmin.value)
    ) {
      return true;
    }

    // 检查权限映射
    const permissionCode = actionPermMap[action] || `${moduleCode}:${action}`;
    return hasPermission(permissionCode);
  };

  // 检查字段权限
  const checkFieldPermission = (
    field: string,
    action: 'read' | 'write' = 'read',
  ): boolean => {
    // 管理员默认拥有所有权限
    if (
      adminHasAll &&
      (typeof isAdmin === 'boolean' ? isAdmin : isAdmin.value)
    ) {
      return true;
    }

    // 检查权限映射
    const permissionCode =
      fieldPermMap[field] || `${moduleCode}:${action}:${field}`;
    return hasPermission(permissionCode);
  };

  // 过滤操作按钮
  const filterActionButtons = (buttons: any[]) => {
    if (!buttons) return [];

    return buttons.filter((btn) => {
      // 获取按钮对应的操作
      const actionMap: Record<string, string> = {
        edit: 'update',
        update: 'update',
        delete: 'delete',
        view: 'read',
        detail: 'read',
        create: 'create',
        add: 'create',
        export: 'export',
        import: 'import',
      };

      const action = actionMap[btn.key] || btn.key;
      return checkActionPermission(action);
    });
  };

  // 过滤表格列
  const filterTableColumns = (columns: any[]) => {
    if (!columns) return [];

    return columns.filter((col) => {
      // 操作列总是显示
      if (!col.dataIndex || col.dataIndex === 'action') {
        return true;
      }

      return checkFieldPermission(col.dataIndex, 'read');
    });
  };

  // 修改配置选项，添加权限控制
  const enhancedOptions: UseAnsengOptions<T, P> = {
    ...options,
    tableOptions: {
      ...options.tableOptions,
      // 过滤操作按钮
      actionButtons: filterActionButtons(
        options.tableOptions.actionButtons || [],
      ),
      // 过滤表格列
      columns: filterTableColumns(options.tableOptions.columns || []),
    },
    searchOptions: options.searchOptions && {
      ...options.searchOptions,
      // 过滤自定义按钮
      customButtons: (options.searchOptions.customButtons || []).filter(
        (btn) => {
          // 新增按钮需要create权限
          if (btn.text.includes('新增') || btn.text.includes('创建')) {
            return checkActionPermission('create');
          }

          if (btn.text.includes('导出')) {
            return checkActionPermission('export');
          }

          if (btn.text.includes('导入')) {
            return checkActionPermission('import');
          }

          return true;
        },
      ),
    },
  };

  // 使用修改后的配置创建useAnsheng实例
  const ansheng = useAnsheng<T, P>(enhancedOptions);

  // 检查各种权限
  const canCreate = computed(() => checkActionPermission('create'));
  const canRead = computed(() => checkActionPermission('read'));
  const canUpdate = computed(() => checkActionPermission('update'));
  const canDelete = computed(() => checkActionPermission('delete'));
  const canExport = computed(() => checkActionPermission('export'));
  const canImport = computed(() => checkActionPermission('import'));

  // 增强的createItem，添加权限检查
  const createItem = () => {
    if (canCreate.value) {
      ansheng.createItem();
    } else {
      message.error('您没有创建权限');
    }
  };

  // 增强的deleteItem，添加权限检查
  const deleteItem = async (id: number) => {
    if (canDelete.value) {
      await ansheng.deleteItem(id);
    } else {
      message.error('您没有删除权限');
    }
  };

  // 增强的editItem，添加权限检查
  const editItem = (id: number) => {
    if (canUpdate.value) {
      ansheng.editItem(id);
    } else {
      message.error('您没有编辑权限');
    }
  };

  // 增强的viewItem，添加权限检查
  const viewItem = (record: T) => {
    if (canRead.value) {
      ansheng.viewItem(record);
    } else {
      message.error('您没有查看权限');
    }
  };

  // 过滤表单字段，移除无权限的字段
  const filterFormGroups = (groups: any[]) => {
    return groups.map((group) => {
      if (group.content && typeof group.content === 'function') {
        // 原始内容渲染函数
        const originalContent = group.content;

        // 增强内容渲染函数，过滤字段
        group.content = (formData: any, selectOptions?: any) => {
          // 调用原始渲染函数获取字段
          const items = originalContent(formData, selectOptions);

          // 过滤没有写权限的字段
          return items.filter((item: any) => {
            // 获取字段名称
            const fieldName = item.props?.name;
            if (!fieldName) return true;

            return checkFieldPermission(fieldName, 'write');
          });
        };
      }

      return group;
    });
  };

  // 增强form.renderFormModal，添加权限过滤
  const originalRenderFormModal = ansheng.form.renderFormModal;
  ansheng.form.renderFormModal = (groups: any[]) => {
    // 过滤表单字段
    const filteredGroups = filterFormGroups(groups);
    return originalRenderFormModal(filteredGroups);
  };

  return {
    ...ansheng,
    // 覆盖方法，添加权限检查
    createItem,
    deleteItem,
    editItem,
    viewItem,

    // 权限相关方法
    permissions: {
      isAdmin: typeof isAdmin === 'boolean' ? isAdmin : isAdmin.value,
      userPermission,
      canCreate,
      canRead,
      canUpdate,
      canDelete,
      canExport,
      canImport,
      checkActionPermission,
      checkFieldPermission,
      showIf: (action: string) => checkActionPermission(action),
      hideIf: (action: string) => !checkActionPermission(action),
    },
  };
}
