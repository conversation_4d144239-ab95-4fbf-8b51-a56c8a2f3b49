// useAnsheng/useForm.ts

import type { ApiResponse, FormGroup, UseFormOptions } from './types';

import { computed, h, markRaw, onMounted, ref, watch } from 'vue';

import { MdiFullscreen, MdiFullscreenExit } from '@vben/icons';

import { Form, message, Modal, Row, Spin } from 'ant-design-vue';

import { FULLSCREEN_CLASS, FULLSCREEN_WRAP_CLASS } from './types';
import { useFieldVisibility } from './useFieldVisibility';
import { useFormState } from './useFormState';
import { useModalInteractions } from './useModalInteractions';
import { useSelectOptions } from './useSelectOptions';
import { asyncOnce, handleError, preventMultiSubmit } from './utils';

/**
 * 表单弹窗管理Hook
 * 提供完整的表单弹窗解决方案，包括表单状态管理、模态框交互、选项加载等功能
 */
export function useForm<T extends Record<string, any>>(
  options: UseFormOptions<T> = {},
) {
  // 弹窗可见性和加载状态
  const visible = ref(false);
  const loading = ref(false);

  // 编辑状态和当前ID
  const isEdit = ref(false);
  const currentId = ref<number>();

  // 防抖锁
  const submitDebounce = ref(false);

  // 组件缓存
  const cachedComponents = ref(new Map());

  // 使用表单状态管理
  const {
    formRef,
    formData,
    formRules,
    setRules,
    addRule,
    resetForm,
    setFormData,
    saveFormData,
    resetToOriginal,
    isModified,
    clearFormData,
    validate,
  } = useFormState<T>(options);

  // 使用模态框交互管理
  const {
    isFullscreen,
    isDragging,
    toggleFullscreen,
    resetPosition,
    initModal,
  } = useModalInteractions({
    fullscreenable: options.fullscreenable,
    draggable: options.draggable,
    width: options.width,
  });

  // 使用选项数据管理
  const {
    selectOptions,
    selectLoading,
    remoteLoading,
    searchKeywords,
    loadSelectOptions,
    handleRemoteSearch,
    createSelectElement,
    createRemoteSelect,
  } = useSelectOptions(options.selectApis);

  // 使用字段可见性管理
  const {
    fieldVisibility,
    setFieldVisibility,
    updateAllFieldVisibility,
    renderField,
  } = useFieldVisibility(formData);

  // 监听modal打开状态，初始化DOM引用
  watch(
    () => visible.value,
    (v) => {
      initModal(v);

      if (!v) {
        // 弹窗关闭时，重置全屏状态
        isFullscreen.value = false;
      }
    },
  );

  // 关闭弹窗
  const close = () => {
    visible.value = false;
    isEdit.value = false;
    currentId.value = undefined;
    // 重置表单数据
    resetForm();
    // 清除验证
    formRef.value?.clearValidate();
    // 清除原始数据记录
    clearFormData();
    options.onCancel?.();
  };

  // 加载详情的优化版本，使用asyncOnce确保不会并发调用
  const loadDetail = asyncOnce(async (id: number) => {
    if (!options.getDetail) return;

    try {
      loading.value = true;
      const res = await options.getDetail(id);
      if (res.code === 1 && res.data) {
        saveFormData(res.data);
        return res.data;
      }
      throw new Error(res.msg || '获取详情失败');
    } catch (error) {
      const formattedError = handleError(error, '加载失败');
      message.error(formattedError.message);
      close();
    } finally {
      loading.value = false;
    }
  });

  // 表单提交处理
  const handleSubmit = preventMultiSubmit(async (values: T) => {
    if (submitDebounce.value) return;

    try {
      submitDebounce.value = true;
      loading.value = true;

      let res: ApiResponse;
      let successMsg = '';

      if (isEdit.value && currentId.value) {
        if (!options.update) throw new Error('未提供更新方法');

        res = await options.update(currentId.value, values);
        successMsg = '修改成功';
      } else {
        if (!options.create) throw new Error('未提供创建方法');

        res = await options.create(values);
        successMsg = '创建成功';
      }

      if (res.code !== 1) {
        throw new Error(res.msg || (isEdit.value ? '修改失败' : '创建失败'));
      }

      message.success(res.msg || successMsg);
      visible.value = false;

      // 确保在操作成功后调用 onSuccess
      if (options.onSuccess) {
        await options.onSuccess();
      }
    } catch (error) {
      const formattedError = handleError(
        error,
        isEdit.value ? '修改失败' : '创建失败',
      );
      message.error(formattedError.message);
      throw formattedError; // 向上抛出错误
    } finally {
      loading.value = false;
      // 延迟重置防抖锁
      setTimeout(() => {
        submitDebounce.value = false;
      }, 500);
    }
  });

  // 打开弹窗
  const show = async (idOrRecord?: number | Partial<T>) => {
    resetForm();
    visible.value = true;

    // 加载Select选项数据
    await loadSelectOptions();

    if (idOrRecord === undefined) {
      // 处理新增情况
      isEdit.value = false;
      currentId.value = undefined;
    } else {
      isEdit.value = true;

      // 处理编辑情况
      if (typeof idOrRecord === 'object') {
        // 如果传入的是对象，直接使用
        const record = idOrRecord as Partial<T>;
        currentId.value = (record as any).id; // 保存ID用于后续更新操作
        saveFormData(record as T);
      } else {
        // 如果传入的是ID，通过getDetail获取数据
        currentId.value = idOrRecord;
        if (options.getDetail) {
          return await loadDetail(idOrRecord);
        }
      }
    }
  };

  // 创建组件缓存
  const getCachedComponent = (key: string, creator: () => any) => {
    if (!cachedComponents.value.has(key)) {
      cachedComponents.value.set(key, markRaw(creator()));
    }
    return cachedComponents.value.get(key);
  };

  // 计算标题
  const modalTitle = computed(() =>
    typeof options.title === 'function'
      ? options.title(isEdit.value)
      : options.title || (isEdit.value ? '编辑' : '新增'),
  );

  // 渲染表单分组 - 增加简单布局支持
  const renderFormGroups = (groups: FormGroup[]) => {
    // 解析简洁布局选项
    const simpleLayoutOptions =
      typeof options.simpleLayout === 'object' ? options.simpleLayout : {};
    // 是否使用简单布局
    // const useSimpleLayout = options.simpleLayout === true;
    const useSimpleLayout = Boolean(options.simpleLayout);

    // 是否显示分组标题 (默认在简洁模式下不显示)
    const showGroupTitle = simpleLayoutOptions.showGroupTitle !== false;

    // 内边距和间距
    const padding =
      simpleLayoutOptions.padding === undefined
        ? useSimpleLayout
          ? 12
          : 24
        : simpleLayoutOptions.padding; // 简洁模式默认使用更小的内边距

    const itemMargin =
      simpleLayoutOptions.itemMargin === undefined
        ? useSimpleLayout
          ? 16
          : 24
        : simpleLayoutOptions.itemMargin; // 简洁模式默认使用更小的间距

    // 自定义容器类名
    const containerClass = simpleLayoutOptions.containerClass || '';

    // 过滤不可见的表单分组
    return groups
      .filter((group) => !group.visible || group.visible(formData))
      .map((group) => {
        // 简单布局的实现
        if (useSimpleLayout) {
          return h(
            'div',
            {
              class: `simple-form-section ${containerClass}`,
              style: `padding: 0 ${padding}px; margin-bottom: ${itemMargin}px;`,
            },
            [
              // 条件性渲染标题
              ...(showGroupTitle && group.title
                ? [
                    h(
                      'div',
                      {
                        class:
                          'text-sm font-medium mb-3 pb-1 border-b border-gray-100',
                      },
                      group.title,
                    ),
                  ]
                : []),
              // 简洁布局直接使用div包裹，不使用Row和Col
              h(
                'div',
                {
                  class: 'simple-form-content',
                  style: `margin-bottom: ${itemMargin}px;`,
                },
                group.content(formData, selectOptions.value),
              ),
            ],
          );
        }

        // 标准布局的实现（原有实现）
        return h('div', { class: 'px-6 mb-8' }, [
          h(
            'div',
            {
              class: 'text-base font-medium mb-5 pb-2.5 border-gray-100',
            },
            group.title,
          ),
          h(Row, { gutter: 24 }, () =>
            group.content(formData, selectOptions.value),
          ),
        ]);
      });
  };

  // 渲染全屏按钮
  const renderFullscreenButton = () => {
    if (options.fullscreenable === false) return null;
    return h(
      'div',
      {
        class: 'modal-fullscreen-btn cursor-pointer ml-4 flex items-center',
        onClick: (e: Event) => {
          e.stopPropagation(); // 阻止事件冒泡
          toggleFullscreen();
        },
        style: 'font-size: 16px;',
      },
      [isFullscreen.value ? h(MdiFullscreenExit) : h(MdiFullscreen)],
    );
  };

  // 渲染弹窗 - 进一步优化简洁模式
  const renderFormModal = (groups: FormGroup[]) => {
    // 解析简洁布局选项
    const simpleLayoutOptions =
      typeof options.simpleLayout === 'object' ? options.simpleLayout : {};

    // 是否使用简洁布局
    const useSimpleLayout = Boolean(options.simpleLayout);

    // 获取表单布局配置
    const formLayout = useSimpleLayout
      ? {
          layout: 'vertical',
          labelCol: { span: 24 }, // 确保标签占满整行
          wrapperCol: { span: 24 }, // 确保控件占满整行
        }
      : {
          layout: 'horizontal',
          labelCol: { span: 6 },
          wrapperCol: { span: 16 },
        };

    // Modal组件配置
    const modalProps = {
      visible: visible.value,
      'onUpdate:visible': (val: boolean) => {
        if (!val) close();
      },
      title: useSimpleLayout
        ? modalTitle.value // 简洁模式使用更简单的标题
        : h(
            'div',
            {
              class: 'text-base font-medium flex items-center modal-title',
              style: options.draggable ? 'cursor: move;' : '',
            },
            [h('span', {}, modalTitle.value), renderFullscreenButton()],
          ),
      // 简洁布局使用更小的宽度
      width: isFullscreen.value
        ? '100%'
        : (useSimpleLayout
          ? options.width || 460 // 简洁模式使用更小的默认宽度
          : options.width || 900),
      confirmLoading: loading.value,
      maskClosable: false,
      destroyOnClose: true,
      centered: !isFullscreen.value,
      class: `common-form-modal ${isFullscreen.value ? FULLSCREEN_CLASS : ''} ${isDragging.value ? 'modal-dragging no-transition' : ''} ${useSimpleLayout ? 'simple-form-modal' : ''}`,
      bodyStyle: {
        padding: useSimpleLayout ? '12px 0' : '12px 0', // 简洁模式使用更小的内边距
        ...(isFullscreen.value
          ? {
              height: 'calc(100vh - 160px)',
              overflow: 'hidden',
            }
          : {}),
      },
      style: isFullscreen.value
        ? {
            top: '0',
            paddingBottom: '0',
            height: '100vh',
            maxHeight: '100vh',
            overflow: 'hidden',
            margin: '0',
          }
        : {},
      onOk: () =>
        validate().then((valid) => {
          if (!valid) {
            // 验证失败，阻止表单提交
            message.error('表单验证失败');
            throw new Error('表单验证失败');
          }
          const formValues = {} as any;
          Object.keys(formData).forEach((key) => {
            formValues[key] = formData[key];
          });
          return handleSubmit(formValues as T);
        }),
      wrapClassName: `channel-form-modal-wrap ${isFullscreen.value ? FULLSCREEN_WRAP_CLASS : ''} ${useSimpleLayout ? 'simple-form-modal-wrap' : ''}`,
    };

    return h(Modal, modalProps, {
      default: () =>
        h(
          Spin,
          {
            spinning: isEdit.value && loading.value,
            tip: '加载中...',
            class: 'w-full h-full',
          },
          {
            default: () =>
              h(
                'div',
                {
                  class: `channel-form-container ${
                    isFullscreen.value
                      ? 'h-full overflow-y-auto'
                      : 'max-h-[calc(100vh-200px)] overflow-y-auto'
                  } ${useSimpleLayout ? 'simple-form-container' : ''}`,
                  style: isFullscreen.value
                    ? 'height: calc(100vh - 160px); max-height: calc(100vh - 160px);'
                    : '',
                },
                h(
                  Form,
                  {
                    ref: formRef,
                    model: formData,
                    rules: formRules.value,
                    onFinish: (values: Record<string, any>) => {
                      const formValues = {} as any;
                      Object.keys(values).forEach((key) => {
                        formValues[key] = values[key];
                      });
                      return handleSubmit(formValues as T);
                    },
                    class: `py-2 channel-form ${useSimpleLayout ? 'simple-form' : ''}`, // 减少顶部和底部内边距
                    ...formLayout, // 应用布局配置
                  },
                  () => renderFormGroups(groups),
                ),
              ),
          },
        ),
    });
  };

  // 添加CSS样式
  onMounted(() => {
    // 添加简洁表单样式
    if (options.simpleLayout) {
      const styleId = 'ansheng-simple-form-style';
      if (!document.getElementById(styleId)) {
        const style = document.createElement('style');
        style.id = styleId;
        style.innerHTML = `
        /* 简洁表单样式调整 */
        .simple-form-modal .simple-form .ant-form-item {
          margin-bottom: 16px; /* 减少表单项间距 */
        }
        .simple-form-modal .simple-form .ant-form-item-label {
          padding-bottom: 4px; /* 减少标签与控件之间的间距 */
        }
        .simple-form-modal .simple-form-content {
          width: 100%;
          padding: 0 2px;
          margin: 0 auto;
        }
        .simple-form-modal .ant-form-item-control-input {
          width: 100%;
        }
        .simple-form-modal .ant-form-vertical .ant-form-item-label {
          padding: 0 0 4px; /* 减少垂直布局的标签底部间距 */
        }
        /* 调整按钮大小和边距 */
        .simple-form-modal .ant-modal-footer {
          padding: 10px 16px; /* 减小footer内边距 */
        }
        /* 调整标题大小和边距 */
        .simple-form-modal .ant-modal-header {

        }
        /* 调整内容区域内边距 */
        .simple-form-modal .ant-modal-body {
          padding-top: 8px;
          padding-bottom: 8px;
        }
      `;
        document.head.append(style);
      }
    }

    options.onMounted?.();
  });

  return {
    formRef,
    formData,
    visible,
    loading,
    isEdit,
    currentId,
    show,
    close,
    setFormData,
    renderFormModal,
    handleSubmit,
    // Select相关功能
    selectOptions,
    selectLoading,
    loadSelectOptions,
    createSelectElement,
    // 远程搜索相关功能
    createRemoteSelect,
    remoteLoading,
    searchKeywords,
    // 全屏相关功能
    isFullscreen,
    toggleFullscreen,
    // 拖拽相关
    isDragging,
    resetPosition,
    // 表单验证规则相关
    formRules,
    setRules,
    addRule,
    // 字段条件显示相关
    fieldVisibility,
    setFieldVisibility,
    renderField,
    // 性能优化相关
    getCachedComponent,
    // 表单原始数据相关
    isModified,
    saveFormData,
    resetToOriginal,
    clearFormData,
  };
}
