import { computed, inject, reactive } from 'vue';

// 权限操作类型
export type PermissionAction =
  | 'create'
  | 'delete'
  | 'export'
  | 'import'
  | 'read'
  | 'update'
  | string;

// 字段权限类型
export type FieldPermissionAction = 'read' | 'write' | string;

// 数据范围类型
export type DataScopeType = 'all' | 'custom' | 'department' | 'self';

// 数据范围配置
export interface DataScope {
  type: DataScopeType;
  deptIds?: number[];
  condition?: string;
}

// 权限信息
export interface Permission {
  code: string; // 权限编码
  actions: PermissionAction[]; // 权限操作
  fieldPermissions?: Record<string, FieldPermissionAction[]>; // 字段级权限
  dataScope?: DataScope; // 数据范围
}

// 权限钩子配置
export interface UsePermissionOptions {
  moduleCode: string; // 模块编码
  permissions?: Permission[]; // 权限列表
  defaultActions?: PermissionAction[]; // 默认允许的操作
  defaultFieldActions?: FieldPermissionAction[]; // 默认允许的字段操作
  defaultDataScope?: DataScope; // 默认数据范围
}

/**
 * 权限控制钩子
 * 支持模块级、操作级、字段级、数据级权限控制
 */
export function usePermission(options: UsePermissionOptions) {
  // 尝试从上下文注入权限数据，如果没有则使用配置中的
  const injectedPermissions = inject<Permission[]>('permissions', []);
  const currentUserPermissions = options.permissions || injectedPermissions;

  // 获取当前模块权限
  const modulePermission = computed(() => {
    return currentUserPermissions.find((p) => p.code === options.moduleCode);
  });

  // 定义权限映射结构
  const permissionState = reactive({
    // 操作权限
    actions: computed(() => {
      if (!modulePermission.value) return options.defaultActions || [];
      return modulePermission.value.actions;
    }),

    // 字段权限
    fieldPermissions: computed(() => {
      if (!modulePermission.value?.fieldPermissions) return {};
      return modulePermission.value.fieldPermissions;
    }),

    // 数据范围
    dataScope: computed(() => {
      if (!modulePermission.value?.dataScope)
        return options.defaultDataScope || { type: 'all' };
      return modulePermission.value.dataScope;
    }),
  });

  // 检查操作权限
  const hasPermission = (action: PermissionAction): boolean => {
    // 如果没有模块权限，使用默认操作
    if (!modulePermission.value) {
      return options.defaultActions
        ? options.defaultActions.includes(action)
        : false;
    }

    return modulePermission.value.actions.includes(action);
  };

  // 检查字段权限
  const hasFieldPermission = (
    field: string,
    action: FieldPermissionAction = 'read',
  ): boolean => {
    // 如果没有模块权限或字段权限配置，使用默认设置
    if (!modulePermission.value?.fieldPermissions) {
      return options.defaultFieldActions
        ? options.defaultFieldActions.includes(action)
        : true;
    }

    const fieldPerm = modulePermission.value.fieldPermissions[field];
    if (!fieldPerm) return true; // 没有配置则默认有权限

    return fieldPerm.includes(action);
  };

  // 获取所有可读字段
  const getReadableFields = (): string[] => {
    if (!modulePermission.value?.fieldPermissions) return []; // 无字段权限限制

    return Object.entries(modulePermission.value.fieldPermissions)
      .filter(([_, actions]) => actions.includes('read'))
      .map(([field]) => field);
  };

  // 获取所有可写字段
  const getWritableFields = (): string[] => {
    if (!modulePermission.value?.fieldPermissions) return []; // 无字段权限限制

    return Object.entries(modulePermission.value.fieldPermissions)
      .filter(([_, actions]) => actions.includes('write'))
      .map(([field]) => field);
  };

  // 获取数据过滤条件
  const getDataFilter = (): Record<string, any> => {
    const scope = permissionState.dataScope;

    if (scope.type === 'all') {
      return {}; // 无需过滤
    }

    if (scope.type === 'department' && scope.deptIds?.length) {
      return { deptIds: scope.deptIds };
    }

    if (scope.type === 'self') {
      // 这里使用一个假定的用户ID，实际使用时应该从用户状态或上下文中获取
      const currentUserId = inject<number>('currentUserId', 0);
      return { createBy: currentUserId };
    }

    if (scope.type === 'custom' && scope.condition) {
      try {
        // 尝试解析自定义条件
        return JSON.parse(scope.condition);
      } catch {
        // 解析失败则返回原始字符串
        return { customCondition: scope.condition };
      }
    }

    return {};
  };

  // 过滤操作按钮
  const filterActionButtons = (buttons: any[]) => {
    return buttons.filter((btn) => {
      const actionMap: Record<string, PermissionAction> = {
        edit: 'update',
        update: 'update',
        delete: 'delete',
        view: 'read',
        detail: 'read',
        create: 'create',
        add: 'create',
        export: 'export',
        import: 'import',
      };

      // 根据按钮key映射权限操作
      const requiredAction = actionMap[btn.key] || btn.key;
      return hasPermission(requiredAction);
    });
  };

  // 过滤表格列
  const filterTableColumns = (columns: any[]) => {
    return columns.filter((col) => {
      // 如果列没有对应的字段，或者是操作列，则总是显示
      if (!col.dataIndex || col.dataIndex === 'action') return true;

      return hasFieldPermission(col.dataIndex, 'read');
    });
  };

  // 过滤表单字段
  const filterFormFields = (fields: string[]) => {
    return fields.filter((field) => hasFieldPermission(field, 'write'));
  };

  return {
    // 状态
    permissionState,
    modulePermission,

    // 权限检查
    hasPermission,
    hasFieldPermission,

    // 字段权限
    getReadableFields,
    getWritableFields,

    // 数据权限
    getDataFilter,

    // 便捷权限方法
    canCreate: computed(() => hasPermission('create')),
    canRead: computed(() => hasPermission('read')),
    canUpdate: computed(() => hasPermission('update')),
    canDelete: computed(() => hasPermission('delete')),
    canExport: computed(() => hasPermission('export')),
    canImport: computed(() => hasPermission('import')),

    // 过滤器
    filterActionButtons,
    filterTableColumns,
    filterFormFields,

    // 权限控制辅助方法
    showIf: (action: PermissionAction) => hasPermission(action),

    // 获取所有可用操作
    getAllActions: () => permissionState.actions,
  };
}
