import type {
  ApiConfig,
  SelectLoadingMap,
  SelectOption,
  SelectOptionsMap,
  SelectOptionsState,
} from './types';

import { h, ref, shallowRef } from 'vue';

import { Select } from 'ant-design-vue';

import { debounce } from './utils';

/**
 * Select选项数据管理hook
 * 处理下拉选项的加载和远程搜索功能
 */
export function useSelectOptions(
  selectApis?: Record<string, ApiConfig>,
): SelectOptionsState {
  // 使用shallowRef优化性能
  const selectOptions = shallowRef<SelectOptionsMap>({});
  const selectLoading = ref<SelectLoadingMap>({});

  // 远程搜索相关状态
  const remoteLoading = ref<Record<string, boolean>>({});
  const searchKeywords = ref<Record<string, string>>({});
  const searchTimers = ref<Record<string, number>>({});

  /**
   * 加载Select选项数据
   */
  const loadSelectOptions = async () => {
    if (!selectApis) return;

    const apis = selectApis;
    const keys = Object.keys(apis);

    if (keys.length === 0) return;

    try {
      // 并行加载所有Select选项
      await Promise.all(
        keys.map(async (key) => {
          const config = apis[key];
          if (!config || !config.api) return;

          try {
            selectLoading.value[key] = true;

            // 调用API函数获取数据
            const res = await config.api(config.params);

            if (res && res.code === 1 && res.data) {
              let options: SelectOption[];

              // 使用自定义转换函数或默认转换逻辑
              if (config.transform) {
                options = config.transform(res.data);
              } else {
                const valueField = config.valueField || 'id';
                const labelField = config.labelField || 'name';

                options = Array.isArray(res.data)
                  ? res.data.map((item: Record<string, any>) => ({
                      label: item[labelField],
                      value: item[valueField],
                      ...item, // 保留原始数据
                    }))
                  : [];
              }

              selectOptions.value = {
                ...selectOptions.value,
                [key]: options,
              };
            } else {
              console.error(`加载${key}选项失败:`, res);
              selectOptions.value = {
                ...selectOptions.value,
                [key]: [],
              };
            }
          } catch (error) {
            console.error(`加载${key}选项出错:`, error);
            selectOptions.value = {
              ...selectOptions.value,
              [key]: [],
            };
          } finally {
            selectLoading.value[key] = false;
          }
        }),
      );
    } catch (error) {
      console.error('加载Select选项出错:', error);
    }
  };

  /**
   * 处理远程搜索
   */
  const handleRemoteSearch = (key: string, value: string) => {
    if (!selectApis) return;

    const apiConfig = selectApis[key];
    if (!apiConfig || !apiConfig.remote) return;

    // 记录搜索关键词
    searchKeywords.value[key] = value;

    // 清除之前的计时器
    if (searchTimers.value[key]) {
      clearTimeout(searchTimers.value[key]);
    }

    // 设置防抖延迟，默认300ms
    const debounceTime = apiConfig.debounce ?? 300;

    // 创建新的计时器
    searchTimers.value[key] = window.setTimeout(async () => {
      // 设置加载状态
      remoteLoading.value[key] = true;

      try {
        // 构建搜索参数
        const searchParams = {
          ...apiConfig.params,
          [apiConfig.searchField || 'keyword']: value,
        };

        // 调用API
        const res = await apiConfig.api(searchParams);

        if (res && res.code === 1 && res.data) {
          let options: SelectOption[];

          // 使用自定义转换函数或默认转换逻辑
          if (apiConfig.transform) {
            options = apiConfig.transform(res.data);
          } else {
            const valueField = apiConfig.valueField || 'id';
            const labelField = apiConfig.labelField || 'name';

            options = Array.isArray(res.data)
              ? res.data.map((item: Record<string, any>) => ({
                  label: item[labelField],
                  value: item[valueField],
                  ...item, // 保留原始数据
                }))
              : [];
          }

          // 更新选项
          selectOptions.value = {
            ...selectOptions.value,
            [key]: options,
          };
        } else {
          console.error(`加载${key}选项失败:`, res);
          selectOptions.value = {
            ...selectOptions.value,
            [key]: [],
          };
        }
      } catch (error) {
        console.error(`远程搜索${key}选项出错:`, error);
        selectOptions.value = {
          ...selectOptions.value,
          [key]: [],
        };
      } finally {
        remoteLoading.value[key] = false;
      }
    }, debounceTime);
  };

  /**
   * 创建选择元素
   */
  const createSelectElement = (key: string, props: any) => {
    // 检查是否有对应的选项数据
    const options = selectOptions.value[key] || [];
    const isLoading = selectLoading.value[key] || false;

    // 结合原始props和自动生成的options
    return h(Select, {
      ...props,
      loading: isLoading,
      options,
    });
  };

  /**
   * 创建远程搜索选择
   */
  const createRemoteSelect = (key: string, props: any) => {
    if (!selectApis) return createSelectElement(key, props);

    // 获取API配置
    const apiConfig = selectApis[key];
    if (!apiConfig || !apiConfig.remote) {
      return createSelectElement(key, props);
    }

    // 使用防抖包装的搜索处理函数
    const debouncedSearch = debounce((value: string) => {
      handleRemoteSearch(key, value);
    }, apiConfig.debounce ?? 300);

    // 合并传入的props和远程搜索功能
    const selectProps = {
      ...props,
      filterOption: false, // 禁用本地过滤
      showSearch: true, // 启用搜索
      loading: remoteLoading.value[key] || false,
      options: selectOptions.value[key] || [],
      onSearch: debouncedSearch,
      onFocus: () => {
        // 聚焦时加载初始数据
        if (
          !selectOptions.value[key] ||
          selectOptions.value[key].length === 0
        ) {
          handleRemoteSearch(key, '');
        }
      },
    };

    return h(Select, selectProps);
  };

  return {
    selectOptions,
    selectLoading,
    remoteLoading,
    searchKeywords,
    loadSelectOptions,
    handleRemoteSearch,
    createSelectElement,
    createRemoteSelect,
  };
}
