// useAnsheng/index.ts
// 整合表单、表格和搜索功能的一站式解决方案

import type { ApiResponse, UseAnsengOptions } from './types';

import { computed, reactive } from 'vue';

import { message } from 'ant-design-vue';

import { useDetail } from './useDetail';
import { useForm } from './useForm';
import { useTable } from './useTable';

/**
 * useAnsheng - 整合表单、表格、搜索和详情功能的主Hook
 *
 * @param options 配置选项
 */
export function useAnsheng<
  T extends Record<string, any>,
  P = Record<string, any>,
>(options: UseAnsengOptions<T, P>) {
  // 默认视图模式
  const viewMode = options.viewMode || 'form';

  // 提取配置选项
  const { formOptions, tableOptions, searchOptions, detailOptions, deleteApi } =
    options;

  // 初始化表单Hook
  const form = useForm<T>(formOptions);

  // 初始化表格Hook
  const table = useTable<T, P>({
    ...tableOptions,
    // 适配API返回格式
    api: async (params: P) => {
      try {
        const res = await tableOptions.api(params);
        // API返回格式可能有多种情况，这里做兼容处理
        if (res.data && Array.isArray(res.data)) {
          // 如果直接返回了数组数据
          return {
            data: {
              rows: res.data,
              total: (res as any).total || 0,
            },
          };
        } else if (res.data && res.data.rows) {
          // 如果返回了标准格式 {data: {rows, total}}
          return {
            data: {
              rows: res.data.rows,
              total: res.data.total || 0,
            },
          };
        } else {
          // 其他情况，尽量尝试提取数据
          return {
            data: {
              rows: Array.isArray(res.data) ? res.data : [],
              total: (res as any).total || 0,
            },
          };
        }
      } catch (error) {
        console.error('获取表格数据失败:', error);
        return {
          data: {
            rows: [],
            total: 0,
          },
        };
      }
    },
  });

  // 初始化详情Hook (如果有配置)
  const detail = detailOptions ? useDetail<T>(detailOptions) : null;

  // 可以从表单中提取
  const formData = reactive({ ...formOptions.defaultValues });

  // 从表格中提取
  const {
    tableData,
    loading,
    pagination,
    virtualConfig,
    virtualScroll,
    handleVirtualScroll,
  } = table;

  // 操作按钮
  const actionButtons = computed(() => {
    return tableOptions.actionButtons || [];
  });

  // =============== 搜索功能 ===============
  // 直接在主hook中实现搜索功能，无需单独的useSearch

  // 搜索状态
  const searchState = reactive({
    // 搜索表单值
    values: { ...searchOptions?.defaultValues },
    // 搜索配置
    basicItems: searchOptions?.basicItems || [],
    advancedItems: searchOptions?.advancedItems || [],
    customButtons: searchOptions?.customButtons || [],
  });

  // 搜索方法
  const handleSearch = () => {
    // 重置页码
    pagination.current = 1;
    // 更新搜索参数
    Object.keys(searchState.values).forEach((key) => {
      table.searchParams[key] = searchState.values[key];
    });
    // 获取列表数据
    table.getList();
  };

  // 重置方法
  const handleReset = () => {
    // 重置表单值
    Object.keys(searchState.values).forEach((key) => {
      searchState.values[key] = undefined;
    });
    // 调用表格重置方法
    table.handleReset();
    // 调用钩子函数
    searchOptions?.afterReset?.();
  };

  // 提供给SearchToolbar组件的props
  const searchToolbarProps = computed(() => ({
    modelValue: searchState.values,
    basicItems: searchState.basicItems,
    advancedItems: searchState.advancedItems,
    customButtons: searchState.customButtons,
    loading: loading.value,
  }));

  // 提供给SearchToolbar组件的绑定对象
  const searchToolbarBind = computed(() => ({
    ...searchToolbarProps.value,
    'onUpdate:modelValue': (val: Record<string, any>) => {
      Object.assign(searchState.values, val);
    },
    onSearch: handleSearch,
    onReset: handleReset,
  }));

  // 提供给BasicTable组件的绑定对象
  const tableBind = computed(() => ({
    columns: tableOptions.columns,
    dataSource: tableData.value,
    loading: loading.value,
    pagination: virtualScroll ? false : pagination, // 虚拟滚动模式下禁用分页
    showAction: Boolean(tableOptions.actionButtons?.length),
    actionButtons: tableOptions.actionButtons || [],
    showIndex: false,
    rowKey: 'id',
    // 虚拟滚动相关配置
    ...(virtualScroll && virtualConfig.value
      ? {
          scroll: { y: virtualConfig.value.scrollY },
          onScroll: handleVirtualScroll,
          virtual: true,
          virtualItemHeight: virtualConfig.value.itemHeight,
        }
      : {}),
  }));

  // 为了保持向后兼容，提供一个与原useSearch返回值类似的对象
  const search = searchOptions
    ? {
        // 与原useSearch返回的对象结构保持一致
        searchToolbarProps: {
          modelValue: searchState.values,
          basicItems: searchState.basicItems,
          advancedItems: searchState.advancedItems,
          customButtons: searchState.customButtons,
          loading: loading.value,
        },
        handleSearch,
        handleReset,
        formValues: searchState.values,
        // 添加其他可能在原useSearch中返回的属性
        updateSearchParams: (params: Record<string, any>) => {
          Object.assign(searchState.values, params);
        },
      }
    : null;

  /**
   * 初始化数据
   */
  const initialize = () => {
    table.getList();
  };

  /**
   * 创建项目
   */
  const createItem = () => {
    form.show();
  };

  /**
   * 编辑项目
   * @param id 项目ID
   */
  const editItem = (id: number) => {
    form.show(id);
  };

  /**
   * 查看项目
   * @param record 记录
   */
  const viewItem = (record: T) => {
    if (viewMode === 'detail' && detail) {
      detail.open(record);
    } else {
      form.show(record.id);
    }
  };

  /**
   * 删除项目
   * @param id 项目ID
   */
  const deleteItem = async (id: number) => {
    if (!deleteApi) {
      message.error('未配置删除API');
      return;
    }

    try {
      const res: ApiResponse = await deleteApi(id);
      if (res.code === 1) {
        message.success(res.msg || '删除成功');
        table.getList();
      } else {
        message.error(res.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error(error instanceof Error ? error.message : '删除失败');
    }
  };

  /**
   * 表格变化处理
   */
  const handleTableChange = (...args: any[]) => {
    // @ts-ignore - table.handleTableChange需要接收任意数量的参数
    table.handleTableChange(...args);
  };

  return {
    // 状态和数据
    tableData,
    loading,
    pagination,
    formData,
    actionButtons,
    virtualScroll,
    virtualConfig,

    // 方法
    handleTableChange,
    initialize,
    createItem,
    editItem,
    viewItem,
    deleteItem,
    handleSearch,
    handleReset,
    handleVirtualScroll,

    // 搜索相关
    searchToolbarProps,
    searchToolbarBind,
    tableBind,
    search,

    // 子hook
    form,
    table,
    detail,
  };
}

export * from './components';
// 导出所有子模块
export * from './types';
export * from './useAnsengPermission';
// 添加新的导出
export * from './useCustomDetail';
export * from './useDetail';
export * from './useDynamicForm';
export * from './useFieldVisibility';
export * from './useForm';
export * from './useFormState';
export * from './useModalInteractions';

// =============== 导出所有子hook ===============

// export * from './useSearch'; // 移除此导出
export * from './useSelectOptions';
export * from './useStepForm';
export * from './useTable';

export * from './utils';
