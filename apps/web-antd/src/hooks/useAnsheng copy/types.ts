// useAnsheng/types.ts
// 整合form-modal、table、search的类型定义

import type { TableColumnType } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

import type { DetailSection } from '#/components/DetailModal/index.vue';
import type {
  CustomButton,
  SearchGroup,
  SearchItemConfig,
} from '#/components/SearchToolbar/types';

// =============== 表单相关类型 ===============

// Select选项的接口定义
export interface SelectOption {
  label: string;
  value: number | string;
  [key: string]: any;
}

// Select选项的映射
export type SelectOptionsMap = Record<string, SelectOption[]>;

// 全局加载状态的映射
export type SelectLoadingMap = Record<string, boolean>;

// 表单验证规则映射
export type FormRules = Record<string, Rule[]>;

// 字段可见性条件
export interface FieldVisibility {
  visible?: (formData: any) => boolean;
  dependencies?: string[];
}

// 表单组类型
export interface FormGroup {
  title: string;
  content: (
    formData: Record<string, any>,
    selectOptions?: Record<string, any[]>,
  ) => any[];
}

// 接口函数配置
export interface ApiConfig {
  api: (...args: any[]) => Promise<any>; // API函数
  params?: any; // 请求参数
  valueField?: string; // 值字段名，默认为'id'
  labelField?: string; // 标签字段名，默认为'name'
  transform?: (data: any[]) => SelectOption[]; // 自定义转换函数
  // 远程搜索相关配置
  remote?: boolean; // 是否支持远程搜索
  searchField?: string; // 搜索字段名称，默认为'keyword'
  debounce?: number; // 防抖延迟时间，默认300ms
}

// 表单配置接口
export interface UseFormOptions<T = Record<string, any>> {
  // 表单标题
  title: ((isEdit: boolean) => string) | string;
  // 表单宽度
  width?: number | string;
  // 获取详情接口
  getDetail?: (idOrRecord: number | Partial<T>) => Promise<ApiResponse<T>>;
  // 创建接口
  create?: (params: T) => Promise<ApiResponse>;
  // 更新接口
  update?: (id: number, params: T) => Promise<ApiResponse>;
  // 默认表单值
  defaultValues: Partial<T>;
  // 成功回调
  onSuccess?: () => void;
  // 取消回调
  onCancel?: () => void;
  // 是否允许全屏
  fullscreenable?: boolean;
  // 是否允许拖拽
  draggable?: boolean;
  // 表单验证规则
  rules?: FormRules;
  // 是否使用简单布局（适用于表单项较少的情况）
  simpleLayout?:
    | {
        // 自定义表单容器类名
        containerClass?: string;
        // 表单项间距(px)
        itemMargin?: number;
        // 内边距(px)
        padding?: number;
        // 是否显示分组标题
        showGroupTitle?: boolean;
      }
    | boolean;
}

// 错误类型定义
export enum ErrorType {
  API_ERROR = 'api_error',
  NETWORK_ERROR = 'network_error',
  UNKNOWN_ERROR = 'unknown_error',
  VALIDATION_ERROR = 'validation_error',
}

// 自定义错误类
export class AnsengError extends Error {
  details?: any;
  type: ErrorType;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN_ERROR,
    details?: any,
  ) {
    super(message);
    this.name = 'AnsengError';
    this.type = type;
    this.details = details;
  }
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  data?: T;
  msg?: string;
}

// 表单状态接口
export interface FormState<T> {
  formData: T;
  originalData: Partial<T>;
  isModified: boolean;
  validate: () => Promise<boolean>;
  resetForm: () => void;
  setFormData: (data: Partial<T>) => void;
  saveFormData: (data: Partial<T>) => void;
  resetToOriginal: () => void;
  clearFormData: () => void;
}

// 模态框交互状态接口
export interface ModalInteractionState {
  isFullscreen: boolean;
  isDragging: boolean;
  toggleFullscreen: () => void;
  resetPosition: () => void;
  initModal: (visible: boolean) => void;
}

// 选项数据状态接口
export interface SelectOptionsState {
  selectOptions: SelectOptionsMap;
  selectLoading: SelectLoadingMap;
  remoteLoading: Record<string, boolean>;
  searchKeywords: Record<string, string>;
  loadSelectOptions: () => Promise<void>;
  handleRemoteSearch: (key: string, value: string) => void;
  createSelectElement: (key: string, props: any) => any;
  createRemoteSelect: (key: string, props: any) => any;
}

// 字段可见性状态接口
export interface FieldVisibilityState {
  fieldVisibility: Record<string, boolean>;
  setFieldVisibility: (field: string, config: FieldVisibility) => void;
  updateAllFieldVisibility: () => void;
  renderField: (
    field: string,
    component: any,
    visibility?: (formData: any) => boolean,
  ) => any | null;
}

// 全屏模式样式常量
export const FULLSCREEN_CLASS = 'fullscreen-modal';
export const FULLSCREEN_WRAP_CLASS = 'fullscreen-wrap';

// =============== 表格相关类型 ===============

// 保存函数类型
export type SaveFunction = (
  record: any,
  value: any,
  key: string,
) => Promise<any>;

// onSave类型
export type OnSaveConfig =
  | SaveFunction
  | {
      [key: string]: SaveFunction;
      default: SaveFunction;
    };

// 表格操作按钮配置
export interface ActionButton {
  key: string;
  text: string;
  type?: 'dashed' | 'default' | 'link' | 'primary' | 'text';
  danger?: boolean;
  icon?: any;
  onClick: (record: any) => void;
  [key: string]: any;
}

// 虚拟滚动配置选项
export interface VirtualScrollOptions {
  // 滚动区域高度
  scrollY?: number;
  // 单行高度
  itemHeight?: number;
  // 触发加载更多的阈值（距离底部多少像素时加载）
  threshold?: number;
  // 其他可能的虚拟滚动配置项
  [key: string]: any;
}

// 表格配置选项
export interface UseTableOptions<
  T = Record<string, any>,
  P = Record<string, any>,
> {
  // 表格数据接口
  api: (params: P) => Promise<{ data: { rows: T[]; total: number } }>;
  // 表格列配置
  columns?: TableColumnType[];
  // 默认查询参数
  defaultParams?: Partial<P>;
  // 默认页码
  defaultPage?: number;
  // 默认每页数量
  defaultPageSize?: number;
  // 表格操作按钮
  actionButtons?: ActionButton[];
  // 请求前参数处理函数
  beforeFetch?: (params: any) => any;
  // 请求后数据处理函数
  afterFetch?: (data: any) => { rows: T[]; total: number };
  // 表格类型，默认为'ant'
  tableType?: 'ant' | 'custom';
  // 单元格保存函数
  onSave?: OnSaveConfig;
  // 是否启用虚拟滚动
  virtualScroll?: boolean;
  // 虚拟滚动配置
  virtualScrollOptions?: VirtualScrollOptions;
}

// =============== 搜索相关类型 ===============

export interface SearchOption {
  label: string;
  value: any;
  [key: string]: any;
}

/**
 * 自定义按钮配置
 */
export interface CustomButton {
  text: string;
  type?: 'dashed' | 'default' | 'link' | 'primary' | 'text';
  icon?: any;
  props?: Record<string, any>;
  onClick: () => void;
}

export interface SearchApiConfig {
  // API函数配置，可以是任何返回Promise的函数
  api: (params?: any) => Promise<any>;
  // 参数转换函数 (可选)
  paramsTransform?: (params: any) => any;
  // 结果转换函数，将API返回结果转换为选项数组
  resultTransform: (result: any) => SearchOption[];
}

export interface SearchOptionConfig {
  // 字段名
  field: string;
  // API配置
  apiConfig: SearchApiConfig;
  // 初始加载 (默认为true)
  loadOnMount?: boolean;
  // 依赖的其他字段 (可选)，当这些字段值变化时重新加载
  dependencies?: string[];
}

export interface UseSearchOptions {
  // 基础搜索项
  basicItems?: SearchItemConfig[];
  // 高级搜索项
  advancedItems?: SearchGroup[];
  // 自定义按钮
  customButtons?: CustomButton[];
  // 需要动态加载选项的配置
  optionsConfig?: SearchOptionConfig[];
  // 默认表单值
  defaultValues?: Record<string, any>;
  // 表单提交前处理函数
  beforeSearch?: (values: Record<string, any>) => boolean | Promise<boolean>;
  // 表单重置后处理函数
  afterReset?: () => void;
}

// =============== 详情相关类型 ===============

export interface DetailItem {
  label: string;
  value?: any;
  field?: string;
  formatter?: (value: any) => number | string;
  bold?: boolean;
}

export interface DetailSection {
  title: string;
  items: DetailItem[];
}

export interface UseDetailOptions {
  // 详情区块
  sections: DetailSection[];
  // 标题
  title?: string;
  // 格式化器
  formatters?: Record<string, (value: any) => number | string>;
}

// =============== 集成Hook相关类型 ===============

export interface UseAnsengOptions<
  T = Record<string, any>,
  P = Record<string, any>,
> {
  // 表单配置
  formOptions: UseFormOptions<T>;

  // 表格配置
  tableOptions: UseTableOptions<T, P>;

  // 搜索配置
  searchOptions?: UseSearchOptions;

  // 详情配置
  detailOptions?: UseDetailOptions;

  // 删除API
  deleteApi?: (id: number) => Promise<ApiResponse>;

  // 查看模式: 'form' - 使用表单查看, 'detail' - 使用详情模态框查看
  viewMode?: 'detail' | 'form';
}
