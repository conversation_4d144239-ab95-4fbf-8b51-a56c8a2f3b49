// useAnsheng/useForm.ts

import type { ApiResponse, FormGroup, UseFormOptions } from './types';

import { computed, h, markRaw, onMounted, ref, watch } from 'vue';

import { Form, message, Modal, Row, Spin } from 'ant-design-vue';

import { FULLSCREEN_CLASS, FULLSCREEN_WRAP_CLASS } from './types';
import { useFieldVisibility } from './useFieldVisibility';
import { useFormState } from './useFormState';
import { useModalInteractions } from './useModalInteractions';
import { useSelectOptions } from './useSelectOptions';
import { asyncOnce, handleError, preventMultiSubmit } from './utils';

/**
 * 表单弹窗管理Hook
 * 提供完整的表单弹窗解决方案，包括表单状态管理、模态框交互、选项加载等功能
 */
export function useForm<T extends Record<string, any>>(
  options: UseFormOptions<T> = {},
) {
  // 弹窗可见性和加载状态
  const visible = ref(false);
  const loading = ref(false);

  // 编辑状态和当前ID
  const isEdit = ref(false);
  const currentId = ref<number>();

  // 防抖锁
  const submitDebounce = ref(false);

  // 组件缓存
  const cachedComponents = ref(new Map());

  // 使用表单状态管理
  const {
    formRef,
    formData,
    formRules,
    setRules,
    addRule,
    resetForm,
    setFormData,
    saveFormData,
    resetToOriginal,
    isModified,
    clearFormData,
    validate,
  } = useFormState<T>(options);

  // 使用模态框交互管理
  const {
    isFullscreen,
    isDragging,
    toggleFullscreen,
    resetPosition,
    initModal,
  } = useModalInteractions({
    fullscreenable: options.fullscreenable,
    draggable: options.draggable,
    width: options.width,
  });

  // 使用选项数据管理
  const {
    selectOptions,
    selectLoading,
    remoteLoading,
    searchKeywords,
    loadSelectOptions,
    handleRemoteSearch,
    createSelectElement,
    createRemoteSelect,
  } = useSelectOptions(options.selectApis);

  // 使用字段可见性管理
  const {
    fieldVisibility,
    setFieldVisibility,
    updateAllFieldVisibility,
    renderField,
  } = useFieldVisibility(formData);

  // 监听modal打开状态，初始化DOM引用
  watch(
    () => visible.value,
    (v) => {
      initModal(v);

      if (!v) {
        // 弹窗关闭时，重置全屏状态
        isFullscreen.value = false;
      }
    },
  );

  // 关闭弹窗
  const close = () => {
    visible.value = false;
    isEdit.value = false;
    currentId.value = undefined;
    // 重置表单数据
    resetForm();
    // 清除验证
    formRef.value?.clearValidate();
    // 清除原始数据记录
    clearFormData();
    options.onCancel?.();
  };

  // 加载详情的优化版本，使用asyncOnce确保不会并发调用
  const loadDetail = asyncOnce(async (id: number) => {
    if (!options.getDetail) return;

    try {
      loading.value = true;
      const res = await options.getDetail(id);
      if (res.code === 1 && res.data) {
        saveFormData(res.data);
        return res.data;
      }
      throw new Error(res.msg || '获取详情失败');
    } catch (error) {
      const formattedError = handleError(error, '加载失败');
      message.error(formattedError.message);
      close();
    } finally {
      loading.value = false;
    }
  });

  // 表单提交处理
  const handleSubmit = preventMultiSubmit(async (values: T) => {
    if (submitDebounce.value) return;

    try {
      submitDebounce.value = true;
      loading.value = true;

      let res: ApiResponse;
      let successMsg = '';

      if (isEdit.value && currentId.value) {
        if (!options.update) throw new Error('未提供更新方法');

        res = await options.update(currentId.value, values);
        successMsg = '修改成功';
      } else {
        if (!options.create) throw new Error('未提供创建方法');

        res = await options.create(values);
        successMsg = '创建成功';
      }

      if (res.code !== 1) {
        throw new Error(res.msg || (isEdit.value ? '修改失败' : '创建失败'));
      }

      message.success(res.msg || successMsg);
      visible.value = false;

      // 确保在操作成功后调用 onSuccess
      if (options.onSuccess) {
        await options.onSuccess();
      }
    } catch (error) {
      const formattedError = handleError(
        error,
        isEdit.value ? '修改失败' : '创建失败',
      );
      message.error(formattedError.message);
      throw formattedError; // 向上抛出错误
    } finally {
      loading.value = false;
      // 延迟重置防抖锁
      setTimeout(() => {
        submitDebounce.value = false;
      }, 500);
    }
  });

  // 打开弹窗
  const show = async (id?: number) => {
    resetForm();
    visible.value = true;

    // 加载Select选项数据
    await loadSelectOptions();

    if (id) {
      isEdit.value = true;
      currentId.value = id;
      if (options.getDetail) {
        return await loadDetail(id);
      }
    } else {
      isEdit.value = false;
      currentId.value = undefined;
    }
  };

  // 创建组件缓存
  const getCachedComponent = (key: string, creator: () => any) => {
    if (!cachedComponents.value.has(key)) {
      cachedComponents.value.set(key, markRaw(creator()));
    }
    return cachedComponents.value.get(key);
  };

  // 计算标题
  const modalTitle = computed(() =>
    typeof options.title === 'function'
      ? options.title(isEdit.value)
      : options.title || (isEdit.value ? '编辑' : '新增'),
  );

  // 渲染表单分组
  const renderFormGroups = (groups: FormGroup[]) => {
    // 过滤不可见的表单分组
    return groups
      .filter((group) => !group.visible || group.visible(formData))
      .map((group) =>
        h('div', { class: 'px-6 mb-8' }, [
          h(
            'div',
            {
              class: 'text-base font-medium mb-5 pb-2.5 border-gray-100',
            },
            group.title,
          ),
          h(Row, { gutter: 24 }, () =>
            group.content(formData, selectOptions.value),
          ),
        ]),
      );
  };

  // 渲染全屏按钮
  const renderFullscreenButton = () => {
    if (options.fullscreenable === false) return null;

    // 导入图标 - 按需导入（确保项目中有这些图标组件）
    // 如果没有，可以使用简单的文本替代
    let fullscreenIcon = '⛶'; // 全屏图标
    let exitFullscreenIcon = '⤧'; // 退出全屏图标

    try {
      // 尝试导入图标，如果导入失败，使用文本符号
      const icons = require('@vben/icons');
      if (icons.MdiFullscreen && icons.MdiFullscreenExit) {
        fullscreenIcon = h(icons.MdiFullscreen);
        exitFullscreenIcon = h(icons.MdiFullscreenExit);
      }
    } catch {
      console.warn('无法加载图标组件，使用文本替代');
    }

    return h(
      'div',
      {
        class: 'modal-fullscreen-btn cursor-pointer ml-4 flex items-center',
        onClick: (e: Event) => {
          e.stopPropagation(); // 阻止事件冒泡
          toggleFullscreen();
        },
        style: 'font-size: 16px;',
      },
      [isFullscreen.value ? exitFullscreenIcon : fullscreenIcon],
    );
  };

  // 渲染弹窗
  const renderFormModal = (groups: FormGroup[]) => {
    // Modal组件配置
    const modalProps = {
      visible: visible.value,
      'onUpdate:visible': (val: boolean) => {
        if (!val) close();
      },
      title: h(
        'div',
        {
          class: 'text-base font-medium flex items-center modal-title',
          style: options.draggable ? 'cursor: move;' : '',
        },
        [h('span', {}, modalTitle.value), renderFullscreenButton()],
      ),
      width: isFullscreen.value ? '100%' : options.width || 900,
      confirmLoading: loading.value,
      maskClosable: false,
      destroyOnClose: true,
      centered: !isFullscreen.value,
      class: `common-form-modal ${isFullscreen.value ? FULLSCREEN_CLASS : ''} ${isDragging.value ? 'modal-dragging no-transition' : ''}`,
      bodyStyle: {
        padding: '12px 0',
        ...(isFullscreen.value
          ? {
              height: 'calc(100vh - 160px)',
              overflow: 'hidden',
            }
          : {}),
      },
      style: isFullscreen.value
        ? {
            top: '0',
            paddingBottom: '0',
            height: '100vh',
            maxHeight: '100vh',
            overflow: 'hidden',
            margin: '0',
          }
        : {},
      onOk: () =>
        validate().then(() => {
          const formValues = {} as any;
          Object.keys(formData).forEach((key) => {
            formValues[key] = formData[key];
          });
          return handleSubmit(formValues as T);
        }),
      wrapClassName: `channel-form-modal-wrap ${isFullscreen.value ? FULLSCREEN_WRAP_CLASS : ''}`,
    };

    return h(Modal, modalProps, {
      default: () =>
        h(
          Spin,
          {
            spinning: isEdit.value && loading.value,
            tip: '加载中...',
            class: 'w-full h-full',
          },
          {
            default: () =>
              h(
                'div',
                {
                  class: `channel-form-container ${
                    isFullscreen.value
                      ? 'h-full overflow-y-auto'
                      : 'max-h-[calc(100vh-200px)] overflow-y-auto'
                  }`,
                  style: isFullscreen.value
                    ? 'height: calc(100vh - 160px); max-height: calc(100vh - 160px);'
                    : '',
                },
                h(
                  Form,
                  {
                    ref: formRef,
                    model: formData,
                    rules: formRules.value,
                    onFinish: (values: Record<string, any>) => {
                      const formValues = {} as any;
                      Object.keys(values).forEach((key) => {
                        formValues[key] = values[key];
                      });
                      return handleSubmit(formValues as T);
                    },
                    layout: 'horizontal',
                    class: 'py-4 channel-form',
                    labelCol: { span: 6 },
                    wrapperCol: { span: 16 },
                  },
                  () => renderFormGroups(groups),
                ),
              ),
          },
        ),
    });
  };

  // 添加CSS样式
  onMounted(() => {
    options.onMounted?.();
  });

  return {
    formRef,
    formData,
    visible,
    loading,
    isEdit,
    currentId,
    show,
    close,
    setFormData,
    renderFormModal,
    handleSubmit,
    // Select相关功能
    selectOptions,
    selectLoading,
    loadSelectOptions,
    createSelectElement,
    // 远程搜索相关功能
    createRemoteSelect,
    remoteLoading,
    searchKeywords,
    // 全屏相关功能
    isFullscreen,
    toggleFullscreen,
    // 拖拽相关
    isDragging,
    resetPosition,
    // 表单验证规则相关
    formRules,
    setRules,
    addRule,
    // 字段条件显示相关
    fieldVisibility,
    setFieldVisibility,
    renderField,
    // 性能优化相关
    getCachedComponent,
    // 表单原始数据相关
    isModified,
    saveFormData,
    resetToOriginal,
    clearFormData,
  };
}
