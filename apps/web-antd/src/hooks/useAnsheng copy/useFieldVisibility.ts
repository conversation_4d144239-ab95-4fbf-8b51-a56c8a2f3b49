import type { FieldVisibility, FieldVisibilityState } from './types';

import { reactive, watchEffect } from 'vue';

/**
 * 字段可见性管理hook
 * 处理表单字段的条件显示逻辑
 */
export function useFieldVisibility<T extends Record<string, any>>(
  formData: T,
): FieldVisibilityState {
  // 字段可见性状态
  const fieldVisibility = reactive<Record<string, boolean>>({});

  /**
   * 设置字段可见性
   */
  const setFieldVisibility = (field: string, config: FieldVisibility) => {
    // 立即更新字段可见性
    fieldVisibility[field] = config.visible ? config.visible(formData) : true;

    // 如果存在依赖字段，则使用watchEffect监听相关变化
    if (
      config.dependencies &&
      config.dependencies.length > 0 &&
      config.visible
    ) {
      // 使用watchEffect代替多个独立的watch，提高性能和可维护性
      watchEffect(() => {
        // 在watchEffect中访问依赖字段，确保它们被追踪
        // 这里只是为了建立响应式依赖，不需要使用返回值
        if (config.dependencies) {
          config.dependencies.forEach((depField) => {
            // 读取依赖字段的值，建立响应式连接
            const _value = formData[depField];
          });
        }

        // 计算新的可见性
        if (config.visible) {
          fieldVisibility[field] = config.visible(formData);
        }
      });
    }
  };

  /**
   * 更新所有字段可见性
   */
  const updateAllFieldVisibility = () => {
    // 触发每个字段的重新计算，以更新可见性
    Object.keys(fieldVisibility).forEach((field) => {
      const currentValue = !!fieldVisibility[field]; // 转为布尔值确保类型安全
      // 通过设置为相同值来触发可能的副作用，如重渲染
      fieldVisibility[field] = currentValue;
    });
  };

  /**
   * 条件渲染字段
   */
  const renderField = (
    field: string,
    component: any,
    visibility?: (formData: any) => boolean,
  ) => {
    // 如果设置了显示条件且结果为false，则不渲染
    if (visibility && !visibility(formData)) {
      return null;
    }

    // 检查字段是否在全局可见性配置中且为不可见
    if (field in fieldVisibility && !fieldVisibility[field]) {
      return null;
    }

    return component;
  };

  return {
    fieldVisibility,
    setFieldVisibility,
    updateAllFieldVisibility,
    renderField,
  };
}
