<script setup lang="ts">
import { computed } from 'vue';

import { Modal, Spin } from 'ant-design-vue';

export interface DetailSection {
  title: string;
  items: {
    bold?: boolean;
    label: string;
    value: number | string;
  }[];
}

const props = defineProps<{
  loading?: boolean;
  sections: DetailSection[];
  title: string;
  visible: boolean | { value: boolean };
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
}>();

// 处理可能是ref的visible属性
const isVisible = computed(() => {
  if (typeof props.visible === 'boolean') {
    return props.visible;
  } else if (props.visible && 'value' in props.visible) {
    return props.visible.value;
  }
  return false;
});

const handleClose = () => {
  // 发送事件
  emit('update:visible', false);

  // 如果是ref对象，直接修改其值
  if (
    typeof props.visible !== 'boolean' &&
    props.visible &&
    'value' in props.visible
  ) {
    // 使用类型断言告诉TypeScript这是一个可写的ref
    const visibleRef = props.visible as { value: boolean };
    visibleRef.value = false;
  }
};
</script>

<template>
  <Modal
    :visible="isVisible"
    :title="props.title"
    :footer="null"
    width="800px"
    :body-style="{ maxHeight: '80vh', overflow: 'auto' }"
    @cancel="handleClose"
  >
    <div class="min-h-[400px]">
      <Spin :spinning="props.loading" class="h-full">
        <div class="px-6 py-4">
          <div
            v-for="(section, index) in props.sections"
            :key="index"
            :class="{ 'mb-6': index !== props.sections.length - 1 }"
          >
            <div class="mb-3 flex items-center text-base font-medium">
              <div class="bg-primary mr-2 h-4 w-1 rounded"></div>
              {{ section.title }}
            </div>
            <div class="grid gap-x-12 gap-y-3 rounded-lg p-4 md:grid-cols-2">
              <div
                v-for="(item, itemIndex) in section.items"
                :key="itemIndex"
                class="flex items-start py-2"
              >
                <span class="w-20 flex-shrink-0">{{ item.label }}：</span>
                <span class="flex-1" :class="[{ 'font-medium': item.bold }]">{{
                  item.value
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </Spin>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
:deep(.ant-modal-body) {
  padding: 0;
}
:deep(.ant-spin-nested-loading) {
  height: 100%;
}
:deep(.ant-spin-container) {
  height: 100%;
}
</style>
