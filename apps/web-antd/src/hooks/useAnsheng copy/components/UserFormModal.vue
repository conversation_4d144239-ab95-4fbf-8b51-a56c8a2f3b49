<script lang="ts" setup>
import type { FormGroup, User } from '../types';

import { defineComponent, h, watch } from 'vue';

import {
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Select,
  Switch,
} from 'ant-design-vue';

import { useForm } from '../useForm';

// 自定义事件
const emit = defineEmits(['success']);

const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
  { label: '锁定', value: 2 },
];

const departments = [
  { id: 1, name: '技术部' },
  { id: 2, name: '市场部' },
  { id: 3, name: '销售部' },
  { id: 4, name: '财务部' },
  { id: 5, name: '人事部' },
];

// 模拟服务类
const MockService = {
  getUserDetail: async (id: number) => {
    return {
      code: 1,
      data: {
        id,
        name: '示例用户',
        email: '<EMAIL>',
        age: 30,
        gender: '男',
        department: '1',
        role: '2',
        status: 1,
        skills: ['1', '2'],
        hireDate: '2022-01-01',
        showAdvanced: false,
      },
    };
  },

  createUser: async (data: any) => {
    return { code: 1, data: { id: 100, ...data }, msg: '创建成功' };
  },

  updateUser: async (id: number, data: any) => {
    return { code: 1, data: { id, ...data }, msg: '更新成功' };
  },

  getRoles: async (params: any) => {
    return [
      { id: 1, name: '管理员' },
      { id: 2, name: '普通用户' },
      { id: 3, name: '访客' },
    ];
  },

  getSkills: async () => {
    return [
      { id: 1, name: 'JavaScript' },
      { id: 2, name: 'TypeScript' },
      { id: 3, name: 'Vue' },
      { id: 4, name: 'React' },
      { id: 5, name: 'Node.js' },
    ];
  },
};

const FormItem = Form.Item;
const TextArea = Input.TextArea;

// 创建表单钩子
const {
  formRef,
  formData,
  visible: _visible,
  isEdit: _isEdit,
  show,
  renderFormModal,
  setFieldVisibility,
  renderField,
  formRules: _formRules,
  setRules,
  createRemoteSelect,
  selectOptions: _selectOptions,
  loadSelectOptions,
  isFullscreen,
  toggleFullscreen,
} = useForm<User>({
  title: (isEdit) => (isEdit ? '编辑用户' : '新增用户'),
  width: 800,
  // 默认值
  defaultValues: {
    name: '',
    email: '',
    age: 25,
    gender: '男',
    department: '',
    role: '',
    status: 1,
    skills: [],
    hireDate: new Date().toISOString().split('T')[0],
    showAdvanced: false,
  },
  // 获取详情方法
  getDetail: MockService.getUserDetail,
  // 创建方法
  create: MockService.createUser,
  // 更新方法
  update: MockService.updateUser,
  // 成功回调
  onSuccess: () => {
    emit('success');
  },
  // 允许全屏和拖拽
  fullscreenable: true,
  draggable: true,
  // Select选项的API配置
  selectApis: {
    // 角色支持远程搜索
    roles: {
      api: MockService.getRoles,
      valueField: 'id',
      labelField: 'name',
      remote: true,
      searchField: 'keyword',
      debounce: 300,
    },
    // 技能不支持远程搜索
    skills: {
      api: MockService.getSkills,
      valueField: 'id',
      labelField: 'name',
    },
  },
});

// 设置表单验证规则
setRules({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度应为2-20个字符', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' },
    {
      type: 'number',
      min: 18,
      max: 65,
      message: '年龄必须在18-65岁之间',
      trigger: 'blur',
    },
  ],
  department: [{ required: true, message: '请选择部门', trigger: 'change' }],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
  hireDate: [{ required: true, message: '请选择入职日期', trigger: 'change' }],
});

// 设置高级设置字段的可见性，依赖于showAdvanced的值
watch(
  () => formData.showAdvanced,
  (value) => {
    setFieldVisibility('advancedSetting', {
      visible: (data) => Boolean(data.showAdvanced),
      dependencies: ['showAdvanced'],
    });

    setFieldVisibility('remarks', {
      visible: (data) => Boolean(data.showAdvanced),
      dependencies: ['showAdvanced'],
    });
  },
  { immediate: true },
);

// 定义表单组
const formGroups: FormGroup[] = [
  // 基本信息组
  {
    title: '基本信息',
    content: (formData) => [
      h(Col, { span: 12 }, [
        h(FormItem, { label: '姓名', name: 'name' }, [
          h(Input, {
            placeholder: '请输入姓名',
            value: formData.name,
            'onUpdate:value': (val: string) => (formData.name = val),
          }),
        ]),
      ]),
      h(Col, { span: 12 }, [
        h(FormItem, { label: '邮箱', name: 'email' }, [
          h(Input, {
            placeholder: '请输入邮箱',
            value: formData.email,
            'onUpdate:value': (val: string) => (formData.email = val),
          }),
        ]),
      ]),
      h(Col, { span: 12 }, [
        h(FormItem, { label: '年龄', name: 'age' }, [
          h(InputNumber, {
            placeholder: '请输入年龄',
            style: 'width: 100%',
            value: formData.age,
            'onUpdate:value': (val: number) => (formData.age = val),
          }),
        ]),
      ]),
      h(Col, { span: 12 }, [
        h(FormItem, { label: '性别', name: 'gender' }, [
          h(
            Radio.Group,
            {
              value: formData.gender,
              'onUpdate:value': (val: string) => (formData.gender = val),
            },
            [h(Radio, { value: '男' }, '男'), h(Radio, { value: '女' }, '女')],
          ),
        ]),
      ]),
    ],
  },

  // 职务信息组
  {
    title: '职务信息',
    content: (formData, selectOptions) => [
      h(Col, { span: 12 }, [
        h(FormItem, { label: '部门', name: 'department' }, [
          h(Select, {
            placeholder: '请选择部门',
            value: formData.department,
            'onUpdate:value': (val: string) => (formData.department = val),
            options: departments.map((dept) => ({
              label: dept.name,
              value: dept.id.toString(),
            })),
          }),
        ]),
      ]),
      h(Col, { span: 12 }, [
        h(FormItem, { label: '角色', name: 'role' }, [
          createRemoteSelect('roles', {
            placeholder: '请选择角色（支持搜索）',
            value: formData.role,
            'onUpdate:value': (val: string) => (formData.role = val),
            allowClear: true,
          }),
        ]),
      ]),
      h(Col, { span: 12 }, [
        h(FormItem, { label: '状态', name: 'status' }, [
          h(Select, {
            placeholder: '请选择状态',
            value: formData.status,
            'onUpdate:value': (val: number) => (formData.status = val),
            options: statusOptions,
          }),
        ]),
      ]),
      h(Col, { span: 12 }, [
        h(FormItem, { label: '入职日期', name: 'hireDate' }, [
          h(DatePicker, {
            valueFormat: 'YYYY-MM-DD',
            style: 'width: 100%',
            value: formData.hireDate,
            'onUpdate:value': (val: string) => (formData.hireDate = val),
          }),
        ]),
      ]),
      h(Col, { span: 24 }, [
        h(FormItem, { label: '技能', name: 'skills' }, [
          h(Select, {
            mode: 'multiple',
            placeholder: '请选择技能',
            value: formData.skills,
            'onUpdate:value': (val: string[]) => (formData.skills = val),
            options: selectOptions?.skills || [],
            style: 'width: 100%',
          }),
        ]),
      ]),
    ],
  },

  // 高级设置组
  {
    title: '高级设置',
    content: (formData) => [
      h(Col, { span: 24 }, [
        h(FormItem, { label: '显示高级选项', name: 'showAdvanced' }, [
          h(Switch, {
            checked: Boolean(formData.showAdvanced),
            'onUpdate:checked': (val: boolean) => (formData.showAdvanced = val),
          }),
        ]),
      ]),
      h(Col, { span: 24 }, [
        renderField(
          'advancedSetting',
          h(FormItem, { label: '高级配置', name: 'advancedSetting' }, [
            h(Input, {
              placeholder: '请输入高级配置项',
              value: formData.advancedSetting,
              'onUpdate:value': (val: string) =>
                (formData.advancedSetting = val),
            }),
          ]),
        ),
      ]),
      h(Col, { span: 24 }, [
        renderField(
          'remarks',
          h(FormItem, { label: '备注', name: 'remarks' }, [
            h(TextArea, {
              placeholder: '请输入备注信息',
              value: formData.remarks,
              'onUpdate:value': (val: string) => (formData.remarks = val),
              rows: 4,
            }),
          ]),
        ),
      ]),
    ],
  },
];

// 创建表单弹窗组件
const formModalComponent = defineComponent({
  setup() {
    return () => renderFormModal(formGroups);
  },
});

// 对外暴露显示方法
defineExpose({
  show,
});
</script>

<template>
  <div>
    <component :is="formModalComponent" />
  </div>
</template>

<style lang="less" scoped>
.modal-fullscreen-btn {
  cursor: pointer;
  margin-left: 16px;
  display: flex;
  align-items: center;
  font-size: 16px;
}
</style>
