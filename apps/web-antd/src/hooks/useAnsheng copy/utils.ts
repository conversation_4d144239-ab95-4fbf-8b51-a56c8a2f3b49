import { AnsengError, ErrorType } from './types';

/**
 * 深拷贝函数 - 优化版本
 * 对于大多数使用场景足够安全，且性能优于structuredClone
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  try {
    // 尝试使用JSON方法进行深拷贝，这对于大多数场景已经足够
    return JSON.parse(JSON.stringify(obj));
  } catch {
    // 如果有循环引用或其他问题，回退到structuredClone
    try {
      return structuredClone(obj);
    } catch (error) {
      console.error('深拷贝失败:', error);
      throw new AnsengError(
        '对象拷贝失败，可能包含循环引用或不可序列化的值',
        ErrorType.UNKNOWN_ERROR,
        { originalError: error },
      );
    }
  }
}

/**
 * 防抖函数
 * 用于限制函数的调用频率，只有当上一次调用过了指定的延迟时间后，才会执行新的调用
 */
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay = 300,
): (...args: Parameters<T>) => void {
  let timer: null | number = null;

  return function (...args: Parameters<T>) {
    if (timer) {
      clearTimeout(timer);
    }

    timer = window.setTimeout(() => {
      fn.apply(this, args);
      timer = null;
    }, delay);
  };
}

/**
 * 统一错误处理函数
 * 根据错误类型执行不同的处理逻辑
 */
export function handleError(
  error: unknown,
  defaultMessage = '操作失败',
): AnsengError {
  // 已经是AnsengError，直接返回
  if (error instanceof AnsengError) {
    return error;
  }

  // 网络错误
  if (error instanceof TypeError && error.message.includes('network')) {
    return new AnsengError(
      '网络连接失败，请检查网络设置',
      ErrorType.NETWORK_ERROR,
      error,
    );
  }

  // API错误
  if (error && typeof error === 'object' && 'code' in error && 'msg' in error) {
    const apiError = error as { code: number; msg: string };
    return new AnsengError(
      apiError.msg || defaultMessage,
      ErrorType.API_ERROR,
      apiError,
    );
  }

  // 普通Error对象
  if (error instanceof Error) {
    return new AnsengError(error.message, ErrorType.UNKNOWN_ERROR, error);
  }

  // 其他情况
  return new AnsengError(
    typeof error === 'string' ? error : defaultMessage,
    ErrorType.UNKNOWN_ERROR,
    error,
  );
}

/**
 * 确保异步函数不会多次并发执行
 * 如果函数正在执行，再次调用会被忽略
 */
export function asyncOnce<T extends (...args: any[]) => Promise<any>>(
  fn: T,
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
  let pending = false;
  let lastPromise: null | Promise<ReturnType<T>> = null;

  return async function (...args: Parameters<T>): Promise<ReturnType<T>> {
    if (pending) {
      return lastPromise as Promise<ReturnType<T>>;
    }

    try {
      pending = true;
      lastPromise = fn.apply(this, args) as Promise<ReturnType<T>>;
      return await lastPromise;
    } finally {
      pending = false;
    }
  };
}

/**
 * 等待指定时间
 */
export function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * 安全获取对象属性，避免因undefined或null导致的错误
 */
export function safeGet<T, K extends keyof T>(
  obj: null | T | undefined,
  key: K,
): T[K] | undefined {
  return obj ? obj[key] : undefined;
}

/**
 * 类型检查工具函数
 */
export function isObject(value: unknown): value is Record<string, unknown> {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}

/**
 * 防止在短时间内多次提交
 * 比普通防抖更严格，在指定时间内绝对不允许第二次提交
 */
export function preventMultiSubmit<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  duration = 1000,
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
  let lastSubmitTime = 0;

  return async function (...args: Parameters<T>): Promise<ReturnType<T>> {
    const now = Date.now();
    const timeSinceLastSubmit = now - lastSubmitTime;

    if (timeSinceLastSubmit < duration) {
      throw new AnsengError(
        `请勿频繁提交，请在${Math.ceil((duration - timeSinceLastSubmit) / 1000)}秒后再试`,
        ErrorType.VALIDATION_ERROR,
      );
    }

    lastSubmitTime = now;
    return await fn.apply(this, args);
  };
}
