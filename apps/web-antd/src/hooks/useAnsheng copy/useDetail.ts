// useAnsheng/useDetail.ts

import type { UseDetailOptions } from './types';

import { nextTick, ref } from 'vue';

/**
 * useDetail hook - 详情查看功能
 * @param options 详情配置
 */
export function useDetail<T extends Record<string, any>>(
  options: UseDetailOptions,
) {
  // 是否显示
  const visible = ref(false);

  // 当前记录
  const record = ref<null | T>(null);

  // 处理详情标题
  const title = options.title || '详情';

  // 处理详情区块
  const sections = options.sections.map((section) => ({
    ...section,
    items: section.items.map((item) => ({
      ...item,
      value: '',
    })),
  }));

  // 根据格式化器处理值
  const formatValue = (field: string, value: any) => {
    if (options.formatters && options.formatters[field]) {
      return options.formatters[field](value);
    }

    // 如果是数组，转换为逗号分隔的字符串
    if (Array.isArray(value)) {
      return value.join(', ');
    }

    // 未定义或null显示为空
    if (value === undefined || value === null) {
      return '';
    }

    return value;
  };

  // 更新详情数据
  const updateDetailData = (data: T) => {
    try {
      // 深拷贝数据，避免引用问题
      const safeData = JSON.parse(JSON.stringify(data));
      record.value = safeData;

      // 更新所有section的items的value
      sections.forEach((section) => {
        section.items.forEach((item) => {
          const field = item.field as keyof T;
          // @ts-ignore - 字段可能在DetailSection.items中是字符串，但在T中是键
          item.value = formatValue(field as string, safeData[field]);
        });
      });
    } catch (error) {
      console.error('更新详情数据失败:', error);
    }
  };

  // 打开详情 - 修复版
  const open = (data: T) => {
    // 已经打开的情况下，先关闭再打开
    if (visible.value) {
      visible.value = false;

      // 使用nextTick确保DOM更新完成后再打开
      nextTick(() => {
        updateDetailData(data);
        // 延迟一点点可以确保UI更新
        setTimeout(() => {
          visible.value = true;
        }, 10);
      });
    } else {
      // 未打开直接更新数据并显示
      updateDetailData(data);
      visible.value = true;
    }
  };

  // 关闭详情
  const close = () => {
    visible.value = false;
    // 延迟清除数据，避免闪烁
    setTimeout(() => {
      record.value = null;
    }, 200);
  };

  return {
    visible,
    record,
    title,
    sections,
    open,
    close,
  };
}
