import { computed } from 'vue';

import { useUserStore } from '@vben/stores';

/**
 * 用户权限相关的hook
 * @returns 用户权限相关的计算属性
 */
export function useUserPermission() {
  const userStore = useUserStore();

  // 判断是否为管理员
  const isAdmin = computed(() => userStore.userInfo?.authority === 1);

  // 获取用户ID
  const userId = computed(() => userStore.userInfo?.id);

  // 获取用户名
  const userName = computed(() => userStore.userInfo?.realName);

  // 获取用户角色
  const userRoles = computed(() => userStore.userRoles || []);

  // 判断是否有特定权限
  const hasPermission = (_permissionCode: string) => {
    // 管理员默认拥有所有权限
    if (isAdmin.value) {
      return true;
    }

    // 这里可以根据实际业务扩展权限判断逻辑
    // 例如检查用户角色是否包含特定权限

    return false;
  };

  return {
    isAdmin,
    userId,
    userName,
    userRoles,
    hasPermission,
  };
}
