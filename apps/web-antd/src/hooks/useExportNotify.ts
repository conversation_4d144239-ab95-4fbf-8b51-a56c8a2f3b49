// apps/web-antd/src/hooks/useExportNotify.ts
import { h, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Modal } from 'ant-design-vue';

export function useExportNotify(downloadPagePath = '/task/system') {
  const timer = ref<null | number>(null);
  const countdown = ref(3);

  const router = useRouter();

  function showExportModal() {
    countdown.value = 3;

    // 这里用 h() 创建响应式内容
    // const contentVNode = () =>
    //   h('span', [
    //     h(
    //       'span',
    //       `${countdown.value}秒后将自动跳转到下载中心，或点击“立即前往”`,
    //     ),
    //   ]);

    Modal.confirm({
      title: '导出成功',
      // content: () =>
      //   `${countdown.value}秒后将自动跳转到下载中心，或点击“立即前往”`,
      content: () =>
        h('span', [
          h(
            'span',
            `${countdown.value}秒后将自动跳转到下载中心，或点击“立即前往”`,
          ),
        ]),
      okText: '立即前往',
      cancelText: '取消',
      onOk: () => {
        if (timer.value) clearInterval(timer.value);
        router.push(downloadPagePath);
      },
      onCancel: () => {
        if (timer.value) clearInterval(timer.value);
      },
      afterClose: () => {
        if (timer.value) clearInterval(timer.value);
      },
    });

    timer.value = window.setInterval(() => {
      countdown.value -= 1;
      if (countdown.value <= 0) {
        if (timer.value) clearInterval(timer.value);
        Modal.destroyAll();
        router.push(downloadPagePath);
      }
    }, 1000);
  }

  return { showExportModal, countdown };
}
