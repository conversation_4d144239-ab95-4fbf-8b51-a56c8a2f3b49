<script setup lang="ts">
import { Card, Spin, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

interface HeaderConfig {
  status?: {
    color: string;
    text: string;
  };
  title?: string;
  extra?: string;
}

interface DetailItem {
  label: string;
  value: any;
  subValue?: string;
  subLabel?: string;
  highlight?: boolean;
  className?: string;
  render?: any;
  format?: 'date' | 'datetime' | 'money' | 'number';
  formatOptions?: any;
}

interface Section {
  type: 'custom' | 'grid' | 'statistic';
  title?: string;
  column?: number;
  items?: DetailItem[];
  slotName?: string;
}

interface ErrorInfo {
  title?: string;
  content: string;
}

const props = withDefaults(
  defineProps<{
    errorInfo?: ErrorInfo;
    headerConfig?: HeaderConfig;
    loading?: boolean;
    sections: Section[];
  }>(),
  {
    loading: false,
  },
);

// 格式化值
const formatValue = (item: DetailItem) => {
  if (!item.format) return item.value ?? '-';

  switch (item.format) {
    case 'date': {
      return item.value ? dayjs(item.value).format('YYYY-MM-DD') : '-';
    }
    case 'datetime': {
      return item.value ? dayjs(item.value).format('YYYY-MM-DD HH:mm:ss') : '-';
    }
    case 'money': {
      return item.value ? `¥${Number(item.value).toFixed(2)}` : '¥0.00';
    }
    case 'number': {
      return item.value ? Number(item.value).toLocaleString() : '0';
    }
    default: {
      return item.value ?? '-';
    }
  }
};

// 计算网格样式
const getGridStyle = (column: number = 3) => ({
  gridTemplateColumns: `repeat(${column}, 1fr)`,
});
</script>

<template>
  <div class="dynamic-detail">
    <div class="content-wrapper" :class="{ loading }">
      <!-- 头部区域 -->
      <div v-if="headerConfig" class="header-section">
        <div class="header-left">
          <Tag v-if="headerConfig.status" :color="headerConfig.status.color">
            {{ headerConfig.status.text }}
          </Tag>
          <div v-if="headerConfig.title" class="header-title">
            {{ headerConfig.title }}
          </div>
        </div>
        <div v-if="headerConfig.extra" class="header-extra">
          <component :is="headerConfig.extra" />
        </div>
      </div>

      <!-- 内容区域 -->
      <Card
        v-for="(section, index) in sections"
        :key="index"
        :bordered="false"
        class="detail-card"
      >
        <!-- 普通信息网格 -->
        <div v-if="section.type === 'grid'" class="card-block">
          <div class="info-grid" :style="getGridStyle(section.column)">
            <div
              v-for="(item, i) in section.items"
              :key="i"
              class="info-item"
              :class="item.className"
            >
              <span class="label">{{ item.label }}</span>
              <span class="value" :class="{ highlight: item.highlight }">
                <component
                  v-if="item.render"
                  :is="item.render"
                  :value="item.value"
                />
                <template v-else>
                  {{ formatValue(item) }}
                </template>
              </span>
            </div>
          </div>
        </div>

        <!-- 统计数值展示 -->
        <div
          v-if="section.type === 'statistic'"
          class="card-block"
          v-domain-permission="1"
        >
          <div class="statistic-section" :style="getGridStyle(section.column)">
            <div
              v-for="(item, i) in section.items"
              :key="i"
              class="statistic-item"
              :class="item.className"
            >
              <span class="label">{{ item.label }}</span>
              <span class="value" :class="{ highlight: item.highlight }">
                {{ formatValue(item) }}
              </span>
              <span v-if="item.subValue" class="sub-value">
                {{ item.subValue }}
              </span>
            </div>
          </div>
        </div>

        <!-- 自定义内容 -->
        <div v-if="section.type === 'custom'" class="card-block">
          <slot :name="section.slotName"></slot>
        </div>
      </Card>

      <!-- 错误信息展示 -->
      <Card v-if="errorInfo" :bordered="false" class="detail-card error-card">
        <div class="error-header">
          <div class="error-icon">!</div>
          <span>{{ errorInfo.title || '错误信息' }}</span>
        </div>
        <pre class="error-content">{{ errorInfo.content }}</pre>
      </Card>
    </div>

    <!-- 加载状态 -->
    <div class="spin-container" v-if="loading">
      <Spin />
    </div>
  </div>
</template>

<style lang="less" scoped>
.dynamic-detail {
  position: relative;
  min-height: 200px;

  .content-wrapper {
    transition: all 0.3s ease-in-out;
    opacity: 1;

    &.loading {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  .spin-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  // 头部区域
  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px;
    background: var(--ant-color-bg-container-secondary);
    border-radius: 6px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      :deep(.ant-tag) {
        margin: 0;
        padding: 4px 12px;
        font-size: 13px;
        font-weight: 500;
        border-radius: 12px;
      }

      .header-title {
        font-size: 13px;
        color: var(--ant-color-text-secondary);
      }
    }

    .header-extra {
      font-size: 24px;
      font-weight: 600;
      color: var(--ant-color-primary);
      font-family:
        'DIN Alternate',
        -apple-system,
        BlinkMacSystemFont,
        sans-serif;
    }
  }

  // 详情卡片
  .detail-card {
    margin-bottom: 12px;
    border-radius: 6px;

    :deep(.ant-card-body) {
      padding: 0;
    }

    .card-block {
      padding: 16px;

      // 信息网格
      .info-grid {
        display: grid;
        gap: 12px;
      }

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: var(--ant-color-bg-container-secondary);
        border-radius: 4px;

        .label {
          font-size: 13px;
          color: var(--ant-color-text-secondary);
        }

        .value {
          font-size: 13px;
          color: var(--ant-color-text);
          text-align: right;

          &.highlight {
            color: var(--ant-color-primary);
            font-weight: 500;
          }
        }
      }

      // 统计数值
      .statistic-section {
        display: grid;
        gap: 12px;

        .statistic-item {
          padding: 12px;
          background: var(--ant-color-bg-container-secondary);
          border-radius: 4px;
          text-align: center;

          .label {
            display: block;
            font-size: 13px;
            color: var(--ant-color-text-secondary);
            margin-bottom: 8px;
          }

          .value {
            font-size: 20px;
            font-weight: 600;
            color: var(--ant-color-text);
            font-family:
              'DIN Alternate',
              -apple-system,
              BlinkMacSystemFont,
              sans-serif;
            margin-bottom: 4px;

            &.highlight {
              color: var(--ant-color-primary);
            }
          }

          .sub-label {
            display: block;
            font-size: 12px;
            color: var(--ant-color-text-secondary);
            margin-bottom: 4px;
          }

          .sub-value {
            display: block;
            font-size: 16px;
            color: var(--ant-color-text);
            font-family:
              'DIN Alternate',
              -apple-system,
              BlinkMacSystemFont,
              sans-serif;
          }
        }
      }
    }
  }

  // 错误信息卡片
  .error-card {
    border-color: var(--ant-color-error);
    border-radius: 6px;
    overflow: hidden;

    .error-header {
      padding: 12px 16px;
      background: var(--ant-color-error-bg);
      color: var(--ant-color-error);
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 13px;
      font-weight: 500;

      .error-icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: var(--ant-color-error);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 12px;
      }
    }

    .error-content {
      margin: 0;
      padding: 16px;
      font-family:
        ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
      font-size: 12px;
      line-height: 1.5;
      color: var(--ant-color-text);
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 160px;
      overflow-y: auto;
      background: var(--ant-color-bg-container);
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .dynamic-detail {
    .header-section {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      padding: 12px;
      margin-bottom: 12px;
    }

    .detail-card {
      .card-block {
        padding: 12px;

        .info-grid,
        .statistic-section {
          grid-template-columns: 1fr !important;
          gap: 8px;
        }
      }
    }
  }
}
</style>
