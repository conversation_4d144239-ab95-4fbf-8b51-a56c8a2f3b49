<script lang="ts" setup>
import type { APIBalanceBuyModalEmits, APIBalanceBuyModalProps } from './types';

import { computed, ref, watch } from 'vue';

import { useUserStore } from '@vben/stores';

import {
  Button,
  Card,
  Divider,
  Empty,
  message,
  Modal,
  QRCode,
  Radio,
  Spin,
} from 'ant-design-vue';

import { getAPIBalanceList } from '#/api/core/api-balance';
import {
  createAgentBalanceOrderAs,
  getPaymentStatus,
  getPayParameter,
} from '#/api/core/payment';

const props = defineProps<APIBalanceBuyModalProps>();
const emit = defineEmits<APIBalanceBuyModalEmits>();

const userStore = useUserStore();

// API预存余额充值套餐列表
const apiBalancePackages = ref<any[]>([]);
const loadingPackages = ref(false);
const selectedPackage = ref<any | null>(null);

// 支付方式列表
const paymentMethods = ref<any[]>([]);
const selectedPayment = ref<any | null>(null);
const loadingPaymentMethods = ref(false);

// 订单和支付相关
const orderInfo = ref<any>(null);
const paymentInfo = ref<any>(null);
const orderLoading = ref(false);
const paymentLoading = ref(false);
const showQRCode = ref(false);
const qrCodeValue = ref('');
const qrCodeLoading = ref(false);

// 获取支付类型名称
const getPaymentTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    wxpay: '微信支付',
    alipay: '支付宝',
    hfpay: '汇付',
    MaPay: '码支付',
    dogohfpay: '斗拱',
    firefacepay: '火脸',
  };
  return typeMap[type] || type;
};

// 重置支付状态
const resetPaymentState = () => {
  selectedPackage.value = null;
  selectedPayment.value = null;
  orderInfo.value = null;
  paymentInfo.value = null;
  showQRCode.value = false;
  qrCodeValue.value = '';
};

// 按支付类型分组的支付方式
const paymentGroups = computed(() => {
  const groups: Record<string, any[]> = {};

  paymentMethods.value.forEach((method) => {
    if (method && method.type) {
      if (!groups[method.type]) {
        groups[method.type] = [];
      }

      const group = groups[method.type];
      if (group) {
        group.push(method);
      }
    }
  });

  return Object.entries(groups).map(([type, methods]) => ({
    type,
    typeName: getPaymentTypeName(type),
    methods,
  }));
});

// 获取API预存余额包列表
const fetchAPIBalancePackages = async () => {
  try {
    loadingPackages.value = true;
    const response = await getAPIBalanceList({ page: 1, pageSize: 10_000 });
    if (
      response &&
      response.code === 1 &&
      response.data &&
      response.data.rows
    ) {
      apiBalancePackages.value = response.data.rows;
    }
  } catch (error) {
    console.error('获取API预存余额包失败:', error);
    message.error('获取API预存余额包失败，请稍后重试');
  } finally {
    loadingPackages.value = false;
  }
};

// 获取支付方式列表
const fetchPaymentMethods = async () => {
  try {
    loadingPaymentMethods.value = true;
    // 使用type=7表示代理购买余额
    const response = await getPaymentStatus({
      type: 7,
      userId: userStore.userInfo?.id?.toString() || '',
    });

    if (response && response.code === 1) {
      // 处理返回的数据，可能是数组或单个对象
      const data = Array.isArray(response.data)
        ? response.data
        : [response.data];
      paymentMethods.value = data;
    }
  } catch (error) {
    console.error('获取支付方式失败:', error);
    message.error('获取支付方式失败，请稍后重试');
  } finally {
    loadingPaymentMethods.value = false;
  }
};

// 关闭余额充值弹窗
const handleClose = () => {
  emit('update:visible', false);
  resetPaymentState();
};

// 选择套餐
const handleSelectPackage = (pkg: any) => {
  selectedPackage.value = pkg;
  // 选择新套餐时重置支付信息
  orderInfo.value = null;
  paymentInfo.value = null;
  showQRCode.value = false;
};

// 创建API预存余额充值订单
const createOrder = async () => {
  if (!selectedPackage.value) {
    message.warning('请先选择充值套餐');
    return null;
  }

  try {
    orderLoading.value = true;
    // 使用createAgentBalanceOrderAs创建API预存余额订单
    const response = await createAgentBalanceOrderAs(
      selectedPackage.value.id.toString(),
    );

    if (response && response.code === 1 && response.data) {
      orderInfo.value = response.data;
      return response.data;
    } else {
      message.error(response?.msg || '创建订单失败');
      return null;
    }
  } catch (error) {
    console.error('创建充值订单失败:', error);
    message.error('创建充值订单失败，请稍后重试');
    return null;
  } finally {
    orderLoading.value = false;
  }
};

// 获取支付参数
const getPaymentParams = async (orderNo: string) => {
  if (!selectedPayment.value) {
    message.warning('请先选择支付方式');
    return null;
  }

  try {
    paymentLoading.value = true;
    const response = await getPayParameter({
      orderNo,
      orderType: '7', // 7表示代理购买余额
      payId: selectedPayment.value.payId.toString(),
      payType: selectedPayment.value.type,
      payWay: selectedPayment.value.payWay || undefined,
      type: '2', // 2表示扫码支付
      notifyUrl: window.location.origin,
    });

    if (response && response.code === 1) {
      paymentInfo.value = response.data;

      // 根据不同的支付方式返回不同的数据结构
      return response;
    } else {
      message.error(response?.msg || '获取支付参数失败');
      return null;
    }
  } catch (error) {
    console.error('获取支付参数失败:', error);
    message.error('获取支付参数失败，请稍后重试');
    return null;
  } finally {
    paymentLoading.value = false;
  }
};

// 监听弹窗可见性变化
const handleVisibleChange = async (visible: boolean) => {
  if (visible) {
    resetPaymentState();
    await Promise.all([fetchAPIBalancePackages(), fetchPaymentMethods()]);
  }
};

// 确认购买
const purchaseLoading = ref(false);

// 处理购买确认
const handleConfirmPurchase = async () => {
  if (!selectedPackage.value) {
    message.warning('请先选择充值套餐');
    return;
  }

  if (!selectedPayment.value) {
    message.warning('请先选择支付方式');
    return;
  }

  try {
    purchaseLoading.value = true;

    // 1. 创建订单
    const order = await createOrder();
    if (!order || !order.systemOrdernumber) {
      return;
    }

    // 2. 获取支付参数
    const payResponse = await getPaymentParams(order.systemOrdernumber);
    if (!payResponse || !payResponse.data) {
      return;
    }

    // 3. 显示支付二维码
    showQRCode.value = true;

    const payData = payResponse.data;

    // 处理不同类型的支付响应
    if (typeof payData === 'string') {
      // 直接是二维码链接（微信扫码或支付宝）
      qrCodeValue.value = payData;
    } else if (
      payData.orderType !== undefined &&
      payData.type !== undefined &&
      payData.url !== undefined
    ) {
      // 智讯码支付
      if (payData.type === 1 || payData.type === 2) {
        qrCodeValue.value = payData.url;
      } else {
        message.warning('暂不支持的支付方式');
      }
    } else if (payData.timeStamp && payData.package && payData.paySign) {
      // 微信JSAPI支付，需要特殊处理
      message.warning('当前环境不支持JSAPI支付，请选择其他支付方式');
    } else {
      message.error('获取支付二维码失败');
    }
  } catch (error) {
    console.error('API预存余额充值失败:', error);
    message.error('充值失败，请稍后重试');
  } finally {
    purchaseLoading.value = false;
  }
};

// 计算总价值
const getTotalValue = (pkg: any) => {
  const amount = Number(pkg.amount || 0);
  const giveAmount = Number(pkg.giveAmount || 0);
  return (amount + giveAmount).toFixed(2);
};

// 监听visible变化
watch(() => props.visible, handleVisibleChange);
</script>

<template>
  <Modal
    :visible="visible"
    title="API预存余额充值"
    @cancel="handleClose"
    :footer="null"
    width="800px"
    :body-style="{ padding: '24px' }"
  >
    <Spin
      :spinning="
        loadingPackages ||
        loadingPaymentMethods ||
        orderLoading ||
        paymentLoading
      "
    >
      <div class="api-balance-modal-container">
        <!-- 套餐选择 -->
        <div v-if="!showQRCode" class="packages-section">
          <h3 class="section-title">选择充值套餐</h3>

          <!-- 套餐列表为空时显示空状态 -->
          <Empty
            v-if="apiBalancePackages.length === 0 && !loadingPackages"
            description="暂无可用充值套餐"
          />

          <!-- 套餐卡片 -->
          <div class="package-cards">
            <Card
              v-for="pkg in apiBalancePackages"
              :key="pkg.id"
              class="package-card"
              :class="{ selected: selectedPackage?.id === pkg.id }"
              hoverable
              :bordered="true"
              @click="handleSelectPackage(pkg)"
            >
              <div class="card-content">
                <div class="pkg-price">¥{{ pkg.amount }}</div>
                <div class="pkg-balance">充值金额: ¥{{ pkg.amount }}</div>
                <div v-if="pkg.giveAmount > 0" class="pkg-gift">
                  赠送金额: ¥{{ pkg.giveAmount }}
                </div>
                <div v-if="pkg.giveAmount > 0" class="pkg-total-value">
                  总价值: ¥{{ getTotalValue(pkg) }}
                </div>
              </div>
              <div v-if="selectedPackage?.id === pkg.id" class="selected-badge">
                <span class="selected-icon">✓</span>
              </div>
            </Card>
          </div>
        </div>

        <!-- 支付方式选择 -->
        <div v-if="selectedPackage && !showQRCode" class="payment-section">
          <Divider />
          <h3 class="section-title">选择支付方式</h3>

          <Empty
            v-if="paymentGroups.length === 0 && !loadingPaymentMethods"
            description="暂无可用支付方式"
          />

          <div v-else class="payment-groups">
            <div
              v-for="group in paymentGroups"
              :key="group.type"
              class="payment-group"
            >
              <div class="payment-group-title">{{ group.typeName }}</div>
              <div class="payment-methods">
                <Radio.Group
                  v-model:value="selectedPayment"
                  button-style="solid"
                >
                  <Radio.Button
                    v-for="method in group.methods"
                    :key="
                      method.key ||
                      `${method.type}-${method.payId}-${method.payWay || ''}`
                    "
                    :value="method"
                    class="payment-method-item"
                  >
                    {{
                      method.name ||
                      `${group.typeName}${method.payWay ? ` ${method.payWay}` : ''}`
                    }}
                  </Radio.Button>
                </Radio.Group>
              </div>
            </div>
          </div>
        </div>

        <!-- 支付二维码 -->
        <div v-if="showQRCode" class="qrcode-section">
          <div class="qrcode-header">
            <h3 class="section-title">请扫码支付</h3>
            <div class="order-info">
              <div class="order-item">
                <span class="label">订单号:</span>
                <span class="value">{{ orderInfo?.systemOrdernumber }}</span>
              </div>
              <div class="order-item">
                <span class="label">金额:</span>
                <span class="value price">¥{{ selectedPackage?.amount }}</span>
              </div>
            </div>
          </div>

          <div class="qrcode-container">
            <Spin :spinning="qrCodeLoading" tip="加载中...">
              <div v-if="qrCodeValue" class="qrcode">
                <QRCode :value="qrCodeValue" :size="200" />
              </div>
              <div v-else class="qrcode-placeholder">
                <Empty description="获取支付二维码失败" />
              </div>
            </Spin>
            <div class="qrcode-tips">
              <p>
                请使用{{
                  selectedPayment?.name ||
                  getPaymentTypeName(selectedPayment?.type)
                }}扫码支付
              </p>
              <p>支付完成后请点击"支付完成"按钮</p>
            </div>
          </div>

          <div class="qrcode-actions">
            <Button @click="showQRCode = false">返回</Button>
            <Button type="primary" @click="emit('success', selectedPackage)">
              支付完成
            </Button>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div v-if="!showQRCode" class="action-section">
          <Button @click="handleClose">取消</Button>
          <Button
            type="primary"
            :disabled="!selectedPackage || !selectedPayment"
            :loading="purchaseLoading"
            @click="handleConfirmPurchase"
          >
            立即充值
          </Button>
        </div>
      </div>
    </Spin>
  </Modal>
</template>

<style lang="scss" scoped>
.api-balance-modal-container {
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
  }

  .packages-section {
    margin-bottom: 24px;

    .package-cards {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;

      .package-card {
        position: relative;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-4px);
        }

        &.selected {
          background-color: var(--primary-1);
          border-color: var(--primary-color);
        }

        .card-content {
          text-align: center;

          .pkg-price {
            margin-bottom: 16px;
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
          }

          .pkg-balance {
            margin-bottom: 8px;
            font-size: 14px;
          }

          .pkg-gift {
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--success-color);
          }

          .pkg-total-value {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .selected-badge {
          position: absolute;
          top: -10px;
          right: -10px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          background-color: var(--primary-color);
          border-radius: 50%;
          box-shadow: 0 2px 6px rgb(0 0 0 / 15%);

          .selected-icon {
            font-size: 14px;
            color: #fff;
          }
        }
      }
    }
  }

  .payment-section {
    margin-bottom: 24px;

    .payment-groups {
      .payment-group {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .payment-group-title {
          font-weight: 500;
          margin-bottom: 12px;
          color: var(--text-color-secondary);
        }

        .payment-methods {
          margin-top: 8px;

          .payment-method-item {
            margin-right: 12px;
            margin-bottom: 12px;
          }
        }
      }
    }
  }

  .qrcode-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;

    .qrcode-header {
      width: 100%;
      text-align: center;
      margin-bottom: 24px;

      .section-title {
        margin-bottom: 16px;
      }

      .order-info {
        background-color: var(--background-light);
        padding: 12px 16px;
        border-radius: 8px;
        display: inline-block;

        .order-item {
          display: flex;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            width: 60px;
            color: var(--text-color-secondary);
          }

          .value {
            font-weight: 500;

            &.price {
              color: var(--primary-color);
              font-size: 18px;
            }
          }
        }
      }
    }

    .qrcode-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 24px;

      .qrcode {
        padding: 16px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .qrcode-placeholder {
        width: 200px;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .qrcode-tips {
        margin-top: 16px;
        text-align: center;
        color: var(--text-color-secondary);
        font-size: 14px;

        p {
          margin-bottom: 8px;
        }
      }
    }

    .qrcode-actions {
      display: flex;
      gap: 12px;
    }
  }

  .action-section {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
  }
}
</style>
