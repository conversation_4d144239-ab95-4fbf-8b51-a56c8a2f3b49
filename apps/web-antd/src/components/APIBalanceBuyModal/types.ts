/**
 * API预存余额包
 */
export interface APIBalancePackage {
  /**
   * 包ID
   */
  id: number;

  /**
   * 预存金额
   */
  amount: number;

  /**
   * 赠送金额
   */
  giveAmount: number;
}

/**
 * API预存余额购买弹窗属性
 */
export interface APIBalanceBuyModalProps {
  /**
   * 弹窗是否可见
   */
  visible: boolean;

  /**
   * 用户ID，可选
   */
  userId?: number;
}

/**
 * API预存余额购买弹窗事件
 */
export interface APIBalanceBuyModalEmits {
  /**
   * 更新可见性状态
   */
  'update:visible': [visible: boolean];

  /**
   * 购买成功事件
   */
  success: [packageInfo: APIBalancePackage];
}
