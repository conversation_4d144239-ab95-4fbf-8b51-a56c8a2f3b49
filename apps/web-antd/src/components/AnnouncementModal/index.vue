<script lang="ts" setup>
import { computed, ref } from 'vue';

import { <PERSON><PERSON>Bell, MdiCalendarClock } from '@vben/icons';

import { <PERSON><PERSON>, Divider, Modal, Tag } from 'ant-design-vue';

export interface AnnouncementItem {
  id: number;
  title: string;
  detail: string;
  type: number;
  status: null | number;
  channel_id: null | number;
  creation_time: string;
  update_time: string;
  importance?: 'important' | 'normal' | 'urgent'; // 重要程度
  read?: boolean; // 是否已读
}

const props = defineProps<{
  announcement?: AnnouncementItem | null;
  showCloseButton?: boolean;
  visible: boolean;
  width?: number | string;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'close'): void;
  (e: 'read', id: number): void;
}>();

const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch {
    return dateString;
  }
};

const importanceMap = {
  normal: { color: 'blue', text: '普通' },
  important: { color: 'orange', text: '重要' },
  urgent: { color: 'red', text: '紧急' },
};

const importanceTag = computed(() => {
  const importance = props.announcement?.importance || 'normal';
  return importanceMap[importance];
});

const handleClose = () => {
  emit('update:visible', false);
  emit('close');
  if (props.announcement?.id && !props.announcement?.read) {
    emit('read', props.announcement.id);
  }
};

// 如果visible变化且为true时，显示模态框
const internalVisible = ref(props.visible);

// 监听props.visible的变化
const handleVisibleChange = (val: boolean) => {
  internalVisible.value = val;
  if (!val) {
    handleClose();
  }
};
</script>

<template>
  <Modal
    v-model:visible="internalVisible"
    :title="null"
    :footer="null"
    :mask-closable="true"
    :width="width || 640"
    :destroy-on-close="true"
    class="custom-announcement-modal"
    @cancel="handleClose"
    @update:visible="handleVisibleChange"
  >
    <div class="p-1">
      <div v-if="announcement" class="p-4">
        <div class="announcement-container">
          <div class="announcement-header mb-2 flex items-center">
            <MdiBell class="announcement-icon mr-2 text-xl text-blue-500" />
            <span class="announcement-title text-lg font-medium">{{
              announcement.title
            }}</span>
            <Tag :color="importanceTag.color" class="ml-2">
              {{ importanceTag.text }}
            </Tag>
          </div>

          <div
            class="announcement-meta mb-3 flex items-center text-sm text-gray-500"
          >
            <MdiCalendarClock class="mr-1" />
            <span>发布时间: {{ formatDate(announcement.creation_time) }}</span>
          </div>

          <Divider class="my-3" />

          <div
            class="announcement-body prose max-w-none whitespace-pre-wrap"
            v-html="announcement.detail"
          ></div>

          <div class="announcement-footer mt-6 flex justify-end">
            <Button type="primary" @click="handleClose">我知道了</Button>
          </div>
        </div>
      </div>
      <div v-else class="p-6 text-center text-gray-500">暂无公告内容</div>
    </div>
  </Modal>
</template>

<style scoped>
.custom-announcement-modal :deep(.ant-modal-content) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.custom-announcement-modal :deep(.ant-modal-body) {
  padding: 0;
}

.announcement-container {
  position: relative;
}

.announcement-title {
  font-weight: 500;
}

.announcement-body {
  min-height: 100px;
  max-height: 60vh;
  overflow-y: auto;
}

.announcement-body :deep(img) {
  max-width: 100%;
  height: auto;
}

.announcement-body :deep(a) {
  color: #1890ff;
  text-decoration: none;
}

.announcement-body :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1rem;
}

.announcement-body :deep(table td),
.announcement-body :deep(table th) {
  border: 1px solid #ddd;
  padding: 8px;
}

.announcement-body :deep(pre) {
  background: #f6f8fa;
  border-radius: 3px;
  padding: 12px;
  overflow: auto;
}
</style>
