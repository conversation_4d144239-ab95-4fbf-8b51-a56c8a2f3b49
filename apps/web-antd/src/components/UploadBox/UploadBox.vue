<script setup lang="ts">
import type {
  UploadBoxEmits,
  UploadBoxInstance,
  UploadBoxProps,
} from './types';

import { computed, ref, watch } from 'vue';

import { MdiClose, MdiCloudUpload, MdiFile } from '@vben/icons';

import { Button, message, Upload } from 'ant-design-vue';

const props = withDefaults(defineProps<UploadBoxProps>(), {
  supportText: '支持 .xlsx, .xls 格式的文件',
  showDownload: true,
  downloadText: '下载模板',
  size: 'default',
  uploadText: '点击或拖拽文件上传',
  subUploadText: '支持单个或批量上传,可拖拽文件上传',
  disabled: false,
  theme: 'primary',
  maxCount: 1,
  maxCountExceedText: '已达到最大上传数量限制',
  accept: '',
  modelValue: '',
  isUrlMode: false,
});

const emit = defineEmits<UploadBoxEmits>();

const fileList = ref<UploadProps['fileList']>([]);
const isDragging = ref(false);
const uploadingFiles = ref<Set<string>>(new Set());

// 监听 modelValue 变化，用于预览
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && props.isUrlMode) {
      // 如果是地址模式，创建一个虚拟文件对象
      fileList.value = [
        {
          uid: '-1',
          name: newVal.split('/').pop() || '',
          status: 'done',
          url: newVal,
        },
      ];
    } else if (!newVal) {
      fileList.value = [];
    }
  },
  { immediate: true },
);

// 处理文件变化
const handleFileChange: UploadProps['onChange'] = (info) => {
  // 处理数量限制
  if (info.fileList.length > props.maxCount) {
    fileList.value = info.fileList.slice(-props.maxCount);
    message.warning(props.maxCountExceedText);
  } else {
    fileList.value = info.fileList;
  }

  // 如果有新文件，触发上传事件
  const file = info.file.originFileObj;
  if (file && !uploadingFiles.value.has(file.uid)) {
    uploadingFiles.value.add(file.uid);
    emit('upload', file);
  }

  emit('change', { ...info, fileList: fileList.value });
};

// 更新文件状态
const updateFileStatus: UploadBoxInstance['updateFileStatus'] = (
  uid,
  status,
  response,
) => {
  const fileIndex = fileList.value.findIndex((file) => file.uid === uid);
  if (fileIndex !== -1) {
    fileList.value[fileIndex].status = status;
    if (response?.path && props.isUrlMode) {
      emit('update:modelValue', response.path);
    }
  }
  uploadingFiles.value.delete(uid);
};

// 处理下载模板
const handleDownload = () => {
  emit('download');
};

// 处理文件移除
const handleRemove = (file: any) => {
  fileList.value = fileList.value.filter((item) => item.uid !== file.uid);
  uploadingFiles.value.delete(file.uid);
  emit('remove', file);
  emit('change', { file, fileList: fileList.value });
  // 清空文件地址
  if (props.isUrlMode) {
    emit('update:modelValue', '');
  }
};

// 处理拖拽状态
const handleDragEnter = () => {
  if (!props.disabled) {
    isDragging.value = true;
  }
};

const handleDragLeave = () => {
  isDragging.value = false;
};

// 根据size计算样式
const sizeStyles = computed(() => {
  const sizes = {
    small: {
      padding: '16px',
      iconSize: 'text-3xl',
      height: '120px',
    },
    default: {
      padding: '24px',
      iconSize: 'text-4xl',
      height: '180px',
    },
    large: {
      padding: '32px',
      iconSize: 'text-5xl',
      height: '240px',
    },
  };
  return sizes[props.size];
});

// 主题色样式
const themeStyles = computed(() => {
  const themes = {
    primary: 'hover:border-primary hover:text-primary',
    success: 'hover:border-success hover:text-success',
    warning: 'hover:border-warning hover:text-warning',
    error: 'hover:border-error hover:text-error',
    info: 'hover:border-info hover:text-info',
  };
  return themes[props.theme];
});

// 暴露方法
defineExpose<UploadBoxInstance>({
  updateFileStatus,
});
</script>

<template>
  <div class="upload-box">
    <div
      v-if="showDownload || supportText"
      class="mb-3 flex items-center justify-between"
    >
      <span v-if="supportText" class="text-sm text-gray-500">{{
        supportText
      }}</span>
      <Button
        v-if="showDownload"
        type="link"
        class="flex items-center gap-1 !px-0"
        :disabled="disabled"
        @click="handleDownload"
      >
        <MdiFile class="text-lg" />
        {{ downloadText }}
      </Button>
    </div>

    <Upload
      :file-list="fileList"
      :before-upload="() => false"
      :disabled="disabled || isMaxCount"
      :show-upload-list="false"
      :accept="accept"
      @change="handleFileChange"
    >
      <div
        class="relative overflow-hidden rounded-lg border-2 border-dashed transition-all duration-300"
        :class="[
          disabled || isMaxCount
            ? 'cursor-not-allowed opacity-50'
            : 'cursor-pointer',
          isDragging ? `border-${props.theme}` : 'border-gray-200',
          themeStyles,
        ]"
        :style="{ height: sizeStyles.height }"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
        @drop="handleDragLeave"
      >
        <div
          class="absolute inset-0 flex flex-col items-center justify-center p-6"
        >
          <template v-if="isMaxCount">
            <div class="text-base text-gray-400">{{ maxCountExceedText }}</div>
          </template>
          <template v-else>
            <MdiCloudUpload
              class="mb-4 transition-transform duration-300"
              :class="[
                sizeStyles.iconSize,
                isDragging ? 'scale-110' : 'scale-100',
                disabled ? 'text-gray-400' : 'text-gray-500',
              ]"
            />
            <div class="space-y-2 text-center">
              <div
                class="text-base font-medium"
                :class="disabled ? 'text-gray-400' : 'text-gray-700'"
              >
                {{ uploadText }}
              </div>
              <div
                class="text-sm"
                :class="disabled ? 'text-gray-300' : 'text-gray-500'"
              >
                {{ subUploadText }}
                <template v-if="maxCount > 1">
                  ({{ fileList.length }}/{{ maxCount }})
                </template>
              </div>
            </div>
          </template>
        </div>
      </div>
    </Upload>

    <!-- 文件列表 -->
    <div v-if="fileList.length > 0" class="mt-4 space-y-2">
      <TransitionGroup name="list">
        <div
          v-for="file in fileList"
          :key="file.uid"
          class="flex items-center justify-between rounded-lg border border-gray-200 px-4 py-2 transition-all duration-300"
        >
          <div class="flex items-center gap-2 overflow-hidden">
            <MdiFile class="flex-shrink-0 text-lg text-gray-500" />
            <span class="truncate text-sm text-gray-700" :title="file.name">
              {{ file.name }}
            </span>
            <span
              v-if="uploadingFiles.has(file.uid)"
              class="text-sm text-gray-400"
            >
              上传中...
            </span>
            <span
              v-else-if="file.status === 'error'"
              class="text-error text-sm"
            >
              上传失败
            </span>
          </div>
          <Button
            type="link"
            class="flex-shrink-0 !px-1"
            @click.stop="handleRemove(file)"
          >
            <MdiClose
              class="hover:text-error text-lg text-gray-400 transition-colors"
            />
          </Button>
        </div>
      </TransitionGroup>
    </div>
  </div>
</template>

<style scoped>
.upload-box {
  width: 100%;
}

.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

:deep(.ant-upload) {
  width: 100%;
  background: none !important;
}

:deep(.ant-upload-list) {
  display: none;
}

:deep(.ant-upload-drag) {
  background: none !important;
}

:deep(.ant-upload-btn) {
  background: none !important;
}

:deep(.ant-upload-drag-container) {
  background: none !important;
}
</style>
