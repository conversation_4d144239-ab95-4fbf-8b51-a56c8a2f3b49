import type { UploadProps } from 'ant-design-vue';

export interface UploadBoxProps {
  // 支持的文件类型提示文本
  supportText?: string;
  // 是否显示下载模板按钮
  showDownload?: boolean;
  // 下载按钮文本
  downloadText?: string;
  // 上传框大小 small | default | large
  size?: 'default' | 'large' | 'small';
  // 上传提示文本
  uploadText?: string;
  // 上传子提示文本
  subUploadText?: string;
  // 是否禁用
  disabled?: boolean;
  // 主题色
  theme?: 'error' | 'info' | 'primary' | 'success' | 'warning';
  // 最大上传数量
  maxCount?: number;
  // 超出数量限制提示
  maxCountExceedText?: string;
  // 允许的文件类型
  accept?: string;
  // 文件地址（用于预览）
  modelValue?: string;
  // 是否为地址链接模式（如果是，则上传时只传地址）
  isUrlMode?: boolean;
}

export interface UploadBoxEmits {
  (e: 'download'): void;
  (e: 'change', info: Parameters<Required<UploadProps>['onChange']>[0]): void;
  (e: 'remove', file: any): void;
  (e: 'upload', file: File): void;
  (e: 'update:modelValue', value: string): void;
}

export interface UploadBoxInstance {
  updateFileStatus: (
    uid: string,
    status: 'done' | 'error',
    response?: any,
  ) => void;
}
