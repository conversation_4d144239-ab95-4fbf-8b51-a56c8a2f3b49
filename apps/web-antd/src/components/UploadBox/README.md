# UploadBox 上传组件

一个功能强大的文件上传组件，支持拖拽上传、文件预览、自定义主题等特性。

## 功能特点

- 支持拖拽上传
- 支持文件预览
- 自定义主题色
- 多种尺寸选择
- 文件数量限制
- 文件类型限制
- 支持地址链接模式
- 上传状态显示
- 动画效果

## 基础用法

```vue
<template>
  <UploadBox
    accept=".jpg,.png,.pdf"
    :max-count="3"
    @upload="handleUpload"
  />
</template>

<script setup lang="ts">
const handleUpload = async (file: File) => {
  try {
    await yourUploadFunction(file);
  } catch (error) {
    message.error('上传失败');
  }
};
</script>
```

## 地址链接模式

```vue
<template>
  <UploadBox
    v-model="fileUrl"
    :is-url-mode="true"
    accept=".pem"
    @upload="handleUpload"
  />
</template>

<script setup lang="ts">
const fileUrl = ref('./upload/example.pem');

const handleUpload = async (file: File) => {
  try {
    const response = await yourUploadFunction(file);
    // fileUrl 会自动更新为 response.path
  } catch (error) {
    message.error('上传失败');
  }
};
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| supportText | 支持的文件类型提示文本 | string | '支持 .xlsx, .xls 格式的文件' |
| showDownload | 是否显示下载模板按钮 | boolean | true |
| downloadText | 下载按钮文本 | string | '下载模板' |
| size | 上传框大小 | 'small' \| 'default' \| 'large' | 'default' |
| uploadText | 上传提示文本 | string | '点击或拖拽文件上传' |
| subUploadText | 上传子提示文本 | string | '支持单个或批量上传,可拖拽文件上传' |
| disabled | 是否禁用 | boolean | false |
| theme | 主题色 | 'primary' \| 'success' \| 'warning' \| 'error' \| 'info' | 'primary' |
| maxCount | 最大上传数量 | number | 1 |
| maxCountExceedText | 超出数量限制提示 | string | '已达到最大上传数量限制' |
| accept | 允许的文件类型 | string | '' |
| modelValue | 文件地址（用于预览） | string | '' |
| isUrlMode | 是否为地址链接模式 | boolean | false |

### Events

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| download | 点击下载模板按钮时触发 | - |
| change | 文件列表变化时触发 | (info: UploadChangeParam) |
| remove | 移除文件时触发 | (file: UploadFile) |
| upload | 选择文件时触发 | (file: File) |
| update:modelValue | 文件地址更新时触发（地址链接模式） | (value: string) |

### Methods

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| updateFileStatus | 更新文件状态 | (uid: string, status: 'done' \| 'error', response?: any) |

## 主题定制

组件支持多种主题色：

```vue
<template>
  <!-- 主题色 -->
  <UploadBox theme="primary" />
  <UploadBox theme="success" />
  <UploadBox theme="warning" />
  <UploadBox theme="error" />
  <UploadBox theme="info" />
</template>
```

## 尺寸选择

提供三种尺寸选择：

```vue
<template>
  <!-- 尺寸 -->
  <UploadBox size="small" />
  <UploadBox size="default" />
  <UploadBox size="large" />
</template>
```

## 注意事项

1. 在地址链接模式下，组件会自动处理文件地址的显示和更新
2. 文件上传需要自行处理，组件只提供文件选择和状态管理
3. 可以通过 `updateFileStatus` 方法更新文件上传状态
4. 组件会自动处理文件数量限制 
