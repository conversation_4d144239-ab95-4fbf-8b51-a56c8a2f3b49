export interface RemoteConfig {
  /** 远程数据接口函数 */
  api: (params?: any) => Promise<any>;
  /** 接口参数 */
  params?: Record<string, any>;
  /** 数据转换函数 */
  transform?: (data: any) => Array<{ label: string; value: any }>;
  /** 是否启用缓存 */
  cache?: boolean;
  /** 缓存时间(毫秒) */
  cacheTime?: number;
  /** 防抖时间(毫秒) */
  debounceTime?: number;
}

export interface SearchItemConfig {
  label: string;
  field: string;
  component:
    | 'DatePicker'
    | 'Input'
    | 'RangePicker'
    | 'Select'
    | 'Switch'
    | 'Textarea'
    | 'TimePicker';
  span?: number;
  props?: {
    [key: string]: any;
    allowClear?: boolean;
    format?: string;
    loading?: boolean;
    options?: Array<{ [key: string]: any; label: string; value: any }>;
    placeholder?: string;
    showTime?: boolean;
    style?: Record<string, any>;
    valueFormat?: string;
  };
  options?: Array<{ [key: string]: any; label: string; value: any }>;
  /** 远程数据配置 */
  remote?: RemoteConfig;
  /** 权限控制 - 需要的权限代码 */
  permissionCode?: string;
  /** 是否仅管理员可见 */
  adminFlag?: boolean;
}

/**
 * 搜索组配置接口
 */
export interface SearchGroup {
  /** 分组标题(可选) */
  label?: string;
  /** 分组内的搜索项 */
  items: SearchItemConfig[];
}

/**
 * 自定义按钮配置
 */
export interface CustomButton {
  text: string;
  type?: 'dashed' | 'default' | 'link' | 'primary' | 'text';
  icon?: any;
  props?: Record<string, any>;
  onClick: () => void;
  /** 是否仅管理员可见，为 1 时表示仅管理员可见 */
  permission?: number;
}

/**
 * 搜索工具栏组件属性
 */
export interface SearchToolbarProps {
  /** 基础搜索项配置 */
  basicItems: SearchItemConfig[];
  /** 高级搜索项配置(可选) */
  advancedItems?: SearchGroup[];
  /** 搜索表单值 */
  modelValue: Record<string, any>;
  /** 加载状态 */
  loading?: boolean;
  /** 是否显示展开按钮 */
  showExpand?: boolean;
  /** 是否显示搜索按钮快捷键提示 */
  showShortcut?: boolean;
  /** 自定义按钮配置 */
  customButtons?: CustomButton[];
  /** 是否使用紧凑布局，紧凑布局时搜索项不会撑满整行 */
  compact?: boolean;
  /** 是否启用权限控制 */
  enablePermissionControl?: boolean;
  /** 是否为管理员 */
  adminFlag?: boolean;
}
