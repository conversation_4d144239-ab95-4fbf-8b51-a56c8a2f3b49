<script setup lang="ts">
import type {
  CustomButton,
  SearchItemConfig,
  SearchToolbarProps,
} from './types';

import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

import { MdiChevronDown, MdiMagnify, MdiReload } from '@vben/icons';

import {
  Button,
  DatePicker,
  Input,
  InputNumber,
  Select,
  Space,
  Switch,
  Textarea,
} from 'ant-design-vue';

import { useUserPermission } from '#/hooks/useUserPermission';

// 组件属性默认值
const props = withDefaults(defineProps<SearchToolbarProps>(), {
  loading: false,
  showExpand: true,
  showShortcut: true,
  advancedItems: () => [],
  customButtons: () => [],
  compact: false,
  enablePermissionControl: false,
  adminFlag: false,
});

// 组件事件
const emit = defineEmits<{
  /** 重置事件 */
  reset: [];
  /** 搜索事件 */
  search: [];
  /** 表单值更新事件 */
  'update:modelValue': [value: Record<string, any>];
}>();

/**
 * 组件映射表 - 用于动态渲染表单控件
 * @description 可以根据需要添加更多组件
 */
const componentMap = {
  Input, // 输入框
  Select, // 下拉选择框
  RangePicker: DatePicker.RangePicker, // 日期范围选择器
  DatePicker, // 日期选择器
  TimePicker: DatePicker.TimePicker, // 时间选择器
  Textarea, // 输入框
  Switch, // 开关
  InputNumber, // 输入框
};

// 定义选项数据类型
export interface SelectOption {
  label: string;
  value: any;
  [key: string]: any;
}

// 定义接口函数类型
export type ApiFunction<T = any> = (params?: T) => Promise<SelectOption[]>;

/**
 * 展开/收起状态
 */
const isExpanded = ref(false);

/**
 * 切换展开状态
 */
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

/**
 * 搜索处理
 */
const handleSearch = () => {
  emit('search');
};

/**
 * 重置处理
 */
const handleReset = () => {
  emit('reset');
};

// 移除全局事件监听
const searchWrapperRef = ref<HTMLDivElement>();

// 修改为本地事件监听
const handleKeydown = (e: KeyboardEvent) => {
  // 确保事件源在搜索组件内
  if (
    e.key === 'Enter' &&
    !e.isComposing &&
    searchWrapperRef.value?.contains(e.target as Node)
  ) {
    handleSearch();
  }
};

// 注册和移除事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});

/**
 * 获取实际组件
 * @param type 组件类型
 */
const getComponent = (type: keyof typeof componentMap) => {
  return componentMap[type];
};

// 缓存选项数据
const optionsCache = new Map<
  string,
  {
    data: SelectOption[];
    timestamp: number;
  }
>();

// 存储当前选项数据
const currentOptions = ref<Record<string, SelectOption[]>>({});

// 存储加载状态
const loadingStates = ref<Record<string, boolean>>({});

// 存储防抖定时器
const debounceTimers = new Map<string, NodeJS.Timeout>();

/**
 * 设置加载状态
 * @param field 字段名
 * @param loading 加载状态
 */
const setLoading = (field: string, loading: boolean) => {
  loadingStates.value[field] = loading;
};

/**
 * 获取加载状态
 * @param field 字段名
 */
const getLoading = (field: string) => {
  return loadingStates.value[field] || false;
};

/**
 * 获取缓存数据
 * @param key 缓存键
 * @param cacheTime 缓存时间
 */
const getCache = (key: string, cacheTime?: number) => {
  const cache = optionsCache.get(key);
  if (!cache) return null;

  if (cacheTime && Date.now() - cache.timestamp > cacheTime) {
    optionsCache.delete(key);
    return null;
  }

  return cache.data;
};

/**
 * 设置缓存数据
 * @param key 缓存键
 * @param data 缓存数据
 */
const setCache = (key: string, data: SelectOption[]) => {
  optionsCache.set(key, {
    data,
    timestamp: Date.now(),
  });
};

/**
 * 获取远程数据
 * @param item 搜索项配置
 */
const fetchRemoteData = async (item: SearchItemConfig) => {
  if (!item.remote) return;

  const { api, params, transform, cache, cacheTime, debounceTime } =
    item.remote;
  const cacheKey = `${item.field}_${JSON.stringify(params || {})}`;

  // 检查缓存
  if (cache) {
    const cachedData = getCache(cacheKey, cacheTime);
    if (cachedData) {
      currentOptions.value[item.field] = cachedData;
      return;
    }
  }

  // 防抖处理
  if (debounceTime) {
    if (debounceTimers.has(cacheKey)) {
      clearTimeout(debounceTimers.get(cacheKey));
    }

    return new Promise((resolve) => {
      debounceTimers.set(
        cacheKey,
        setTimeout(async () => {
          try {
            setLoading(item.field, true);
            const response = await api(params);
            const data = transform ? transform(response) : response;

            if (cache) {
              setCache(cacheKey, data);
            }

            currentOptions.value[item.field] = data;
            resolve(data);
          } catch (error) {
            console.error(`获取远程数据失败: ${item.field}`, error);
            currentOptions.value[item.field] = [];
          } finally {
            setLoading(item.field, false);
            debounceTimers.delete(cacheKey);
          }
        }, debounceTime),
      );
    });
  }

  // 直接请求
  try {
    setLoading(item.field, true);
    const response = await api(params);
    const data = transform ? transform(response) : response;

    if (cache) {
      setCache(cacheKey, data);
    }

    currentOptions.value[item.field] = data;
  } catch (error) {
    console.error(`获取远程数据失败: ${item.field}`, error);
    currentOptions.value[item.field] = [];
  } finally {
    setLoading(item.field, false);
  }
};

/**
 * 初始化选项数据
 * @param items 搜索项配置
 */
const initOptions = async (items: SearchItemConfig[]) => {
  for (const item of items) {
    if (item.component === 'Select') {
      if (item.options) {
        // 使用静态选项数据，确保存入 currentOptions 以便一致处理
        currentOptions.value[item.field] = item.options;
      } else if (item.remote) {
        // 使用远程数据
        await fetchRemoteData(item);
      }
    }
  }
};

// 监听展开状态变化，初始化高级搜索的选项数据
watch(isExpanded, async (expanded) => {
  if (expanded && props.advancedItems) {
    for (const group of props.advancedItems) {
      await initOptions(group.items);
    }
  }
});

// 组件挂载时初始化基础搜索的选项数据
onMounted(async () => {
  await initOptions(props.basicItems);
});

// 获取用户权限相关的功能
const { isAdmin } = useUserPermission();

/**
 * 检查搜索项是否有权限显示
 * @param _item 搜索项配置或自定义按钮
 * @returns 是否有权限显示
 */
const hasItemPermission = (_item: CustomButton | SearchItemConfig) => {
  // 如果不启用权限控制，所有项目都可见
  if (!props.enablePermissionControl) return true;

  // 如果是管理员，所有项目都可见
  if (props.adminFlag || isAdmin.value) return true;

  // 处理adminFlag属性（按钮和搜索项都可以有这个属性）
  if ('adminFlag' in _item && _item.adminFlag === true) {
    // 非管理员不能看到需要管理员权限的项目
    return false;
  }

  // 暂时不使用权限码，后续会加上
  // if ('permissionCode' in _item && _item.permissionCode) {
  //   return hasPermission(_item.permissionCode);
  // }

  return true; // 暂时所有用户都可见
};

/**
 * 过滤有权限显示的基础搜索项
 */
const filteredBasicItems = computed(() => {
  return props.basicItems.filter((item) => hasItemPermission(item));
});

/**
 * 过滤有权限显示的高级搜索项
 */
const filteredAdvancedItems = computed(() => {
  if (!props.advancedItems?.length) return [];

  return props.advancedItems
    .map((group) => ({
      ...group,
      items: group.items.filter((item) => hasItemPermission(item)),
    }))
    .filter((group) => group.items.length > 0);
});

/**
 * 过滤有权限显示的自定义按钮
 */
const filteredCustomButtons = computed(() => {
  if (!props.customButtons?.length) return [];

  // 移除调试日志
  return props.customButtons.filter((btn) => hasItemPermission(btn));
});
</script>

<template>
  <div class="search-toolbar" ref="searchWrapperRef">
    <!-- 基础搜索区域 -->
    <div class="basic-search">
      <!-- 基础搜索项 - 使用更灵活的栅格布局 -->
      <div class="search-row" :class="{ 'compact-mode': compact }">
        <div
          v-for="item in filteredBasicItems"
          :key="item.field"
          class="form-item custom-width"
          :style="item.span ? { '--search-item-width': `${item.span}px` } : {}"
        >
          <div class="form-label">{{ item.label }}</div>
          <component
            :is="getComponent(item.component)"
            :value="modelValue[item.field]"
            @update:value="
              (val) =>
                emit('update:modelValue', { ...modelValue, [item.field]: val })
            "
            v-bind="{
              ...item.props,
              showSearch: item.component === 'Select',
              optionFilterProp: 'label',
            }"
            :loading="item.component === 'Select' && getLoading(item.field)"
            @keyup.enter="handleSearch"
          >
            <!-- Select 组件的选项处理 -->
            <template v-if="item.component === 'Select'">
              <Select.Option
                v-for="option in item.options ||
                currentOptions[item.field] ||
                []"
                :key="option.value"
                :value="option.value"
                :label="option.label"
              >
                {{ option.label }}
              </Select.Option>
            </template>
          </component>
        </div>
      </div>

      <!-- 操作按钮区域 - 优化布局 -->
      <div class="action-row">
        <div class="action-buttons">
          <Space>
            <Button
              type="primary"
              :loading="loading"
              @click="handleSearch"
              class="toolbar-button"
            >
              <span class="button-content">
                <MdiMagnify class="button-icon" />
                <span class="button-text">搜索</span>
                <span v-if="showShortcut" class="shortcut-tip">(Enter)</span>
              </span>
            </Button>
            <Button @click="handleReset" class="toolbar-button">
              <span class="button-content">
                <MdiReload class="button-icon" />
                <span class="button-text">重置</span>
              </span>
            </Button>
          </Space>
        </div>
        <a
          v-if="showExpand && filteredAdvancedItems?.length"
          class="expand-button"
          @click="toggleExpand"
        >
          {{ isExpanded ? '收起筛选' : '展开筛选' }}
          <MdiChevronDown :class="{ expanded: isExpanded }" />
        </a>
      </div>
    </div>

    <!-- 高级搜索区域 - 改进分组布局 -->
    <div
      v-if="filteredAdvancedItems?.length"
      class="advanced-search"
      :class="{ expanded: isExpanded }"
    >
      <div
        v-for="(group, index) in filteredAdvancedItems"
        :key="index"
        class="search-group"
      >
        <div v-if="group.label" class="group-title">{{ group.label }}</div>
        <div class="group-items" :class="{ 'compact-mode': compact }">
          <div
            v-for="item in group.items"
            :key="item.field"
            class="form-item custom-width"
            :style="
              item.span ? { '--search-item-width': `${item.span}px` } : {}
            "
          >
            <div class="form-label">{{ item.label }}</div>
            <component
              :is="getComponent(item.component)"
              :value="modelValue[item.field]"
              @update:value="
                (val) =>
                  emit('update:modelValue', {
                    ...modelValue,
                    [item.field]: val,
                  })
              "
              v-bind="{
                ...item.props,
                showSearch: item.component === 'Select',
                optionFilterProp: 'label',
              }"
              :loading="item.component === 'Select' && getLoading(item.field)"
              @keyup.enter="handleSearch"
            >
              <!-- Select 组件的选项处理 -->
              <template v-if="item.component === 'Select'">
                <Select.Option
                  v-for="option in item.options ||
                  currentOptions[item.field] ||
                  []"
                  :key="option.value"
                  :value="option.value"
                  :label="option.label"
                >
                  {{ option.label }}
                </Select.Option>
              </template>
            </component>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义按钮区域 - 优化布局 -->
    <div v-if="filteredCustomButtons.length > 0" class="custom-buttons">
      <Space class="custom-buttons-space">
        <Button
          v-for="btn in filteredCustomButtons"
          :key="btn.text"
          :type="btn.type"
          v-bind="btn.props"
          @click="btn.onClick"
          class="custom-button"
        >
          <component
            v-if="btn.icon"
            :is="btn.icon"
            class="custom-button-icon"
          />
          <span>{{ btn.text }}</span>
        </Button>
      </Space>
    </div>
  </div>
</template>

<style lang="less" scoped>
.search-toolbar {
  background-color: var(--background-deep);
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;

  .basic-search {
    .search-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 16px;

      // 紧凑布局模式
      &.compact-mode {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;

        .form-item {
          flex: 0 0 auto;
          margin-right: 16px;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;

        &.compact-mode {
          flex-direction: column;

          .form-item {
            margin-right: 0;
            margin-bottom: 16px;
          }
        }
      }

      .form-item {
        display: flex;
        flex-direction: column;
        gap: 8px;

        &.custom-width {
          max-width: var(--search-item-width, auto);
          min-width: 200px;
        }

        .form-label {
          font-size: 14px;
          color: var(--text-color);
          white-space: nowrap;
        }

        :deep(.ant-input),
        :deep(.ant-select),
        :deep(.ant-picker) {
          width: 100%;
        }
      }
    }

    .action-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;

      .toolbar-button {
        display: flex;
        align-items: center;
        gap: 4px;

        .button-content {
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .button-icon {
          font-size: 16px;
        }

        .button-text {
          margin-left: 4px;
        }

        .shortcut-tip {
          color: var(--text-color-secondary);
          font-size: 12px;
          margin-left: 4px;
        }
      }

      .expand-button {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--primary-color);
        cursor: pointer;
        font-size: 14px;

        &:hover {
          opacity: 0.8;
        }

        .expanded {
          transform: rotate(180deg);
        }
      }
    }
  }

  .advanced-search {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
    transition: all 0.3s ease;

    &.expanded {
      display: block;
      opacity: 1;
      transform: translateY(0);
    }

    &:not(.expanded) {
      display: none;
      opacity: 0;
      transform: translateY(-10px);
    }

    .search-group {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .group-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-color);
        margin-bottom: 12px;
        padding-left: 8px;
        border-left: 3px solid var(--primary-color);
      }

      .group-items {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        padding: 8px;
        background-color: var(--background-light);
        border-radius: 4px;

        // 紧凑布局模式
        &.compact-mode {
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;

          .form-item {
            flex: 0 0 auto;
            margin-right: 16px;

            &:last-child {
              margin-right: 0;
            }
          }
        }

        @media (max-width: 768px) {
          grid-template-columns: 1fr;

          &.compact-mode {
            flex-direction: column;

            .form-item {
              margin-right: 0;
              margin-bottom: 16px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }

        .form-item {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .form-label {
            font-size: 14px;
            color: var(--text-color);
            white-space: nowrap;
          }

          :deep(.ant-input),
          :deep(.ant-select),
          :deep(.ant-picker) {
            width: 100%;
          }
        }
      }
    }
  }

  .custom-buttons {
    padding-top: 16px;
    margin-top: 16px;
    border-top: 1px solid var(--border-color);

    .custom-buttons-space {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .custom-button {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 100px;

      @media (max-width: 768px) {
        flex: 1 1 auto;
      }

      .custom-button-icon {
        margin-right: 8px;
      }
    }
  }
}
</style>
