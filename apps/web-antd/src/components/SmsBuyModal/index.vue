<script lang="ts" setup>
import type { SmsBuyModalEmits, SmsBuyModalProps } from './types';

import type { SmsPackage } from '#/api/core/sms';

import { ref, watch } from 'vue';

import { Button, Card, Empty, message, Modal, Spin } from 'ant-design-vue';

import { getSmsPackages, purchaseSmsPackage } from '#/api/core/sms';

const props = defineProps<SmsBuyModalProps>();
const emit = defineEmits<SmsBuyModalEmits>();

// 短信套餐列表
const smsPackages = ref<SmsPackage[]>([]);
const loadingSmsPackages = ref(false);
const selectedPackage = ref<null | SmsPackage>(null);

// 获取短信包列表
const fetchSmsPackages = async () => {
  try {
    loadingSmsPackages.value = true;
    const response = await getSmsPackages();
    if (response && response.data) {
      smsPackages.value = response.data;
    }
  } catch (error) {
    console.error('获取短信包失败:', error);
    message.error('获取短信包失败，请稍后重试');
  } finally {
    loadingSmsPackages.value = false;
  }
};

// 监听弹窗可见性变化
const handleVisibleChange = async (visible: boolean) => {
  if (visible) {
    selectedPackage.value = null;
    await fetchSmsPackages();
  }
};

// 关闭短信购买弹窗
const handleClose = () => {
  emit('update:visible', false);
};

// 选择套餐
const handleSelectPackage = (pkg: SmsPackage) => {
  selectedPackage.value = pkg;
};

// 确认购买
const confirmPurchase = ref(false);
const purchaseLoading = ref(false);

// 处理购买确认
const handleConfirmPurchase = async () => {
  if (!selectedPackage.value) {
    message.warning('请先选择套餐');
    return;
  }

  try {
    purchaseLoading.value = true;
    // 调用购买API
    await purchaseSmsPackage({
      sms_package_id: selectedPackage.value.id,
    });
    message.success('购买成功');
    emit('success', selectedPackage.value);
    handleClose();
  } catch (error) {
    console.error('购买短信包失败:', error);
    message.error('购买失败，请稍后重试');
  } finally {
    purchaseLoading.value = false;
    confirmPurchase.value = false;
  }
};

// 优惠信息
const getDiscountInfo = (pkg: SmsPackage) => {
  const count = Number(pkg.count);
  const price = Number(pkg.price);
  if (count > 0 && price > 0) {
    const unitPrice = price / count;
    return unitPrice.toFixed(4);
  }
  return '0.0000';
};

// 监听visible变化
watch(() => props.visible, handleVisibleChange);
</script>

<template>
  <Modal
    :visible="visible"
    title="购买短信套餐"
    @cancel="handleClose"
    :footer="null"
    width="800px"
    :body-style="{ padding: '24px' }"
  >
    <Spin :spinning="loadingSmsPackages">
      <div class="sms-modal-container">
        <!-- 说明信息 -->
        <!-- <div class="intro-section">
          <h3 class="section-title">短信套餐说明</h3>
          <div class="info-card">
            <div class="info-item">
              <Badge
                status="processing"
                text="套餐购买后立即生效，有效期自购买之日起计算"
              />
            </div>
            <div class="info-item">
              <Badge
                status="processing"
                text="套餐内短信可用于系统内所有短信发送功能"
              />
            </div>
            <div class="info-item">
              <Badge status="warning" text="套餐到期后未使用的短信将自动失效" />
            </div>
            <div class="info-item">
              <Badge status="default" text="如有其他疑问，请联系客服" />
            </div>
          </div>
        </div>

        <Divider /> -->

        <!-- 套餐选择 -->
        <div class="packages-section">
          <!-- <h3 class="section-title">选择套餐</h3> -->

          <!-- 套餐列表为空时显示空状态 -->
          <Empty
            v-if="smsPackages.length === 0 && !loadingSmsPackages"
            description="暂无可用套餐"
          />

          <!-- 套餐卡片 -->
          <div class="package-cards">
            <Card
              v-for="pkg in smsPackages"
              :key="pkg.id"
              class="package-card"
              :class="{ selected: selectedPackage?.id === pkg.id }"
              hoverable
              @click="handleSelectPackage(pkg)"
            >
              <div class="card-content">
                <h4 class="pkg-name">{{ pkg.name }}</h4>
                <div class="pkg-count">{{ pkg.count }}条</div>
                <div class="pkg-price">¥{{ pkg.price }}</div>
                <div class="pkg-unit-price">
                  单价: {{ getDiscountInfo(pkg) }}元/条
                </div>
                <div class="pkg-validity">
                  有效期: {{ pkg.validity_period }}天
                </div>
              </div>
              <div v-if="selectedPackage?.id === pkg.id" class="selected-badge">
                <span class="selected-icon">✓</span>
              </div>
            </Card>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-section">
          <Button @click="handleClose">取消</Button>
          <Button
            type="primary"
            :disabled="!selectedPackage"
            :loading="purchaseLoading"
            @click="handleConfirmPurchase"
          >
            立即购买
          </Button>
        </div>
      </div>
    </Spin>

    <!-- 确认购买弹窗 -->
    <Modal
      v-model:visible="confirmPurchase"
      title="确认购买"
      @ok="handleConfirmPurchase"
      :confirm-loading="purchaseLoading"
      ok-text="确认"
      cancel-text="取消"
    >
      <template v-if="selectedPackage">
        <p>确定要购买以下套餐吗？</p>
        <div class="confirm-package-info">
          <div class="confirm-item">
            <span class="label">套餐名称:</span>
            <span class="value">{{ selectedPackage.name }}</span>
          </div>
          <div class="confirm-item">
            <span class="label">短信数量:</span>
            <span class="value">{{ selectedPackage.count }}条</span>
          </div>
          <div class="confirm-item">
            <span class="label">价格:</span>
            <span class="value price">¥{{ selectedPackage.price }}</span>
          </div>
          <div class="confirm-item">
            <span class="label">有效期:</span>
            <span class="value">{{ selectedPackage.validity_period }}天</span>
          </div>
        </div>
      </template>
    </Modal>
  </Modal>
</template>

<style lang="scss" scoped>
.sms-modal-container {
  .section-title {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
  }

  .intro-section {
    margin-bottom: 24px;

    .info-card {
      padding: 16px;
      background-color: var(--background-light);
      border-radius: 8px;

      .info-item {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .packages-section {
    margin-bottom: 24px;

    .package-cards {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;

      .package-card {
        position: relative;
        border: 2px solid transparent;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-4px);
        }

        &.selected {
          background-color: var(--primary-1);
          border-color: var(--primary-color);
        }

        .card-content {
          text-align: center;

          .pkg-name {
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: 500;
          }

          .pkg-count {
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--text-color-secondary);
          }

          .pkg-price {
            margin-bottom: 16px;
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
          }

          .pkg-unit-price {
            margin-bottom: 8px;
            font-size: 12px;
            color: var(--success-color);
          }

          .pkg-validity {
            font-size: 12px;
            color: var(--text-color-secondary);
          }
        }

        .selected-badge {
          position: absolute;
          top: -10px;
          right: -10px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          background-color: var(--primary-color);
          border-radius: 50%;
          box-shadow: 0 2px 6px rgb(0 0 0 / 15%);

          .selected-icon {
            font-size: 14px;
            color: #fff;
          }
        }
      }
    }
  }

  .action-section {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
  }
}

.confirm-package-info {
  padding: 16px;
  margin-top: 16px;
  background-color: var(--background-light);
  border-radius: 8px;

  .confirm-item {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 80px;
      color: var(--text-color-secondary);
    }

    .value {
      flex: 1;
      font-weight: 500;

      &.price {
        color: var(--primary-color);
      }
    }
  }
}
</style>
