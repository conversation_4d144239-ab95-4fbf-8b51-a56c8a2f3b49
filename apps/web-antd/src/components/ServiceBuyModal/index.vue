<script lang="ts" setup>
import type { ServiceBuyModalEmits, ServiceBuyModalProps } from './types';

import { computed, ref, watch } from 'vue';

import {
  Button,
  Form,
  Input,
  message,
  Modal,
  QRCode,
  Radio,
  Spin,
} from 'ant-design-vue';

import {
  getPaymentStatusByControl,
  getPayParameterByControl,
} from '#/api/core/payment';
import { createServiceCategoryOrderApi } from '#/api/core/system';
import { getFileUrl } from '#/utils/file';

const props = defineProps<ServiceBuyModalProps>();
const emit = defineEmits<ServiceBuyModalEmits>();

// 表单相关
const formRef = ref();
const formState = ref({
  contactType: 'phone', // phone 或 wxAcc
  phone: '',
  wxAcc: '',
});

// 支付方式列表
const paymentMethods = ref<any[]>([]);
const selectedPayment = ref<any | null>(null);
const loadingPaymentMethods = ref(false);

// 订单和支付相关
const orderInfo = ref<any>(null);
const paymentInfo = ref<any>(null);
const orderLoading = ref(false);
const paymentLoading = ref(false);
const showQRCode = ref(false);
const qrCodeValue = ref('');
const qrCodeLoading = ref(false);
const isDirectImage = ref(false);
// 码支付请求储存
const maPayRequest = ref<any>(null);

// 获取支付类型名称
const getPaymentTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    wxpay: '微信支付',
    alipay: '支付宝',
    hfpay: '汇付',
    MaPay: '码支付',
    dogohfpay: '斗拱',
  };
  return typeMap[type] || type;
};

// 按支付类型分组的支付方式
const paymentGroups = computed(() => {
  const groups: Record<string, any[]> = {};

  paymentMethods.value.forEach((method) => {
    if (method && method.type) {
      if (!groups[method.type]) {
        groups[method.type] = [];
      }

      const group = groups[method.type];
      if (group) {
        group.push(method);
      }
    }
  });

  return Object.entries(groups).map(([type, methods]) => ({
    type,
    typeName: getPaymentTypeName(type),
    methods,
  }));
});

// 获取支付方式列表
const fetchPaymentMethods = async () => {
  try {
    loadingPaymentMethods.value = true;
    const response = await getPaymentStatusByControl({ type: 3 }); // 3: 增值服务
    if (response && response.code === 1) {
      const data = Array.isArray(response.data)
        ? response.data
        : [response.data];
      paymentMethods.value = data;
    }
  } catch (error) {
    console.error('获取支付方式失败:', error);
    message.error('获取支付方式失败，请稍后重试');
  } finally {
    loadingPaymentMethods.value = false;
  }
};

// 创建增值服务订单
const createOrder = async () => {
  if (!props.service) {
    message.warning('服务信息不完整');
    return null;
  }

  try {
    orderLoading.value = true;

    // 根据联系方式类型选择参数
    const params: any = {
      serviceId: Number(props.service.id),
    };

    if (formState.value.contactType === 'phone') {
      params.phone = formState.value.phone;
    } else {
      params.wxAcc = formState.value.wxAcc;
    }

    const response = await createServiceCategoryOrderApi(params);

    if (response && response.code === 1 && response.data) {
      // 这里接口返回的是数组或对象，处理一下
      orderInfo.value = Array.isArray(response.data)
        ? response.data[0]
        : response.data;
      return orderInfo.value;
    } else {
      message.error(response?.msg || '创建订单失败');
      return null;
    }
  } catch (error) {
    console.error('创建增值服务订单失败:', error);
    message.error('创建增值服务订单失败，请稍后重试');
    return null;
  } finally {
    orderLoading.value = false;
  }
};

// 获取支付参数
const getPaymentParams = async (orderNo: string) => {
  if (!selectedPayment.value) {
    message.warning('请先选择支付方式');
    return null;
  }

  try {
    paymentLoading.value = true;
    const response = await getPayParameterByControl({
      orderNo,
      orderType: '3', // 3: 增值服务
      payId: selectedPayment.value.payId.toString(),
      payType: selectedPayment.value.type,
      payWay: selectedPayment.value.payWay || undefined,
    });

    if (response && response.code === 1 && response.data) {
      paymentInfo.value = response.data;
      return response.data;
    } else {
      message.error(response?.msg || '获取支付参数失败');
      return null;
    }
  } catch (error) {
    console.error('获取支付参数失败:', error);
    message.error('获取支付参数失败，请稍后重试');
    return null;
  } finally {
    paymentLoading.value = false;
  }
};

// 监听弹窗可见性变化
const handleVisibleChange = async (visible: boolean) => {
  if (visible) {
    resetPaymentState();
    await fetchPaymentMethods();
  }
};

// 重置支付状态
const resetPaymentState = () => {
  selectedPayment.value = null;
  orderInfo.value = null;
  paymentInfo.value = null;
  showQRCode.value = false;
  qrCodeValue.value = '';
  formState.value = {
    contactType: 'phone',
    phone: '',
    wxAcc: '',
  };
};

// 关闭增值服务购买弹窗
const handleClose = () => {
  emit('update:visible', false);
  resetPaymentState();
};

// 确认购买
const purchaseLoading = ref(false);

// 处理购买确认
const handleConfirmPurchase = async () => {
  // 表单验证
  try {
    await formRef.value.validate();
  } catch {
    return;
  }

  if (!props.service) {
    message.warning('服务信息不完整');
    return;
  }

  if (!selectedPayment.value) {
    message.warning('请先选择支付方式');
    return;
  }

  try {
    purchaseLoading.value = true;

    // 1. 创建订单
    const order = await createOrder();
    if (!order || !order.systemOrdernumber) {
      return;
    }

    // 2. 获取支付参数
    const payParams = await getPaymentParams(order.systemOrdernumber);
    if (!payParams) {
      return;
    }

    // 3. 显示支付二维码或图片
    showQRCode.value = true;

    // 判断是否为码支付(MaPay)，如果是则直接显示图片
    isDirectImage.value = selectedPayment.value.type === 'MaPay';

    // 处理不同的返回格式情况
    if (typeof payParams === 'string') {
      // 直接是字符串链接的情况
      qrCodeValue.value = payParams;
    } else if (payParams.url) {
      // 直接在payParams对象中有url的情况
      qrCodeValue.value = payParams.url;
    } else if (payParams.data) {
      // 在data属性中的情况
      if (payParams.data.url) {
        maPayRequest.value = payParams;
        qrCodeValue.value = getFileUrl(payParams.data.url, 'java');
      } else if (typeof payParams.data === 'string') {
        qrCodeValue.value = payParams.data;
      }
    } else {
      message.error('获取支付二维码失败');
      showQRCode.value = false;
    }
  } catch (error) {
    console.error('增值服务购买失败:', error);
    message.error('购买失败，请稍后重试');
  } finally {
    purchaseLoading.value = false;
  }
};

// 监听visible变化
watch(() => props.visible, handleVisibleChange);

// 监听支付方式变化
watch(
  () => selectedPayment.value,
  () => {
    // 切换支付方式时重置支付信息
    orderInfo.value = null;
    paymentInfo.value = null;
    showQRCode.value = false;
    qrCodeValue.value = '';
  },
);

// 监听联系方式类型变化
watch(
  () => formState.value.contactType,
  (newType) => {
    // 切换联系方式类型时清空相应的输入
    if (newType === 'phone') {
      formState.value.wxAcc = '';
    } else {
      formState.value.phone = '';
    }
  },
);
</script>

<template>
  <Modal
    :visible="visible"
    title="增值服务购买"
    @cancel="handleClose"
    :footer="null"
    width="800px"
    :body-style="{ padding: '24px' }"
  >
    <Spin :spinning="loadingPaymentMethods || orderLoading || paymentLoading">
      <div class="service-modal-container">
        <!-- 服务信息 -->
        <div v-if="!showQRCode" class="service-info-section">
          <h3 class="section-title">服务信息</h3>
          <div class="service-details">
            <div class="service-name">{{ service?.name }}</div>
            <div class="service-description">{{ service?.description }}</div>
            <div class="service-price">
              <span class="price-value">¥{{ service?.price }}</span>
              <span class="price-unit">/{{ service?.unit }}</span>
            </div>
          </div>
        </div>

        <!-- 联系方式 -->
        <div v-if="!showQRCode" class="contact-section">
          <h3 class="section-title">联系方式</h3>
          <Form ref="formRef" :model="formState" layout="vertical">
            <Form.Item label="联系方式类型">
              <Radio.Group v-model:value="formState.contactType">
                <Radio value="phone">手机号</Radio>
                <Radio value="wxAcc">微信号</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              v-if="formState.contactType === 'phone'"
              name="phone"
              label="手机号"
              :rules="[{ required: true, message: '请输入手机号' }]"
            >
              <Input
                v-model:value="formState.phone"
                placeholder="请输入手机号"
              />
            </Form.Item>

            <Form.Item
              v-else
              name="wxAcc"
              label="微信号"
              :rules="[{ required: true, message: '请输入微信号' }]"
            >
              <Input
                v-model:value="formState.wxAcc"
                placeholder="请输入微信号"
              />
            </Form.Item>
          </Form>
        </div>

        <!-- 支付方式选择 -->
        <div v-if="!showQRCode" class="payment-section">
          <h3 class="section-title">选择支付方式</h3>

          <div
            v-if="paymentGroups.length === 0 && !loadingPaymentMethods"
            class="empty-payment"
          >
            暂无可用支付方式
          </div>

          <div v-else class="payment-groups">
            <div
              v-for="group in paymentGroups"
              :key="group.type"
              class="payment-group"
            >
              <div class="payment-group-title">{{ group.typeName }}</div>
              <div class="payment-methods">
                <Radio.Group
                  v-model:value="selectedPayment"
                  button-style="solid"
                >
                  <Radio.Button
                    v-for="method in group.methods"
                    :key="method.key || `${method.type}-${method.payId}`"
                    :value="method"
                    class="payment-method-item"
                  >
                    {{ method.name }}
                  </Radio.Button>
                </Radio.Group>
              </div>
            </div>
          </div>
        </div>

        <!-- 支付二维码 -->
        <div v-if="showQRCode" class="qrcode-section">
          <div class="qrcode-header">
            <h3 class="section-title">请扫码支付</h3>
            <div class="order-info">
              <div class="order-item">
                <span class="label">订单号:</span>
                <span class="value">{{ orderInfo?.systemOrdernumber }}</span>
              </div>
              <div class="order-item">
                <span class="label">金额:</span>
                <span v-if="!isDirectImage" class="value price"
                  >¥{{ service?.price }}</span
                >
                <span v-else class="value price"
                  >¥{{ maPayRequest?.orderPrice }}</span
                >
              </div>
            </div>
          </div>

          <div class="qrcode-container">
            <Spin :spinning="qrCodeLoading" tip="加载中...">
              <div v-if="qrCodeValue" class="qrcode">
                <!-- 根据支付方式显示二维码或图片 -->
                <QRCode
                  v-if="!isDirectImage"
                  :value="qrCodeValue"
                  :size="200"
                />
                <img
                  v-else
                  :src="qrCodeValue"
                  class="payment-image"
                  width="300"
                />
              </div>
              <div v-else class="qrcode-placeholder">获取支付二维码失败</div>
            </Spin>
            <div class="qrcode-tips">
              <p v-if="!isDirectImage">
                请使用{{ selectedPayment?.name }}扫码支付
              </p>
              <p v-else>请使用{{ selectedPayment?.name }}进行支付</p>
              <p>支付完成后请点击"支付完成"按钮</p>
            </div>
          </div>

          <div class="qrcode-actions">
            <Button @click="showQRCode = false">返回</Button>
            <Button
              type="primary"
              @click="
                service &&
                emit('success', {
                  id: Number(service.id),
                  title: service.name,
                  desc: service.description,
                  price: Number(service.price),
                  effectiveTime: 0,
                })
              "
            >
              支付完成
            </Button>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div v-if="!showQRCode" class="action-section">
          <Button @click="handleClose">取消</Button>
          <Button
            type="primary"
            :disabled="!selectedPayment"
            :loading="purchaseLoading"
            @click="handleConfirmPurchase"
          >
            立即购买
          </Button>
        </div>
      </div>
    </Spin>
  </Modal>
</template>

<style lang="less" scoped>
.service-modal-container {
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
  }

  .service-info-section {
    margin-bottom: 24px;

    .service-details {
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;

      .service-name {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .service-description {
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 16px;
      }

      .service-price {
        .price-value {
          font-size: 24px;
          font-weight: 500;
          color: #f5222d;
        }

        .price-unit {
          font-size: 14px;
          margin-left: 4px;
        }
      }
    }
  }

  .contact-section {
    margin-bottom: 24px;
  }

  .payment-section {
    margin-bottom: 24px;

    .empty-payment {
      padding: 24px;
      text-align: center;
      color: rgba(0, 0, 0, 0.45);
    }

    .payment-groups {
      .payment-group {
        margin-bottom: 16px;

        .payment-group-title {
          margin-bottom: 8px;
          font-weight: 500;
        }

        .payment-methods {
          margin-bottom: 16px;

          .payment-method-item {
            margin-right: 8px;
            margin-bottom: 8px;
          }
        }
      }
    }
  }

  .qrcode-section {
    .qrcode-header {
      margin-bottom: 24px;

      .order-info {
        .order-item {
          margin-bottom: 8px;

          .label {
            color: rgba(0, 0, 0, 0.45);
            margin-right: 8px;
          }

          .value {
            font-weight: 500;

            &.price {
              color: #f5222d;
            }
          }
        }
      }
    }

    .qrcode-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 24px;

      .qrcode {
        margin-bottom: 16px;
      }

      .qrcode-placeholder {
        height: 200px;
        width: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        color: rgba(0, 0, 0, 0.45);
      }

      .payment-image {
        max-width: 100%;
      }

      .qrcode-tips {
        text-align: center;
        color: rgba(0, 0, 0, 0.65);

        p {
          margin-bottom: 8px;
        }
      }
    }

    .qrcode-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
    }
  }

  .action-section {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 24px;
  }
}
</style>
