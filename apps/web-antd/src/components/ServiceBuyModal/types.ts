export interface ServicePackage {
  id: number;
  title: string;
  desc: string;
  price: number;
  effectiveTime: number;
}

export interface ServiceBuyModalProps {
  visible: boolean;
  service?: {
    description: string;
    id: string;
    name: string;
    price: string;
    unit: string;
  };
}

export interface ServiceBuyModalEmits {
  'update:visible': [visible: boolean];
  success: [serviceInfo: ServicePackage];
}
