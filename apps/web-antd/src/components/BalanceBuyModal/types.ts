/**
 * 余额充值包
 */
export interface BalancePackage {
  /**
   * 包ID
   */
  id: number;

  /**
   * 余额金额
   */
  balance: number;

  /**
   * 赠送金额
   */
  giftBalance: number;

  /**
   * 价格
   */
  price: number;
}

/**
 * 余额购买弹窗属性
 */
export interface BalanceBuyModalProps {
  /**
   * 弹窗是否可见
   */
  visible: boolean;

  /**
   * 用户ID，可选
   */
  userId?: number;
}

/**
 * 余额购买弹窗事件
 */
export interface BalanceBuyModalEmits {
  /**
   * 更新可见性状态
   */
  'update:visible': [visible: boolean];

  /**
   * 购买成功事件
   */
  success: [packageInfo: BalancePackage];
}
