<script lang="ts" setup>
import type { BalanceBuyModalEmits, BalanceBuyModalProps } from './types';

import { computed, ref, watch } from 'vue';

import {
  Button,
  Card,
  Divider,
  Empty,
  Image,
  message,
  Modal,
  QRCode,
  Radio,
  Spin,
} from 'ant-design-vue';

import { getBalancePackageList } from '#/api/core/balanceRecharge';
import {
  createBalanceOrder,
  getPaymentStatusByControl,
  getPayParameterByControl,
} from '#/api/core/payment';
import { getFileUrl } from '#/utils/file';

const props = defineProps<BalanceBuyModalProps>();
const emit = defineEmits<BalanceBuyModalEmits>();

// 余额充值套餐列表
const balancePackages = ref<any[]>([]);
const loadingPackages = ref(false);
const selectedPackage = ref<any | null>(null);

// 支付方式列表
const paymentMethods = ref<any[]>([]);
const selectedPayment = ref<any | null>(null);
const loadingPaymentMethods = ref(false);

// 订单和支付相关
const orderInfo = ref<any>(null);
const paymentInfo = ref<any>(null);
const orderLoading = ref(false);
const paymentLoading = ref(false);
const showQRCode = ref(false);
const qrCodeValue = ref('');
const qrCodeLoading = ref(false);
const isDirectImage = ref(false);

// 码支付请求解决储存
const maPayRequest = ref<any>(null);

// 获取支付类型名称
const getPaymentTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    wxpay: '微信支付',
    alipay: '支付宝',
    hfpay: '汇付',
    MaPay: '码支付',
    dogohfpay: '斗拱',
  };
  return typeMap[type] || type;
};

// 按支付类型分组的支付方式
const paymentGroups = computed(() => {
  const groups: Record<string, any[]> = {};

  paymentMethods.value.forEach((method) => {
    if (method && method.type) {
      if (!groups[method.type]) {
        groups[method.type] = [];
      }

      const group = groups[method.type];
      if (group) {
        group.push(method);
      }
    }
  });

  return Object.entries(groups).map(([type, methods]) => ({
    type,
    typeName: getPaymentTypeName(type),
    methods,
  }));
});

// 获取余额充值包列表
const fetchBalancePackages = async () => {
  try {
    loadingPackages.value = true;
    const response = await getBalancePackageList(props.userId);
    if (response && response.code === 1 && response.data) {
      balancePackages.value = response.data;
    }
  } catch (error) {
    console.error('获取余额充值包失败:', error);
    message.error('获取余额充值包失败，请稍后重试');
  } finally {
    loadingPackages.value = false;
  }
};

// 获取支付方式列表
const fetchPaymentMethods = async () => {
  try {
    loadingPaymentMethods.value = true;
    const response = await getPaymentStatusByControl({ type: 1 }); // 1: 授权用户充值余额
    if (response && response.code === 1) {
      // 处理返回的数据，可能是数组或单个对象
      const data = Array.isArray(response.data)
        ? response.data
        : [response.data];
      paymentMethods.value = data;
    }
  } catch (error) {
    console.error('获取支付方式失败:', error);
    message.error('获取支付方式失败，请稍后重试');
  } finally {
    loadingPaymentMethods.value = false;
  }
};

// 创建余额充值订单
const createOrder = async () => {
  if (!selectedPackage.value) {
    message.warning('请先选择充值套餐');
    return null;
  }

  try {
    orderLoading.value = true;
    const response = await createBalanceOrder({
      balanceId: selectedPackage.value.id.toString(),
    });

    if (response && response.code === 1 && response.data) {
      orderInfo.value = response.data;
      return response.data;
    } else {
      message.error(response?.msg || '创建订单失败');
      return null;
    }
  } catch (error) {
    console.error('创建充值订单失败:', error);
    message.error('创建充值订单失败，请稍后重试');
    return null;
  } finally {
    orderLoading.value = false;
  }
};

// 获取支付参数
const getPaymentParams = async (orderNo: string) => {
  if (!selectedPayment.value) {
    message.warning('请先选择支付方式');
    return null;
  }

  try {
    paymentLoading.value = true;
    const response = await getPayParameterByControl({
      orderNo,
      orderType: '1', // 1: 授权用户充值余额
      payId: selectedPayment.value.payId.toString(),
      payType: selectedPayment.value.type,
      payWay: selectedPayment.value.payWay || undefined,
    });

    if (response && response.code === 1 && response.data) {
      paymentInfo.value = response.data;
      return response.data;
    } else {
      message.error(response?.msg || '获取支付参数失败');
      return null;
    }
  } catch (error) {
    console.error('获取支付参数失败:', error);
    message.error('获取支付参数失败，请稍后重试');
    return null;
  } finally {
    paymentLoading.value = false;
  }
};

// 监听弹窗可见性变化
const handleVisibleChange = async (visible: boolean) => {
  if (visible) {
    resetPaymentState();
    await Promise.all([fetchBalancePackages(), fetchPaymentMethods()]);
  }
};

// 重置支付状态
const resetPaymentState = () => {
  selectedPackage.value = null;
  selectedPayment.value = null;
  orderInfo.value = null;
  paymentInfo.value = null;
  showQRCode.value = false;
  qrCodeValue.value = '';
};

// 关闭余额充值弹窗
const handleClose = () => {
  emit('update:visible', false);
  resetPaymentState();
};

// 选择套餐
const handleSelectPackage = (pkg: any) => {
  selectedPackage.value = pkg;
  // 选择新套餐时重置支付信息
  orderInfo.value = null;
  paymentInfo.value = null;
  showQRCode.value = false;
};

// 确认购买
const confirmPurchase = ref(false);
const purchaseLoading = ref(false);

// 处理购买确认
const handleConfirmPurchase = async () => {
  if (!selectedPackage.value) {
    message.warning('请先选择充值套餐');
    return;
  }

  if (!selectedPayment.value) {
    message.warning('请先选择支付方式');
    return;
  }

  try {
    purchaseLoading.value = true;

    // 1. 创建订单
    const order = await createOrder();
    if (!order || !order.systemOrdernumber) {
      return;
    }

    // 2. 获取支付参数
    const payParams = await getPaymentParams(order.systemOrdernumber);
    console.log('payParams', payParams);
    if (!payParams) {
      return;
    }
    // 3. 显示支付二维码
    showQRCode.value = true;

    // 判断是否为码支付(MaPay)，如果是则直接显示图片
    isDirectImage.value = selectedPayment.value.type === 'MaPay';

    // 处理不同的返回格式情况
    if (typeof payParams === 'string') {
      // 直接是字符串链接的情况
      qrCodeValue.value = payParams;
    } else if (payParams.url) {
      // 直接在payParams对象中有url的情况
      qrCodeValue.value = payParams.url;
    } else if (payParams.data) {
      // 在data属性中的情况
      if (payParams.data.url) {
        maPayRequest.value = payParams;
        qrCodeValue.value = getFileUrl(payParams.data.url, 'java');
      } else if (typeof payParams.data === 'string') {
        qrCodeValue.value = payParams.data;
      }
    } else {
      message.error('获取支付二维码失败');
      showQRCode.value = false;
    }
  } catch (error) {
    console.error('余额充值失败:', error);
    message.error('充值失败，请稍后重试');
  } finally {
    purchaseLoading.value = false;
    confirmPurchase.value = false;
  }
};

// 计算总价值
const getTotalValue = (pkg: any) => {
  const balance = Number(pkg.balance || 0);
  const giftBalance = Number(pkg.giftBalance || 0);
  return (balance + giftBalance).toFixed(2);
};

// 监听visible变化
watch(() => props.visible, handleVisibleChange);

// 监听支付方式变化
watch(
  () => selectedPayment.value,
  () => {
    // 切换支付方式时重置支付信息
    orderInfo.value = null;
    paymentInfo.value = null;
    showQRCode.value = false;
    qrCodeValue.value = '';
  },
);
</script>

<template>
  <Modal
    :visible="visible"
    title="余额充值"
    @cancel="handleClose"
    :footer="null"
    width="800px"
    :body-style="{ padding: '24px' }"
  >
    <Spin
      :spinning="
        loadingPackages ||
        loadingPaymentMethods ||
        orderLoading ||
        paymentLoading
      "
    >
      <div class="balance-modal-container">
        <!-- 套餐选择 -->
        <div v-if="!showQRCode" class="packages-section">
          <h3 class="section-title">选择充值套餐</h3>

          <!-- 套餐列表为空时显示空状态 -->
          <Empty
            v-if="balancePackages.length === 0 && !loadingPackages"
            description="暂无可用充值套餐"
          />

          <!-- 套餐卡片 -->
          <div class="package-cards">
            <Card
              v-for="pkg in balancePackages"
              :key="pkg.id"
              class="package-card"
              :class="{ selected: selectedPackage?.id === pkg.id }"
              hoverable
              :bordered="true"
              @click="handleSelectPackage(pkg)"
            >
              <div class="card-content">
                <div class="pkg-price">¥{{ pkg.price }}</div>
                <div class="pkg-balance">充值金额: ¥{{ pkg.balance }}</div>
                <div v-if="pkg.giftBalance > 0" class="pkg-gift">
                  赠送金额: ¥{{ pkg.giftBalance }}
                </div>
                <div v-if="pkg.giftBalance > 0" class="pkg-total-value">
                  总价值: ¥{{ getTotalValue(pkg) }}
                </div>
              </div>
              <div v-if="selectedPackage?.id === pkg.id" class="selected-badge">
                <span class="selected-icon">✓</span>
              </div>
            </Card>
          </div>
        </div>

        <!-- 支付方式选择 -->
        <div v-if="selectedPackage && !showQRCode" class="payment-section">
          <Divider />
          <h3 class="section-title">选择支付方式</h3>

          <Empty
            v-if="paymentGroups.length === 0 && !loadingPaymentMethods"
            description="暂无可用支付方式"
          />

          <div v-else class="payment-groups">
            <div
              v-for="group in paymentGroups"
              :key="group.type"
              class="payment-group"
            >
              <div class="payment-group-title">{{ group.typeName }}</div>
              <div class="payment-methods">
                <Radio.Group
                  v-model:value="selectedPayment"
                  button-style="solid"
                >
                  <Radio.Button
                    v-for="method in group.methods"
                    :key="method.key || `${method.type}-${method.payId}`"
                    :value="method"
                    class="payment-method-item"
                  >
                    {{ method.name }}
                  </Radio.Button>
                </Radio.Group>
              </div>
            </div>
          </div>
        </div>

        <!-- 支付二维码 -->
        <div v-if="showQRCode" class="qrcode-section">
          <div class="qrcode-header">
            <h3 class="section-title">请扫码支付</h3>
            <div class="order-info">
              <div class="order-item">
                <span class="label">订单号:</span>
                <span class="value">{{ orderInfo?.systemOrdernumber }}</span>
              </div>
              <div class="order-item">
                <span class="label">金额:</span>
                <span v-if="!isDirectImage" class="value price">¥{{ selectedPackage?.price }}</span>
                <span v-else class="value price">¥{{ maPayRequest?.orderPrice }}</span>
                <!-- <span class="value price">¥{{ selectedPackage?.price }}</span> -->
              </div>
            </div>
          </div>

          <div class="qrcode-container">
            <Spin :spinning="qrCodeLoading" tip="加载中...">
              <div v-if="qrCodeValue" class="qrcode">
                <!-- <QRCode :value="qrCodeValue" :size="200" /> -->
                <!-- 根据支付方式显示二维码或图片 -->
                <QRCode
                  v-if="!isDirectImage"
                  :value="qrCodeValue"
                  :size="200"
                />
                <Image
                  v-else
                  :src="qrCodeValue"
                  :width="300"
                  class="payment-image"
                />
              </div>
              <div v-else class="qrcode-placeholder">
                <Empty description="获取支付二维码失败" />
              </div>
            </Spin>
            <div class="qrcode-tips">
              <p v-if="!isDirectImage">
                请使用{{ selectedPayment?.name }}扫码支付
              </p>
              <p v-else>请使用{{ selectedPayment?.name }}进行支付</p>
              <p>支付完成后请点击"支付完成"按钮</p>
              <!-- <p>请使用{{ selectedPayment?.name }}扫码支付</p>
              <p>支付完成后请点击"支付完成"按钮</p> -->
            </div>
          </div>

          <div class="qrcode-actions">
            <Button @click="showQRCode = false">返回</Button>
            <Button type="primary" @click="emit('success', selectedPackage)">
              支付完成
            </Button>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div v-if="!showQRCode" class="action-section">
          <Button @click="handleClose">取消</Button>
          <Button
            type="primary"
            :disabled="!selectedPackage || !selectedPayment"
            :loading="purchaseLoading"
            @click="handleConfirmPurchase"
          >
            立即充值
          </Button>
        </div>
      </div>
    </Spin>
  </Modal>
</template>

<style lang="scss" scoped>
.balance-modal-container {
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
  }

  .packages-section {
    margin-bottom: 24px;

    .package-cards {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;

      .package-card {
        position: relative;
        // border: 2px solid transparent;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-4px);
        }

        &.selected {
          background-color: var(--primary-1);
          border-color: var(--primary-color);
        }

        .card-content {
          text-align: center;

          .pkg-price {
            margin-bottom: 16px;
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
          }

          .pkg-balance {
            margin-bottom: 8px;
            font-size: 14px;
          }

          .pkg-gift {
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--success-color);
          }

          .pkg-total-value {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .selected-badge {
          position: absolute;
          top: -10px;
          right: -10px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          background-color: var(--primary-color);
          border-radius: 50%;
          box-shadow: 0 2px 6px rgb(0 0 0 / 15%);

          .selected-icon {
            font-size: 14px;
            color: #fff;
          }
        }
      }
    }
  }

  .payment-section {
    margin-bottom: 24px;

    .payment-groups {
      .payment-group {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .payment-group-title {
          font-weight: 500;
          margin-bottom: 12px;
          color: var(--text-color-secondary);
        }

        .payment-methods {
          margin-top: 8px;

          .payment-method-item {
            margin-right: 12px;
            margin-bottom: 12px;
          }
        }
      }
    }
  }

  .qrcode-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;

    .qrcode-header {
      width: 100%;
      text-align: center;
      margin-bottom: 24px;

      .section-title {
        margin-bottom: 16px;
      }

      .order-info {
        background-color: var(--background-light);
        padding: 12px 16px;
        border-radius: 8px;
        display: inline-block;

        .order-item {
          display: flex;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            width: 60px;
            color: var(--text-color-secondary);
          }

          .value {
            font-weight: 500;

            &.price {
              color: var(--primary-color);
              font-size: 18px;
            }
          }
        }
      }
    }

    .qrcode-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 24px;

      .qrcode {
        padding: 16px;
        background-color: var(--background-color);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .qrcode-placeholder {
        width: 200px;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .qrcode-tips {
        margin-top: 16px;
        text-align: center;
        color: var(--text-color-secondary);
        font-size: 14px;

        p {
          margin-bottom: 8px;
        }
      }
    }

    .qrcode-actions {
      display: flex;
      gap: 12px;
    }
  }

  .action-section {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
  }
}

.confirm-package-info {
  padding: 16px;
  margin-top: 16px;
  background-color: var(--background-light);
  border-radius: 8px;

  .confirm-item {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 80px;
      color: var(--text-color-secondary);
    }

    .value {
      flex: 1;
      font-weight: 500;

      &.price {
        color: var(--primary-color);
      }
    }
  }
}
</style>
