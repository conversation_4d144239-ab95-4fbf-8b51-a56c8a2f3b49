/**
 * 余额充值包
 */
export interface BalancePackage {
  /**
   * 包ID
   */
  id: number;

  /**
   * 收款金额
   */
  collectionBalance: number;

  /**
   * 赠送金额
   */
  giftBalance: number;

  /**
   * 价格
   */
  price: number;

  /**
   * 标语说明
   */
  slogan: string;
}

/**
 * 充值方式
 */
export enum PaymentMethod {
  /**
   * 支付宝
   */
  Alipay = 'alipay',

  /**
   * 余额
   */
  Balance = 'balance',

  /**
   * 微信
   */
  Wechat = 'wxpay',
}

/**
 * 余额充值弹窗属性
 */
export interface BalanceRechargeModalProps {
  /**
   * 弹窗是否可见
   */
  visible: boolean;

  /**
   * 用户ID，可选
   */
  userId?: number;
}

/**
 * 余额充值弹窗事件
 */
export interface BalanceRechargeModalEmits {
  /**
   * 更新可见性状态
   */
  'update:visible': [visible: boolean];

  /**
   * 充值成功事件
   */
  success: [packageInfo: BalancePackage];
}

/**
 * 充值模式
 */
export enum RechargeMode {
  /**
   * 代理充值模式
   */
  Agent = 'agent',

  /**
   * 控制端充值模式
   */
  Control = 'control',
}
