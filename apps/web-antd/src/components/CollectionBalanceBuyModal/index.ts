import type { App } from 'vue';

import CollectionBalanceBuyModal from './index.vue';

CollectionBalanceBuyModal.install = (app: App) => {
  app.component('CollectionBalanceBuyModal', CollectionBalanceBuyModal);
};

export { CollectionBalanceBuyModal };
export default CollectionBalanceBuyModal;

export {
  type CollectionBalanceBuyModalEmits,
  type CollectionBalanceBuyModalProps,
  type CollectionBalancePackage,
} from './types';
