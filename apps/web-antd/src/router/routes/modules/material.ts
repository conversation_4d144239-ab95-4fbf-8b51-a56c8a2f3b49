import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:image',
      order: 10,
      title: '素材管理',
      permissionId: PERMISSION_ID.PAGE.MATERIAL.ROOT,
    },
    name: 'Material',
    path: '/material',
    children: [
      {
        name: 'MaterialLibrary',
        path: '/material/library',
        component: () => import('#/views/material/library/index.vue'),
        meta: {
          title: '图片素材',
          icon: 'mdi:folder-image',
          permissionId: PERMISSION_ID.PAGE.MATERIAL.LIBRARY,
          keepAlive: true,
        },
      },
      {
        name: 'ImageType',
        path: '/material/image-type',
        component: () => import('#/views/material/image-type/index.vue'),
        meta: {
          title: '图片分类',
          icon: 'mdi:image-filter',
          permissionId: PERMISSION_ID.PAGE.MATERIAL.IMAGE_TYPE,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
