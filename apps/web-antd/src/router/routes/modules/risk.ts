import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:shield-lock', // 使用盾牌锁图标表示风险防控
      order: 18, // 排在系统日志后面
      title: '风险防控',
      permissionId: PERMISSION_ID.PAGE.RISK.ROOT,
    },
    name: 'Risk',
    path: '/risk',
    children: [
      {
        name: 'AgentVerification',
        path: '/risk/agent-verification',
        component: () => import('#/views/risk/agent-verification/index.vue'),
        meta: {
          title: '代理实名',
          icon: 'mdi:account-check',
          permissionId: PERMISSION_ID.PAGE.RISK.AGENT_VERIFICATION,
          keepAlive: true,
        },
      },
      {
        name: 'AgreementManage',
        path: '/risk/agreement',
        component: () => import('#/views/risk/agreement/index.vue'),
        meta: {
          title: '协议管理',
          icon: 'mdi:file-document-edit',
          permissionId: PERMISSION_ID.PAGE.RISK.AGREEMENT,
          keepAlive: true,
        },
      },
      {
        name: 'RiskArea',
        path: '/risk/area',
        component: () => import('#/views/risk/area/index.vue'),
        meta: {
          title: '风险地区',
          icon: 'mdi:map-marker-alert',
          permissionId: PERMISSION_ID.PAGE.RISK.AREA,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
