import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:account-circle',
      order: 11,
      title: '个人中心',
      permissionId: PERMISSION_ID.PAGE.ACCOUNT.ROOT,
    },
    name: 'Account',
    path: '/account',
    children: [
      {
        name: 'AccountSettings',
        path: '/account/settings',
        component: () => import('#/views/account/settings/index.vue'),
        meta: {
          title: '个人设置',
          icon: 'mdi:account-cog',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ACCOUNT.SETTINGS,
        },
      },
      // 增值服务
      {
        name: 'AccountValueAddedServices',
        path: '/account/value-added-services',
        component: () =>
          import('#/views/account/value-added-services/index.vue'),
        meta: {
          title: '增值服务',
          icon: 'mdi:account-cog',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ACCOUNT.VALUE_ADDED_SERVICES,
        },
      },
    ],
  },
];

export default routes;
