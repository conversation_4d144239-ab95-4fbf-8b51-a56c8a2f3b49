import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:cash',
      order: 15,
      title: '财务管理',
      permissionId: PERMISSION_ID.PAGE.FINANCE.ROOT,
    },
    name: 'Finance',
    path: '/finance',
    children: [
      {
        name: 'BalanceDetail',
        path: '/finance/balance',
        component: () => import('#/views/finance/balance/index.vue'),
        meta: {
          title: '余额明细',
          icon: 'mdi:cash-multiple',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.FINANCE.BALANCE,
        },
      },
      {
        name: 'CommissionDetail',
        path: '/finance/commission',
        component: () => import('#/views/finance/commission/index.vue'),
        meta: {
          title: '佣金明细',
          icon: 'mdi:cash-check',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.FINANCE.COMMISSION,
        },
      },
      {
        name: 'WithdrawRecord',
        path: '/finance/withdraw',
        component: () => import('#/views/finance/withdraw/index.vue'),
        meta: {
          title: '提现记录',
          icon: 'mdi:cash-refund',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.FINANCE.WITHDRAW,
        },
      },
      {
        name: 'InvoiceConfig',
        path: '/finance/invoice-config',
        component: () => import('#/views/finance/invoice-config/index.vue'),
        meta: {
          title: '发票配置',
          icon: 'mdi:file-document-edit',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.FINANCE.INVOICE_CONFIG,
        },
      },
      {
        name: 'InvoiceTitle',
        path: '/finance/invoice-title',
        component: () => import('#/views/finance/invoice-title/index.vue'),
        meta: {
          title: '抬头管理',
          icon: 'mdi:file-document-multiple',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.FINANCE.INVOICE_TITLE,
        },
      },
    ],
  },
];

export default routes;
