import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:monitor-dashboard', // 使用监控仪表盘图标
      order: 1, // 排在仪表盘前面
      title: '数据大屏',
      permissionId: PERMISSION_ID.PAGE.SCREEN.ROOT,
    },
    name: 'Screen',
    path: '/screen',
    children: [
      {
        name: 'ScreenOverview',
        path: '/screen/overview',
        component: () => import('#/views/screen/overview/index.vue'),
        meta: {
          title: '订单地图',
          icon: 'mdi:view-dashboard',
          permissionId: PERMISSION_ID.PAGE.SCREEN.OVERVIEW,
          keepAlive: true,
        },
      },
      {
        name: 'DataStatistics',
        path: '/screen/data-statistics',
        component: () => import('#/views/screen/data-statistics/index.vue'),
        meta: {
          icon: 'lucide:area-chart',
          title: $t('page.dashboard.dataStatistics'),
          permissionId: PERMISSION_ID.PAGE.SCREEN.DATA_STATISTICS,
          keepAlive: true,
        },
      },
      // {
      //   name: 'ScreenSim',
      //   path: '/screen/sim',
      //   component: () => import('#/views/screen/sim/index.vue'),
      //   // component: () => import('#/views/screen/sim/bigscreen/index.vue'),
      //   meta: {
      //     title: 'SIM卡监控',
      //     icon: 'mdi:sim-outline',
      //     permissionId: PERMISSION_ID.PAGE.SCREEN.SIM,
      //   },
      // },
      // 服务器监控
      // {
      //   name: 'ServerMonitor',
      //   path: '/screen/server-monitor',
      //   component: () => import('#/views/screen/server-monitor/index.vue'),
      //   meta: {
      //     title: '服务器监控',
      //     icon: 'mdi:server',
      //   },
      // },
      {
        name: 'ScreenOrder',
        path: '/screen/order',
        component: () => import('#/views/screen/order/index.vue'),
        meta: {
          title: '订单监控',
          icon: 'mdi:file-document-outline',
          permissionId: PERMISSION_ID.PAGE.SCREEN.ORDER,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
