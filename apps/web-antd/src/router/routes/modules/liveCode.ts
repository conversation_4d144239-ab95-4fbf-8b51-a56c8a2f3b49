import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:qrcode',
      order: 9, // 排在订单管理后面
      title: '活码管理',
      permissionId: PERMISSION_ID.PAGE.LIVE_CODE.ROOT,
    },
    name: 'LiveCode',
    path: '/live-code',
    redirect: '/live-code/qrcode-config',
    children: [
      {
        path: 'qrcode-config',
        name: 'LiveCodeQrcodeConfig',
        component: () => import('#/views/live-code/qrcode-config/index.vue'),
        meta: {
          title: '活码配置',
          icon: 'mdi:qrcode',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.LIVE_CODE.CONFIG,
        },
      },
      {
        path: 'qrcode-list',
        name: 'LiveCodeQrcodeList',
        component: () => import('#/views/live-code/qrcode-list/index.vue'),
        meta: {
          title: '活码列表',
          icon: 'mdi:qrcode-scan',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.LIVE_CODE.LIST,
        },
      },
      // {
      //   path: 'qrcode-config2',
      //   name: 'LiveCodeQrcodeConfig2',
      //   component: () =>
      //     import('#/views/live-code copy/qrcode-config/index.vue'),
      //   meta: {
      //     title: '活码配置',
      //     icon: 'mdi:qrcode',
      //     keepAlive: true,
      //     permissionId: PERMISSION_ID.PAGE.LIVE_CODE.CONFIG,
      //   },
      // },
      // {
      //   path: 'qrcode-list2',
      //   name: 'LiveCodeQrcodeList2',
      //   component: () => import('#/views/live-code copy/qrcode-list/index.vue'),
      //   meta: {
      //     title: '活码列表',
      //     icon: 'mdi:qrcode-scan',
      //     keepAlive: true,
      //     permissionId: PERMISSION_ID.PAGE.LIVE_CODE.LIST,
      //   },
      // },
    ],
  },
];

export default routes;
