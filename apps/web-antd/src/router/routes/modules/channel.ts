import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    path: '/channel',
    component: BasicLayout,
    meta: {
      icon: 'mdi:transit-connection',
      order: 3,
      title: $t('page.channel.manageage'),
      permissionId: PERMISSION_ID.PAGE.CHANNEL.ROOT,
    },
    name: 'ChannelManage',
    children: [
      {
        name: 'CardChannel',
        path: '/channel/card',
        component: () => import('#/views/channel/card-channel/index.vue'),
        meta: {
          title: '卡片通道',
          icon: 'mdi:transit-connection-variant',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.CHANNEL.SIM_CHANNEL,
        },
      },
      // 设备通道
      {
        name: 'DeviceChannel',
        path: '/channel/device',
        component: () => import('#/views/channel/device-channel/index.vue'),
        meta: {
          title: '设备通道',
          icon: 'mdi:network',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.CHANNEL.DEVICE_CHANNEL,
        },
      },
    ],
  },
];

export default routes;
