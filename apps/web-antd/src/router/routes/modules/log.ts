import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:text-box-search',
      order: 17,
      title: '系统日志',
      permissionId: PERMISSION_ID.PAGE.LOG.ROOT,
      noBasicLayout: true,
    },
    name: 'Log',
    path: '/log',
    redirect: '/log/unified',
    children: [
      {
        name: 'UnifiedLog',
        path: '/log/unified',
        component: () => import('#/views/log/unified-log/index.vue'),
        meta: {
          title: '日志管理',
          icon: 'mdi:text-box-multiple',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.LOG.UNIFIED,
        },
      },
      {
        name: 'RiskInterception',
        path: '/log/risk',
        component: () => import('#/views/log/risk/index.vue'),
        meta: {
          title: '风险拦截',
          icon: 'mdi:shield-alert',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.LOG.RISK,
          hideMenu: true,
        },
      },
      {
        name: 'MerchantCallback',
        path: '/log/merchant-callback',
        component: () => import('#/views/log/merchant-callback/index.vue'),
        meta: {
          title: '商户回调',
          icon: 'mdi:webhook',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.LOG.MERCHANT_CALLBACK,
          hideMenu: true,
        },
      },
      {
        name: 'AlipayCallback',
        path: '/log/alipay-callback',
        component: () => import('#/views/log/alipay-callback/index.vue'),
        meta: {
          title: '支付宝网关支付回调',
          icon: 'mdi:network-pos',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.LOG.ALIPAY_CALLBACK,
          hideMenu: true,
        },
      },
      {
        name: 'CardTransfer',
        path: '/log/card-transfer',
        component: () => import('#/views/log/card-transfer/index.vue'),
        meta: {
          title: '卡信息转移',
          icon: 'mdi:sim-alert',
          permissionId: PERMISSION_ID.PAGE.LOG.CARD_TRANSFER,
          hideMenu: true,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
