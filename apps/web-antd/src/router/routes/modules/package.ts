import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:package-variant',
      order: 4, // 排在设备管理后面
      title: '套餐管理',
      permissionId: PERMISSION_ID.PAGE.PACKAGE.ROOT,
    },
    name: 'Package',
    path: '/package',
    redirect: '/package/category',
    children: [
      // 实名规则
      {
        name: 'DeviceRealName',
        path: '/package/real-name',
        component: () => import('#/views/package/real-name/index.vue'),
        meta: {
          title: '实名规则',
          icon: 'mdi:cogs',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PACKAGE.REAL_NAME,
        },
      },
      {
        name: 'PackageCategory',
        path: '/package/category',
        component: () =>
          import('#/views/package/package-category-billing/index.vue'),
        meta: {
          title: '套餐分类/计费',
          icon: 'mdi:format-list-group',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PACKAGE.CATEGORY,
        },
      },
      {
        // name: 'PackageCardRule',
        name: 'PackageRule',
        path: '/package/package-rule',
        // component: () => import('#/views/package/cardRule/index.vue'),
        component: () => import('#/views/package/package-rules/index.vue'),
        meta: {
          title: '套餐规则',
          // title: '卡片套餐规则',
          icon: 'mdi:card-bulleted-settings',
          keepAlive: true,
          // permissionId: PERMISSION_ID.PAGE.PACKAGE.CARD_RULE,
          permissionId: PERMISSION_ID.PAGE.PACKAGE.PACKAGE_RULE,
        },
      },
      {
        name: 'PackageCard',
        path: '/package/card',
        component: () => import('#/views/package/card/index.vue'),
        meta: {
          title: '卡片套餐',
          icon: 'mdi:sim',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PACKAGE.CARD,
        },
      },
      {
        name: 'PackageDevice',
        path: '/package/device',
        component: () => import('#/views/package/device/index.vue'),
        meta: {
          title: '设备套餐',
          icon: 'mdi:devices',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PACKAGE.DEVICE,
        },
      },
    ],
  },
];

export default routes;
