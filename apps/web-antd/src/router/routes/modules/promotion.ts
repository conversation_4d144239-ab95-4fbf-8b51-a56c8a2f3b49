import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:gift',
      order: 11,
      title: '推广活动',
      permissionId: PERMISSION_ID.PAGE.PROMOTION.ROOT,
    },
    name: 'Promotion',
    path: '/promotion',
    children: [
      {
        name: 'Coupon',
        path: '/promotion/coupon',
        component: () => import('#/views/promotion/coupon/index.vue'),
        meta: {
          title: '优惠券',
          icon: 'mdi:ticket-percent',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PROMOTION.COUPON,
        },
      },
      {
        name: 'SystemCarousel',
        path: '/promotion/carousel',
        component: () => import('#/views/promotion/carousel/index.vue'),
        meta: {
          title: '轮播推广',
          icon: 'mdi:presentation-play',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PROMOTION.CAROUSEL,
        },
      },
      {
        name: 'DeviceMemberTemplate',
        path: '/promotion/member-template',
        component: () => import('#/views/promotion/member-template/index.vue'),
        meta: {
          // title: '会员模板',
          title: '设备会员模板',
          icon: 'mdi:account-card-outline', // 使用会员卡图标
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PROMOTION.MEMBER_TEMPLATE,
        },
      },
    ],
  },
];

export default routes;
