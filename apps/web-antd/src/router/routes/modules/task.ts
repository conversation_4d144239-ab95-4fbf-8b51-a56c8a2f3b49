import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:clipboard-list',
      order: 16,
      title: '任务中心',
      permissionId: PERMISSION_ID.PAGE.TASK.ROOT,
    },
    name: 'Task',
    path: '/task',
    children: [
      {
        name: 'SystemTask',
        path: '/task/system',
        component: () => import('#/views/task/system/index.vue'),
        meta: {
          title: '系统任务',
          icon: 'mdi:cog',
          permissionId: PERMISSION_ID.PAGE.TASK.SYSTEM,
          keepAlive: true,
        },
      },
      {
        name: 'PollingFailedCard',
        path: '/task/polling-failed',
        component: () => import('#/views/task/polling-failed/index.vue'),
        meta: {
          title: '轮询失败卡',
          icon: 'mdi:sim-alert',
          permissionId: PERMISSION_ID.PAGE.TASK.POLLING_FAILED,
          keepAlive: true,
        },
      },

      // 轮询状态
      {
        name: 'PollingStatus',
        path: '/task/polling-status',
        component: () => import('#/views/task/polling-status/index.vue'),
        meta: {
          title: '轮询状态',
          icon: 'mdi:refresh',
          permissionId: PERMISSION_ID.PAGE.TASK.POLLING_STATUS,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
