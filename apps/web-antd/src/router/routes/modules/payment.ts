import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:cash-register', // 使用收银机图标表示支付
      order: 16, // 排在通知任务后面
      title: '支付配置',
      permissionId: PERMISSION_ID.PAGE.PAYMENT.ROOT,
    },
    name: 'Payment',
    path: '/payment',
    children: [
      {
        name: 'WechatPublic',
        path: '/payment/wechat-public',
        component: () => import('#/views/payment/wechat-public/index.vue'),
        meta: {
          title: '公众号配置',
          icon: 'mdi:wechat',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PAYMENT.WECHAT_PUBLIC,
        },
      },
      {
        name: 'WechatMerchant',
        path: '/payment/wechat-merchant',
        component: () => import('#/views/payment/wechat-merchant/index.vue'),
        meta: {
          title: '微信商户配置',
          icon: 'mingcute:wechat-pay-line',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PAYMENT.WECHAT_MERCHANT,
        },
      },
      {
        name: 'AlipayMerchant',
        path: '/payment/alipay-merchant',
        component: () => import('#/views/payment/alipay-merchant/index.vue'),
        meta: {
          title: '支付宝商户配置',
          icon: 'cib:alipay',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PAYMENT.ALIPAY_MERCHANT,
        },
      },
      {
        name: 'DougongAggregate',
        path: '/payment/dougong-aggregate',
        component: () => import('#/views/payment/dougong-aggregate/index.vue'),
        meta: {
          title: '斗拱聚合支付',
          icon: 'mdi:cash-multiple',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PAYMENT.DOUGONG_AGGREGATE,
        },
      },
      {
        name: 'DougongHuifu',
        path: '/payment/dougong-huifu',
        component: () => import('#/views/payment/dougong-huifu/index.vue'),
        meta: {
          title: '斗拱汇付支付',
          icon: 'mdi:cash-sync',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PAYMENT.DOUGONG_HUIFU,
        },
      },
      {
        name: 'HuolianMerchant',
        path: '/payment/huolian-merchant',
        component: () => import('#/views/payment/huolian-merchant/index.vue'),
        meta: {
          title: '火脸商户配置',
          icon: 'mdi:fire',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PAYMENT.HUOLIAN_MERCHANT,
        },
      },
      {
        name: 'XiaomageMerchant',
        path: '/payment/xiaomage-merchant',
        component: () => import('#/views/payment/xiaomage-merchant/index.vue'),
        meta: {
          title: '小马哥商户',
          icon: 'mdi:horse',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PAYMENT.XIAOMAGE_MERCHANT,
        },
      },
      // 智讯易支付
      {
        name: 'ZhiXunYiMerchant',
        path: '/payment/zhi-xun-yi-merchant',
        component: () =>
          import('#/views/payment/zhi-xun-yi-merchant/index.vue'),
        meta: {
          title: '智讯易商户',
          icon: 'cryptocurrency:pay',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.PAYMENT.ZHIXUNYI_MERCHANT,
        },
      },
      {
        name: 'WithdrawConfig',
        path: '/payment/withdraw',
        component: () => import('#/views/payment/withdraw/index.vue'),
        meta: {
          title: '提现配置',
          icon: 'mdi:cash-refund',
          permissionId: PERMISSION_ID.PAGE.PAYMENT.WITHDRAW,
          keepAlive: true,
        },
      },
      {
        name: 'ComprehensiveConfig',
        path: '/payment/comprehensive',
        component: () => import('#/views/payment/comprehensive/index.vue'),
        meta: {
          title: '综合配置',
          icon: 'mdi:cog-transfer',
          permissionId: PERMISSION_ID.PAGE.PAYMENT.COMPREHENSIVE,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
