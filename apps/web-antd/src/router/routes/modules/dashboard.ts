import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:layout-dashboard',
      order: -1,
      title: $t('page.dashboard.title'),
      // permissionId: PERMISSION_ID.PAGE.DASHBOARD.ROOT,
    },
    name: 'Dashboard',
    path: '/',
    children: [
      {
        name: 'Analytics',
        path: '/analytics',
        component: () => import('#/views/dashboard/analytics/index.vue'),
        meta: {
          affixTab: true,
          icon: 'lucide:area-chart',
          title: $t('page.dashboard.analytics'),
          keepAlive: true,
          // permissionId: PERMISSION_ID.PAGE.DASHBOARD.ANALYTICS,
        },
      },
      // 演示
      // {
      //   name: 'Demo',
      //   path: '/demo',
      //   component: () => import('#/views/demo/AnsengCrudDemo.vue'),
      //   meta: {
      //     title: '演示',
      //     keepAlive: true,
      //   },
      // },
      // // // 演示
      // {
      //   name: 'UseAnshengDemo',
      //   path: '/use-ansheng-demo',
      //   component: () => import('#/views/demo/useAnshengDemo.vue'),
      //   meta: {
      //     title: 'useAnsheng示例',
      //     keepAlive: true,
      //   },
      // },
      // 演示
      // {
      //   name: 'FormDemo',
      //   path: '/form-demo',
      //   component: () => import('#/views/demo/FormModalDemo.vue'),
      //   meta: {
      //     title: '表单演示',
      //     // permissionId: PERMISSION_ID.PAGE.DASHBOARD.FORM_DEMO,
      //   },
      // },
      // {
      //   name: 'AnshengDemo',
      //   path: '/ansheng-demo',
      //   component: () => import('#/views/demo/AnshengDemo.vue'),
      //   meta: {
      //     title: '安笙演示',
      //     // permissionId: PERMISSION_ID.PAGE.DASHBOARD.ANSHENG_DEMO,
      //   },
      // },
      // {
      //   name: 'Workspace',
      //   path: '/workspace',
      //   component: () => import('#/views/dashboard/workspace/index.vue'),
      //   meta: {
      //     icon: 'carbon:workspace',
      //     title: $t('page.dashboard.workspace'),
      //     permissionId: PERMISSION_ID.PAGE.DASHBOARD.WORKSPACE,
      //   },
      // },
    ],
  },
];

export default routes;
