import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:cog-outline',
      order: 2000,
      title: '系统管理',
      permissionId: PERMISSION_ID.PAGE.SYSTEM.ROOT,
    },
    name: 'System',
    path: '/system',
    children: [
      {
        name: 'SystemSettings',
        path: '/system/settings',
        component: () => import('#/views/system/settings/index.vue'),
        meta: {
          title: '系统配置',
          icon: 'mdi:cog-box',
          permissionId: PERMISSION_ID.PAGE.SYSTEM.SETTINGS,
          keepAlive: true,
        },
      },
      // 数据库备份
      {
        name: 'SystemDatabaseBackup',
        path: '/system/database-backup',
        component: () => import('#/views/system/database-backup/index.vue'),
        meta: {
          title: '数据库备份',
          icon: 'mdi:database',
          permissionId: PERMISSION_ID.PAGE.SYSTEM.DATABASE_BACKUP,
          keepAlive: true,
        },
      },
      {
        name: 'SystemSMS',
        path: '/system/sms',
        component: () => import('#/views/system/sms/index.vue'),
        meta: {
          title: '短信配置',
          icon: 'mdi:message-text',
          permissionId: PERMISSION_ID.PAGE.SYSTEM.SMS,
          keepAlive: true,
        },
      },
      {
        name: 'SystemAPIBalance',
        path: '/system/api-balance',
        component: () => import('#/views/system/api-balance/index.vue'),
        meta: {
          title: 'API预存面额',
          icon: 'mdi:currency-usd',
          permissionId: PERMISSION_ID.PAGE.SYSTEM.API_BALANCE,
          keepAlive: true,
        },
      },
      {
        name: 'SystemRecharge',
        path: '/system/recharge',
        component: () => import('#/views/system/recharge/index.vue'),
        meta: {
          title: '充值端配置',
          icon: 'mdi:cash-register',
          permissionId: PERMISSION_ID.PAGE.SYSTEM.RECHARGE,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
