import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout, IFrameView } from '#/layouts';
import { $t } from '#/locales';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      badgeType: 'dot',
      // 文档
      icon: 'mdi:document',
      order: 9999,
      title: $t('demos.vben.document'),
      permissionId: PERMISSION_ID.PAGE.ANSHENG.ROOT,
    },
    name: 'VbenProject',
    path: '/ansheng-admin',
    children: [
      // ai机器人
      {
        name: 'VbenAiRobot',
        path: '/ansheng-admin/ai-robot',
        component: IFrameView,
        meta: {
          icon: 'mdi:robot',
          iframeSrc: 'https://udify.app/chatbot/OpljXBXE3emg0VRO',
          title: 'AI智能文档',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ANSHENG.AI_ROBOT,
        },
      },
      // 【腾讯文档】智讯物联网管理平台手册 https://docs.qq.com/doc/DSXJMdEN6Q0hVeGNq
      {
        name: 'VbenDocument',
        path: '/ansheng-admin/document',
        component: IFrameView,
        meta: {
          icon: 'cryptocurrency:pay',
          iframeSrc: 'https://docs.qq.com/doc/DSXJMdEN6Q0hVeGNq',
          title: '系统手册',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ANSHENG.DOCUMENT,
        },
      },
      // api接口文档 https://doc.apipost.net/docs/detail/2aa0c24e3464000?target_id=4976a1d
      {
        name: 'VbenApi',
        path: '/ansheng-admin/api',
        component: IFrameView,
        meta: {
          icon: 'lucide:book-open-text',
          iframeSrc:
            'https://doc.apipost.net/docs/detail/2aa0c24e3464000?target_id=4976a1d',
          title: $t('demos.vben.api'),
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ANSHENG.API,
        },
      },
      // 微信支付配置教程 https://docs.qq.com/doc/DWVdCZVNWa1BzYWJm
      {
        name: 'VbenWechatPay',
        path: '/ansheng-admin/wechat-pay',
        component: IFrameView,
        meta: {
          icon: 'mdi:wechat',
          iframeSrc: 'https://docs.qq.com/doc/DWVdCZVNWa1BzYWJm',
          title: $t('demos.vben.wechat-pay'),
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ANSHENG.WECHAT_PAY,
        },
      },
      // 支付宝支付配置 https://docs.qq.com/doc/DWWJteE1xWnpDeHRE
      {
        name: 'VbenAlipayPay',
        path: '/ansheng-admin/alipay-pay',
        component: IFrameView,
        meta: {
          icon: 'cib:alipay',
          iframeSrc: 'https://docs.qq.com/doc/DWWJteE1xWnpDeHRE',
          title: $t('demos.vben.alipay-pay'),
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ANSHENG.ALIPAY_PAY,
        },
      },
      // 斗拱聚合支付申请 https://console.adapay.tech/merconsole/login
      {
        name: 'VbenAdapayPay',
        path: '/ansheng-admin/adapay-pay',
        component: IFrameView,
        meta: {
          icon: 'mdi:cash-multiple',
          iframeSrc: 'https://console.adapay.tech/merconsole/login',
          title: $t('demos.vben.adapay-pay'),
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ANSHENG.ADAPAY_PAY,
        },
      },
      // 关于我们
      // {
      //   name: 'VbenAbout',
      //   path: '/ansheng-admin/about',
      //   component: () => import('#/views/_core/about/index.vue'),
      //   meta: {
      //     icon: 'lucide:copyright',
      //     title: $t('demos.vben.about'),
      //     permissionId: PERMISSION_ID.PAGE.VBEN.ABOUT,
      //   },
      // },
      // {
      //   name: 'VbenDocument',
      //   path: '/ansheng-admin/document',
      //   component: IFrameView,
      //   meta: {
      //     icon: 'lucide:book-open-text',
      //     link: VBEN_DOC_URL,
      //     title: $t('demos.vben.document'),
      //     permissionId: PERMISSION_ID.PAGE.VBEN.DOCUMENT,
      //   },
      // },
      // {
      //   name: 'VbenGithub',
      //   path: '/ansheng-admin/github',
      //   component: IFrameView,
      //   meta: {
      //     icon: 'mdi:github',
      //     link: VBEN_GITHUB_URL,
      //     title: 'Github',
      //     permissionId: PERMISSION_ID.PAGE.VBEN.GITHUB,
      //   },
      // },
      // {
      //   name: 'VbenNaive',
      //   path: '/ansheng-admin/naive',
      //   component: IFrameView,
      //   meta: {
      //     badgeType: 'dot',
      //     icon: 'logos:naiveui',
      //     link: VBEN_NAIVE_PREVIEW_URL,
      //     title: $t('demos.vben.naive-ui'),
      //     permissionId: PERMISSION_ID.PAGE.VBEN.NAIVE,
      //   },
      // },
      // {
      //   name: 'VbenElementPlus',
      //   path: '/ansheng-admin/ele',
      //   component: IFrameView,
      //   meta: {
      //     badgeType: 'dot',
      //     icon: 'logos:element',
      //     link: VBEN_ELE_PREVIEW_URL,
      //     title: $t('demos.vben.element-plus'),
      //     permissionId: PERMISSION_ID.PAGE.VBEN.ELEMENT_PLUS,
      //   },
      // },
    ],
  },
];

export default routes;
