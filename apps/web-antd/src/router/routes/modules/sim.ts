import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:sim', // 使用 SIM 卡图标
      order: 4, // 排在订单管理后面
      title: '卡片管理',
      permissionId: PERMISSION_ID.PAGE.SIM.ROOT,
    },
    name: 'Sim',
    path: '/sim',
    children: [
      {
        name: 'SimList',
        path: '/sim/list',
        component: () => import('#/views/sim/list/index.vue'),
        meta: {
          title: '卡片列表',
          icon: 'mdi:format-list-bulleted',
          permissionId: PERMISSION_ID.PAGE.SIM.LIST,
          keepAlive: true,
        },
      },
      {
        name: 'SimDetail',
        path: '/sim/detail',
        component: () => import('#/views/sim/detail/index.vue'),
        meta: {
          title: '单卡查询',
          // hideInMenu: true, // 在菜单中隐藏
          icon: 'mdi:sim-outline',
          permissionId: PERMISSION_ID.PAGE.SIM.DETAIL,
          keepAlive: true,
          maxNumOfOpenTab: 1,
          query: {
            cardNo: '',
          },
        },
      },
      {
        name: 'SimReplacement',
        path: '/sim/replacement',
        component: () => import('#/views/sim/replacement/index.vue'),
        meta: {
          title: '卡片更换',
          icon: 'mdi:sim-alert',
          permissionId: PERMISSION_ID.PAGE.SIM.REPLACEMENT,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
