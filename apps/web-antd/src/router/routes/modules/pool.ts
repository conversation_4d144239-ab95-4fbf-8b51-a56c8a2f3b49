import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:database',
      order: 6,
      title: '分池管理',
      permissionId: PERMISSION_ID.PAGE.POOL.ROOT,
    },
    name: 'Pool',
    path: '/pool',
    children: [
      {
        name: 'PoolGroup',
        path: '/pool/group',
        component: () => import('#/views/pool/group/index.vue'),
        meta: {
          title: '分池池组',
          icon: 'mdi:database-cog',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.POOL.GROUP,
        },
      },
      {
        name: 'PoolCard',
        path: '/pool/card',
        component: () => import('#/views/pool/card/index.vue'),
        meta: {
          title: '分池卡片',
          icon: 'mdi:sim',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.POOL.CARD,
        },
      },
      {
        name: 'PoolOrder',
        path: '/pool/order',
        component: () => import('#/views/pool/order/index.vue'),
        meta: {
          title: '分池订单',
          icon: 'mdi:file-document',
          permissionId: PERMISSION_ID.PAGE.POOL.ORDER,
          keepAlive: true,
        },
      },
      {
        name: 'CardChangeLog',
        path: '/pool/card-log',
        component: () => import('#/views/pool/card-log/index.vue'),
        meta: {
          title: '卡更变记录',
          icon: 'mdi:card-text',
          permissionId: PERMISSION_ID.PAGE.POOL.CARD_LOG,
          keepAlive: true,
        },
      },
      {
        name: 'PoolBalanceLog',
        path: '/pool/balance-log',
        component: () => import('#/views/pool/balance-log/index.vue'),
        meta: {
          title: '池余额变更日志',
          icon: 'mdi:cash',
          permissionId: PERMISSION_ID.PAGE.POOL.BALANCE_LOG,
          keepAlive: true,
        },
      },
      {
        name: 'PoolPollingLog',
        path: '/pool/polling-log',
        component: () => import('#/views/pool/polling-log/index.vue'),
        meta: {
          title: '池轮询日志',
          icon: 'mdi:refresh',
          permissionId: PERMISSION_ID.PAGE.POOL.POLLING_LOG,
          keepAlive: true,
        },
      },
      {
        name: 'BillingDetail',
        path: '/pool/billing',
        // component: () => import('#/views/pool/billing/index.vue'),
        component: () => import('#/views/pool/billing/example-use-ansheng.vue'),
        meta: {
          title: '账单明细',
          icon: 'mdi:receipt',
          permissionId: PERMISSION_ID.PAGE.POOL.BILLING,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
