import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:wechat',
      order: 12,
      title: '小程序',
      permissionId: PERMISSION_ID.PAGE.MINIAPP.ROOT,
    },
    name: 'Miniapp',
    path: '/miniapp',
    children: [
      {
        name: 'Wechat<PERSON><PERSON><PERSON>',
        path: '/miniapp/wechat',
        component: () => import('#/views/miniapp/wechat/index.vue'),
        meta: {
          title: '微信小程序',
          icon: 'mdi:wechat',
          permissionId: PERMISSION_ID.PAGE.MINIAPP.WECHAT,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
