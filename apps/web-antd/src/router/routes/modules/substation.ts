import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    path: '/substation',
    component: BasicLayout,
    meta: {
      icon: 'eos-icons:cluster',
      order: 8,
      title: $t('page.substation.manage'),
      permissionId: PERMISSION_ID.PAGE.SUBSTATION.ROOT,
    },
    name: 'SubstationManage',
    children: [
      {
        name: 'SubstationList',
        path: '/substation/list',
        component: () => import('#/views/substation/list/index.vue'),
        meta: {
          icon: 'eos-icons:background-tasks',
          title: $t('page.substation.list'),
          permissionId: PERMISSION_ID.PAGE.SUBSTATION.LIST,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
