import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:bullhorn',
      order: 17,
      title: '公告管理',
      permissionId: PERMISSION_ID.PAGE.ANNOUNCEMENT.ROOT,
    },
    name: 'Announcement',
    path: '/announcement',
    children: [
      // 公告配置
      {
        name: 'AnnouncementConfig',
        path: '/announcement/config',
        component: () => import('#/views/announcement/config/index.vue'),
        // component: () => import('#/views/system/settings/index.vue'),
        meta: {
          icon: 'mdi:bullhorn',
          title: '公告配置',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ANNOUNCEMENT.CONFIG,
        },
      },
      {
        name: 'CardAnnouncement',
        path: '/announcementstem/card',
        component: () => import('#/views/announcement/card/index.vue'),
        meta: {
          title: '卡片公告配置',
          icon: 'mdi:card-text',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ANNOUNCEMENT.CARD,
        },
      },
      {
        name: 'SingleCardAnnouncement',
        path: '/announcement/singleCard',
        component: () => import('#/views/announcement/single-card/index.vue'),
        meta: {
          title: '单卡公告',
          icon: 'mdi:card',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ANNOUNCEMENT.SINGLECARD,
        },
      },
      {
        name: 'DeviceAnnouncement',
        path: '/announcement/device',
        component: () => import('#/views/announcement/device/index.vue'),
        meta: {
          title: '设备公告',
          icon: 'mdi:devices',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.ANNOUNCEMENT.DEVICE,
        },
      },
    ],
  },
];

export default routes;
