import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:store',
      order: 8,
      title: '代发商城',
      permissionId: PERMISSION_ID.PAGE.SHOP.ROOT,
    },
    name: 'Shop',
    path: '/shop',
    children: [
      // {
      //   name: 'CardChannel',
      //   path: '/shop/channel',
      //   component: () => import('#/views/shop/channel/index.vue'),
      //   meta: {
      //     title: '号卡通道',
      //     icon: 'mdi:sim',
      //     permissionId: PERMISSION_ID.PAGE.SHOP.CHANNEL,
      //   },
      // },
      {
        name: 'ProductCategory',
        path: '/shop/category',
        component: () => import('#/views/shop/category/index.vue'),
        meta: {
          title: '商品分类',
          icon: 'mdi:format-list-bulleted',
          permissionId: PERMISSION_ID.PAGE.SHOP.CATEGORY,
          keepAlive: true,
        },
      },
      {
        name: 'ProductList',
        path: '/shop/product',
        component: () => import('#/views/shop/product/index.vue'),
        meta: {
          title: '商品列表',
          icon: 'mdi:package-variant',
          permissionId: PERMISSION_ID.PAGE.SHOP.PRODUCT,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
