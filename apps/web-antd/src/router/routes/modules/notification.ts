import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:bell-outline',
      order: 13,
      // title: '通知任务',
      title: '通知公告',
      permissionId: PERMISSION_ID.PAGE.NOTIFICATION.ROOT,
    },
    name: 'Notification',
    path: '/notification',
    children: [
      {
        name: 'SmsNotification',
        path: '/notification/sms',
        component: () => import('#/views/notification/sms/index.vue'),
        meta: {
          title: '短信通知',
          icon: 'mdi:message-text',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.NOTIFICATION.SMS,
        },
      },
      {
        name: 'NotificationPlan',
        path: '/notification/plan',
        component: () => import('#/views/notification/plan/index.vue'),
        meta: {
          title: '通知计划',
          icon: 'mdi:calendar-clock',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.NOTIFICATION.PLAN,
        },
      },
      {
        name: 'WechatTemplate',
        path: '/notification/wechat-template',
        component: () =>
          import('#/views/notification/wechat-template/index.vue'),
        meta: {
          title: '微信通知模板',
          icon: 'mdi:wechat',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.NOTIFICATION.WECHAT_TEMPLATE,
        },
      },
      {
        name: 'WechatPlan',
        path: '/notification/wechat-plan',
        component: () => import('#/views/notification/wechat-plan/index.vue'),
        meta: {
          title: '微信通知计划',
          icon: 'mdi:calendar-clock',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.NOTIFICATION.WECHAT_PLAN,
        },
      },
    ],
  },
];

export default routes;
