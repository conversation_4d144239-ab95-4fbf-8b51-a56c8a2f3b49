import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:file-document-outline',
      order: 7, // 排在用户管理后面
      title: '订单管理',
      permissionId: PERMISSION_ID.PAGE.ORDER.ROOT,
    },
    name: 'Order',
    path: '/order',
    children: [
      {
        name: 'OrderList',
        path: '/order/list',
        component: () => import('#/views/order/list/index.vue'),
        meta: {
          title: '卡片套餐订单',
          icon: 'mdi:format-list-bulleted',
          permissionId: PERMISSION_ID.PAGE.ORDER.LIST,
          keepAlive: true,
        },
      },
      // {
      //   name: 'OrderRecharge',
      //   path: '/order/recharge',
      //   component: () => import('#/views/order/recharge/index.vue'),
      //   meta: {
      //     title: '话费订单',
      //     icon: 'mdi:phone-sync',
      //     permissionId: PERMISSION_ID.PAGE.ORDER.RECHARGE,
      //   },
      // },
      {
        name: 'OrderPrepaid',
        path: '/order/prepaid',
        component: () => import('#/views/order/prepaid/index.vue'),
        meta: {
          title: '卡片预存订单',
          icon: 'mdi:wallet-plus',
          permissionId: PERMISSION_ID.PAGE.ORDER.PREPAID,
          keepAlive: true,
        },
      },
      {
        name: 'OrderChange',
        path: '/order/change',
        component: () => import('#/views/order/change/index.vue'),
        meta: {
          // title: '换卡订单',
          title: '设备更换订单',
          icon: 'mdi:sim',
          permissionId: PERMISSION_ID.PAGE.ORDER.CHANGE,
          keepAlive: true,
        },
      },
      {
        name: 'OrderDevicePackage',
        path: '/order/device-package',
        component: () => import('#/views/order/device-package/index.vue'),
        meta: {
          title: '设备套餐订单',
          icon: 'mdi:devices',
          permissionId: PERMISSION_ID.PAGE.ORDER.DEVICE_PACKAGE,
          keepAlive: true,
        },
      },
      {
        name: 'OrderBalance',
        path: '/order/balance',
        component: () => import('#/views/order/balance/index.vue'),
        meta: {
          title: '账号余额订单',
          icon: 'mdi:credit-card-outline',
          permissionId: PERMISSION_ID.PAGE.ORDER.BALANCE,
          keepAlive: true,
        },
      },
      {
        name: 'OrderProduct',
        path: '/order/product',
        component: () => import('#/views/order/product/index.vue'),
        meta: {
          title: '代发商城订单',
          icon: 'mdi:shopping',
          permissionId: PERMISSION_ID.PAGE.ORDER.PRODUCT,
          keepAlive: true,
        },
      },
      {
        name: 'OrderCoupon',
        path: '/order/coupon',
        component: () => import('#/views/order/coupon/index.vue'),
        meta: {
          title: '优惠券订单',
          icon: 'mdi:ticket-percent',
          permissionId: PERMISSION_ID.PAGE.ORDER.COUPON,
          keepAlive: true,
        },
      },
      {
        name: 'OrderDeviceBalance',
        path: '/order/device-balance',
        component: () => import('#/views/order/device-balance/index.vue'),
        meta: {
          title: '设备预存订单',
          icon: 'mdi:cellphone-check',
          permissionId: PERMISSION_ID.PAGE.ORDER.DEVICE_BALANCE,
          keepAlive: true,
        },
      },
      {
        name: 'OrderAlipay',
        path: '/order/alipay',
        component: () => import('#/views/order/alipay/index.vue'),
        meta: {
          title: '支付宝网关支付订单',
          icon: 'mdi:cash-multiple',
          permissionId: PERMISSION_ID.PAGE.ORDER.ALIPAY,
          keepAlive: true,
        },
      },
      {
        name: 'OrderInvoice',
        path: '/order/invoice',
        component: () => import('#/views/order/invoice/index.vue'),
        meta: {
          title: '已开票订单',
          icon: 'mdi:receipt',
          permissionId: PERMISSION_ID.PAGE.ORDER.INVOICE,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
