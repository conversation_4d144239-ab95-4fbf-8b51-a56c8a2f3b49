import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:phone',
      order: 9,
      title: '话费管理',
      permissionId: PERMISSION_ID.PAGE.RECHARGE.ROOT,
    },
    name: 'Recharge',
    path: '/recharge',
    children: [
      {
        name: 'ServiceNumber',
        path: '/recharge/service-number',
        component: () => import('#/views/recharge/service-number/index.vue'),
        meta: {
          title: '服务号码',
          icon: 'mdi:phone-classic',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.RECHARGE.SERVICE_NUMBER,
        },
      },
      {
        name: 'RechargeChannel',
        path: '/recharge/channel',
        component: () => import('#/views/recharge/channel/index.vue'),
        meta: {
          title: '话费通道',
          icon: 'mdi:transit-connection',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.RECHARGE.CHANNEL,
        },
      },
      {
        name: 'RechargeParams',
        path: '/recharge/params',
        component: () => import('#/views/recharge/params/index.vue'),
        meta: {
          title: '话费参数',
          icon: 'mdi:cog',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.RECHARGE.PARAMS,
        },
      },
      {
        name: 'RechargeManage',
        path: '/recharge/manage',
        component: () => import('#/views/recharge/manage/index.vue'),
        meta: {
          title: '充值管理',
          icon: 'mdi:cash-plus',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.RECHARGE.MANAGE,
        },
      },
      {
        name: 'RechargeCommissionDetail',
        path: '/recharge/commission',
        component: () => import('#/views/recharge/commission/index.vue'),
        meta: {
          title: '佣金明细',
          icon: 'mdi:cash-multiple',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.RECHARGE.COMMISSION,
        },
      },
      {
        name: 'RechargeNotice',
        path: '/recharge/notice',
        component: () => import('#/views/recharge/notice/index.vue'),
        meta: {
          title: '话费公告',
          icon: 'mdi:bullhorn',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.RECHARGE.NOTICE,
        },
      },
    ],
  },
];

export default routes;
