import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:robot',
      order: 14,
      title: '模版管理',
      permissionId: PERMISSION_ID.PAGE.AUTOMATION.ROOT,
    },
    name: 'Automation',
    path: '/automation',
    children: [
      {
        name: 'DepositTemplate',
        path: '/automation/deposit',
        component: () => import('#/views/automation/index.vue'),
        meta: {
          title: '模版列表',
          icon: 'mdi:wallet-plus',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.AUTOMATION.DEPOSIT,
        },
      },
    ],
  },
];

export default routes;
