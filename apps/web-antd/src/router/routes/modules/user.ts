import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    path: '/user',
    component: BasicLayout,
    meta: {
      icon: 'mdi:account-group',
      order: 2,
      title: $t('page.user.manage'),
      permissionId: PERMISSION_ID.PAGE.USER.ROOT,
    },
    name: 'User<PERSON>anage',
    children: [
      {
        name: 'UserList',
        path: '/user/list',
        component: () => import('#/views/user/list/index.vue'),
        meta: {
          icon: 'mdi:account-multiple',
          title: $t('page.user.list'),
          permissionId: PERMISSION_ID.PAGE.USER.LIST,
          keepAlive: true,
        },
      },
      {
        name: 'RoleList',
        path: '/user/role',
        component: () => import('#/views/user/role/index.vue'),
        meta: {
          icon: 'mdi:shield-account',
          title: $t('page.user.role'),
          permissionId: PERMISSION_ID.PAGE.USER.ROLE,
          keepAlive: true,
        },
      },
    ],
  },
];

export default routes;
