import type { RouteRecordRaw } from 'vue-router';

// import { BasicLayout } from '#/layouts';
import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    path: '/forum',
    name: 'Forum',
    redirect: '/forum/home',
    meta: {
      orderNo: 2000,
      icon: 'ion:chatbubble-ellipses-outline',
      title: '论坛',
      permissionId: PERMISSION_ID.PAGE.FORUM.ROOT,
    },
    children: [
      {
        path: 'home',
        name: 'ForumHome',
        component: () => import('#/views/forum/home/<USER>'),
        meta: {
          title: '论坛首页',
          icon: 'mdi:forum',
          noBasicLayout: true,
          permissionId: PERMISSION_ID.PAGE.FORUM.HOME,
        },
      },
      {
        path: 'post/:id',
        name: 'PostDetail',
        component: () => import('#/views/forum/post-detail/index.vue'),
        meta: {
          title: '帖子详情',
          hideInMenu: true,
          noBasicLayout: true,
        },
      },
    ],
  },
];

export default routes;
