import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

import { PERMISSION_ID } from '../permission-map';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:laptop-outlined',
      order: 5, // 排在订单管理后面
      title: '设备管理',
      permissionId: PERMISSION_ID.PAGE.DEVICE.ROOT,
    },
    name: 'Device',
    path: '/device',
    redirect: '/device/info',
    children: [
      {
        name: 'DeviceSimList',
        path: '/device/sim',
        component: () => import('#/views/device/device-sim-list/index.vue'),
        meta: {
          title: '设备卡片',
          icon: 'mdi:format-list-bulleted',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.DEVICE.SIM_LIST,
        },
      },
      // 设备列表
      {
        name: 'DeviceList',
        path: '/device/list',
        component: () => import('#/views/device/list/index.vue'),
        meta: {
          title: '设备列表',
          icon: 'mdi:view-list',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.DEVICE.LIST,
        },
      },
      // 设备信息
      {
        name: 'DeviceInfo',
        path: '/device/info',
        component: () => import('#/views/device/info/index.vue'),
        meta: {
          title: '设备查询',
          icon: 'mdi:information',
          keepAlive: true,
          permissionId: PERMISSION_ID.PAGE.DEVICE.INFO,
        },
      },
    ],
  },
];

export default routes;
