// 权限映射器 - 使用自增ID生成唯一数字权限码
const PermissionMapper = {
  private: {
    currentId: 1,
  },

  generateId(): number {
    return this.private.currentId++;
  },
};

// 权限码定义 - 直接使用数字
// export const PERMISSION_CODE = {
export const PERMISSION_ID = {
  PAGE: {
    /** 仪表盘模块 */
    DASHBOARD: {
      ROOT: 1, // 1 // 概括
      ANALYTICS: 2, // 2 // 分析页
    },
    /** 数据大屏模块 */
    SCREEN: {
      ROOT: 30, // 数据大屏根节点
      OVERVIEW: 31, // 订单地图
      DATA_STATISTICS: 32, // 数据统计
      ORDER: 33, // 订单监控
    },
    /** 用户管理模块 */
    USER: {
      ROOT: 60,
      LIST: 61, // 用户列表
      ROLE: 62, // 角色列表
    },
    /** 设备管理模块 */
    DEVICE: {
      ROOT: 90,
      SIM_LIST: 91, // 设备卡列表
      LIST: 92, // 设备列表
      INFO: 93, // 设备信息
    },
    /** 套餐管理模块 */
    PACKAGE: {
      ROOT: 120,
      REAL_NAME: 121, // 实名规则
      CATEGORY: 122, // 套餐分类/计费
      PACKAGE_RULE: 123, // 套餐规则
      CARD: 124, // 卡片套餐
      DEVICE: 125, // 设备套餐
    },
    /** 流量池模块 */
    POOL: {
      ROOT: 150,
      GROUP: 151, // 分池池组
      CARD: 152, // 分池卡片
      ORDER: 153, // 分池订单
      CARD_LOG: 154, // 卡更变记录
      BALANCE_LOG: 155, // 池余额变更日志
      POLLING_LOG: 156, // 池轮询日志
      BILLING: 157, // 账单明细
    },
    /** 素材管理模块 */
    MATERIAL: {
      ROOT: 180,
      LIBRARY: 181, // 素材库
      IMAGE_TYPE: 182, // 图片类型
    },
    /** 号卡商城模块 */
    SHOP: {
      ROOT: 210,
      CHANNEL: 211, // 号卡通道
      CATEGORY: 212, // 商品分类
      PRODUCT: 213, // 商品列表
    },
    /** 话费管理模块 */
    RECHARGE: {
      ROOT: 240,
      SERVICE_NUMBER: 241, // 服务号码
      CHANNEL: 242, // 话费通道
      PARAMS: 243, // 话费参数
      MANAGE: 244, // 充值管理
      COMMISSION: 245, // 佣金明细
      NOTICE: 246, // 话费公告
    },
    /** 订单管理模块 */
    ORDER: {
      ROOT: 270,
      LIST: 271, // 订单列表
      PACKAGE: 272, // 套餐订单
      RECHARGE: 273, // 话费订单
      PREPAID: 274, // 预存订单
      BALANCE: 275, // 卡片余额
      COUPON: 276, // 优惠券订单
      PRODUCT: 277, // 商品订单
      CHANGE: 278, // 换卡订单
      DEVICE_PACKAGE: 279, // 设备套餐订单
      DEVICE_BALANCE: 280, // 设备余额订单
      ALIPAY: 281, // 支付宝网关支付订单
      INVOICE: 282, // 已开票订单
    },
    /** 推广活动模块 */
    PROMOTION: {
      ROOT: 300,
      COUPON: 301, // 优惠券
      CAROUSEL: 301, // 轮播推广
      MEMBER_TEMPLATE: 301, // 设备会员模板
    },
    /** 小程序模块 */
    MINIAPP: {
      ROOT: 330,
      WECHAT: 331, // 微信小程序
    },
    /** 通知任务模块 */
    NOTIFICATION: {
      ROOT: 360,
      SMS: 361, // 短信通知
      PLAN: 362, // 通知计划
      WECHAT_TEMPLATE: 363, // 微信通知模板
      WECHAT_PLAN: 364, // 微信通知计划
    },
    /** 模版列表管理模块 */
    AUTOMATION: {
      ROOT: 390,
      DEPOSIT: 391, // 模版列表
    },
    /** 财务管理模块 */
    FINANCE: {
      ROOT: 420,
      BALANCE: 421, // 余额明细
      COMMISSION: 422, // 佣金明细
      WITHDRAW: 423, // 提现记录
      INVOICE_CONFIG: 424, // 发票配置
      INVOICE_TITLE: 425, // 抬头管理
    },
    /** 支付配置模块 */
    PAYMENT: {
      ROOT: 450,
      WECHAT_PUBLIC: 451, // 公众号配置
      WECHAT_MERCHANT: 452, // 微信商户配置
      ALIPAY_MERCHANT: 453, // 支付宝商户配置
      DOUGONG_AGGREGATE: 454, // 斗拱聚合支付
      DOUGONG_HUIFU: 455, // 斗拱汇付支付
      HUOLIAN_MERCHANT: 456, // 火脸商户配置
      XIAOMAGE_MERCHANT: 457, // 小马哥商户
      ZHIXUNYI_MERCHANT: 458, // 智讯易商户
      WITHDRAW: 459, // 提现配置
      COMPREHENSIVE: 460, // 综合配置
    },
    /** 任务中心模块 */
    TASK: {
      ROOT: 480,
      SYSTEM: 481, // 系统任务
      POLLING_FAILED: 482, // 轮询失败卡
      POLLING_STATUS: 483, // 轮询状态
    },
    /** 系统日志模块 */
    LOG: {
      ROOT: 510,
      UNIFIED: 511, // 统一日志
      RISK: 512, // 风险拦截
      MERCHANT_CALLBACK: 513, // 商户回调
      ALIPAY_CALLBACK: 514, // 支付宝网关支付回调
      CARD_TRANSFER: 515, // 卡信息转移
    },
    /** 风险防控模块 */
    RISK: {
      ROOT: 540,
      AGENT_VERIFICATION: 541, // 代理实名
      AGREEMENT: 542, // 协议管理
      AREA: 543, // 风险地区
    },
    /** 系统管理模块 */
    SYSTEM: {
      ROOT: 570,
      SETTINGS: 571, // 系统配置
      DATABASE_BACKUP: 572, // 数据库备份
      MENU: 603, // 菜单管理
      SMS: 604, // 短信配置
      API_BALANCE: 605, // API预存面额
      RECHARGE: 606, // 充值端配置
      API_MONITOR: 607, // API监控
    },
    /** 个人中心模块 */
    ACCOUNT: {
      ROOT: 600,
      SETTINGS: 601, // 个人设置
      VALUE_ADDED_SERVICES: 602, // 增值服务
    },
    /** SIM卡管理模块 */
    SIM: {
      ROOT: 630,
      LIST: 631, // 卡片列表
      DETAIL: 632, // 单卡查询
      REPLACEMENT: 633, // 卡片更换
    },
    /** 文档相关模块 */
    ANSHENG: {
      ROOT: 660,
      API: 661, // api文档
      WECHAT_PAY: 662, // 微信支付配置教程
      ALIPAY_PAY: 663, // 支付宝支付配置
      ADAPAY_PAY: 664, // 斗拱聚合支付申请
      DOCUMENT: 665, // 系统手册
      AI_ROBOT: 666, // AI机器人
    },
    /** 分站管理模块 */
    SUBSTATION: {
      ROOT: 690,
      LIST: 691, // 分站列表
    },
    /** 公告管理模块 */
    ANNOUNCEMENT: {
      ROOT: 720,
      CONFIG: 721, // 公告配置
      CARD: 722, // 卡片公告
      SINGLECARD: 723, // 单卡公告
      DEVICE: 724, // 设备公告
    },

    // 通道管理
    CHANNEL: {
      ROOT: 750,
      SIM_CHANNEL: 751, // 卡片通道
      DEVICE_CHANNEL: 752, // 设备通道
    },
    /** 活码管理模块 */
    LIVE_CODE: {
      ROOT: 780,
      LIST: 781, // 活码列表
      CONFIG: 782, // 活码配置
    },
    /** 论坛模块 */
    FORUM: {
      ROOT: 810,
      HOME: 811, // 论坛首页
    },
  },
  COMPONENT: {
    /** 按钮权限 */
    BUTTON: {
      ADD: PermissionMapper.generateId(), // 新增按钮
      EDIT: PermissionMapper.generateId(), // 编辑按钮
      DELETE: PermissionMapper.generateId(), // 删除按钮
      EXPORT: PermissionMapper.generateId(), // 导出按钮
      IMPORT: PermissionMapper.generateId(), // 导入按钮
      SUBMIT: PermissionMapper.generateId(), // 提交按钮
      SYNC: PermissionMapper.generateId(), // 同步按钮
    },
    /** 表格权限 */
    TABLE: {
      OPERATION: PermissionMapper.generateId(), // 操作列
      SENSITIVE: PermissionMapper.generateId(), // 敏感数据列
      FINANCE: PermissionMapper.generateId(), // 财务数据列
      STATUS: PermissionMapper.generateId(), // 状态列
      AMOUNT: PermissionMapper.generateId(), // 金额列
    },
    /** 表单权限 */
    FORM: {
      SENSITIVE: PermissionMapper.generateId(), // 敏感信息表单
      FINANCE: PermissionMapper.generateId(), // 财务信息表单
    },
  },
} as const;

// 权限检查工具
export const PermissionChecker = {
  hasPermission(
    userPermissions: number | string,
    permissionId: number,
  ): boolean {
    // 如果是字符串，先转换为数字数组
    const permissions =
      typeof userPermissions === 'string'
        ? userPermissions.split(',').map(Number)
        : [userPermissions];
    return permissions.includes(permissionId);
  },

  hasAnyPermission(
    userPermissions: number | string,
    ...permissionIds: number[]
  ): boolean {
    return permissionIds.some((id) => this.hasPermission(userPermissions, id));
  },

  hasAllPermissions(
    userPermissions: number | string,
    ...permissionIds: number[]
  ): boolean {
    return permissionIds.every((id) => this.hasPermission(userPermissions, id));
  },
};
