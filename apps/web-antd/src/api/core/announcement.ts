import type { BaseResponse } from '#/api/model';

import { requestClient } from '#/api/request';

// 公告列表参数接口
export interface AnnouncementListParams {
  page: number;
  pageSize: number;
  title?: string;
  type?: number;
}

// 公告数据接口
export interface AnnouncementItem {
  id?: number;
  title?: string;
  detail?: string;
  type?: number;
  status?: null | number;
  channel_id?: null | number;
  creation_time?: string;
  update_time?: string;
  tag?: string; //  1 活动  2通知 3 系统
}

// 公告列表响应接口
export interface AnnouncementListResponse extends BaseResponse {
  data: {
    rows: AnnouncementItem[];
    total: number;
  };
}

// 控制端公告 /py/backend/backstage/getsystembulletinview/
// {
// 	"code": 1,
// 	"msg": "success",
// 	"data": {
// 		"total": 117,
// 		"rows": [
// 			{
// 				"bulletin_id": 4,
// 				"bulletin__title": "测试标题",
// 				"bulletin__content": "测试内容",
// 				"id": 77
// 			}
// 		]
// 	}
// }

export interface AnnouncementItem {
  bulletin_id: number;
  bulletin__title: string;
  bulletin__content: string;
  id: number;
}

export interface AnnouncementListResponse extends BaseResponse {
  data: {
    rows: AnnouncementItem[];
    total: number;
  };
}
// 控制端公告列表
export const getControlAnnouncementList = (params: AnnouncementListParams) => {
  return requestClient.get<AnnouncementListResponse>(
    '/py/backend/backstage/getsystembulletinview/',
    { params },
  );
};

// 修改控制端公告状态 /py/backend/backstage/getsystembulletinview/
export const updateControlAnnouncementStatus = (data: {
  id: number | string;
  op_type: number;
}) => {
  return requestClient.put<BaseResponse>(
    `/py/backend/backstage/getsystembulletinview/`,
    data,
  );
};

// 获取公告列表
export const getAnnouncementList = (params: AnnouncementListParams) => {
  return requestClient.get<AnnouncementListResponse>(
    '/py/backend/backstage/announcement/',
    { params },
  );
};

// 新增公告
export const addAnnouncement = (data: Omit<AnnouncementItem, 'id'>) => {
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    if (value !== undefined) {
      formData.append(key, value.toString());
    }
  });
  return requestClient.post<BaseResponse>(
    '/py/backend/backstage/announcement/',
    formData,
  );
};

// 更新公告
export const updateAnnouncement = (data: AnnouncementItem) => {
  return requestClient.put<BaseResponse>(
    '/py/backend/backstage/announcement/',
    data,
  );
};

// 删除公告
export const deleteAnnouncement = (id: number) => {
  return requestClient.delete<BaseResponse>(
    `/py/backend/backstage/announcement/?id=${id}`,
  );
};
