import type { BaseResponse } from '#/api/model';

import { requestClient } from '#/api/request';

// 设备公告列表参数接口
export interface DeviceAnnouncementListParams {
  page: number;
  pageSize: number;
  ac_type?: number;
  jump_url?: string;
  title?: string;
}

// 设备公告数据接口
export interface DeviceAnnouncementItem {
  id: number;
  title: string;
  ac_type: number;
  content: string;
  jump_url: string;
  creation_time: string;
  update_time: string;
}

// 设备公告列表响应接口
export interface DeviceAnnouncementListResponse extends BaseResponse {
  data: {
    rows: DeviceAnnouncementItem[];
    total: number;
  };
}

// 获取设备公告列表
export const getDeviceAnnouncementList = (
  params: DeviceAnnouncementListParams,
) => {
  return requestClient.get<DeviceAnnouncementListResponse>(
    '/py/backend/backstage/deviceannouncementconfig/',
    { params },
  );
};

// 新增设备公告
export const addDeviceAnnouncement = (
  data: Omit<DeviceAnnouncementItem, 'creation_time' | 'id' | 'update_time'>,
) => {
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    if (value !== undefined) {
      formData.append(key, value.toString());
    }
  });
  return requestClient.post<BaseResponse>(
    '/py/backend/backstage/deviceannouncementconfig/',
    formData,
  );
};

// 更新设备公告
export const updateDeviceAnnouncement = (data: DeviceAnnouncementItem) => {
  return requestClient.put<BaseResponse>(
    '/py/backend/backstage/deviceannouncementconfig/',
    data,
  );
};

// 删除设备公告
// /py/backend/backstage/deviceannouncementconfig/?id=1
// 请求方法:
// DELETE
export const deleteDeviceAnnouncement = (id: number) => {
  return requestClient.delete<BaseResponse>(
    `/py/backend/backstage/deviceannouncementconfig/?id=${id}`,
  );
};

// /py/backend/backstage/deviceannouncementconfig/
// /py/backend/backstage/singledeviceannouncementconfig/
// 单设备公告配置列表项
export interface SingleDeviceAnnouncementConfigItem {
  ac__content: string;
  ac__jump_url: string;
  ac__title: string;
  ac_id: number;
}

// 单设备公告配置列表参数
export interface SingleDeviceAnnouncementConfigListParams {
  device_id: string; // 设备id
}

// 单设备公告配置列表响应
export interface SingleDeviceAnnouncementConfigListResponse {
  code: number;
  data: SingleDeviceAnnouncementConfigItem[];
  msg: string;
}

export function getSingleDeviceAnnouncementConfigList(params: {
  device_id: string;
}) {
  return requestClient.get<SingleDeviceAnnouncementConfigListResponse>(
    '/py/backend/backstage/singledeviceannouncementconfig/',
    {
      params,
    },
  );
}

// /py/backend/backstage/singledeviceannouncementconfig/ put 更新单设备公告
export const setSingleDeviceAnnouncement = (data: {
  ac_id: number;
  device_id: string;
  view: string;
}) => {
  return requestClient.put<BaseResponse>(
    '/py/backend/backstage/singledeviceannouncementconfig/',
    data,
  );
};

// /py/backend/backstage/singledeviceannouncementconfig/ 设置设备公告
export const setDeviceAnnouncement = (data: {
  ac_id: number;
  device_id: number;
  view: string;
}) => {
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    if (value !== undefined) {
      formData.append(key, value.toString());
    }
  });
  return requestClient.post<BaseResponse>(
    '/py/backend/backstage/singledeviceannouncementconfig/',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};
