import { requestClient } from '../request';

export interface PoolGroupItem {
  more_mode: number;
  low_dissipation: number;
  name: string;
  channel_id: number;
  channel__api_name: string;
  payment_mode: number;
  user_id: number;
  user__user_name: null | string;
  settlement: number;
  price: string;
  more_price: null | string;
  operator: string;
  remarks: null | string;
  status: number;
  id: number;
  settlement_date: number;
  user__user_account: string;
  billing_mode: number;
  balance: string;
  creation_time: string;
  total_price: number;
  low_total_flow: number;
  low_total_price: number;
  total_flow: number;
  total_card: number;
  total_activated: number;
}

export interface PoolGroupResponse {
  code: number;
  msg: string;
  data: {
    rows: PoolGroupItem[];
    total: number;
  };
}

export interface PoolGroupListParams {
  page: number;
  pageSize: number;
  name?: string;
  operator?: string;
  status?: number;
  channel_id?: number;
  billing_mode?: number;
  payment_mode?: number;
}

export const getPoolGroupList = (params: PoolGroupListParams) => {
  return requestClient.get<PoolGroupResponse>(
    '/py/poolgroup/poolgroupconfig/',
    { params },
  );
};

export interface CreatePoolGroupParams {
  name: string;
  channel_id: number;
  operator: string;
  billing_mode: number;
  payment_mode: number;
  price: string;
  more_price?: string;
  low_dissipation: number;
  settlement_date: number;
  remarks?: string;
  more_mode: number;
  balance: string;
  user_id?: number;
}

export interface CreatePoolGroupResponse {
  code: number;
  msg: string;
}

export const addPoolGroup = (params: CreatePoolGroupParams) => {
  // 创建 FormData 对象
  const formData = new FormData();

  // 将参数添加到 FormData
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      formData.append(key, value.toString());
    }
  });

  return requestClient.post<CreatePoolGroupResponse>(
    '/py/poolgroup/poolgroupconfig/',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};

export interface UpdatePoolGroupResponse {
  code: number;
  msg: string;
}

export const updatePoolGroup = (params: CreatePoolGroupParams) => {
  return requestClient.put<UpdatePoolGroupResponse>(
    '/py/poolgroup/poolgroupconfig/',
    {
      ...params,
      id: params.id,
    },
  );
};

export const deletePoolGroup = (id: number) => {
  return requestClient.delete<{
    code: number;
    data: null;
    msg: string;
  }>(`/py/poolgroup/poolgroupconfig/${id}/`);
};

export interface UpdatePoolGroupStatusParams {
  id: number;
  status: number;
}

export const updatePoolGroupStatus = (params: UpdatePoolGroupStatusParams) => {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
  }>('/py/poolgroup/poolgroupconfig/status/', params);
};

export const getPoolGroupDetail = (id: number) => {
  return requestClient.get<{
    code: number;
    data: PoolGroupItem;
    msg: string;
  }>(`/py/poolgroup/poolgroupconfig/${id}/`);
};

export interface ChannelOption {
  id: number;
  api_name: string;
}

export const getChannelOptions = () => {
  return requestClient.get<{
    code: number;
    data: ChannelOption[];
    msg: string;
  }>('/py/poolgroup/channeloptions/');
};

// 修改余额参数接口
export interface UpdateBalanceParams {
  id: number;
  recharge_amount: number;
}

// 增加余额 API
export const addBalance = (params: UpdateBalanceParams) => {
  // 创建 FormData 对象
  const formData = new FormData();
  formData.append('id', params.id.toString());
  formData.append('recharge_amount', params.recharge_amount.toString());

  return requestClient.post<{
    code: number;
    msg: string;
  }>('/py/poolgroup/poolgroupbalance/', formData);
};

// 扣除余额 API
export const deductBalance = (params: UpdateBalanceParams) => {
  return requestClient.delete<{
    code: number;
    msg: string;
  }>(
    `/py/poolgroup/poolgroupbalance/?recharge_amount=${params.recharge_amount}&id=${params.id}`,
  );
};

// 生成账单参数接口
export interface GenerateBillParams {
  pool_group_id: number;
  year_month: string;
}

// 生成账单 API
export const generateBill = (params: GenerateBillParams) => {
  return requestClient.post<{
    code: number;
    msg: string;
  }>('/py/poolgroup/generatebill/', params);
};

// 分池卡片列表项接口
export interface PoolCardItem {
  pool_group_id: number;
  pool_group__name: string;
  pool_group__payment_mode: number;
  pool_group__user__user_name: string;
  pool_group__user__user_id: number;
  void_number: string;
  pool_group__user_id: number;
  msisdn_number: string;
  iccid_number: string;
  used_flow: string;
  status: number;
  status_msg: null | string;
  real_name: number;
  creation_time: string;
  update_time: string;
}

// 分池卡片列表响应接口
export interface PoolCardResponse {
  code: number;
  msg: string;
  data: {
    rows: PoolCardItem[];
    total: number;
  };
}

// 分池卡片列表参数接口
export interface PoolCardListParams {
  page: number;
  pageSize: number;
  iccid_number?: string;
  msisdn_number?: string;
  real_name?: number;
  pool_group_id?: number;
  void_number?: string;
  status?: number;
  pool_group__user_id?: number;
}

// 获取分池卡片列表
export const getPoolCardList = (params: PoolCardListParams) => {
  return requestClient.get<PoolCardResponse>('/py/poolgroup/poolgroupcard/', {
    params,
  });
};

// 获取导入模板 API
export const getImportTemplate = () => {
  return requestClient.get('/py/media/file/导入模板.xls', {
    responseType: 'blob', // 指定响应类型为 blob
  });
};

// 导入卡片参数接口
export interface ImportCardParams {
  file: File;
  pool_group_id: number;
}

// 导入卡片 API
export const importCard = (params: ImportCardParams) => {
  const formData = new FormData();
  formData.append('file', params.file);
  formData.append('pool_group_id', params.pool_group_id.toString());

  return requestClient.post<{
    code: number;
    msg: string;
  }>('/py/poolgroup/poolgroupcard/', formData);
};

// 激活矫正参数接口
export interface ActivationCorrectionParams {
  pool_group_id: number;
}

// 激活矫正 API
export const activationCorrection = (params: ActivationCorrectionParams) => {
  return requestClient.put<{
    code: number;
    msg: string;
  }>('/py/poolgroup/activationcorrection/', params);
};

// 导出卡片列表
export const exportPoolCard = (params: Partial<PoolCardListParams>) => {
  return requestClient.get('/py/poolgroup/poolgroupcard/', {
    params: {
      ...params,
      export: 1,
    },
    responseType: 'blob', // 指定响应类型为 blob
  });
};

// 分池订单列表项接口
export interface PoolOrderItem {
  pool_group_id: number;
  pool_group__name: string;
  recharge_amount: string;
  actual_amount: string;
  before_amount: string;
  after_amount: string;
  pay_method: number;
  odd_numbers: null | string;
  pay_numbers: null | string;
  remarks: string;
  status: number;
  creation_time: string;
  update_time: string;
  pool_group__user_id: number;
  pool_group__user__user_name: null | string;
  pool_group__user__user_account: string;
}

// 分池订单列表响应接口
export interface PoolOrderResponse {
  code: number;
  msg: string;
  data: {
    rows: PoolOrderItem[];
    total: number;
  };
}

// 分池订单列表参数接口
export interface PoolOrderListParams {
  page: number;
  pageSize: number;
  pool_group_id?: number;
  odd_numbers?: string;
  pay_numbers?: string;
  remarks?: string;
  pool_group__user_id?: number;
}

// 获取分池订单列表
export const getPoolOrderList = (params: PoolOrderListParams) => {
  return requestClient.get<PoolOrderResponse>(
    '/py/poolgroup/poolgrouprechargeorder/',
    { params },
  );
};

// 卡变更记录列表项接口
export interface CardChangeItem {
  card_id: number;
  card__iccid_number: string;
  card__void_number: string;
  status: number;
  user_id: number;
  remarks?: string;
  user__user_account: string;
  user__user_name: null | string;
  creation_time: string;
}

// 卡变更记录列表响应接口
export interface CardChangeResponse {
  code: number;
  msg: string;
  data: {
    rows: CardChangeItem[];
    total: number;
  };
}

// 卡变更记录列表参数接口
export interface CardChangeListParams {
  page: number;
  pageSize: number;
  card__iccid_number?: string;
  card__void_number?: string;
  user_id?: number;
}

// 获取卡变更记录列表
export const getCardChangeList = (params: CardChangeListParams) => {
  return requestClient.get<CardChangeResponse>(
    '/py/poolgroup/cardchangerecord/',
    { params },
  );
};

// 余额变更记录列表项接口
export interface BalanceLogItem {
  pool_group_id: number;
  pool_group__name: string;
  change_type: number;
  before_change: string;
  change_amount: string;
  after_change: string;
  change_reason: string;
  order_id: null | number;
  order__odd_numbers: null | string;
  order__pay_numbers: null | string;
  creation_time: string;
}

// 余额变更记录列表响应接口
export interface BalanceLogResponse {
  code: number;
  msg: string;
  data: {
    rows: BalanceLogItem[];
    total: number;
  };
}

// 余额变更记录列表参数接口
export interface BalanceLogListParams {
  page: number;
  pageSize: number;
  pool_group_id?: number;
  change_type?: number;
  order__odd_numbers?: string;
  order__pay_numbers?: string;
}

// 获取余额变更记录列表
export const getBalanceLogList = (params: BalanceLogListParams) => {
  return requestClient.get<BalanceLogResponse>(
    '/py/poolgroup/poolgroupbalancedetails/',
    { params },
  );
};

// 池轮询日志列表项接口
export interface PoolPollingItem {
  card_id: number;
  card__iccid_number: string;
  p_type: number;
  op_type: number;
  status_msg: string;
  creation_time: string;
}

// 池轮询日志列表响应接口
export interface PoolPollingResponse {
  code: number;
  msg: string;
  data: {
    rows: PoolPollingItem[];
    total: number;
  };
}

// 池轮询日志列表参数接口
export interface PoolPollingListParams {
  page: number;
  pageSize: number;
  card__iccid_number?: string;
  p_type?: number;
  op_type?: number;
  status_msg?: string;
}

// 获取池轮询日志列表
export const getPoolPollingList = (params: PoolPollingListParams) => {
  return requestClient.get<PoolPollingResponse>(
    '/py/poolgroup/poolgrouppolling/',
    { params },
  );
};

// 账单明细列表项接口
export interface BillDetailItem {
  id: number;
  pool_group_id: number;
  pool_group__name: string;
  year_month: string;
  total_flow: string;
  total_price: string;
  status: number;
  creation_time: string;
}

// 账单明细列表响应接口
export interface BillDetailResponse {
  code: number;
  msg: string;
  data: {
    rows: BillDetailItem[];
    total: number;
  };
}

// 账单明细列表参数接口
export interface BillDetailListParams {
  page: number;
  pageSize: number;
  pool_group__name?: string;
  year_month?: string;
  status?: number;
}

// 获取账单明细列表
export const getBillDetailList = (params: BillDetailListParams) => {
  return requestClient.get<BillDetailResponse>(
    '/py/backend/backstage/flowcalculation/',
    { params },
  );
};

// 导入账单参数接口
export interface ImportBillParams {
  name: string;
  file: File;
  cost: string;
  remarks?: string;
}

// 导入账单响应接口
export interface ImportBillResponse {
  code: number;
  msg: string;
}

// 导入账单 API
export const importBill = (params: ImportBillParams) => {
  const formData = new FormData();
  formData.append('name', params.name);
  formData.append('file', params.file);
  formData.append('cost', params.cost);
  if (params.remarks) {
    formData.append('remarks', params.remarks);
  }

  return requestClient.post<ImportBillResponse>(
    '/py/backend/backstage/flowcalculation/',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};

// /py/poolgroup/poolgroupcard/
// 请求方法:
// PUT
// 请求参数: card_id: 31706
// op_type: 1

export const updatePoolCard = (params: {
  card_id: number;
  op_type: 1 | 2 | 3; // 操作类型 1更新 2 停机 3复机
}) => {
  return requestClient.put('/py/poolgroup/poolgroupcard/', params);
};
