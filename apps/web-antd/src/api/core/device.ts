import type { BaseResponse } from '#/api/request';

import { requestClient } from '#/api/request';

export interface DeviceCard {
  id: number;
  apiName: string;
  iccidNumber: string;
  msisdnNumber: string;
  voidNumber: string;
  nameStatus: number;
  upperStatus: string;
  upperStatusMsg: string;
  status: number;
  statusMsg: string;
  totalFlow: number;
  usedFlow: number;
  residualFlow: number;
  configId: number;
  creationTime: string;
  updateTime: string;
  activationDatetime: null | string;
  network: null | string;
  vTotalFlow: number;
  vUsedFlow: number;
  vResidualFlow: number;
}

export interface DeviceCardListParams {
  page: number;
  pageSize: number;
  voidNumber?: string;
}

export interface DeviceCardListResponse {
  code: number;
  msg: string;
  data: {
    rows: DeviceCard[];
    total: number;
  };
  orderNo: null;
}

export const getDeviceCardList = (params: DeviceCardListParams) =>
  requestClient.get<DeviceCardListResponse>(
    '/java/admin/deviceCard/manage/getCardList',
    {
      params,
    },
  );

export interface CalculateImportCardParams {
  voidNumber: string;
  msisdnNumber: string;
  iccIdNumber: string;
  num: string;
  configId: string;
}

export const calculateImportCard = (params: CalculateImportCardParams) => {
  const formData = new FormData();
  formData.append('voidNumber', params.voidNumber);
  formData.append('msisdnNumber', params.msisdnNumber);
  formData.append('iccIdNumber', params.iccIdNumber);
  formData.append('num', params.num);
  formData.append('configId', params.configId);

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/deviceCard/manage/calculateImportCard', formData);
};

export interface ImportCardResponse {
  code: number;
  msg: string;
  data: null;
  orderNo: null;
}

export const importInsertCard = (file: File, configId: string) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('configId', configId);

  return requestClient.post<ImportCardResponse>(
    '/java/admin/deviceCard/manage/importInsertCard',
    formData,
  );
};

// 删除卡
export const deleteCardByIds = (ids: (number | string)[]) => {
  return requestClient.delete(
    `/java/admin/deviceCard/manage/deleteCardByIds/${ids.join(',')}`,
  );
};

// 按通道删除卡片
export const deleteCardByConfigId = (configId: string) => {
  const formData = new FormData();
  formData.append('configId', configId);
  return requestClient.delete(
    `/java/admin/deviceCard/manage/deleteCardByConfigId`,
    {
      data: formData,
    },
  );
};

// 导入修改卡片通道 /java/admin/deviceCard/manage/importUpdateCardConfig put  configId file
export const importUpdateCardConfig = (configId: string, file: File) => {
  const formData = new FormData();
  formData.append('configId', configId);
  formData.append('file', file);
  return requestClient.put(
    '/java/admin/deviceCard/manage/importUpdateCardConfig',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};

// 更新卡片
export const updateCardInfo = (cardId: number | string) => {
  const formData = new FormData();
  formData.append('cardId', String(cardId));
  return requestClient.post(
    '/java/admin/deviceCard/manage/updateCardInfo',
    formData,
  );
};

// 获取获取通道选项
export const getDeviceChannelOptions = () => {
  return requestClient.get('/java/admin/apiConfigDevice/manage/getOption');
};

/**
 * 设备请求参数
 */
export interface DeviceListParams {
  page: number;
  pageSize: number;
  deviceNo: null | string;
  imeiNo: null | string;
  notes: null | string;
  phone: null | string;
  devicePackageGroupIds: null | string;
  deviceConfigIds: null | string;
  userIds: null | string;
  currentNetwork: null | string;
  powerOnStatus: null | string;
  presence: null | string;
  status: null | string;
  nameStatus: null | string;
  restartTimeBegin: null | string;
  restartTimeEnd: null | string;
  reseTimeBegin: null | string;
  reseTimeEnd: null | string;
  recentHeartbeatBegin: null | string;
  recentHeartbeatEnd: null | string;
  creationTimeBegin: null | string;
  creationTimeEnd: null | string;
  updateTimeBegin: null | string;
  updateTimeEnd: null | string;
  activationDatetimeBegin: null | string;
  activationDatetimeEnd: null | string;
}

// 设备列表
export const getDeviceList = (params: DeviceListParams) => {
  return requestClient.get('/java/admin/device/getDeviceList', {
    params,
  });
};

// 导出设备 /java/admin/device/exportDevice
export const exportDevice = (params: DeviceListParams) => {
  return requestClient.get('/java/admin/device/exportDevice', {
    params,
  });
};

/**
 * 设备通道请求参数
 */
export interface DeviceChannelListParams {
  page: number;
  pageSize: number;
  // apiName: string;
  // apiPlatform: string;
  // apiType: string;
  // userId: string;
}

// 设备通道列表
export const getDeviceChannelList = (params: DeviceChannelListParams) => {
  return requestClient.get(
    '/java/admin/apiConfigDevice/manage/getApiConfigDeviceList',
    {
      params,
    },
  );
};

export interface DeviceChannel {
  apiName: string; // 通道名称
  apiAccount: string; // API 账户
  apiPassword: string; // API 密码
  apiKey?: string; // API 密钥1 (可选)
  apiKey2?: string; // API 密钥2 (可选)
  requestAddress: string; // 请求地址
  apiType: number; // API 类型
  apiCardType: number; // API 卡片类型
  apiSources: number; // API 来源
  apiPlatform: string; // API 平台
  apiVersion?: string; // API 版本 (可选)
  stopType: number; // 停止类型
  apiPolling: number; // API 拉取
  singleCardSleep: number; // 单卡休眠时间
  apiOrderSubmission: number; // API 订单提交
  notes?: string; // 备注 (可选)
  pollingCardType: number; // 拉取卡片类型
  rePolling: number; // 重新拉取
  orderMachine: number; // 订单机器
}

// 添加通道
export const addDeviceChannel = (params: DeviceChannel) => {
  return requestClient.post(
    '/java/admin/apiConfigDevice/manage/insertApiConfigDevice',
    params,
  );
};

// 验证设备通道 /java/admin/apiConfigDevice/manage/getVerifyDock
export const getVerifyDock = (params: {
  configId: number | string;
  deviceNo: number | string;
}) => {
  const formData = new FormData();
  formData.append('configId', String(params.configId));
  formData.append('deviceNo', String(params.deviceNo));
  return requestClient.post(
    '/java/admin/apiConfigDevice/manage/getVerifyDock',
    formData,
  );
};

// {
// 	"code": 1,
// 	"msg": "成功",
// 	"data": [
// 		{
// 			"id": "NotDock",
// 			"name": "暂不对接",
// 			"type": 2,
// 			"prompt": "无示例，必填项随意填写"
// 		},
// 	],
// 	"orderNo": null
// }

export interface PlatformDevice {
  id: string;
  name: string;
  type: number;
  prompt: string;
}

export interface PlatformDeviceResponse extends BaseResponse {
  data: PlatformDevice[];
}

// 对接平台选项 /java/admin/ApiVersion/manage/getPlatformDevice
export const getPlatformDevice = () => {
  return requestClient.get<PlatformDeviceResponse>(
    '/java/admin/ApiVersion/manage/getPlatformDevice',
  );
};

// 删除设备通道 /java/admin/apiConfigDevice/manage/deleteApiConfigDevice/2
export const deleteDeviceChannel = (configId: number | string) => {
  return requestClient.delete(
    `/java/admin/apiConfigDevice/manage/deleteApiConfigDevice/${configId}`,
  );
};

// 修改通道 /java/admin/apiConfigDevice/manage/editApiConfigDevice
export const editApiConfigDevice = (id: number, params: DeviceChannel) => {
  return requestClient.put(
    '/java/admin/apiConfigDevice/manage/editApiConfigDevice',
    {
      ...params,
      id,
    },
  );
};

// 获取设备规则 /java/admin/devicePackageGroup/manage/getDevicePackageGroupList
export const getDeviceRule = () => {
  return requestClient.get(
    '/java/admin/devicePackageGroup/manage/getDevicePackageGroupList',
  );
};

// 设备实名 /java/admin/realName/manage/getOption
export const getDeviceRealNameOption = () => {
  return requestClient.get('/java/admin/realName/manage/getOption');
};

export interface DeviceRule {
  name: string; // 规则名称
  group_img: string; // 规则主图路径
  msg: string; // 激活描述
  channel_id: number; // 厂家通道
  device_type: number; // 设备类型 1单网 2双网 3三网 4四网
  card_type: number; // 卡片类型
  support_5g: number; // 支持5g 1不支持 2支持
  network_switch: number; // 网络切换 1不支持 2支持 3虚假 4内置卡切换
  master_card: number; // 默认主卡 卡槽1 卡槽2 卡槽3 卡槽4
  power_display: number; // 电量显示 1不支持 2支持
  signaling: number; // 信号显示 1不支持 2支持
  wifi_name: number; // 名称修改 1不支持 2支持
  wifi_pwd: number; // 密码修改 1不支持 2支持
  wifi_status: number; // 隐藏状态 1不支持 2支持
  restore_factory: number; // 重置设备 1不支持 2支持
  restart: number; // 重启设备 1不支持 2支持
  clear_cache: number; // 清除缓存 1不支持 2支持
  next_rpt_time: number; // 上报时间秒
  device_address: string; // 设备地址
  tutorial_address: string; // 教程地址
  traffic_sharing: number; // 流量共享 1不共享 2共享
  card_slot_1: number; // 卡槽网络1 1中国电信 2中国联通 3中国移动 4中国广电
  card_slot_2?: number; // 卡槽网络2
  card_slot_3?: number; // 卡槽网络3
  card_slot_4?: number; // 卡槽网络4
  real_name_1_id: number; // 实名规则1
  real_name_2_id?: number; // 实名规则2
  real_name_3_id?: number; // 实名规则3
  real_name_4_id?: number; // 实名规则4
  status: number; // 规则状态 1下架 2上架
  in_advance_stop: number; // 提前停机
  speedLimitRules: number; // 限速规则 1卡片限速 2设备限速 必填默认卡片限速
  maximum_dosage1: number; // 自然月限制用量卡1(mb)
  maximum_dosage2: number; // 自然月限制用量卡2(mb)
  maximum_dosage3: number; // 自然月限制用量卡3(mb)
  maximum_dosage4: number; // 自然月限制用量卡4(mb)
  reconstruction_rules: number; // 复机规则 1仅主卡  2所有卡
}

// 添加设备规则
export const addDeviceRule = (params: DeviceRule) => {
  return requestClient.post(
    '/java/admin/devicePackageGroup/manage/insertDevicePackageGroup',
    params,
  );
};

// 获取设备规则列表
export interface DeviceRuleListParams {
  page: number;
  pageSize: number;
  name?: string;
}

export interface DeviceRuleListResponse {
  code: number;
  msg: string;
  data: {
    rows: DeviceRule[];
    total: number;
  };
  orderNo: null;
}

export const getDeviceRuleList = (params: DeviceRuleListParams) => {
  return requestClient.get<DeviceRuleListResponse>(
    '/java/admin/devicePackageGroup/manage/getDevicePackageGroupList',
    { params },
  );
};

// 修改设备规则
export const updateDeviceRule = (params: DeviceRule) => {
  return requestClient.put(
    '/java/admin/devicePackageGroup/manage/editDevicePackageGroup',
    params,
  );
};

// 删除设备规则
export const deleteDeviceRule = (id: number | string) => {
  return requestClient.delete(
    `/java/admin/devicePackageGroup/manage/deleteDevicePackageGroup/${id}`,
  );
};

// 实名规则列表参数
export interface RealNameListParams {
  page: number;
  pageSize: number;
  name?: string;
}

// 实名规则数据结构
export interface RealName {
  id: number;
  name: string;
  realNameRules: number;
  realNameLike: number;
  likeAddress: null | string;
  requestAddress: null | string;
  apiAccount: null | string;
  apiPwd: null | string;
  apiKey: null | string;
  creationTime: string;
  updateTime: string;
}

// 实名规则列表响应
export interface RealNameListResponse {
  code: number;
  msg: string;
  data: {
    rows: RealName[];
    total: number;
  };
  orderNo: null;
}

// 获取实名规则列表
export const getRealNameList = (params: RealNameListParams) => {
  return requestClient.get<RealNameListResponse>(
    '/java/admin/realName/manage/getList',
    { params },
  );
};

// 添加实名规则
export const addRealName = (params: RealName) => {
  return requestClient.post(
    '/java/admin/realName/manage/insertRealName',
    params,
  );
};

// 修改实名规则
export const updateRealName = (params: RealName) => {
  return requestClient.put('/java/admin/realName/manage/editRealName', params);
};

// 删除实名规则
export const deleteRealName = (id: number | string) => {
  return requestClient.delete(
    `/java/admin/realName/manage/deleteRealName/${id}`,
  );
};

// 更新设备信息
export const updateDeviceInfo = (deviceId: number | string) => {
  const formData = new FormData();
  formData.append('deviceId', String(deviceId));
  return requestClient.post('/java/admin/device/updateDeviceData', formData);
};

// 修改设备WIFI密码
export interface UpdateWifiParams {
  deviceId: number | string;
  type: '1' | '2'; // 1:4g 2:5g
  wifiName: string;
  wifiPwd: string;
}

export const updateDeviceWifi = (params: UpdateWifiParams) => {
  const formData = new FormData();
  formData.append('deviceId', String(params.deviceId));
  formData.append('type', params.type);
  formData.append('wifiName', params.wifiName);
  formData.append('wifiPwd', params.wifiPwd);
  return requestClient.put('/java/admin/device/updateDeviceInfo', formData);
};

// 修改WIFI隐藏状态
export interface UpdateWifiHideParams {
  deviceId: number | string;
  type: '1' | '2'; // 1:4g 2:5g
  hide: '1' | '2'; // 1:隐藏 2:显示
}

export const updateWifiHide = (params: UpdateWifiHideParams) => {
  const formData = new FormData();
  formData.append('deviceId', String(params.deviceId));
  formData.append('type', params.type);
  formData.append('hide', params.hide);
  return requestClient.put('/java/admin/device/updateWifiHide', formData);
};

// 远程控制
export interface DeviceControlParams {
  deviceId: number | string;
  type: '1' | '2' | '3'; // 1:关机 2:重启 3:出厂设置
}

export const deviceControl = (params: DeviceControlParams) => {
  const formData = new FormData();
  formData.append('deviceId', String(params.deviceId));
  formData.append('type', params.type);
  return requestClient.put('/java/admin/device/deviceControl', formData);
};

// 切换网络
export interface ChangeNetworkParams {
  deviceId: number | string;
  switching: '1' | '2' | '3' | '4'; // 1中国电信 2中国联通 3中国移动 4中国广电
}

export const changeNetwork = (params: ChangeNetworkParams) => {
  const formData = new FormData();
  formData.append('deviceId', String(params.deviceId));
  formData.append('switching', params.switching);
  return requestClient.put('/java/admin/device/changeNet', formData);
};

// 切换主卡
export interface UpdateMasterCardParams {
  deviceId: number | string;
  num: '1' | '2' | '3' | '4'; // 卡槽数 1-4
}

export const updateMasterCard = (params: UpdateMasterCardParams) => {
  const formData = new FormData();
  formData.append('deviceId', String(params.deviceId));
  formData.append('num', params.num);
  return requestClient.put(
    '/java/admin/device/manage/updateMasterCard',
    formData,
  );
};

// 删除设备
export const deleteDevice = (deviceId: number | string) => {
  return requestClient.delete(
    `/java/admin/device/manage/deleteDevice/${deviceId}`,
  );
};

// 设备导入文档下载接口 /java/admin/exportModel/ExportInsertDevice
export const downloadDeviceImportTemplate = () => {
  return requestClient.get('/java/admin/exportModel/ExportInsertDevice', {
    responseType: 'blob',
  });
};

// 下载设备卡号矫正模板
export const downloadDeviceCorrectTemplate = () => {
  return requestClient.get('/java/admin/exportModel/ExportEditDeviceNo', {
    responseType: 'blob',
  });
};

// 批量矫正设备卡号
export const correctDeviceCardByImport = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  return requestClient.post(
    '/java/admin/device/manage/importEditDeviceNo',
    formData,
  );
};

// 导入接口 /java/admin/device/manage/importDevice
export const importDeviceApi = (params: FormData) => {
  return requestClient.post('/java/admin/device/manage/importDevice', params);
};

// 获取设备充值套餐 /java/admin/devicePack/getDevicePackageList?page=1&pageSize=10000
export const getDevicePackageList = () => {
  return requestClient.get('/java/admin/devicePack/getDevicePackageList', {
    params: {
      page: 1,
      pageSize: 10_000,
    },
  });
};

// 获取套餐列表 /java/admin/devicePack/getPackageListPay?packType=1&classId=&popular=&deviceId=
export const getPackageListPay = (params: {
  classId?: number; // 套餐分类(非必传)
  deviceId: number; // 设备id
  packType: number; // 套餐类型 1.基础套餐 2.加油包 3.加速宝 4.体验包 5.短信包 6.语音包
  popular?: number; // 是否热门(非必传)
}) => {
  return requestClient.get('/java/admin/devicePack/getPackageListPay', {
    params,
  });
};

// 设备批量订购接口 /java/admin/devicePackageOrder/lineBreakRecharge
export const batchOrderDevice = (params: {
  deviceNos: null | string;
  packageId: null | number | string;
  takeeffectType: null | number | string;
}) => {
  const from = new FormData();
  from.append('deviceNos', params.deviceNos);
  from.append('packageId', String(params.packageId));
  from.append('takeeffectType', String(params.takeeffectType));
  return requestClient.post(
    '/java/admin/devicePackageOrder/lineBreakRecharge',
    from,
  );
};

// 下载设备分配模版 /java/admin/exportModel/ExportDeviceFp
export const downloadDeviceAssignTemplate = () => {
  return requestClient.get('/java/admin/exportModel/ExportDeviceFp', {
    responseType: 'blob',
  });
};

// 导入分配设备 /java/admin/device/importFpDevice
/**
 *
 * @param params
 * @param params.file 文件
 * @param params.userId 指定账户id
 * @returns
 */
export const importDeviceAssign = (params: FormData) => {
  return requestClient.post('/java/admin/device/importFpDevice', params);
};

// 连号分配设备 /java/admin/device/linkFpDevice
/**
 *
 * @param params
 * @param params.startDeviceNo 起始号码
 * @param params.endDeviceNo 结尾号码
 * @param params.userId 指定账户id
 * @returns
 */
export const linkDeviceAssign = (params: FormData) => {
  return requestClient.post('/java/admin/device/linkFpDevice', params);
};

// 分隔符分配设备 /java/admin/device/separatorFpDevice
/**
 *
 * @param params
 * @param params.cardNos 起始号码
 * @param params.userId 指定账户id
 * @returns
 */
export const separatorDeviceAssign = (params: FormData) => {
  return requestClient.post('/java/admin/device/separatorFpDevice', params);
};

// 导入回收设备 /java/admin/device/importRecycleDevice
/**
 *
 * @param params
 * @param params.file 文件
 * @returns
 */
export const importRecycleDevice = (params: FormData) => {
  return requestClient.put('/java/admin/device/importRecycleDevice', params);
};

// 分隔符回收设备 /java/admin/device/separatorRecycleDevice
/**
 *
 * @param params
 * @param params.cardNos 123,456,789
 * @returns
 */
export const separatorRecycleDevice = (params: FormData) => {
  return requestClient.put('/java/admin/device/separatorRecycleDevice', params);
};

// 连号回收设备 /java/admin/device/recycleDevice
/**
 *
 * @param params
 * @param params.startDeviceNo 起始号码
 * @param params.endDeviceNo 结尾号码
 * @returns
 */
export const linkRecycleDevice = (params: FormData) => {
  return requestClient.put('/java/admin/device/recycleDevice', params);
};

// 设备基本信息 java/admin/device/getDeviceInfo?deviceNo=88888888
export const getDeviceInfo = (deviceNo: string) => {
  return requestClient.get('/java/admin/device/getDeviceInfo', {
    params: {
      deviceNo,
    },
  });
};

// 限制设备多卡 /java/admin/device/manage/limitMultipleCards
export const limitMultipleCards = (deviceId: string, type: 1 | 2) => {
  const formData = new FormData();
  formData.append('deviceId', deviceId);
  formData.append('type', type.toString());
  return requestClient.put(
    '/java/admin/device/manage/limitMultipleCards',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};

// 单卡套餐记录 /java/admin/deviceCardPackage/getDevicePackageList
export const getDeviceCardPackageList = (params: {
  deviceId: string;
  page: number;
  pageSize: number;
}) => {
  return requestClient.get(
    '/java/admin/deviceCardPackage/getDevicePackageList',
    {
      params,
    },
  );
};

// 设备复机接口参数
export interface DeviceRestartParams {
  deviceId: number | string;
  type: '1' | '2'; // 复机类型 1复机所有 2复机主卡
}

// 设备复机接口
export function deviceRestartApi(params: DeviceRestartParams) {
  const formData = new FormData();
  formData.append('deviceId', params.deviceId.toString());
  formData.append('type', params.type);

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/device/deviceReMachine', formData);
}

// 设备停机接口参数
export interface DeviceStopParams {
  deviceId: number | string;
}

// 设备停机接口
export function deviceStopApi(params: DeviceStopParams) {
  const formData = new FormData();
  formData.append('deviceId', params.deviceId.toString());

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/device/manage/deviceStop', formData);
}

// 修改余额接口参数
export interface UpdateDeviceBalanceParams {
  deviceId: number | string;
  type: '1' | '2'; // 1加 2扣
  balance: number | string;
  msg?: string; // 备注(可选)
}

// 修改余额接口
export function updateDeviceBalanceApi(params: UpdateDeviceBalanceParams) {
  const formData = new FormData();
  formData.append('deviceId', params.deviceId.toString());
  formData.append('type', params.type);
  formData.append('balance', params.balance.toString());
  if (params.msg) {
    formData.append('msg', params.msg);
  }

  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/device/manage/updateBalance', formData);
}

// 设备转移接口参数
export interface DeviceTransferParams {
  oidDeviceNo: string;
  newDeviceNo: string;
}

// 设备转移接口
export function deviceTransferApi(params: DeviceTransferParams) {
  const formData = new FormData();
  formData.append('oidDeviceNo', params.oidDeviceNo);
  formData.append('newDeviceNo', params.newDeviceNo);

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/device/manage/deviceTransfer', formData);
}

// 会员模板列表项
export interface MemberTemplateItem {
  id: number;
  limitName: string; // 模板名称
  noBookingReminder: number; // 状态
  reminderPoster: null | string; // 提醒海报
  creationTime: string; // 创建时间
}

// 会员模板列表请求参数
export interface MemberTemplateListParams {
  page: number;
  pageSize: number;
  limitName?: string; // 模板名称(可选)
}

// 会员模板列表接口
export function getMemberTemplateList(params: MemberTemplateListParams) {
  return requestClient.get<{
    code: number;
    data: {
      rows: MemberTemplateItem[];
      total: number;
    };
    msg: string;
    orderNo: null;
  }>('/java/admin/membershipLimit/manage/getList', {
    params,
  });
}

// 会员模板编辑参数
export interface MemberTemplateEditParams {
  id?: number;
  limitName: string;
  noBookingReminder: 1 | 2;
  reminderPoster?: string;
}

// 会员模板编辑接口
export function editMemberTemplate(params: MemberTemplateEditParams) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
  }>('/java/admin/membershipLimit/manage/editLimit', params);
}

// 会员规则项
export interface MemberRuleItem {
  id: number;
  limitId: number;
  vipName: string;
  vipPrice: number;
  isShow: 1 | 2;
  isDefault: 1 | 2;
  sort: number;
  speedLimitId: null | number;
  vipImg: string;
}

// 会员规则列表请求参数
export interface MemberRuleListParams {
  page: number;
  pageSize: number;
  limitId: number;
}

// 会员规则列表接口
export function getMemberRuleList(params: MemberRuleListParams) {
  return requestClient.get<{
    code: number;
    data: {
      rows: MemberRuleItem[];
      total: number;
    };
    msg: string;
    orderNo: null;
  }>('/java/admin/membershipRules/manage/getList', {
    params,
  });
}

// 限速模板选项
export interface SpeedLimitOption {
  id: number;
  name: string;
}

// // 获取限速模板选项
// export function getSpeedLimitOptions() {
//   return requestClient.get<{
//     code: number;
//     data: SpeedLimitOption[];
//     msg: string;
//     orderNo: null;
//   }>('/java/admin/speedLimit/manage/SpeedLimitOption');
// }

// 会员规则添加参数
export interface MemberRuleAddParams {
  limitId: number;
  vipName: string;
  vipPrice: number;
  isShow: 1 | 2;
  isDefault: 1 | 2;
  sort: number;
  speedLimitId: number;
  vipImg: string;
}

// 添加会员规则
export function addMemberRule(params: MemberRuleAddParams) {
  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/membershipRules/manage/addRules', params);
}

// 修改会员规则 /java/admin/membershipRules/manage/editRules
export function editMemberRule(params: MemberRuleAddParams) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/membershipRules/manage/editRules', params);
}

// 删除会员规则 /java/admin/membershipRules/manage/deleteRules/
export function deleteMemberRule(id: number | string) {
  return requestClient.delete<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>(`/java/admin/membershipRules/manage/deleteRules/${id}`);
}
/**
 * 删除会员模板
 */
export function deleteMemberTemplate(id: number | string) {
  return requestClient.delete<ApiResponse<null>>(
    `/java/admin/membershipLimit/manage/deleteLimit/${id}`,
  );
}

/**
 * 创建会员模板
 */
export function createMemberTemplate(params: MemberTemplateEditParams) {
  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
  }>('/java/admin/membershipLimit/manage/addLimit', params);
}

// 计费组列表项
export interface BillingGroupItem {
  id: number;
  seriesName: string;
  creationTime: string;
}

// 计费组列表请求参数
export interface BillingGroupListParams {
  page: number;
  pageSize: number;
  seriesName?: string; // 计费组名称(可选)
}

// 计费组列表接口
export function getBillingGroupList(params: BillingGroupListParams) {
  return requestClient.get<{
    code: number;
    data: {
      rows: BillingGroupItem[];
      total: number;
    };
    msg: string;
    orderNo: null;
  }>('/java/admin/packSeries/manage/getList', {
    params,
  });
}

// 分隔符分配计费组参数接口
export interface AssignSeparatorBillingGroupParams {
  cardNos: string; // 设备号码，多个用逗号分隔
  seriesId: number; // 计费组ID
}

// 分隔符分配计费组 cardNos seriesId form data
export function assignSeparatorBillingGroup(
  params: AssignSeparatorBillingGroupParams,
) {
  const formData = new FormData();
  formData.append('cardNos', params.cardNos);
  formData.append('seriesId', params.seriesId.toString());

  return requestClient.post(
    '/java/admin/device/manage/separatorFpSeries',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

// 导入分配计费组参数接口
export interface ImportSeparatorBillingGroupParams {
  file: File; // 导入文件
  seriesId: number; // 计费组ID
}

// 导入分配计费组 file seriesId
export function importSeparatorBillingGroup(
  params: ImportSeparatorBillingGroupParams,
) {
  const formData = new FormData();
  formData.append('file', params.file);
  formData.append('seriesId', params.seriesId.toString());

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/device/manage/importFpSeries', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 连号分配计费组参数接口
export interface AssignSequentialBillingGroupParams {
  startDeviceNo: string; // 开始设备号
  endDeviceNo: string; // 结束设备号
  seriesId: number; // 计费组ID
}

// 连号分配计费组 startDeviceNo endDeviceNo seriesId
export function assignSequentialBillingGroup(
  params: AssignSequentialBillingGroupParams,
) {
  const formData = new FormData();
  formData.append('startDeviceNo', params.startDeviceNo);
  formData.append('endDeviceNo', params.endDeviceNo);
  formData.append('seriesId', params.seriesId.toString());

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/device/manage/linkFpSeries', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 计费组选项接口
export interface BillingGroupOption {
  id: number;
  name: string;
}

// // 获取计费组选项
// export function getBillingGroupOptions() {
//   return requestClient.get<{
//     code: number;
//     data: BillingGroupOption[];
//     msg: string;
//     orderNo: null;
//   }>('/java/admin/packSeries/manage/getOption');
// }

// 计费组编辑参数
export interface BillingGroupEditParams {
  id?: number;
  seriesName: string;
}

// 创建计费组接口
export function createBillingGroup(params: BillingGroupEditParams) {
  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/packSeries/manage/insertSeries', params);
}

// 编辑计费组接口
export function editBillingGroup(params: BillingGroupEditParams) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/packSeries/manage/editSeries', params);
}

// 删除计费组接口
export function deleteBillingGroup(id: number | string) {
  return requestClient.delete<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>(`/java/admin/packSeries/manage/deleteSeries/${id}`);
}

// 活码配置列表项
export interface QrcodeConfigItem {
  id: number;
  primary_domain_name: string; // 主域名
  points_domain_name: string; // 指向域名
  remarks: null | string; // 备注
  creation_time: string; // 创建时间
  update_time: string; // 更新时间
}

// 活码配置列表请求参数
export interface QrcodeConfigListParams {
  page: number;
  pageSize: number;
  primary_domain_name?: string; // 主域名(可选)
  points_domain_name?: string; // 指向域名(可选)
  remarks?: string; // 备注(可选)
}

// 活码配置列表接口
export function getQrcodeConfigList(params: QrcodeConfigListParams) {
  return requestClient.get<{
    code: number;
    data: {
      rows: QrcodeConfigItem[];
      total: number;
    };
    msg: string;
  }>('/py/backend/backstage/livecodeconfiguration/', {
    params,
  });
}

// 活码配置编辑参数
export interface QrcodeConfigEditParams {
  id?: number;
  primary_domain_name: string;
  points_domain_name: string;
  remarks?: string;
}

// 创建活码配置接口
export function createQrcodeConfig(params: QrcodeConfigEditParams) {
  const formData = new FormData();
  formData.append('primary_domain_name', params.primary_domain_name);
  formData.append('points_domain_name', params.points_domain_name);
  if (params.remarks) {
    formData.append('remarks', params.remarks);
  }

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
  }>('/py/backend/backstage/livecodeconfiguration/', formData);
}

// 编辑活码配置接口
export function editQrcodeConfig(params: QrcodeConfigEditParams) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
  }>('/py/backend/backstage/livecodeconfiguration/', {
    ...params,
  });
}

// 删除活码配置接口
export function deleteQrcodeConfig(id: number | string) {
  return requestClient.delete<{
    code: number;
    data: null;
    msg: string;
  }>(`/py/backend/backstage/livecodeconfiguration/?id=${id}`);
}

// 导出活码配置列表
export function exportQrcodeConfig(params: QrcodeConfigListParams) {
  return requestClient.get('/py/backend/backstage/livecodeconfiguration/', {
    params: {
      ...params,
      export: 1,
    },
    responseType: 'blob',
  });
}

// 活码列表项
export interface QrcodeListItem {
  ac_id: number;
  device_id: number;
  jump_type: 1 | 2; // 1系统充值 2第三方链接
  button_content: string;
  url_path: null | string;
  remarks: null | string;
  path_link: string;
  creation_time: string;
  update_time: string;
  device__device_no: string;
  id: number;
}

// 活码列表请求参数
export interface QrcodeListParams {
  page: number;
  pageSize: number;
  device__device_no?: string; // 设备号(可选)
  remarks?: string; // 备注(可选)
  jump_type?: 1 | 2; // 跳转类型(可选)
}

// 活码列表接口
export function getQrcodeList(params: QrcodeListParams) {
  return requestClient.get<{
    code: number;
    data: {
      rows: QrcodeListItem[];
      total: number;
    };
    msg: string;
  }>('/py/backend/backstage/cardactivecode/', {
    params,
  });
}

// 活码新增参数
export interface QrcodeAddParams {
  device__device_no: string;
  ac_id: number;
  jump_type: 1 | 2;
  button_content?: string;
  url_path?: string;
  remarks?: string;
}

// 新增活码接口
export function addQrcode(params: QrcodeAddParams) {
  const formData = new FormData();
  formData.append('device__device_no', params.device__device_no);
  formData.append('ac_id', params.ac_id.toString());
  formData.append('jump_type', params.jump_type.toString());
  if (params.button_content) {
    formData.append('button_content', params.button_content);
  }
  if (params.url_path) {
    formData.append('url_path', params.url_path);
  }
  if (params.remarks) {
    formData.append('remarks', params.remarks);
  }

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
  }>('/py/backend/backstage/cardactivecode/', formData);
}

// 批量新增活码参数
export interface BatchQrcodeAddParams {
  start_device_no: string;
  end_device_no: string;
  ac_id: number;
  jump_type: 1 | 2;
  button_content?: string;
  url_path?: string;
  remarks?: string;
}

// 批量新增活码接口
export function batchAddQrcode(params: BatchQrcodeAddParams) {
  const formData = new FormData();
  formData.append('start_device_no', params.start_device_no);
  formData.append('end_device_no', params.end_device_no);
  formData.append('ac_id', params.ac_id.toString());
  formData.append('jump_type', params.jump_type.toString());
  if (params.button_content) {
    formData.append('button_content', params.button_content);
  }
  if (params.url_path) {
    formData.append('url_path', params.url_path);
  }
  if (params.remarks) {
    formData.append('remarks', params.remarks);
  }

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
  }>('/py/backend/backstage/batchcardactiveCode/', formData);
}

// 导出活码列表
export function exportQrcodeList(params: QrcodeListParams) {
  return requestClient.get('/py/backend/backstage/cardactivecode/', {
    params: {
      ...params,
      export: 1,
    },
    responseType: 'blob',
  });
}

// 获取所有活码配置
export function getAllQrcodeConfig() {
  return requestClient.get<{
    code: number;
    data: {
      rows: QrcodeConfigItem[];
      total: number;
    };
    msg: string;
  }>('/py/backend/backstage/livecodeconfiguration/', {
    params: {
      page: 1,
      pageSize: 100_000,
    },
  });
}

// 编辑活码参数
export interface QrcodeEditParams {
  id: number;
  ac_id: number;
  device_id: number;
  device__device_no: string;
  jump_type: 1 | 2;
  button_content?: string;
  url_path?: string;
  remarks?: string;
  path_link?: string;
  creation_time?: string;
  update_time?: string;
}

// 编辑活码接口
export function editQrcode(params: QrcodeEditParams) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
  }>('/py/backend/backstage/cardactivecode/', params);
}

// 删除活码接口
export function deleteQrcode(id: number | string) {
  return requestClient.delete<{
    code: number;
    data: null;
    msg: string;
  }>(`/py/backend/backstage/cardactivecode/?id=${id}`);
}
