import { requestClient } from '#/api/request';

/** API监控接口 */

/**
 * 获取API调用的基础统计数据
 */
export function getMonitorData() {
  return requestClient.get('/java/monitor/data');
}

/**
 * 获取IP调用详情
 */
export function getIpStats() {
  return requestClient.get('/java/monitor/ip-stats');
}

/**
 * 获取API调用错误的历史记录
 */
export function getErrorHistory() {
  return requestClient.get('/java/monitor/error-history');
}

/**
 * 获取API调用的详细信息
 */
export function getCallDetails() {
  return requestClient.get('/java/monitor/call-details');
}

/** 任务监控接口 */

/**
 * 获取所有定时任务的基本信息
 */
export function getTasks() {
  return requestClient.get('/java/monitor/tasks');
}

/**
 * 获取所有任务的最新执行记录
 */
export function getLatestTaskExecutions() {
  return requestClient.get('/java/monitor/tasks/executions/latest');
}

/**
 * 获取指定任务的执行历史记录
 * @param jobName 任务名称
 * @param groupName 任务组名称
 */
export function getTaskExecutions(jobName: string, groupName: string) {
  return requestClient.get('/java/monitor/tasks/executions', {
    params: {
      jobName,
      groupName,
    },
  });
}

/**
 * 获取任务名称与中文描述的映射
 */
export function getTaskDescriptions() {
  return requestClient.get('/java/monitor/tasks/descriptions');
}

/**
 * 获取任务状态与中文描述的映射
 */
export function getTaskStatusDescriptions() {
  return requestClient.get('/java/monitor/tasks/status-descriptions');
}

/**
 * 获取任务组与中文名称的映射
 */
export function getTaskGroupNames() {
  return requestClient.get('/java/monitor/tasks/group-names');
}

/**
 * 获取Cron表达式与中文解释的映射
 */
export function getCronDescriptions() {
  return requestClient.get('/java/monitor/tasks/cron-descriptions');
}

/**
 * 修正任务状态
 * @param id 任务执行记录ID
 * @param status 新状态
 * @param errorMessage 错误信息
 */
export function updateTaskStatus(
  id: number,
  status: string,
  errorMessage?: string,
) {
  return requestClient.post('/java/monitor/tasks/update-status', {
    params: {
      id,
      status,
      errorMessage,
    },
  });
}

/**
 * 停止正在运行的任务
 * @param id 任务执行记录ID
 */
export function stopTask(id: number) {
  return requestClient.post('/java/monitor/tasks/stop', {
    params: {
      id,
    },
  });
}
