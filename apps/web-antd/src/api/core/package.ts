import type { BaseResponse } from '../model';

import { requestClient } from '#/api/request';

// 套餐分类项
export interface PackageCategory {
  id: number;
  className: string;
  sort: number;
}

// 套餐分类列表请求参数
export interface PackageCategoryListParams {
  page: number;
  pageSize: number;
  className?: string;
}

// 套餐分类列表响应
export interface PackageCategoryListResponse {
  code: number;
  msg: string;
  data: {
    rows: PackageCategory[];
    total: number;
  };
  orderNo: null;
}

// 运营商枚举
export enum OperatorEnum {
  CCTC = 4, // 中国广电
  MOBILE = 3, // 中国移动
  OTHER = 5, // 其他
  TELECOM = 1, // 中国电信
  UNICOM = 2, // 中国联通
}

// 套餐规则项
export interface PackageRule {
  id: number;
  groupName: string;
  groupRemarks: null | string;
  groupOperator: OperatorEnum;
  groupStatus: 1 | 2;
  configId: number;
  apiName: string;
  activationRestrictions: 0 | 1 | 2;
  packCount: number;
  cardCount: number;
  // ... 其他字段
}

// 套餐规则接口
export interface PackageRule {
  id: number;
  packageId: number;
  templateId: number;
  packageName: string;
  packageCost: number;
  packagePrice: number;
  creationTime: string;
  updateTime: string;
  groupId: number;
  groupName: string;
}

// 套餐规则列表请求参数
export interface PackageRuleListParams {
  page: number;
  pageSize: number;
  groupName?: string;
  groupRemarks?: string;
  groupOperator?: OperatorEnum;
  groupStatus?: 1 | 2;
  configId?: number;
  activationRestrictions?: 0 | 1 | 2;
}

// 套餐规则列表响应
export interface PackageRuleListResponse extends BaseResponse {
  data: {
    rows: PackageRule[];
    total: number;
  };
}

// 获取套餐规则列表
export function getPackageRuleList(params: {
  page: number;
  pageSize: number;
  templateId: number;
}) {
  return requestClient.get<PackageRuleListResponse>(
    '/java/admin/packageTemplatePrice/getList',
    { params },
  );
}

// 获取卡片套餐规则 /java/admin/packageGroup/list
export function getCardPackageRuleList(params: {
  // 换卡类型 1强制换卡 2非强制换卡
  changeCard?: string;
  // 通道ID
  configId?: string;
  // 创建开始时间
  creationBegin?: string;
  // 创建结束时间
  creationEnd?: string;

  // 套餐组名称
  groupName?: string;

  // 组运营商 1中国电信 2中国联通 3中国移动 4中国广电
  groupOperator?: string;

  // 备注
  groupRemarks?: string;

  // 组状态 1上架 2下架
  groupStatus?: string;

  // 分页页码
  page: string;

  // 每页记录数
  pageSize: string;

  // 更新开始时间
  updateBegin?: string;

  // 更新结束时间
  updateEnd?: string;
}) {
  return requestClient.get<PackageRuleListResponse>(
    '/java/admin/packageGroup/list',
    { params },
  );
}
// 添加套餐规则组
export interface PackageRuleFormData {
  groupName: string; // 组名称 (必填)
  groupRemarks?: string; // 组备注
  automaticRenewalRules: number; // 自动续费规则 0关闭功能 1强制续费 2选择续费
  renewalThreshold?: number; // 自动续费阈值 (%)
  renewalArrears?: number; // 自动续费可欠费金额
  groupOperator: number; // 组运营商 (必填)
  configId: number; // 通道id (必填)
  bindPhoneNumber: number; // 绑定手机号
  realNameRules: number; // 实名规则 (必填)
  realNameAddressRules: number; // 实名地址规则 (必填)
  realNameAddress?: string; // 自定义实名地址
  realNameRequestAddress?: string; // 对接实名请求域名地址
  realNameRequestAddress2?: string; // 对接实名请求域名地址2
  realNameRequestAccount?: string; // 对接账号
  realNameRequestPassword?: string; // 对接密码
  realNameApiPlatform?: string; // 对接平台标识
  realNameApiVersion?: string; // 平台版本
  realNameRequestKey?: string; // 对接密钥
  realNameRequestKey2?: string; // 对接密钥2
  realNameCardType: number; // api递交卡号
  inAdvanceStop: number; // 提前停机
  realNameMsg?: string; // 实名说明
  activationRestrictions: number; // 限制激活
  notification?: string; // 通知
}

// 获取套餐组详情
export interface PackageGroupDetail {
  id: number;
  groupName: string;
  realNameRules: number;
  automaticRenewalRules: number;
  renewalThreshold: number;
  renewalArrears: number;
  activationRestrictions: number;
  realNameAddressRules: number;
  realNameAddress: null | string;
  realNameApiVersion: string;
  realNameRequestAddress: string;
  realNameRequestAddress2: string;
  realNameRequestAccount: string;
  realNameRequestPassword: string;
  realNameRequestKey: null | string;
  realNameRequestKey2: null | string;
  realNameApiPlatform: string;
  realNameCardType: number;
  groupRemarks: null | string;
  groupOperator: number;
  groupStatus: number;
  bindPhoneNumber: number;
  offSheifNotification: null | string;
  notification: null | string;
  configId: number;
  inAdvanceStop: number;
  realNameMsg: null | string;
  creationTime: string;
  updateTime: string;
  changeCard: number;
  apiName: null | string;
  packCount: null | number;
  cardCount: null | number;
}

// 添加更新接口的类型定义
export interface UpdatePackageGroupParams {
  id: number;
  groupName: string;
  realNameRules: number;
  automaticRenewalRules: number;
  activationRestrictions: number;
  realNameAddressRules: number;
  realNameApiVersion: string;
  realNameRequestAddress: string;
  realNameRequestAddress2: string;
  realNameRequestAccount: string;
  realNameRequestPassword: string;
  realNameRequestKey: null | string;
  realNameRequestKey2: null | string;
  realNameApiPlatform: string;
  realNameCardType: number;
  groupRemarks: null | string;
  groupOperator: number;
  groupStatus: number;
  bindPhoneNumber: number;
  offSheifNotification: null | string;
  notification: null | string;
  configId: number;
  inAdvanceStop: number;
  realNameMsg: null | string;
}

// 卡片套餐规则新增接口
export interface AddPackageGroupParams {
  groupName: string;
  groupRemarks: string;
  groupOperator: number;
  configId: number;
  automaticRenewalRules: number;
  renewalThreshold: number;
  renewalArrears: number;
  activationRestrictions: number;
  realNameAddressRules: number;
  realNameAddress: string;
  realNameRequestAddress: string;
  realNameRequestAddress2: string;
  realNameRequestAccount: string;
  realNameRequestPassword: string;
  realNameRequestKey: string;
  realNameRequestKey2: string;
  realNameCardType: number;
  inAdvanceStop: number;
  realNameMsg: string;
  notification: string;
  bindPhoneNumber: number;
  offSheifNotification: string;
  groupStatus: number;
  changeCard: number;
  apiName: string;
  packCount: number;
  cardCount: number;
  templateId: number;
  groupId: number;
}

// 套餐列表请求参数
export interface PackageListParams {
  page: number;
  pageSize: number;
  packageName?: string;
  speedLimitId?: string;
  voidId?: string;
  cycleId?: string;
  stackingRules?: string;
  orderingRules?: string;
  packageType?: string;
  packageStatus?: string;
  packageValidity?: string;
  creationBegin?: string;
  creationEnd?: string;
  updateBegin?: string;
  updateEnd?: string;
  classId?: string;
}

// 套餐列表项
export interface PackageItem {
  id: number;
  packageName: string;
  groupName: string;
  packageCost: number;
  packagePrice: number;
  stackingRules: number;
  orderingRules: number;
  packageType: number;
  packageStatus: number;
  packageValidity: number;
  validityDays: number;
  automaticRenewal: number;
  defaultRenewal: number;
  packOrder: number;
  systemPackageCode: string;
  packageTotal: number;
  creationTime: string;
  updateTime: string;
}

/**
 * 获取套餐分类列表
 */
export function getPackageCategoryList(params: PackageCategoryListParams) {
  return requestClient.get<PackageCategoryListResponse>(
    '/java/admin/packageClass/manage/getList',
    { params },
  );
}

/**
 * 新增套餐分类
 */
export function addPackageCategory(params: Omit<PackageCategory, 'id'>) {
  return requestClient.post<BaseResponse>(
    '/java/admin/packageClass/manage/insertClass',
    params,
  );
}

/**
 * 编辑套餐分类
 */
export function editPackageCategory(params: PackageCategory) {
  return requestClient.put<BaseResponse>(
    '/java/admin/packageClass/manage/editClass',
    params,
  );
}

/**
 * 删除套餐分类
 */
export function deletePackageCategory(id: number | string) {
  return requestClient.delete<BaseResponse>(
    `/java/admin/packageClass/manage/deleteClass/${id}`,
  );
}

// 新增套餐参数
export interface AddPackageRuleParams {
  templateId: number;
  groupId: number;
  packageName: string;
  packageCost: number;
  packagePrice: number;
}

// 新增套餐
export function addPackageRule(params: AddPackageRuleParams) {
  return requestClient.post<BaseResponse>(
    '/java/admin/packageTemplatePrice/add',
    params,
  );
}

/**
 * 编辑套餐规则
 */
export function editPackageRule(params: PackageRuleFormData & { id: number }) {
  return requestClient.put<BaseResponse>(
    '/java/admin/packageGroup/manage/edit',
    params,
  );
}

/**
 * 删除套餐规则
 */
export function deletePackageRule(id: number | string) {
  return requestClient.delete<BaseResponse>(
    `/java/admin/packageRule/manage/deleteRule/${id}`,
  );
}

/**
 * 获取通道列表
 */
export function getChannelList() {
  return requestClient.get<{
    code: number;
    data: Array<{
      id: number;
      name: string;
    }>;
    msg: string;
    orderNo: null;
  }>('/java/admin/channel/manage/list');
}

// 连号分配套餐规则
export function assignPackageRuleByRange(params: {
  endCardNo: string;
  groupId: number;
  startCardNo: string;
}) {
  const formData = new FormData();
  formData.append('groupId', params.groupId.toString());
  formData.append('startCardNo', params.startCardNo);
  formData.append('endCardNo', params.endCardNo);

  return requestClient.put<BaseResponse>(
    '/java/admin/cmpCard/manage/allocationPackageGroup',
    formData,
  );
}

// 下载套餐导入模板
export function downloadPackageTemplate() {
  return requestClient.get('/java/admin/exportModel/ExportInsertPackage', {
    responseType: 'blob',
  });
}

// 导入分配套餐规则
export function importPackageRuleAssign(params: {
  file: File;
  packageGroupId: number;
}) {
  const formData = new FormData();
  formData.append('packageGroupId', params.packageGroupId.toString());
  formData.append('file', params.file);

  return requestClient.put<BaseResponse>(
    '/java/admin/cmpCard/manage/importAllocationGroup',
    formData,
  );
}

export function getPackageGroupDetail(groupId: number) {
  return requestClient.get<{
    code: number;
    data: PackageGroupDetail;
    msg: string;
    orderNo: null;
  }>('/java/admin/packageGroup/getPackGroup', {
    params: { groupId },
  });
}

// 卡片套餐规则更新接口
export function updatePackageGroup(params: UpdatePackageGroupParams) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/packageGroup/manage/update', params);
}

// 卡片套餐规则新增接口
export function addPackageGroup(params: AddPackageGroupParams) {
  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/packageGroup/manage/add', params);
}

// 复制套餐规则
export function copyPackageGroup(groupId: number) {
  const formData = new FormData();
  formData.append('groupId', String(groupId));

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/packageGroup/manage/copyPackageGroup', formData);
}

// 删除套餐规则
export function deletePackageGroup(id: number) {
  return requestClient.delete<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>(`/java/admin/packageGroup/manage/delete/${id}`);
}

// 更新套餐规则状态
export function updatePackageGroupStatus(params: {
  groupStatus: number;
  id: number;
}) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/packageGroup/manage/editStatus', params);
}

// 获取套餐列表
export function getPackageList(params: PackageListParams) {
  return requestClient.get<{
    code: number;
    data: {
      rows: PackageItem[];
      total: number;
    };
    msg: string;
  }>('/java/admin/package/list', {
    params,
  });
}

// 更新套餐状态
export function updatePackageStatus(params: {
  automaticRenewal?: number;
  defaultRenewal?: number;
  id: number;
}) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/package/manage/updateStatus', params);
}

// 更新套餐上下架状态
export function updatePackageShelfStatus(params: {
  packageId: number;
  status: number;
}) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/packagePrice/updatePackageData', params);
}

// 获取套餐分类选项
export function getPackageClassOptions() {
  return requestClient.get<{
    code: number;
    data: { id: number; name: string }[];
    msg: string;
  }>('/java/admin/packageClass/manage/getOption');
}

// 获取限速模板选项
export function getSpeedLimitOptions() {
  return requestClient.get<{
    code: number;
    data: { id: number; name: string }[];
    msg: string;
  }>('/java/admin/speedLimit/manage/SpeedLimitOption');
}

// 获取周期模板选项
export function getCycleLimitOptions() {
  return requestClient.get<{
    code: number;
    data: { id: number; name: string }[];
    msg: string;
  }>('/java/admin/cycleLimit/manage/getOption');
}

// 获取虚量模版选项
export function getVoidLimitOptions() {
  return requestClient.get<{
    code: number;
    data: { id: number; name: string }[];
    msg: string;
  }>('/java/admin/voidLimit/manage/getOption');
}

// 添加套餐
export function createPackage(params: {
  automaticRenewal: number; // 自动续费
  billingInstructions?: string; // 平均价格介绍
  classId: number; // 分类名称
  cycleCount?: number; // 周期个数
  cycleId?: number; // 周期模板
  cycleRule: number; // 周期规则
  defaultRenewal: number; // 默认续费
  firstTimeRule: number; // 首次规则
  groupId: number; // 套餐规则
  intervalOrdering: number; // 间隔时间
  orderRule: number; // 订购规则
  packageCode: string; // 对接编码
  packageCost: number; // 套餐成本
  packageIntroduce?: string; // 套餐说明
  packageName: string; // 套餐名称
  packagePrice: number; // 套餐售价
  packageStatus: number; // 套餐状态
  packageTotal: number; // 总流量(MB)
  packageType: number; // 套餐类型
  packageValidity: number; // 有效类型
  packOrder: number; // 显示顺序
  speedLimitId: number; // 限速模板
  stackingRules: number; // 叠加规则
  trafficDesc?: string; // 流量介绍
  useAreaId?: string; // 额外对接编码
  validityDays?: number; // 自定义时生效 按天自定义则需要设置   有效期类型按天自定义时必填
  voidId?: number; // 虚量模版id
  voidRatio: number; // 统一虚量(%)
  voidType: number; // 虚量模式
}) {
  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
  }>('/java/admin/package/manage/add', params);
}

// 获取套餐编辑数据
export function getPackageDetail(packageId: number) {
  return requestClient.get<{
    code: number;
    data: {
      automaticRenewal: number;
      billingInstructions: string;
      classId: null | number;
      cycleCount: number;
      cycleId: null | number;
      cycleRule: number;
      defaultRenewal: number;
      firstTimeRule: number;
      groupId: number;
      id: number;
      intervalOrdering: number;
      orderRule: number;
      packageCode: null | string;
      packageCost: null | number;
      packageIntroduce: string;
      packageName: string;
      packagePrice: null | number;
      packageStatus: number;
      packageTotal: number;
      packageType: number;
      packageValidity: number;
      packOrder: number;
      speedLimitId: number;
      stackingRules: number;
      systemPackageCode: string;
      trafficDesc: string;
      useAreaId: null | string;
      voidRatio: number;
      voidType: number;
    };
    msg: string;
  }>('/java/admin/package/manage/getPackageUpdate', {
    params: { packageId },
  });
}

// 更新套餐
export function updatePackage(params: {
  automaticRenewal: number; // 自动续费
  billingInstructions?: string; // 平均价格介绍
  classId: number; // 分类名称
  cycleCount?: number; // 周期个数
  cycleId?: number; // 周期模板
  cycleRule: number; // 周期规则
  defaultRenewal: number; // 默认续费
  firstTimeRule: number; // 首次规则
  groupId: number; // 套餐规则
  id: number; // 套餐ID
  intervalOrdering: number; // 间隔时间
  orderRule: number; // 订购规则
  packageCode: string; // 对接编码
  packageCost: number; // 套餐成本
  packageIntroduce?: string; // 套餐说明
  packageName: string; // 套餐名称
  packagePrice: number; // 套餐售价
  packageStatus: number; // 套餐状态
  packageTotal: number; // 总流量(MB)
  packageType: number; // 套餐类型
  packageValidity: number; // 有效类型
  packOrder: number; // 显示顺序
  speedLimitId: number; // 限速模板
  stackingRules: number; // 叠加规则
  trafficDesc?: string; // 流量介绍
  useAreaId?: string; // 额外对接编码
  validityDays?: number; // 自定义时生效 按天自定义则需要设置   有效期类型按天自定义时必填
  voidId?: number; // 虚量模版id
  voidRatio: number; // 统一虚量(%)
  voidType: number; // 虚量模式
}) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
  }>('/java/admin/package/manage/update', params);
}

// 复制套餐
export function copyPackage(packId: number) {
  const formData = new FormData();
  formData.append('packId', String(packId));

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
  }>('/java/admin/package/manage/copy', formData);
}

// 重置套餐编码
export function resetPackageCode(packageId: number) {
  const formData = new FormData();
  formData.append('packageId', String(packageId));

  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
  }>('/java/admin/package/manage/updatePackageCode', formData);
}

// 删除套餐
export function deletePackage(id: number) {
  return requestClient.delete<{
    code: number;
    data: null;
    msg: string;
  }>(`/java/admin/package/manage/delete/${id}`);
}

// 导入套餐
export function importPackage(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/package/manage/ImportPackage', formData);
}

// 设备套餐列表项
export interface DevicePackageItem {
  id: number;
  popular: number;
  name: string;
  deviceGroupId: null | number;
  seriesId: null | number;
  ruleExplanation: string;
  billingInstructions: null | string;
  membershipId: null | number;
  voidType: number;
  voidRatio: number;
  voidId: null | number;
  speedLimitId: null | number;
  classId: number;
  packageImg: string;
  cycleRule: number;
  cycleId: null | number;
  cycleCount: number;
  orderRule: number;
  stackingRules: number;
  packageType: number;
  packageStatus: number;
  packageValidity: number;
  validityDays: number;
  systemPackageCode: null | string;
  packageCode: null | string;
  useAreaId: null | string;
  packageTotal: number;
  packageIntroduce: string;
  packOrder: number;
  creationTime: string;
  updateTime: string;
  usePay: null | number;
  packagePrice: number;
  packageCost: number;
  seriesName: null | string;
  className: string;
  cycles: null | string;
  membershipRules: null | string;
}

// 获取设备套餐列表
export function getDevicePackageListApi(params: {
  name?: string;
  packageStatus?: number;
  packageType?: number;
  page: number;
  pageSize: number;
}) {
  return requestClient.get<{
    code: number;
    data: {
      rows: DevicePackageItem[];
      total: number;
    };
    msg: string;
  }>('/java/admin/devicePack/getDevicePackageList', {
    params,
  });
}

// 快捷修改设备套餐价格
// /java/admin/devicePackagePrice/updatePrice
export function updateDevicePackagePrice(params: {
  packageCost: number; // 套餐成本
  packageId: number; // 套餐id
  packagePrice: number; // 套餐售价
}) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
  }>('/java/admin/devicePackagePrice/updatePrice', params);
}

// 计费组选项
export interface BillingGroupOption {
  id: number;
  name: string;
}

// 获取计费组选项
export function getBillingGroupOptions() {
  return requestClient.get<{
    code: number;
    data: BillingGroupOption[];
    msg: string;
  }>('/java/admin/packSeries/manage/getOption');
}

// 会员模板选项
export interface MembershipTemplateOption {
  id: number;
  name: string;
}

// 获取会员模板选项
export function getMembershipTemplateOptions() {
  return requestClient.get<{
    code: number;
    data: MembershipTemplateOption[];
    msg: string;
  }>('/java/admin/membershipLimit/manage/getOption');
}

// 支付方式选项
export interface PaymentOption {
  id: string;
  name: string;
}

// 获取支付方式列表
export function getPaymentOptions() {
  return requestClient.get<{
    code: number;
    data: PaymentOption[];
    msg: string;
  }>('/java/admin/pay/getPayList');
}

// 虚量模板选项
export interface VoidTemplateOption {
  id: number;
  name: string;
}

// 获取虚量模板选项
export function getVoidTemplateOptions() {
  return requestClient.get<{
    code: number;
    data: VoidTemplateOption[];
    msg: string;
  }>('/java/admin/voidLimit/manage/getOption');
}

// 添加设备套餐
export function addDevicePackage(params: {
  billingInstructions?: string;
  classId: number;
  cycleCount?: number;
  cycleRule: number;
  membershipId?: number;
  name: string;
  orderRule: number;
  packageCode?: string;
  packageCost: number;
  packageImg?: string;
  packageIntroduce?: string;
  packagePrice: number;
  packageStatus: number;
  packageTotal: number;
  packageType: number;
  packageValidity: number;
  packOrder?: number;
  popular: number;
  ruleExplanation?: string;
  seriesId: number;
  stackingRules: number;
  systemPackageCode?: string;
  useAreaId?: string;
  usePay?: string[];
  validityDays?: number;
  voidId?: number;
  voidRatio?: number;
  voidType: number;
}) {
  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
  }>('/java/admin/devicePack/manage/insertDevicePackage', {
    ...params,
    // 如果 usePay 是数组，转换为逗号分隔的字符串
    usePay: Array.isArray(params.usePay)
      ? params.usePay.join(',')
      : params.usePay,
  });
}

// 编辑设备套餐
export function editDevicePackage(params: {
  billingInstructions?: string;
  classId: number;
  cycleCount?: number;
  cycleRule: number;
  id: number;
  membershipId?: number;
  name: string;
  orderRule: number;
  packageCode?: string;
  packageCost: number;
  packageImg?: string;
  packageIntroduce?: string;
  packagePrice: number;
  packageStatus: number;
  packageTotal: number;
  packageType: number;
  packageValidity: number;
  packOrder?: number;
  popular: number;
  ruleExplanation?: string;
  seriesId: number;
  stackingRules: number;
  systemPackageCode?: string;
  useAreaId?: string;
  usePay?: string[];
  validityDays?: number;
  voidId?: number;
  voidRatio?: number;
  voidType: number;
}) {
  return requestClient.put<{
    code: number;
    data: null;
    msg: string;
  }>('/java/admin/devicePack/manage/editDevicePackage', {
    ...params,
    usePay: Array.isArray(params.usePay)
      ? params.usePay.join(',')
      : params.usePay,
  });
}

// 删除设备套餐
export function deleteDevicePackage(ids: number | number[]) {
  const idStr = Array.isArray(ids) ? ids.join(',') : ids;
  return requestClient.delete<{
    code: number;
    data: null;
    msg: string;
  }>(`/java/admin/devicePack/manage/deleteDevicePackage/${idStr}`);
}

// 复制设备套餐
export function copyDevicePackage(packageId: number) {
  const formData = new FormData();
  formData.append('packageId', String(packageId));
  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
  }>('/java/admin/devicePack/manage/copyDevicePackage', formData);
}

// downloadDeleteTemplate
export function downloadAllocationTemplate() {
  return requestClient.get('/java/admin/exportModel/ExportAllocation', {
    responseType: 'blob',
  });
}

// 套餐模板接口
export interface PackageTemplate {
  id: number;
  templateName: string;
  templateNotes: string;
  userId: number;
  creationTime: string;
  updateTime: string;
}

// 套餐模板列表响应
export interface PackageListResponse extends BaseResponse {
  data: {
    rows: PackageTemplate[];
    total: number;
  };
}

// 获取套餐模板列表
export function getPackageTemplateList(params: {
  page: number;
  pageSize: number;
  templateName?: string;
  templateNotes?: string;
}) {
  return requestClient.get<PackageListResponse>(
    '/java/admin/packageTemplate/getList',
    { params },
  );
}

// 新增模板参数
export interface AddPackageTemplateParams {
  templateName: string;
  templateNotes: string;
}

// 新增模板
export function addPackageTemplate(params: AddPackageTemplateParams) {
  return requestClient.post<BaseResponse>(
    '/java/admin/packageTemplate/addTemplate',
    params,
  );
}

// 编辑模板参数
export interface EditPackageTemplateParams extends AddPackageTemplateParams {
  id: number;
  userId: number;
}

// 编辑模板
export function editPackageTemplate(params: EditPackageTemplateParams) {
  return requestClient.put<BaseResponse>(
    '/java/admin/packageTemplate/editTemplate',
    params,
  );
}

// 删除模板
export function deletePackageTemplate(id: number) {
  return requestClient.delete<BaseResponse>(
    `/java/admin/packageTemplate/deleteTemplate/${id}`,
  );
}

// 新增套餐价格参数
export interface AddPackagePriceParams {
  groupId: number;
  packageId: number;
  packageCost: number;
  packagePrice: number;
  templateId: number;
}

// 新增套餐价格
export function addPackagePrice(params: AddPackagePriceParams) {
  return requestClient.post<BaseResponse>(
    '/java/admin/packageTemplatePrice/addPackPrice',
    params,
  );
}

// 编辑套餐价格参数
export interface EditPackagePriceParams extends AddPackagePriceParams {
  id: number;
}

// 编辑套餐价格
export function editPackagePrice(params: EditPackagePriceParams) {
  return requestClient.put<BaseResponse>(
    '/java/admin/packageTemplatePrice/editPackPrice',
    params,
  );
}

// 删除套餐价格
export function deletePackagePrice(id: number) {
  return requestClient.delete<BaseResponse>(
    `/java/admin/packageTemplatePrice/deletePackPrice/${id}`,
  );
}

// 回收套餐
export function deletePackagePriceApi(params: {
  packageId: number;
  userId: number;
}) {
  const formData = new FormData();
  formData.append('packageId', params.packageId.toString());
  formData.append('userId', params.userId.toString());

  return requestClient.delete<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/packagePrice/deletePrice', {
    data: formData,
  });
}

// 套餐数据类型
export interface PackageData {
  id: number;
  packageRule: string; // 套餐规则
  packageName: string; // 套餐名称
  totalSize: number; // 总量(MB)
  cost: number; // 成本
  price: number; // 售价
  agentCost: string; // 代理成本
  agentPrice: string; // 代理售价
}

// 套餐列表查询参数
export interface PackageListParams {
  page: number;
  pageSize: number;
  packageName?: string;
  packageRule?: string;
}

/**
 * 获取套餐列表
 */
export function getPackageListApi(params: PackageListParams) {
  return requestClient.get<{
    code: number;
    data: {
      rows: PackageData[];
      total: number;
    };
    msg: string;
    orderNo: null;
  }>('/java/admin/package/list', { params });
}

// 用户套餐数据类型
export interface UserPackageData {
  id: number;
  package_cost: number;
  package_price2: null | number;
  group_name: string;
  package_name: string;
  count: null | number;
  price_id: number;
  package_cost2: null | number;
  activate_commission2: null | number;
  package_price: number;
  package_total: number;
  status: number;
}

// 获取用户套餐列表参数
export interface GetUserPackageListParams {
  page: number;
  pageSize: number;
  userId: number;
  packageName?: string;
  groupName?: string;
  isAssigned?: 0 | 1;
}

// 获取用户套餐列表
export function getUserPackageListApi(params: GetUserPackageListParams) {
  return requestClient.get<{
    code: number;
    data: {
      rows: UserPackageData[];
      total: number;
    };
    msg: string;
    orderNo: null;
  }>('/java/admin/package/getPackageByUid', { params });
}

// 设置套餐价格参数
export interface SetPackagePriceParams {
  userId: number;
  packageId: number;
  packageCost: string;
  packagePrice: string;
  price: number;
}

// 设置套餐价格
export function setPackagePriceApi(params: SetPackagePriceParams) {
  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/packagePrice/setPackagePrice', params);
}

// 批量设置套餐价格参数
export interface BatchSetPackagePriceParams {
  packageIds: string; // 套餐id，多个逗号分开
  userId: string;
  cost?: string; // 单个套餐时必填
  price?: string; // 单个套餐时必填
  bfb?: string; // 多个分配时必填
  activateCommission: string;
}

// 批量设置套餐价格
export function batchSetPackagePriceApi(params: BatchSetPackagePriceParams) {
  const formData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      formData.append(key, value);
    }
  });

  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/packagePrice/batchSetPackagePrice', formData);
}

// 设备套餐数据类型
export interface DevicePackageData {
  id: null;
  packageId: number;
  className: string;
  packageName: string;
  totalFlow: number;
  cost: number;
  price: number;
  userCost: null | number;
  userPrice: null | number;
  seriesName: string;
}

// 获取设备套餐列表参数
export interface GetDevicePackageListParams {
  page: number;
  pageSize: number;
  fpUserId: number;
  packageName?: string;
  className?: string;
  isFp?: 1 | 2;
}

// 获取设备套餐列表
export function getYesSetPackageList(params: GetDevicePackageListParams) {
  return requestClient.get<{
    code: number;
    data: {
      rows: DevicePackageData[];
      total: number;
    };
    msg: string;
    orderNo: null;
  }>('/java/admin/devicePack/getYesSetPackageList', { params });
}

// 设置设备套餐价格参数
export interface SetDevicePackagePriceParams {
  packageCost: number;
  packagePrice: number;
  packageId: number;
  userId: number;
}

// 设置设备套餐价格
export function setDevicePackagePriceApi(params: SetDevicePackagePriceParams) {
  return requestClient.post<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>('/java/admin/devicePackagePrice/setPackagePrice', params);
}

// 删除设备套餐价格
export function deleteDevicePackagePriceApi(packageId: number, userId: number) {
  return requestClient.delete<{
    code: number;
    data: null;
    msg: string;
    orderNo: null;
  }>(
    `/java/admin/devicePackagePrice/deletePackagePrice?packageId=${packageId}&userId=${userId}`,
  );
}

// 获取优享套餐 /java/admin/webOptimalAccelerationPackage/manage/getOptions
export function getOptimalAccelerationPackageOptions() {
  return requestClient.get<{
    code: number;
    data: {
      id: number;
      name: string;
    }[];
    msg: string;
    orderNo: null;
  }>('/java/admin/webOptimalAccelerationPackage/manage/getOptions');
}

// /java/admin/packagePrice/updatePrice 修改套餐价格 put
// userid 修改的账号id（不传则修改自己的）
// packageId 修改的套餐id
// packagePrice 修改的价格
export function updatePackagePriceApi(params: {
  packageId: number;
  packagePrice: number;
  userid?: number;
}) {
  const formData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      formData.append(key, value.toString());
    }
  });
  return requestClient.put('/java/admin/packagePrice/updatePrice', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
