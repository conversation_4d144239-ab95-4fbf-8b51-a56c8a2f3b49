import type { BaseResponse } from '#/api/model';

import { requestClient } from '#/api/request';

// 列表请求参数接口
export interface DeviceBalanceOrderListParams {
  page: number;
  pageSize: number;
  deviceNo?: string;
  orderName?: string;
  systemOrdernumber?: string;
  paymentOrdernumber?: string;
  orderState?: number;
  orderPayment?: number;
  orderIp?: string;
  rechargeAddress?: string;
}

// 订单数据接口
export interface DeviceBalanceOrder {
  id: number;
  orderName: string;
  systemOrdernumber: string;
  paymentOrdernumber: null | string;
  userId: number;
  deviceId: number;
  payId: null | number;
  prestorePrice: number;
  paidInAmount: number;
  prestoreGive: number;
  couponRuleId: null | number;
  givePosition: number;
  orderState: number;
  orderPayment: null | number;
  orderIp: string;
  rechargeAddress: string;
  creationTime: string;
  rechargeTime: null | string;
  refundTime: string;
}

// 列表响应接口
export interface DeviceBalanceOrderListResponse extends BaseResponse {
  data: {
    rows: DeviceBalanceOrder[];
    total: number;
  };
}

// 获取设备订单列表
export const getDeviceBalanceOrderList = (
  params: DeviceBalanceOrderListParams,
) => {
  return requestClient.get<DeviceBalanceOrderListResponse>(
    '/java/admin/devicePrestoreOrder/getList',
    { params },
  );
};

// 删除设备余额订单 /java/admin/devicePrestoreOrder/manage/deleteOrder/2,3
export const deleteDeviceBalanceOrder = (orderIds: number[]) => {
  return requestClient.delete(
    `/java/admin/devicePrestoreOrder/manage/deleteOrder/${orderIds.join(',')}`,
  );
};

// 设备订单退款 post /java/admin/devicePrestoreOrder/manage/refundOrder
export const refundDeviceBalanceOrder = (params: {
  amount: number; // 退款金额
  msg?: string; // 退款备注
  orderId: number; // 订单ID
}) => {
  const fromData = new FormData();
  fromData.append('amount', params.amount.toString());
  fromData.append('msg', params.msg || '');
  fromData.append('orderId', params.orderId.toString());
  return requestClient.post(
    // `/java/admin/devicePrestoreOrder/manage/refundOrder`,
    `/java/admin/devicePrestoreOrder/manage/orderReturn`,
    fromData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};
