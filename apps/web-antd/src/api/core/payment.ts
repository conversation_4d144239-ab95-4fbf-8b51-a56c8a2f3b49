import type { BaseResponse } from '#/api/request';

import { requestClient } from '#/api/request';

export interface WechatPublicItem {
  id: number;
  appid: string;
  secret: string;
  empower_domain_name: string;
  creation_time: string;
  update_time: string;
  name: string;
  user__user_name: null | string;
  user__user_account: string;
  user_id: number;
}

export interface WechatPublicResponse extends BaseResponse {
  data: {
    rows: WechatPublicItem[];
    total: number;
  };
}

// 获取公众号配置列表
export const getWechatPublicList = (params: {
  appid?: string;
  name?: string;
  page: number;
  pageSize: number;
  secret?: string;
  user_id?: number;
}) => {
  return requestClient.get<WechatPublicResponse>(
    '/py/backend/backstage/wxpublic/',
    {
      params,
    },
  );
};

// 新增公众号配置
export const addWechatPublic = (data: Omit<WechatPublicItem, 'id'>) => {
  // 使用 FormData 格式提交
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value);
  });

  return requestClient.post('/py/backend/backstage/wxpublic/', formData);
};

// 修改公众号配置
export const updateWechatPublic = (data: WechatPublicItem) => {
  // const formData = new FormData();
  // Object.entries(data).forEach(([key, value]) => {
  //   formData.append(key, value);
  // });

  return requestClient.put('/py/backend/backstage/wxpublic/', data);
};

// 删除公众号配置
export const deleteWechatPublic = (id: number) => {
  return requestClient.delete(`/py/backend/backstage/wxpublic/?id=${id}`);
};

// 上传支付授权文件
export const uploadPaymentAuthFile = (file: File) => {
  const formData = new FormData();
  formData.append('public_file', file);
  return requestClient.post('/py/backend/backstage/wxpay/', formData);
};

// 微信支付商户接口类型
export interface WechatMerchantItem {
  id?: number;
  name: string;
  merchantNum: string;
  mchserialNo: string;
  apiv3Key: string;
  apiclientKeyUrl: string;
  fees: number;
  openStatus: number;
  publicId: number;
  userId: number;
}

export interface WechatMerchantResponse extends BaseResponse {
  data: {
    rows: WechatMerchantItem[];
    total: number;
  };
}

// 获取微信支付商户列表
export const getWechatMerchantList = (params: {
  name?: string;
  openStatus?: number;
  page: number;
  pageSize: number;
  publicId?: number;
}) => {
  return requestClient.get<WechatMerchantResponse>(
    '/java/admin/wxPay/getPayList',
    { params },
  );
};

// 新增微信支付商户
export const addWechatMerchant = (data: Omit<WechatMerchantItem, 'id'>) => {
  return requestClient.post<BaseResponse>('/java/admin/wxPay/addWxPay', data);
};

// 修改微信支付商户
export const updateWechatMerchant = (data: WechatMerchantItem) => {
  return requestClient.put<BaseResponse>('/java/admin/wxPay/editWxPay', data);
};

// 删除微信支付商户
export const deleteWechatMerchant = (id: number) => {
  return requestClient.delete<BaseResponse>(
    `/java/admin/wxPay/deleteWxPay/${id}`,
  );
};

// 支付宝商户接口类型
export interface AlipayMerchantItem {
  id?: number;
  name: string;
  appId: string;
  notifyUrl: string;
  privateKey: string;
  alipayPublicKey: string;
  status: number;
  userId: number;
  randomSubtraction: number; // 随机立减 1开启 2关闭
  commission: number;
  appCertPublicKeyUrl: null | string;
  alipayCertPublicKeyUrl: null | string;
  alipayRootCertUrl: null | string;
  checkType: number;
  remark?: string;
  creationTime?: string;
  updateTime?: string;
  payAmount?: null | number;
  userAccount?: string;
}

export interface AlipayMerchantResponse extends BaseResponse {
  data: {
    rows: AlipayMerchantItem[];
    total: number;
  };
}

// 获取支付宝商户配置列表
export const getAlipayMerchantList = (params: {
  name?: string;
  page: number;
  pageSize: number;
}) => {
  return requestClient.get<AlipayMerchantResponse>(
    '/java/admin/allPayConfig/getAliPayConfigList',
    { params },
  );
};

// 新增支付宝商户配置
export const addAlipayMerchant = (data: Omit<AlipayMerchantItem, 'id'>) => {
  return requestClient.post<BaseResponse>(
    '/java/admin/allPayConfig/addAliPayConfig',
    data,
  );
};

// 修改支付宝商户配置
export const updateAlipayMerchant = (data: AlipayMerchantItem) => {
  return requestClient.put<BaseResponse>(
    '/java/admin/allPayConfig/editAliPayConfig',
    data,
  );
};

// 删除支付宝商户配置
export const deleteAlipayMerchant = (id: number) => {
  return requestClient.delete<BaseResponse>(
    `/java/admin/allPayConfig/deleteAliPayConfig/${id}`,
  );
};

export interface DougongAggregateItem {
  id: number;
  name: string;
  appId: string;
  apiKey: string;
  rsaPrivateKey: string;
  payChannel: string;
  wxPublicId?: number;
  notifyUrl: string;
  status: number;
  commission: number;
  wxJsapi: number;
  zfbSm: number;
  userId: number;
  creationTime: string;
  updateTime: string;
}

export interface DougongAggregateResponse extends BaseResponse {
  data: {
    rows: DougongAggregateItem[];
    total: number;
  };
}

// 获取斗拱聚合支付配置列表
export const getDougongAggregateList = (params: {
  merchantId?: string;
  name?: string;
  page: number;
  pageSize: number;
  status?: number;
}) => {
  return requestClient.get<DougongAggregateResponse>(
    '/java/admin/adapay/getList',
    {
      params,
    },
  );
};

// 新增斗拱聚合支付配置
export const addDougongAggregate = (data: Omit<DougongAggregateItem, 'id'>) => {
  return requestClient.post('/java/admin/adapay/addAdaPayConfig', data);
};

// 修改斗拱聚合支付配置
export const updateDougongAggregate = (data: DougongAggregateItem) => {
  return requestClient.put('/java/admin/adapay/editAdaPayConfig', data);
};

// 删除斗拱聚合支付配置
export const deleteDougongAggregate = (id: number) => {
  return requestClient.delete(`/java/admin/adapay/deleteAdaPayConfig/${id}`);
};

// 斗拱汇付商户列表请求参数
export interface DougongHuifuListParams {
  page: number;
  pageSize: number;
  name?: string;
  wxPublicIds?: string;
  procutId?: string;
  sysId?: string;
  huifuId?: string;
  notifyUrl?: string;
  status?: number;
  randomSubtraction?: number;
  zfbSm?: number;
  wxJsapi?: number;
  commissionMin?: number;
  commissionMax?: number;
  creationTimeBegin?: string;
  creationTimeEnd?: string;
  updateTimeBegin?: string;
  updateTimeEnd?: string;
}

// 斗拱汇付商户项
export interface DougongHuifuItem {
  id: number;
  name: string;
  rsaPrivateKey: string;
  rsaPublicKey: string;
  procutId: string;
  sysId: string;
  huifuId: string;
  status: number;
  commission: number;
  wxJsapi: number;
  zfbSm: number;
  creationTime: string;
  updateTime: string;
}

// 获取斗拱汇付商户列表
export const getDougongHuifuList = (params: DougongHuifuListParams) => {
  return requestClient.get<{
    code: number;
    data: {
      rows: DougongHuifuItem[];
      total: number;
    };
    msg: string;
    orderNo: null;
  }>('/java/admin/huifuConfig/getList', { params });
};

// 火脸商户接口类型
export interface HuolianMerchantItem {
  id?: number;
  name: string;
  authCode: string;
  key: string;
  merchantNo: string;
  operatorAccount: number | string;
  payWay?: 'alipay' | 'all' | 'cloud' | 'wechat';
  status?: 1 | 2;
  commission?: number;
  wxJsapi?: 1 | 2;
  zfbSm?: 1 | 2;
  randomSubtraction?: 1 | 2;
  userId?: number;
  creationTime?: string;
  updateTime?: string;
}

export interface HuolianMerchantResponse extends BaseResponse {
  data: {
    rows: HuolianMerchantItem[];
    total: number;
  };
}

// 获取火脸商户列表
export const getHuolianMerchantList = (params: {
  name?: string;
  page: number;
  pageSize: number;
  status?: number;
}) => {
  return requestClient.get<HuolianMerchantResponse>(
    '/java/admin/firefacepay/getList',
    { params },
  );
};

// 新增火脸商户
export const addHuolianMerchant = (data: Omit<HuolianMerchantItem, 'id'>) => {
  return requestClient.post<BaseResponse>(
    '/java/admin/firefacepay/addConfig',
    data,
  );
};

// 修改火脸商户
export const updateHuolianMerchant = (data: HuolianMerchantItem) => {
  return requestClient.put<BaseResponse>(
    '/java/admin/firefacepay/editConfig',
    data,
  );
};

// 删除火脸商户
export const deleteHuolianMerchant = (id: number) => {
  return requestClient.delete<BaseResponse>(
    `/java/admin/firefacepay/deleteConfig/${id}`,
  );
};

// 小马哥商户项
export interface XiaomageMerchantItem {
  id?: number;
  name: string;
  deviceId: string;
  deviceSecret: string;
  dockingDeviceId: string;
  siteUserId: string;
  dockingSecret: string;
  userId: number;
  status: 1 | 2;
  wxJsapi: 1 | 2;
  zfbSm: 1 | 2;
  randomSubtraction: 1 | 2;
  commission: number;
  creationTime?: string;
  updateTime?: string;
}

export interface XiaomageMerchantResponse extends BaseResponse {
  data: {
    rows: XiaomageMerchantItem[];
    total: number;
  };
}

// 获取小马哥商户列表
export const getXiaomageMerchantList = (params: {
  deviceId?: string;
  deviceSecret?: string;
  dockingDeviceId?: string;
  dockingSecret?: string;
  name?: string;
  page: number;
  pageSize: number;
  randomSubtraction?: number;
  siteUserId?: string;
  status?: number;
  wxJsapi?: number;
  zfbSm?: number;
}) => {
  return requestClient.get<XiaomageMerchantResponse>(
    '/java/admin/xiaoMaPay/getPayList',
    { params },
  );
};

// 新增小马哥商户配置
export const addXiaomageMerchant = (data: Omit<XiaomageMerchantItem, 'id'>) => {
  return requestClient.post<BaseResponse>(
    '/java/admin/xiaoMaPay/insertPay',
    data,
  );
};

// 修改小马哥商户配置
export const updateXiaomageMerchant = (data: XiaomageMerchantItem) => {
  return requestClient.put<BaseResponse>('/java/admin/xiaoMaPay/editPay', data);
};

// 删除小马哥商户配置
export const deleteXiaomageMerchant = (id: number) => {
  return requestClient.delete<BaseResponse>(
    `/java/admin/xiaoMaPay/deletePay/${id}`,
  );
};

// 提现配置项
export interface WithdrawConfigItem {
  id?: number;
  withdrawName: string;
  withdrawIntervalTime: number;
  withdrawRate: number;
  withdrawMin: number;
  withdrawMax: number;
  withdrawReserve: number;
  withdrawAlipay: 1 | 2;
  withdrawAlipayId: number;
  withdrawWechat: 1 | 2;
  withdrawWechatId: number;
  withdrawBankCard: 1 | 2;
  withdrawPaymentType: 1 | 2;
  withdrawCheckType: 1 | 2;
  userId: number;
  status: 1 | 2;
  prompt?: string;
  multiple: number;
  creationTime?: string;
  updateTime?: string;
}

// /java/admin/wxPay/getOption 获取微信商户
export const getWechatMerchantOption = () => {
  return requestClient.get<{
    code: number;
    data: {
      id: number;
      name: string;
    }[];
    msg: string;
    orderNo: string;
  }>('/java/admin/wxPay/getOption');
};

// /java/admin/allPayConfig/getOption 获取支付宝商户
export const getAllPayConfigOption = () => {
  return requestClient.get<{
    code: number;
    data: {
      id: number;
      name: string;
    }[];
    msg: string;
    orderNo: string;
  }>('/java/admin/allPayConfig/getOption');
};
// 获取提现配置列表
export const getWithdrawConfigList = (params: {
  page: number;
  pageSize: number;
  status?: number;
  withdrawName?: string;
}) => {
  return requestClient.get<{
    code: number;
    data: {
      rows: WithdrawConfigItem[];
      total: number;
    };
    msg: string;
  }>('/java/admin/withdraw/getWithdrawConfigList', { params });
};

// 综合配置项
export interface ComprehensiveConfigItem {
  id?: number;
  payName: string;
  randomSubtraction: number; // 随机减 1开启 2关闭
  paymentMode: number; // 支付方式 1人工 2自动
  status: number; // 状态 1启用 2关闭
  userId: number;
  creationTime?: string;
  updateTime?: string;
}

// 获取综合配置列表
export const getComprehensiveConfigList = (params: {
  page: number;
  pageSize: number;
  payName?: string;
}) => {
  return requestClient.get<{
    code: number;
    data: {
      rows: ComprehensiveConfigItem[];
      total: number;
    };
    msg: string;
  }>('/java/admin/webPayConfig/getList', { params });
};

// 新增综合配置
export const addComprehensiveConfig = (
  data: Omit<ComprehensiveConfigItem, 'creationTime' | 'id' | 'updateTime'>,
) => {
  return requestClient.post<BaseResponse>(
    '/java/admin/webPayConfig/insertConfig',
    data,
  );
};

// 修改综合配置
export const updateComprehensiveConfig = (
  data: Omit<ComprehensiveConfigItem, 'creationTime' | 'updateTime'>,
) => {
  return requestClient.put<BaseResponse>(
    '/java/admin/webPayConfig/editConfig',
    data,
  );
};

// 删除综合配置
export const deleteComprehensiveConfig = (id: number) => {
  return requestClient.delete<BaseResponse>(
    `/java/admin/webPayConfig/deleteConfig/${id}`,
  );
};

// 上传证书文件
export const uploadCertFile = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post<{
    code: number;
    data: string;
    msg: string;
    orderNo: null;
  }>('/java/admin/file/uploadAlipayCrtFile', formData);
};

// 新增斗拱汇付商户
export const addDougongHuifu = (data: {
  commission?: number;
  huifuId: string;
  name: string;
  notifyUrl: string;
  procutId: string;
  randomSubtraction?: number;
  rsaPrivateKey: string;
  rsaPublicKey: string;
  status?: number;
  subAppid: string;
  sysId: string;
  userId: number;
  wxJsapi: number;
  wxPublicId?: number;
  zfbSm: number;
}) => {
  return requestClient.post('/java/admin/huifuConfig/insetConfig', data);
};

// 修改斗拱汇付商户
export const updateDougongHuifu = (data: {
  commission?: number;
  huifuId: string;
  id: number;
  name: string;
  notifyUrl: string;
  procutId: string;
  randomSubtraction?: number;
  rsaPrivateKey: string;
  rsaPublicKey: string;
  status?: number;
  subAppid: string;
  sysId: string;
  userId: number;
  wxJsapi: number;
  wxPublicId?: number;
  zfbSm: number;
}) => {
  return requestClient.put('/java/admin/huifuConfig/editConfig', data);
};

// 删除斗拱汇付商户
export const deleteDougongHuifu = (id: number) => {
  return requestClient.delete(`/java/admin/huifuConfig/deleteConfig/${id}`);
};

// 添加提现配置
export const addWithdrawConfig = (
  data: Omit<WithdrawConfigItem, 'creationTime' | 'id' | 'updateTime'>,
) => {
  return requestClient.post<BaseResponse>(
    '/java/admin/withdraw/addWithdrawConfig',
    data,
  );
};

// 修改提现配置
export const editWithdrawConfig = (
  data: Omit<WithdrawConfigItem, 'creationTime' | 'updateTime'>,
) => {
  return requestClient.put<BaseResponse>(
    '/java/admin/withdraw/editWithdrawConfig',
    data,
  );
};

// 删除提现配置
export const deleteWithdrawConfig = (id: number) => {
  return requestClient.delete<BaseResponse>(
    `/java/admin/withdraw/deleteWithdrawConfig/${id}`,
  );
};

// 智讯易商户接口类型
export interface ZhiXunYiMerchantItem {
  id?: number;
  payName: string;
  userId: number;
  collectionLimit: number;
  status: number;
  wxStatus: number;
  wxUrl: string;
  zfbStatus: number;
  zfbUrl: string;
  callbackPath: string;
  collectionLimitId?: null | number;
  creationTime: string;
}

export interface ZhiXunYiMerchantResponse extends BaseResponse {
  data: {
    rows: ZhiXunYiMerchantItem[];
    total: number;
  };
}

// 智讯易商户列表
export const getZhiXunYiMerchantList = (params: {
  callbackPath?: string;
  creationTimeBegin?: string;
  creationTimeEnd?: string;
  page?: number;
  pageSize?: number;
  payName?: string;
  status?: number;
  updateTimeBegin?: string;
  updateTimeEnd?: string;
  userId?: number;
  wxStatus?: number;
  zfbStatus?: number;
}) => {
  return requestClient.get<ZhiXunYiMerchantResponse>(
    '/java/admin/webMapayMerchant/getList',
    { params },
  );
};

// {"payName":"商户名称","userId":1,"status":2,"wxStatus":2,"wxUrl":"xxxx","zfbStatus":2,"zfbUrl":"xxx","callbackPath":"","collectionLimitId":null}

// 添加商户
export const addZhiXunYiMerchant = (data: ZhiXunYiMerchantItem) => {
  return requestClient.post('/java/admin/webMapayMerchant/add', data);
};

// 修改商户
export const updateZhiXunYiMerchant = (data: ZhiXunYiMerchantItem) => {
  return requestClient.put('/java/admin/webMapayMerchant/update', data);
};

// 删除商户
export const deleteZhiXunYiMerchant = (id: number) => {
  return requestClient.delete(`/java/admin/webMapayMerchant/delete/${id}`);
};

// 管理员直接修改额度
export const updateZhiXunYiMerchantCollectionLimit = (data: {
  collectionLimit: number; // 操作额度
  id: number; // 商户id
  type: 1 | 2; // 操作类型 1增加 2减少
}) => {
  const formData = new FormData();
  formData.append('collectionLimit', data.collectionLimit.toString());
  formData.append('id', data.id.toString());
  formData.append('type', data.type.toString());
  return requestClient.put(
    '/java/admin/webMapayMerchant/manage/updateCollectionLimit',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};

// 收款额度模板列表
export const getCollectionLimitList = (params: {
  creationTimeBegin?: string;
  creationTimeEnd?: string;
  name?: string;
  page?: number;
  pageSize?: number;
  updateTimeBegin?: string;
  updateTimeEnd?: string;
}) => {
  return requestClient.get<BaseResponse>(
    '/java/admin/webCollectionLimit/manage/getList',
    { params },
  );
};

// 添加收款额度模板
export const addCollectionLimit = (name: string) => {
  return requestClient.post('/java/admin/webCollectionLimit/manage/add', {
    name,
  });
};

// 修改收款额度模板
export const updateCollectionLimit = (data: {
  id: null | number;
  name: string;
}) => {
  return requestClient.put(
    '/java/admin/webCollectionLimit/manage/update',
    data,
  );
};

// 删除收款额度模板
export const deleteCollectionLimit = (id: number) => {
  return requestClient.delete(
    `/java/admin/webCollectionLimit/manage/delete/${id}`,
  );
};

// 收款额度模板选项
export const getCollectionLimitOptions = () => {
  return requestClient.get<BaseResponse>(
    '/java/admin/webCollectionLimit/manage/getOptions',
  );
};

interface RechargeAmountResponse extends BaseResponse {
  data: {
    rows: {
      collectionBalance: number; // 收款面额
      giftBalance: number; // 赠送面额
      id: number;
      limitId: number;
      price: number; // 购买价格
      slogan: string; // 购买标语说明
    }[];
    total: number;
  };
}

// 获取充值收款额度可充值面额
export const getRechargeAmountList = (merchantId: number) => {
  return requestClient.get<RechargeAmountResponse>(
    `/java/admin/webCollection/getListByMerchantId?merchantId=${merchantId}`,
  );
};

// 创建未支付订单
export const createUnpaidOrder = (params: {
  collectionId: number;
  merchantId: number;
}) => {
  return requestClient.post(
    `/java/admin/webMerchantCollectionOrder/createOrder?collectionId=${params.collectionId}&merchantId=${params.merchantId}`,
  );
};

interface RechargeAmountListByLimitIdResponse extends BaseResponse {
  data: {
    rows: {
      collectionBalance: number; // 收款面额
      giftBalance: number; // 赠送面额
      id: number;
      limitId: number;
      price: number; // 购买价格
      slogan: string; // 购买标语说明
    }[];
    total: number;
  };
}

// 获取模版下面可充值面额
export const getRechargeAmountListByLimitId = (params: {
  limitId: number;
  slogan?: string;
}) => {
  return requestClient.get<RechargeAmountListByLimitIdResponse>(
    `/java/admin/webCollection/manage/getList`,
    { params },
  );
};

// 添加收款面额
export const addRechargeAmount = (data: {
  collectionBalance: number; // 收款面额 (必填)
  giftBalance: number; // 赠送面额 (必填)
  limitId: number; // 模板id (必填)
  price: number; // 购买价格 (必填)
  slogan: string; // 购买标语说明 (必填)
}) => {
  return requestClient.post('/java/admin/webCollection/manage/add', data);
};

// 修改收款面额
export const updateRechargeAmount = (data: {
  collectionBalance: number; // 收款面额 (必填)
  giftBalance: number; // 赠送面额 (必填)
  id: number; // 收款面额id (必填)
  price: number; // 购买价格 (必填)
  slogan: string; // 购买标语说明 (必填)
}) => {
  return requestClient.put('/java/admin/webCollection/manage/update', data);
};

// 删除收款面额
export const deleteRechargeAmount = (id: number) => {
  return requestClient.delete(`/java/admin/webCollection/manage/delete/${id}`);
};

// 码支付商户接口类型
export interface MapayConfigResponse extends BaseResponse {
  data: {
    rows: {
      apiUrl: string; // 请求地址
      appId: string; // 对接账号
      appKey: string; // 对接密码
      commission: number; // 手续费
      configName: string; // 配置名称
      creationTime: string; // 创建时间
      id: number;
      merchantId: string; // 商户id
      status: number; // 开启状态 1开启 2关闭
      updateTime: string; // 修改时间
      userAccount: string; // 所属账号
      userId: number; // 账号id
      wxSm: number; // 微信收款码 1开启 2关闭
      zfbSm: number; // 支付宝收款码 1开启 2关闭
    }[];
    total: number;
  };
}

// 获取码支付商户列表 /java/admin/webMapayConfig/getList
export const getMapayConfigList = (params: {
  // 请求地址
  apiUrl?: string;
  // 对接账号
  appId?: string;
  // 对接密码
  appKey?: string;
  // 配置名称
  configName?: string;
  // 创建时间区间 - 开始
  creationTimeBegin?: string;
  // 创建时间区间 - 结束
  creationTimeEnd?: string;
  // 商户id
  merchantId?: string;
  // 页码
  page: number;
  // 每页条数
  pageSize: number;
  // 开启状态 1开启 2关闭
  status?: string;
  // 修改时间区间 - 开始
  updateTimeBegin?: string;
  // 修改时间区间 - 结束
  updateTimeEnd?: string;
  // 账号id
  userId?: string;
  // 微信收款码 1开启 2关闭
  wxSm?: string;
  // 支付宝收款码 1开启 2关闭
  zfbSm?: string;
}) => {
  return requestClient.get<MapayConfigResponse>(
    '/java/admin/webMapayConfig/getList',
    {
      params,
    },
  );
};

// 添加码支付商户
export const addMapayConfig = (data: {
  apiUrl: string; // API地址 必填
  appId: string; // 对接账号 必填
  appKey: string; // 对接密码 必填
  commission: number; // 手续费 必填
  configName: string; // 配置名称 必填
  merchantId: string; // 商户ID 必填
  status: number; // 状态 1启用 2关闭 必填
  userId: number; // 账号id 仅支持管理员选择
  wxSm: number; // 微信收款码 1开启 2关闭 必填
  zfbSm: number; // 支付宝收款码 1开启 2关闭 必填
}) => {
  return requestClient.post('/java/admin/webMapayConfig/add', data);
};

// 修改码支付商户
export const updateMapayConfig = (data: {
  apiUrl: string; // API地址 必填
  appId: string; // 对接账号 必填
  appKey: string; // 对接密码 必填
  commission: number; // 手续费 必填
  configName: string; // 配置名称 必填
  id: number; // 配置id 必填
  merchantId: string; // 商户ID 必填
  status: number; // 状态 1启用 2关闭 必填
  userId: number; // 账号id 仅支持管理员选择
  wxSm: number; // 微信收款码 1开启 2关闭 必填
  zfbSm: number; // 支付宝收款码 1开启 2关闭 必填
}) => {
  return requestClient.put('/java/admin/webMapayConfig/update', data);
};

// 删除码支付商户
export const deleteMapayConfig = (id: number) => {
  return requestClient.delete(`/java/admin/webMapayConfig/delete/${id}`);
};

// appid: string;
// name: string;
// payId: number;
// payWay: null | string;
// type: string;

export interface PaymentStatusItem {
  appid: string;
  name: string;
  payId: number;
  payWay: null | string;
  type: string;
}

interface PaymentStatusResponse extends BaseResponse {
  data: PaymentStatusItem[];
}

// 控制端
interface PaymentStatusByControlResponse extends BaseResponse {
  data: {
    appid: string; // appid
    key: string; // 唯一标识
    name: string; // 支付名称
    payAmount: null | number; // 收款金额
    payId: number; // 商户id
    payWay: null | string; // 四方支付内置类型
    type: string; // 支付类型
  };
}

// 获取支付开启情况 系统代理查询使用
export const getPaymentStatus = (params: {
  type: number; // 场景1套餐  2卡片预存 3代发商品 4充值话费 5后台提现 6优惠券 7代理购买余额 8购买分池流量 9设备购买预存 10设备购买套餐 11设备购买优惠券 12网关支付 13更换设备订单 14收款额度;
  userId: string; // 后台查询直接传登录账号id     充值断查询传卡片所属账号id
}) => {
  return requestClient.get<PaymentStatusResponse>(
    '/java/front/index/getPayUse',
    { params },
  );
};

// 获取支付开启情况 控制端查询使用 /java/admin/UpdateTianPayQiUpdate/getTianPayQiUpdate?type=1
export const getPaymentStatusByControl = (params: {
  type: number; // 支付场景 1授权用户充值余额 2授权用户充值收款额度
}) => {
  return requestClient.get<PaymentStatusByControlResponse>(
    '/java/admin/UpdateTianPayQiUpdate/getTianPayQiUpdate',
    { params },
  );
};

// 获取支付所需参数(仅支持二维码) 控制端使用
export const getPayParameterByControl = (params: {
  orderNo: string; // 订单号
  orderType: string; // 支付场景 1授权用户充值余额 2授权用户收款面额
  payId: string; // 商户id
  payType: string; // 支付类型 wxpay 微信支付
  payWay?: string; // 第四方支付类型 根据支付开启情况里面的传
}) => {
  return requestClient.get<PaymentStatusByControlResponse>(
    '/java/admin/UpdateTianPayQiUpdate/getTianPayQiParameter',
    { params },
  );
};

// 创建余额订单 控制端 /java/admin/BalanceOrderUpdate/createBalanceOrder
export const createBalanceOrder = (params: {
  balanceId: string; // 余额id
}) => {
  return requestClient.post(
    `/java/admin/BalanceOrderUpdate/createBalanceOrder?balanceId=${params.balanceId}`,
    { params },
  );
};

// 创建代理预存余额充值订单
export const createAgentBalanceOrderAs = (
  userPrestoreId: string, // 预存信息id
) => {
  const formData = new FormData();
  formData.append('userPrestoreId', userPrestoreId);
  return requestClient.post(
    `/java/admin/userPrestoreOrder/addUserPrestoreOrder`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};
interface GetPayParameterParams {
  // 场景类型：1套餐 2卡片预存 3代发商品 4充值话费 5后台提现 6优惠券 7代理购买余额 8购买分池流量 9设备购买预存 10设备购买套餐 11设备购买优惠券 12网关支付 13更换设备订单 14收款额度
  orderType: string;

  // 支付方式：wxpay 微信支付 alipay支付宝支付 hfpay汇付 Mapay码支付
  payType: string;

  // openid 微信jsapi时需要传入
  openId?: string;

  // 订单号
  orderNo: string;

  // 第三方支付类型：1小程序支付 2扫码支付 3app支付 4h5支付
  type: string;

  // 第四方支付类型
  payWay?: string;

  // 商户id
  payId: string;

  // 回调域名 传当前网页域名
  notifyUrl: string;

  // 微信h5 场景类型，使用H5支付的场景：Wap、iOS、Android
  h5InfoType?: string;
}

export interface WxJsapiResponse extends BaseResponse {
  data: {
    appId: string;
    nonceStr: string;
    package: string;
    paySign: string;
    signType: string;
    timeStamp: string;
  };
}

export interface QrCodePayResponse extends BaseResponse {
  data: string;
}

export interface ZhixunPayResponse extends BaseResponse {
  data: {
    orderType: 1 | 2; // 1: 微信，2: 支付宝
    type: 1 | 2; // 1: 二维码路径，2: 跳转链接
    url: string;
  };
}

export type AnyPayResponse =
  | PaymentStatusResponse
  | QrCodePayResponse
  | WxJsapiResponse
  | ZhixunPayResponse;

// 获取支付参数 /java/pay/getPayParameter
export const getPayParameter = (params: GetPayParameterParams) => {
  return requestClient.get<AnyPayResponse>('/java/pay/getPayParameter', {
    params,
  });
};

// {"code": 1, "msg": "\u67e5\u8be2\u6210\u529f", "content": {"is_menu_open": 1, "selfmenu_info": {"button": [{"name": "\u5145\u503c\u67e5\u8be2", "sub_button": {"list": [{"type": "view", "name": "\u5145\u503c\u67e5\u8be2", "url": "https://cmp.tqzhkj.com/appAn/UserLogin"}]}}]}}}
interface WechatMenuResponse extends BaseResponse {
  content: {
    is_menu_open: number;
    selfmenu_info: {
      button: {
        name: string;
        sub_button?: {
          name: string;
          type: string;
          url: string;
        }[];
        type: string;
        url: string;
      };
    }[];
  };
}

// 获取公众号菜单
export const getWechatMenu = (public_id: number) => {
  return requestClient.get<WechatMenuResponse>(
    '/py/backend/backstage/wxmenu/',
    {
      params: {
        public_id,
      },
    },
  );
};

// 所有菜单类型
type MenuType = 'click' | 'miniprogram' | 'view';

// 通用字段
interface BaseButton {
  type: MenuType;
  name: string;
}

// click 类型
interface ClickButton extends BaseButton {
  type: 'click';
  key: string;
}

// view 类型
interface ViewButton extends BaseButton {
  type: 'view';
  url: string;
}

// miniprogram 类型
interface MiniProgramButton extends BaseButton {
  type: 'miniprogram';
  url: string;
  appid: string;
  pagepath: string;
}

// 子菜单按钮类型（不能再嵌套 sub_button）
type SubButton = ClickButton | MiniProgramButton | ViewButton;

// 一级菜单，可以是点击/跳转类型，也可以是包含子菜单的父级
type TopLevelButton =
  | (ClickButton | MiniProgramButton | ViewButton)
  | {
      name: string;
      sub_button: SubButton[];
    };

// 菜单接口主结构
// interface WechatMenu {
//   button: TopLevelButton[];
// }

// 更新公众号菜单
export const updateWechatMenu = (params: {
  content: { button: TopLevelButton[] };
  public_id: number; // 公众号id
}) => {
  return requestClient.put('/py/backend/backstage/wxmenu/', params);
};
