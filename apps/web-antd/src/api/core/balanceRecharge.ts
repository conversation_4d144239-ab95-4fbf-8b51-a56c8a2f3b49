import type { BalancePackage } from '#/components/BalanceRechargeModal/types';

import { requestClient } from '#/api/request';

/**
 * 获取余额充值包列表
 * @param userId 用户ID
 * @returns 充值包列表响应
 */
export const getBalancePackageList = (userId?: number) => {
  return requestClient.get<{
    code: number;
    data: BalancePackage[];
    msg: string;
  }>('/java/admin/BalanceOrderUpdate/getBalanceList', {
    params: { userId },
  });
};

/**
 * 创建充值订单参数
 */
export interface CreateRechargeOrderParams {
  /**
   * 充值包ID
   */
  rechargeId: number;

  /**
   * 支付方式 alipay:支付宝 wxpay:微信支付 balance:余额支付
   */
  payType: string;

  /**
   * 用户ID
   */
  userId?: number;
}

/**
 * 创建余额充值订单
 * @param params 充值订单参数
 * @returns 创建订单响应
 */
export const createRechargeOrder = (params: CreateRechargeOrderParams) => {
  return requestClient.post<{
    code: number;
    data: {
      orderNo: string;
      qrCode?: string;
    };
    msg: string;
  }>('/java/admin/webCollection/createOrder', params);
};

/**
 * 查询订单状态
 * @param orderNo 订单号
 * @returns 订单状态响应
 */
export const queryOrderStatus = (orderNo: string) => {
  return requestClient.get<{
    code: number;
    data: {
      package?: BalancePackage;
      status: number; // 1-待支付 2-支付成功 3-支付失败
    };
    msg: string;
  }>('/java/admin/webCollection/queryOrderStatus', {
    params: { orderNo },
  });
};
interface BalanceList {
  id: number;
  limitId: number;
  balance: number; // 额度面额
  giftBalancze: number; // 赠送额度
  price: number; // 购买价格
}

// 获取控制端 充值额度 /java/admin/CollectionBalanceOrderUpdate/getBalanceList
export const getBalanceList = () => {
  return requestClient.get<{
    code: number;
    data: BalanceList[];
    msg: string;
  }>('/java/admin/CollectionBalanceOrderUpdate/getBalanceList');
};

// 创建充值额度订单 /java/admin/CollectionBalanceOrderUpdate/createCollectionBalanceOrder
interface BalanceOrderList {
  id: number;
  orderName: string; // 订单名称
  systemOrdernumber: string; // 系统单号
  paymentOrdernumber: null | string;
  payId: null | number;
  payType: null | string;
  status: null | number;
  refundStatus: null | number;
  balance: number; // 收款额度
  giftBalance: number; // 赠送额度
  orderPrice: number; // 购买价格
  payAmount: null | number;
  totalPrice: number;
  userId: number;
  creationTime: string; // 创建时间
  payTime: string;
  refundTime: string;
}
export const createCollectionBalanceOrder = (params: { balanceId: number }) => {
  const formData = new FormData();
  formData.append('balanceId', params.balanceId.toString());
  return requestClient.post<{
    code: number;
    data: BalanceOrderList[];
    msg: string;
  }>(
    '/java/admin/CollectionBalanceOrderUpdate/createCollectionBalanceOrder',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};
