import type { BaseResponse } from '#/api/model';

import { requestClient } from '#/api/request';

// 检查系统更新 /java/admin/update/manage/getIsUpdate
export const checkSystemUpdateApi = () => {
  return requestClient.get<BaseResponse>(
    '/java/admin/update/manage/getIsUpdate',
  );
};

// 更新最新项目
export const updateBoot = () => {
  return requestClient.put<BaseResponse>(
    '/java/admin/update/manage/updateBoot',
  );
};

// 这里没有id  只有文件名 因为是直接获取的文件目录 所有没有id  然后删除的时候 也是传递这个文件名 才能删除
interface BackupItem {
  filename: string; // 备份文件名
  size_bytes: number; // 文件大小（字节）
  size_readable: string; // 文件大小（可读格式）
  backup_time: string; // 备份时间
  file_path: string; // 文件路径
}

interface BackupListResponse extends BaseResponse {
  data: {
    rows: BackupItem[];
    total: number;
  };
}

// 查询数据库备份/py/backend/backstage/backup/status/
export const getBackupListApi = (params: {
  backup_time?: string;
  file_path?: string;
  filename?: string;
  page: number;
  pageSize: number;
  size_bytes?: number;
  size_readable?: string;
}) => {
  return requestClient.get<BackupListResponse>(
    '/py/backend/backstage/backup/status/',
    { params },
  );
};

// 下载备份API响应类型
interface BackupFilePathResponse extends BaseResponse {
  data: {
    file_path: string;
    filename: string;
  };
}

// 下载备份 - 第一步获取文件路径
export const handleDownloadBackupApi = (filename: string) => {
  const formatData = new FormData();
  formatData.append('filename', filename);
  return requestClient.post<BackupFilePathResponse>(
    '/py/backend/backstage/backup/download/',
    formatData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};

// 下载备份 - 第二步通过文件路径下载文件
export const downloadBackupFileApi = (filePath: string) => {
  // 如果路径以./开头，去掉./
  const path = filePath.startsWith('./') ? filePath.slice(2) : filePath;
  return requestClient.get(`/${path}`, {
    responseType: 'blob',
    timeout: 300_000, // 5分钟超时
  });
};

// 删除备份 /py/backend/backstage/backup/delete/?filename=cmp_systemiot_2025-04-07_21-19-49.sql
export const handleDeleteBackupApi = (filename: string) => {
  return requestClient.delete('/py/backend/backstage/backup/delete/', {
    params: { filename },
  });
};

// 开启数据库备份 /py/backend/backstage/backup/control/
// 操作类型
// start 启动定时备份
// stop：停止定时备份
// restart：重启定时备份
// backup_now：立即执行一次备份，不影响定时任务
/**
 * 开启数据库备份
 * @param params
 * @param params.action 操作类型
 * @returns
 */
export const handleControlBackupApi = (params: {
  action: 'backup_now' | 'restart' | 'start' | 'stop';
}) => {
  const formData = new FormData();
  formData.append('action', params.action);
  return requestClient.post('/py/backend/backstage/backup/control/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// 站点基本信息 响应类型
interface TerminalSystemuserResponse extends BaseResponse {
  data: {
    appid: string;
    appSecret: string;
    balance: number; // 站点余额
    balanceLimitId: number; // 站点余额限制ID
    collectionBalance: number; // 站点收款余额
    collectionBalanceLimitId: number; // 站点收款余额限制ID
    creationMethod: number; // 创建方式
    creationTime: string; // 创建时间
    domainName: string; // 站点域名
    editionId: number;
    expirationTime: string;
    id: number;
    ip: string;
    name: string;
    password: string;
    paymentMethod: number;
    remark: string;
    status: number;
    superior: number;
    updateTime: string;
  };
}

// 站点基本信息 /java/admin/userTerminalSystemuser/getTerminalSystemuser
export const getTerminalSystemuserApi = () => {
  return requestClient.get<TerminalSystemuserResponse>(
    '/java/admin/userTerminalSystemuser/getTerminalSystemuser',
  );
};

// {
//   "code": 1,
//   "msg": "success",
//   "data": [
//       {
//           "id": 1, //服务id
//           "title": "竭诚服务20分钟", //标题
//           "desc": "服务由安生屁眼服务", //详情
//           "price": 20, //价格
//           "effectiveTime": 20, //服务时间(分钟)
//           "creationTime": "2025-06-20T20:06:54", //创建时间
//           "updateTime": "2025-06-20T20:08:09" //修改时间
//       }
//   ]
// }
interface ServiceCategoryListResponse extends BaseResponse {
  data: {
    creationTime: string;
    desc: string;
    effectiveTime: number;
    id: number;
    price: number;
    title: string;
    updateTime: string;
  }[];
}

// 获取增值服务列表 /java/admin/UpdateApi/user/serviceCategoryOrder/getCategoryList
export const getServiceCategoryListApi = () => {
  return requestClient.get<ServiceCategoryListResponse>(
    '/java/admin/UpdateApi/user/serviceCategoryOrder/getCategoryList',
  );
};

// 创建增值服务订单 /java/admin/UpdateApi/user/serviceCategoryOrder/createOrder
export const createServiceCategoryOrderApi = (params: {
  phone?: string;
  // phone 和 wxAcc 二选一，至少传一个
  serviceId: number;
  wxAcc?: string;
}) => {
  const formData = new FormData();
  if (params.phone) {
    formData.append('phone', params.phone);
  }
  if (params.wxAcc) {
    formData.append('wxAcc', params.wxAcc);
  }
  formData.append('serviceId', params.serviceId.toString());
  return requestClient.post<BaseResponse>(
    '/java/admin/UpdateApi/user/serviceCategoryOrder/createOrder',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
};
