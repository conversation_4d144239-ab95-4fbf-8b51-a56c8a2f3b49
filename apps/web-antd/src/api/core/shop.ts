import type { BaseResponse } from '#/api/model';

import { requestClient } from '#/api/request';

// 商品分类数据接口
export interface CategoryItem {
  id: number;
  name: string;
  sort: number;
  creation_time: string;
}

// 分类列表请求参数
export interface CategoryListParams {
  page: number;
  pageSize: number;
  name?: string;
  sort?: number;
}

// 分类列表响应接口
export interface CategoryListResponse extends BaseResponse {
  data: {
    rows: CategoryItem[];
    total: number;
  };
}

// 添加商品
export interface AddGoods {
  goods__name: string; // 商品名称
  goods__goods_type: '1' | '2'; // 商品类型 1: 普通商品, 2: 号卡产品
  goods__wages_type?: '1' | '2'; // 佣金方式 1: 下单立返, 2: 次月返 (仅 goods_goods_type 为 2 时传入)
  goods__main: string; // 格式为 "1,2,3,4,5,6,7,"
  goods__classification_id?: string; // 分类 ID
  goods__channel_id?: string; // 通道 ID 可为空
  goods__sales: string; // 初始销量, 用于前端展示销量
  goods__postage: string; // 邮费
  goods__description: string; // 商品描述
  goods__stock: string; // 库存
  goods__sort: string; // 排序，序号越大优先展示
  status: '1' | '2'; // 状态 1: 上架, 2: 下架
  goods__encoding: string; // 对接编码
  selling: string; // 售价
  cost: string; // 成本价
  goods__update_time: string; // 创建时间（仅返回示例）
  goods__creation_time: string; // 修改时间（仅返回示例）
  rebates?: string; // 充值端返佣金额 (例: 8 表示返佣 8 元)
}

// 获取分类列表
export const getCategoryList = (params: CategoryListParams) => {
  return requestClient.get<CategoryListResponse>(
    '/py/mall/classificationconfig/',
    { params },
  );
};

// 新增分类
export const addCategory = (data: { name: string; sort: any | null }) => {
  const formData = new FormData();
  formData.append('name', data.name);
  formData.append('sort', data.sort || 0);
  return requestClient.post<BaseResponse>(
    '/py/mall/classificationconfig/',
    formData,
  );
};

// 更新分类
export const updateCategory = (data: Omit<CategoryItem, 'creation_time'>) => {
  return requestClient.put<BaseResponse>(
    '/py/mall/classificationconfig/',
    data,
  );
};

// 删除分类
export const deleteCategory = (id: number) => {
  return requestClient.delete<BaseResponse>(
    `/py/mall/classificationconfig/?id=${id}`,
  );
};

// 商品数据接口
export interface ProductItem {
  id: number;
  goods_id: number;
  goods__classification__name: string;
  goods__name: string;
  goods__goods_type: number;
  goods__wages_type: number;
  goods__main: string;
  goods__channel__name: null | string;
  goods__sales: number;
  goods__postage: string;
  goods__description: string;
  goods__stock: number;
  goods__sort: number;
  goods__encoding: null | string;
  goods__creation_time: string;
  goods__update_time: string;
  selling: string;
  cost: string;
  goods__classification_id: number;
  goods__channel__id: null | number;
  goods__main_path: string;
  status: number;
}

// 商品列表请求参数
export interface ProductListParams {
  page: number;
  pageSize: number;
  goods__name?: string;
  goods__sort?: number;
  goods__channel__name?: string;
  goods__goods_type?: number;
  goods__wages_type?: number;
}

// 商品列表响应接口
export interface ProductListResponse extends BaseResponse {
  data: {
    rows: ProductItem[];
    total: number;
  };
}

// 获取商品列表
export const getProductList = (params: ProductListParams) => {
  return requestClient.get<ProductListResponse>('/py/mall/goodsconfig/', {
    params,
  });
};

// 添加商品
export const addGoods = (data: AddGoods) => {
  const formData = new FormData();
  Object.entries(data).forEach(([key, value]) => {
    formData.append(key, value);
  });
  return requestClient.post('/py/mall/goodsconfig/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// 修改商品 py/mall/goodsconfig/
export const updateGoods = (data: AddGoods) => {
  return requestClient.put('/py/mall/goodsconfig/', data);
};

// 删除商品 py/mall/goodsconfig/
export const deleteGoods = (id: number) => {
  // /py/mall/goodsconfig/?id=45
  return requestClient.delete(`/py/mall/goodsconfig/?id=${id}`);
};

// 查询商品图片信息 /py/mall/imginfo/
export const getGoodsImgInfo = (goods__main: string, file_path: string) => {
  return requestClient.get(
    `/py/mall/imginfo/?goods__main=${goods__main}&file_path=${file_path}`,
  );
};

// 获取商城通道列表
export const getMallChannelList = (params: {
  page: number;
  pageSize: number;
}) => {
  return requestClient.get('/py/mall/channel/', { params });
};

// 查询代理商品列表请求参数
export interface MallAllocationItem {
  goods_id: number;
  selling: string;
  cost: string;
  goods__name: string;
  user_id: number;
  goods__classification__name: string;
  current_selling: string; // 当前登录用户的售价
  current_cost: string; // 当前登录用户的成本价
  status: number; // 代理上架状态 1上架 2下架
}

// 查询代理商品列表响应接口
export interface MallAllocationListResponse extends BaseResponse {
  data: {
    rows: MallAllocationItem[];
    total: number;
  };
}

// 查询代理商品 py/mall/allocation/?page=1&pageSize=10&user_id=106
export const getMallAllocationList = (params: {
  goods__classification__name?: string;
  goods__name?: string;
  page: number;
  pageSize: number;
  user_id: number;
}) => {
  return requestClient.get<MallAllocationListResponse>('/py/mall/allocation/', {
    params,
  });
};

// 代理上架 py/mall/allocation/
export const addMallAllocation = (data: {
  cost: number; // 成本价
  goods_id: number; // 商品id
  selling: number; // 售价
  user_id: number; // 要分配的代理id
}) => {
  const formData = new FormData();
  formData.append('cost', data.cost.toString());
  formData.append('goods_id', data.goods_id.toString());
  formData.append('selling', data.selling.toString());
  formData.append('user_id', data.user_id.toString());
  return requestClient.post('/py/mall/allocation/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

// 修改 /py/mall/allocation/
export const updateMallAllocation = (data: MallAllocationItem) => {
  return requestClient.put('/py/mall/allocation/', data);
};

// 回收 /py/mall/allocation/
export const recycleMallAllocation = (data: {
  goods_id: number;
  user_id: number;
}) => {
  return requestClient.delete('/py/mall/allocation/', { params: data });
};

// /py/mall/h5getcarouselchartview/ 获取轮播图
// export const getCarouselChart = () => {
//   return requestClient.get('/py/mall/h5getcarouselchartview/');
// };
