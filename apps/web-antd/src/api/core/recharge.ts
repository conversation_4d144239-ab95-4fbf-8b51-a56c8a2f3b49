import type { BaseResponse } from '#/api/model';

import { requestClient } from '#/api/request';

// 充值配置列表参数接口
export interface RechargeConfigListParams {
  page: number;
  pageSize: number;
  frontName?: string;
  balancePayVerification?: number;
  type?: number;
  status?: number;
}

// 充值配置数据接口
export interface RechargeConfigItem {
  id: number;
  frontName: string;
  customerServiceConnection: string;
  frontTips: string;
  balancePayVerification: number;
  type: number;
  status: number;
  wxPublicId: number;
  appId: null | string;
  tutorialLink: null | string;
  creationTime: string;
  updateTime: string;
  phoneLoginVerification: number;
}

// 充值配置列表响应接口
export interface RechargeConfigListResponse extends BaseResponse {
  data: {
    rows: RechargeConfigItem[];
    total: number;
  };
}

// 获取充值配置列表
export const getRechargeConfigList = (params: RechargeConfigListParams) => {
  return requestClient.get<RechargeConfigListResponse>(
    '/java/admin/frontSetup/manage/getFrontSetupList',
    { params },
  );
};

// 新增充值配置
export const addRechargeConfig = (data: Omit<RechargeConfigItem, 'id'>) => {
  return requestClient.post<BaseResponse>(
    '/java/admin/frontSetup/manage/addFrontSetup',
    data,
  );
};

// 更新充值配置
export const updateRechargeConfig = (data: RechargeConfigItem) => {
  return requestClient.put<BaseResponse>(
    '/java/admin/frontSetup/manage/editFrontSetup',
    data,
  );
};

// 删除充值配置
export const deleteRechargeConfig = (id: number) => {
  return requestClient.delete<BaseResponse>(
    `/java/admin/frontSetup/manage/deleteFrontSetup/${id}`,
  );
};
