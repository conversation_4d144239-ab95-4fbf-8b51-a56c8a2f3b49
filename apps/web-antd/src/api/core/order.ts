import { requestClient } from '#/api/request';

/** 订单列表数据类型 */
export interface OrderData {
  id: number;
  status: number; // 状态
  nameStatus: number; // 实名状态
  orderName: string; // 订单名称
  systemOrdernumber: string; // 系统订单号
  upstreamOrdernumber: null | string; // 上游订单号
  paymentOrdernumber: null | string; // 支付订单号
  userAccount: string; // 用户账号
  voidNumber: string; // 空号
  packagePrice: number; // 套餐价格
  packageCost: number; // 套餐成本
  paidInAmount: number; // 实付金额
  orderProfit: number; // 订单利润
  orderState: number; // 订单状态
  orderPayment: number; // 支付方式
  orderIp: string; // 下单IP
  rechargeAddress: string; // 充值地区
  creationTime: string; // 创建时间
  rechargeTime: null | string; // 充值时间
  takeeffectTime: null | string; // 生效时间
  expirationTime: null | string; // 过期时间
  refundTime: null | string; // 退款时间
  errorLog: string; // 错误日志
  firstStatus: number; // 首充状态
  refundStatus: number; // 退款状态
  apiName: string; // API名称
  takeeffectType: number; // 生效类型 1立即生效 2次月生效 3客户自选
}

/** 订单详情数据类型 */
export interface OrderDetailData {
  status: null | number; // 状态
  nameStatus: null | number; // 实名状态
  id: number; // 订单ID
  orderName: string; // 订单名称
  systemOrdernumber: string; // 系统订单号
  upstreamOrdernumber: null | string; // 上游订单号
  paymentOrdernumber: null | string; // 支付订单号
  cardUserId: number; // 卡片用户ID
  userId: number; // 用户ID
  userAccount: string; // 用户账号
  voidNumber: string; // 空号
  cardId: number; // 卡片ID
  packageId: number; // 套餐ID
  configId: null | number; // 配置ID
  apiName: string; // API名称
  packageGroupId: number; // 套餐组ID
  groupName: null | string; // 组名称
  packageCost: number; // 套餐成本
  packagePrice: number; // 套餐价格
  paidInAmount: number; // 实付金额
  orderProfit: number; // 订单利润
  adminPrice: number; // 管理价格
  adminCost: number; // 管理成本
  adminProfit: number; // 管理利润
  discountAmount: number; // 折扣金额
  discountType: number; // 折扣类型
  certificateId: null | number; // 证书ID
  takeeffectType: number; // 生效类型
  orderState: number; // 订单状态
  orderCount: number; // 订单数量
  payId: number; // 支付ID
  openId: null | string; // 开放ID
  orderPayment: number; // 支付方式
  orderIp: string; // 下单IP
  rechargeAddress: string; // 充值地区
  creationTime: string; // 创建时间
  rechargeTime: null | string; // 充值时间
  takeeffectTime: null | string; // 生效时间
  expirationTime: null | string; // 过期时间
  refundTime: null | string; // 退款时间
  errorLog: string; // 错误日志
  firstStatus: number; // 首充状态
  refundStatus: number; // 退款状态
}

/** 订单详情响应 */
export interface OrderDetailResponse {
  code: number;
  msg: string;
  data: OrderDetailData;
  orderNo: null;
}

/** 订单列表响应 */
export interface OrderListResponse {
  code: number;
  msg: string;
  data: {
    rows: OrderData[];
    total: number;
  };
  orderNo: null;
}

/** 订单列表查询参数 */
export interface OrderListParams {
  page: number;
  pageSize: number;
  systemOrdernumber?: string;
  userAccount?: string;
  orderState?: number;
  orderPayment?: number;
  orderIp?: string;
  rechargeAddress?: string;
  cardUserId?: number;
  takeeffectType?: number;
  creationTime?: string[];
  userId?: number;
  configId?: number;
  packageGroupId?: number;
  orderName?: string;
  paymentOrdernumber?: string;
  upstreamOrdernumber?: string;
  multiCardQuery?: boolean;
  cardNos?: string;
  startCardNo?: string;
  endCardNo?: string;
}

/** 订单状态选项 */
export const OrderStateOptions = [
  { label: '等待递交', value: 1 },
  { label: '成功', value: 2 },
  { label: '失败', value: 3 },
  { label: '待支付', value: 4 },
  { label: '已过期', value: 5 },
  { label: '已退款', value: 6 },
];

/** 实名状态选项 */
export const NameStatusOptions = [
  { label: '未知', value: 1 },
  { label: '未实名', value: 2 },
  { label: '已实名', value: 3 },
];

/** 首充状态选项 */
export const FirstStatusOptions = [
  { label: '否', value: 1 },
  { label: '是', value: 2 },
];

/** 退款状态选项 */
export const RefundStatusOptions = [
  { label: '未退款', value: 1 },
  { label: '已退款', value: 2 },
];

/** 支付方式选项 */
export const PaymentOptions = [
  { label: '未支付', value: 0 },
  { label: '余额支付', value: 1 },
  { label: '批量充值', value: 2 },
  { label: '后台单冲', value: 3 },
  { label: '接口递交', value: 4 },
  { label: '微信支付', value: 5 },
  { label: '支付宝支付', value: 6 },
  { label: '点卡充值', value: 7 },
  { label: '自动订购', value: 8 },
  { label: '汇付', value: 9 },
  { label: '火脸', value: 10 },
];

/** 补单参数 */
export interface ReplenishOrderParams {
  orderId: string; // 订单id, 多个逗号隔开
  type: 0 | 1 | 2; // 补单类型：0只修改状态 1不提交api 2提交api
}

/** 退款参数 */
export interface RefundOrderParams {
  orderId: string; // 订单Id
  type: 0 | 1 | 2 | 3; // 退款类型：0不退金额 1原路退回 2退回余额 3抹除佣金
  amount: number; // 退款金额
  msg?: string; // 退款描述备注
  amountType: 0 | 1; // 退款金额类型：0退款金额 1退款百分比
}

/** 获取订单列表 */
export function getOrderListApi(params: OrderListParams) {
  return requestClient.get<OrderListResponse>(
    '/java/admin/packageOrder/getOrderList',
    { params },
  );
}

// 订单导出 /java/admin/packageOrder/excelPackageOrderList?startCardNo=111&export=1
export function exportOrderApi(params: OrderListParams) {
  return requestClient.get<OrderListResponse>(
    '/java/admin/packageOrder/excelPackageOrderList',
    {
      params: {
        ...params,
        export: 1,
      },
    },
  );
}

/** 补单 */
export function replenishOrderApi(params: ReplenishOrderParams) {
  const fromData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    fromData.append(key, value.toString());
  });
  return requestClient.put<OrderListResponse>(
    '/java/admin/packageOrder/manage/repairOrder',
    fromData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

/** 退款 */
export function refundOrderApi(params: RefundOrderParams) {
  const fromData = new FormData();
  Object.entries(params).forEach(([key, value]) => {
    fromData.append(key, value.toString());
  });
  return requestClient.put<OrderListResponse>(
    '/java/admin/packageOrder/refundOrder',
    fromData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );
}

// 批量退款 /java/admin/packageOrder/importRefundOrder
export function batchRefundOrderApi(params: {
  file: File; // 文件
  msg?: string; // 退款描述备注
  type: 0 | 1 | 2 | 3; // 退款类型：0不退金额 1原路退回 2退回余额 3抹除
}) {
  const fromData = new FormData();
  fromData.append('file', params.file);
  fromData.append('msg', params.msg || '');
  fromData.append('type', params.type.toString());
  return requestClient.post<OrderListResponse>(
    '/java/admin/packageOrder/importRefundOrder',
    fromData,
    {
      headers: { 'Content-Type': 'multipart/form-data' },
    },
  );
}

/** 删除订单 */
export function deleteOrderApi(id: number) {
  return requestClient.delete<OrderListResponse>(
    `/java/admin/packageOrder/${id}`,
  );
}

// 批量退款模版下载 /java/admin/exportModel/ExportAllocationGlobal
export function exportAllocationGlobalApi() {
  return requestClient.get<OrderListResponse>(
    '/java/admin/exportModel/ExportAllocationGlobal',
    {
      responseType: 'blob',
    },
  );
}

interface UserOption {
  id: number;
  name: string;
  opennessApi?: number;
}

interface ChannelOption {
  id: number;
  name: string;
}

interface PackageRuleOption {
  id: number;
  name: string;
}

// 修改 API 请求方法的返回类型
export async function getUserListApi() {
  return requestClient.get<UserOption[]>('/java/admin/users/getUserList');
}

export async function getChannelListApi() {
  return requestClient.get<ChannelOption[]>('/java/admin/config/getSelect');
}

export async function getPackageRuleListApi() {
  return requestClient.get<PackageRuleOption[]>(
    '/java/admin/packageGroup/getPackageGroupOption',
  );
}

/** 获取订单详情 */
export function getOrderDetailApi(orderId: number) {
  return requestClient.get<OrderDetailResponse>(
    '/java/admin/packageOrder/manage/getOrderData',
    {
      params: { orderId },
    },
  );
}

/** 报文详情数据类型 */
export interface OrderMessage {
  id: string;
  requestName: string;
  orderId: number;
  requestUrl: string;
  bodyType: string;
  requestBody: string;
  requestHeader: string;
  requestQuery: null | string;
  returnParameter: string;
  requestTime: string;
}

/** 报文详情响应 */
export interface OrderMessageResponse {
  code: number;
  msg: string;
  data: {
    rows: OrderMessage[];
    total: number;
  };
  orderNo: null;
}

/** 获取订单报文详情 */
export function getOrderMessageApi(orderId: number) {
  return requestClient.get<OrderMessageResponse>(
    '/java/admin/packageOrder/manage/getRequestLog',
    {
      params: {
        orderId,
        page: 1,
        pageSize: 10,
      },
    },
  );
}

// 换卡订单数据类型
export interface ChangeOrderData {
  id: number;
  systemOrdernumber: string; // 系统单号
  paymentOrdernumber: null | string; // 支付单号
  userId: number;
  deviceId: null | number;
  orderPrice: number; // 换卡费用
  paidInAmount: number; // 实际付款
  orderState: number; // 订单状态 1已支付 2无需支付 3未支付 4已退款
  orderPayment: number; // 订购方式0未知 1微信支付 2支付宝  3斗拱聚合 4火脸  5斗拱汇付 6小马哥
  payId: null | number;
  orderIp: string; // 订单IP
  rechargeAddress: string; // 支付地址
  rechargeTime: null | string; // 支付时间
  creationTime: string; // 创建时间
  refundTime: null | string; // 退款时间
  changeConfigId: number; // 换卡配置id
  recipientName: string; // 收件人姓名
  recipientPhone: string; // 电话
  recipientProvince: string; // 省
  recipientMarket: string; // 市
  recipientDistinguish: string; // 区
  detailedAddress: string; // 详细地址
  oidCardNo: string; // 旧卡
  newCardNo: null | string; // 新卡
  trackingNumber: null | string; // 快递单号
  changeType: number; // 更换类型 1卡片 2设备
  userAccount: string; // 卡片所属账号
  // id: number;
  // order_number: string; // 平台订单号
  // pay_number: string; // 支付订单号
  // card_id: number; // 旧卡号id
  // card__void_number: string; // 旧卡号
  // new_card_id: number; // 新卡号id
  // new_card__void_number: string; // 新卡号
  // user_id: number; // 代理id
  // user__user_name: string; // 代理名称
  // cost: number; // 换卡费用
  // actual_price: number; // 实付金额
  // name: string; // 收件人姓名
  // phone: string; // 手机号
  // address: string; // 收件人地址
  // detailed_address: string; // 详细地址
  // pay_status: 1 | 2; // 支付状态 1已支付 2未支付
  // status: 1 | 2; // 发货状态 1已发货 2未发货
  // express_number: string; // 快递单号
  // express_company: string; // 快递公司
}

// 换卡订单查询参数
export interface ChangeOrderListParams {
  page: number;
  pageSize: number;
  order_number?: string; // 平台订单号
  pay_number?: string; // 支付订单号
  card__void_number?: string; // 旧卡号
  new_card__void_number?: string; // 新卡号
  user_id?: number; // 代理id
  name?: string; // 收件人姓名
  phone?: string; // 手机号
  address?: string; // 收件人地址
  detailed_address?: string; // 详细地址
}

// 获取换卡订单列表
export function getChangeOrderListApi(params: ChangeOrderListParams) {
  return requestClient.get<{
    code: number;
    data: {
      rows: ChangeOrderData[];
      total: number;
    };
    msg: string;
    // }>('/py/backend/backstage/replacement/order/', { params });
  }>('/java/admin/changeCardApplyOrder/manage/getList', { params });
}

// 换卡订单发货
export function changeCardOrderDeliveryApi(params: {
  newCardNo: string; // 新卡号
  orderId: number; // 订单id
  trackingNumber: string; // 快递单号
}) {
  return requestClient.post<OrderListResponse>(
    '/java/admin/changeCardApplyOrder/manage/shippingSettings',
    params,
  );
}

// 导出换卡列表
export function exportOperateLogApi(params: any) {
  return requestClient.get<OrderListResponse>(
    '/java/admin/ChangeCardApplyOrder/manage/exportOperateLog',
    { params: { ...params, export: 1 } },
  );
}
