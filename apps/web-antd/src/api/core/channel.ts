import type { BaseResponse } from '#/api/request';

import { requestClient } from '#/api/request';

export interface ChannelItem {
  id: number;
  apiName: string;
  notes: string;
  apiType: number;
  apiPolling: number;
  pollingCardType: number;
  stopType: number;
  rePolling: number;
  singleCardSleep: number;
  apiOrderSubmission: number;
  orderMachine: number;
  apiPlatform: string;
  apiVersion: string;
  apiBalance: number;
  apiCardType: number;
  apiSources: number;
  apiAccount: string;
  apiPassword: string;
  apiKey?: string;
  apiKey2?: string;
  requestAddress: string;
  creationTime: string;
  updateTime: string;
  changeCard: number;
  cardCount: number;
  groupCount: number;
}

export interface ChannelListParams {
  page: number;
  pageSize: number;
  apiName?: string;
  apiPlatform?: string;
  apiType?: number;
  userId?: number;
}

export interface ChannelListResponse {
  code: number;
  msg: string;
  data: {
    rows: ChannelItem[];
    total: number;
  };
  orderNo: null;
}

export interface ChannelDetailResponse {
  code: number;
  msg: string;
  data: ChannelItem;
  orderNo: null;
}

// enum Api {
//   List = '/java/admin/config/manage/list',
// }

export const getChannelList = (params: ChannelListParams) => {
  return requestClient.get<ChannelListResponse>(
    '/java/admin/config/manage/list',
    {
      params,
    },
  );
};

export const getChannelDetail = (id: number) => {
  return requestClient.get<ChannelDetailResponse>(
    `/java/admin/config/manage/getConfig`,
    { params: { id } },
  );
};

interface PlatformItem {
  id: null | string;
  name: string; // 名称
  type: string; // 平台类型 1流量池 2物联卡 3行业卡 4NB卡
  prompt: string; // 提示
}

export interface PlatformResponse extends BaseResponse {
  data: PlatformItem[];
}

// 获取对接平台 /java/admin/ApiVersion/manage/getPlatform
export const getPlatform = () => {
  return requestClient.get<PlatformResponse>(
    '/java/admin/ApiVersion/manage/getPlatform',
  );
};

// 修改版本相关接口定义
export interface ApiVersionOption {
  label: string;
  value: string;
}

interface ApiVersionResponseData {
  id: null | number;
  platformIdentifying: string; // 版本标识
  versionName: string; // 版本名称
  versionId: null | number;
  prompt: string; // 提示
}

export interface ApiVersionResponse extends BaseResponse {
  data: ApiVersionResponseData[];
}

// 获取对接版本列表
export const getApiVersions = (platform: string) => {
  return requestClient.get<ApiVersionResponse>(
    '/java/admin/ApiVersion/manage/getByPlatformVersion',
    { params: { platform } },
  );
};

export interface ChannelFormData {
  apiName: string;
  apiAccount: string;
  apiPassword: string;
  apiKey?: string;
  apiKey2?: string;
  apiKey3?: string;
  apiKey4?: string;
  requestAddress: string;
  requestAddress2?: string;
  apiType: number;
  apiCardType: number;
  apiSources: number;
  apiPlatform: string;
  apiVersion?: string; // 改为可选
  stopType: number;
  apiPolling: number;
  singleCardSleep: number;
  apiOrderSubmission: number;
  notes?: string;
  pollingCardType: number;
  rePolling: number;
  orderMachine: number;
}

interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
  orderNo: null | string;
}

export interface CreateChannelData {
  apiName: string;
  apiPlatform: string;
  apiVersion?: string;
  apiAccount: string;
  apiPassword: string;
  apiKey?: string;
  apiKey2?: string;
  apiSources: number;
  apiType: number;
  apiCardType: number;
  apiOrderSubmission: number;
  apiPolling: number;
  singleCardSleep: number;
  notes?: string;
  pollingCardType: number;
  rePolling: number;
  orderMachine: number;
  requestAddress: string;
}

export function createChannel(params: CreateChannelData) {
  return requestClient.post<ApiResponse>(
    '/java/admin/config/manage/add',
    params,
  );
}

export function updateChannel(id: number, params: CreateChannelData) {
  return requestClient.put<ApiResponse>('/java/admin/config/manage/edit', {
    ...params,
    id,
  });
}

export interface VerifyChannelParams {
  id: number;
  apiName: string;
  cardNo: string;
}

export function verifyChannel(params: VerifyChannelParams) {
  const formData = new FormData();
  formData.append('id', String(params.id));
  formData.append('apiName', params.apiName);
  formData.append('cardNo', params.cardNo);

  return requestClient.put<ApiResponse>(
    '/java/admin/config/manage/verifyDocking',
    formData,
  );
}

export function copyChannel(id: number) {
  const formData = new FormData();
  formData.append('id', String(id));

  return requestClient.post<ApiResponse>(
    '/java/admin/config/manage/copyApi',
    formData,
  );
}

// 导出卡片渠道列表(已整改到任务中心)
export function excelApiConfigList(params: ChannelListParams) {
  return requestClient.get<ApiResponse>(
    '/java/admin/config/excelApiConfigList',
    { params },
  );
}

export function deleteChannel(id: number) {
  return requestClient.delete<ApiResponse>(
    `/java/admin/config/manage/delete/${id}`,
  );
}
