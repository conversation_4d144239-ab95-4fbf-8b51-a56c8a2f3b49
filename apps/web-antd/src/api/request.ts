/**
 * 该文件可自行根据业务逻辑进行调整
 */
import type { HttpResponse } from '@vben/request';

import { useAppConfig } from '@vben/hooks';
import { preferences } from '@vben/preferences';
import {
  authenticateResponseInterceptor,
  errorMessageResponseInterceptor,
  RequestClient,
} from '@vben/request';
import { useAccessStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import md5 from 'md5';

import { useAuthStore } from '#/store';

import { refreshTokenApi } from './core';

const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

export interface BaseResponse {
  code: number;
  msg: string;
  data: any;
  orderNo: null;
}

function createRequestClient(baseURL: string) {
  const client = new RequestClient({
    baseURL,
  });

  /**
   * 重新认证逻辑
   */
  async function doReAuthenticate() {
    console.warn('Access token or refresh token is invalid or expired. ');
    const accessStore = useAccessStore();
    const authStore = useAuthStore();
    accessStore.setAccessToken(null);
    if (
      preferences.app.loginExpiredMode === 'modal' &&
      accessStore.isAccessChecked
    ) {
      accessStore.setLoginExpired(true);
    } else {
      await authStore.logout();
    }
  }

  /**
   * 刷新token逻辑
   */
  async function doRefreshToken() {
    const accessStore = useAccessStore();
    const resp = await refreshTokenApi();
    const newToken = resp.data;
    accessStore.setAccessToken(newToken);
    return newToken;
  }

  function formatToken(token: null | string) {
    // return token ? `Bearer ${token}` : null;
    return token ? `${token}` : null;
  }

  // 请求头处理
  client.addRequestInterceptor({
    fulfilled: async (config) => {
      const accessStore = useAccessStore();
      // 获取当前时间的11位数时间戳（毫秒）
      const timestamp = Date.now();
      const url = config?.url;
      if (url.includes('/java')) {
        config.headers.Token = formatToken(accessStore.accessToken);
      } else if (url.includes('/py')) {
        config.headers.Authorization = formatToken(accessStore.accessToken);
      }

      // config.headers.Authorization = formatToken(accessStore.accessToken);
      // config.headers.Token = formatToken(accessStore.accessToken);
      config.headers['Accept-Language'] = preferences.app.locale;
      config.headers['Conten-Date'] = timestamp;
      config.headers['Conten-Zx'] = md5(timestamp.toString());
      return config;
    },
  });

  // response数据解构
  client.addResponseInterceptor<HttpResponse>({
    fulfilled: async (response) => {
      const { data: responseData, status } = response;

      // 如果是 blob 类型的响应，直接返回
      if (response.config.responseType === 'blob') {
        return response;
      }

      const { code, msg } = responseData;

      // 处理token过期的情况
      if (code === 0 && msg === 'NOT_LOGIN') {
        await doReAuthenticate(); // 触发重新认证
        throw Object.assign(new Error('Token expired'), { response });
      }

      if (
        [
          '/java/admin/users/getIsCheckCode',
          '/java/admin/users/getUserBindPhoneNumberTp1',
          '/py/backend/backstage/capwd/',
          '/java/monitor/error-history',
          '/java/monitor/data',
          '/java/monitor/ip-stats',
          '/java/monitor/call-details',
        ].some((url) => response.config.url.includes(url))
      ) {
        return responseData;
      }

      // 处理正常响应
      if (status >= 200 && status < 400 && code === 1) {
        return responseData;
      }

      if (code === -1) {
        throw Object.assign({}, response, { response });
      }

      throw Object.assign({}, response, { response });
    },
  });

  // token过期的处理
  client.addResponseInterceptor(
    authenticateResponseInterceptor({
      client,
      doReAuthenticate,
      doRefreshToken,
      enableRefreshToken: preferences.app.enableRefreshToken,
      formatToken,
    }),
  );

  // 通用的错误处理,如果没有进入上面的错误处理逻辑，就会进入这里
  client.addResponseInterceptor(
    errorMessageResponseInterceptor((msg: string, error) => {
      const responseData = error?.response?.data ?? {};
      const { code, msg: responseMsg } = responseData;

      // 处理业务错误码
      if (code === -1) {
        message.error(responseMsg || '操作失败');
        return;
      }

      if (code === 0) {
        message.error(responseMsg || '操作失败');
        return;
      }

      // 处理 HTTP 状态码错误
      const errorMessage = responseData?.error ?? responseData?.message ?? '';
      message.error(errorMessage || msg);
    }),
  );

  return client;
}

export const requestClient = createRequestClient(apiURL);

export const baseRequestClient = new RequestClient({ baseURL: apiURL });
