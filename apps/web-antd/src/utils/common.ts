/**
 * 将对象转换为FormData
 * @param data 要转换的对象
 * @param options 配置选项
 * @returns FormData对象
 */
export function objectToFormData(
  data: Record<string, any>,
  options: {
    // 是否自动将数字转为字符串
    autoStringify?: boolean;
    // 特殊字段处理函数
    customTransform?: Record<string, (value: any) => any>;
  } = {},
): FormData {
  const formData = new FormData();
  const { autoStringify = true, customTransform = {} } = options;

  Object.entries(data).forEach(([key, value]) => {
    // 跳过undefined和null值
    if (value === undefined || value === null) return;

    // 应用自定义转换
    if (customTransform[key]) {
      value = customTransform[key](value);
    }
    // 自动将数字转为字符串
    else if (autoStringify && typeof value === 'number') {
      value = String(value);
    }
    // 处理数组和对象
    else if (
      typeof value === 'object' &&
      !(value instanceof File || value instanceof Blob)
    ) {
      value = JSON.stringify(value);
    }

    formData.append(key, value);
  });

  return formData;
}
