import { getDomainName } from './domain';

/**
 * 获取完整的文件URL
 * @param url 文件相对路径
 * @returns 完整的文件URL
//  */
// export function getFileUrl(url: string) {
//   if (!url) return '';
//   if (url.startsWith('http')) return url;
//   const domainName = getDomainName();
//   // return `${window.location.origin}/api/py/${url}`;
//   // return `${window.location.origin}/py/${url}`;
//   return `${domainName}/py/${url}`;
// }

export function getFileUrl(url: string, type = 'py') {
  if (!url) return '';
  if (url.startsWith('http')) return url;
  // const domainName = type === 'py' ? getDomainName() : 'http://1.12.37.50';
  // const domainName = type === 'py' ? getDomainName() : 'https://cmp.tqzhkj.com';
  const domainName = getDomainName();
  // return `${window.location.origin}/api/py/${url}`;
  // return `${window.location.origin}/py/${url}`;
  const urlPy = `${domainName}/py/${url}`;
  const urlJavr = `${domainName}${url}`;
  return type === 'py' ? urlPy : urlJavr;
  // return `${domainName}${type === 'py' ? '/py' : ''}/${url}`;
}
