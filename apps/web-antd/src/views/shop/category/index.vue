<script lang="ts" setup>
import type { CycleTemplate } from '#/api/core/cycle';
import type { CategoryItem } from '#/api/core/shop';

import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, Form, Input, InputNumber, message, Modal } from 'ant-design-vue';

import {
  addCategory,
  deleteCategory,
  getCategoryList,
  updateCategory,
} from '#/api/core/shop';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { columns, searchItems } from './config';

// 使用 useTable hook
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable<CycleTemplate, { page: number; pageSize: number }>({
  api: getCategoryList,
  defaultParams: {},
});

// 状态定义
const formRef = ref();
const modalVisible = ref(false);
const modalTitle = ref('新增分类');
const formData = ref<Partial<CategoryItem>>({});
const isEdit = ref(false);

// 处理新增/编辑
const handleSubmit = async () => {
  try {
    const values = await formRef.value.validateFields();
    if (isEdit.value) {
      await updateCategory({ ...values, id: formData.value.id! });
      message.success('更新成功');
    } else {
      await addCategory(values);
      message.success('添加成功');
    }
    modalVisible.value = false;
    getList();
  } catch (error) {
    console.error(error);
  }
};

// 处理新增
const handleAdd = () => {
  isEdit.value = false;
  modalTitle.value = '新增分类';
  formData.value = {};
  modalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: CategoryItem) => {
  isEdit.value = true;
  modalTitle.value = '编辑分类';
  formData.value = { ...record };
  modalVisible.value = true;
};

// 处理删除
const handleDelete = (record: CategoryItem) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该分类吗？',
    async onOk() {
      try {
        await deleteCategory(record.id);
        message.success('删除成功');
        getList();
      } catch {
        message.error('删除失败');
      }
    },
  });
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    text: '新增分类',
    type: 'primary' as const,
    icon: MdiPlus,
    onClick: handleAdd,
  },
];

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="shop-category p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :custom-buttons="toolbarButtons"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 新增/编辑弹窗 -->
    <Modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleSubmit"
    >
      <Form
        ref="formRef"
        :model="formData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <Form.Item
          label="分类名称"
          name="name"
          :rules="[{ required: true, message: '请输入分类名称' }]"
        >
          <Input v-model:value="formData.name" placeholder="请输入分类名称" />
        </Form.Item>
        <Form.Item label="排序" name="sort">
          <InputNumber
            v-model:value="formData.sort"
            :min="0"
            :precision="0"
            style="width: 100%"
          />
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.shop-category {
  background-color: var(--background-deep);
}
</style>
