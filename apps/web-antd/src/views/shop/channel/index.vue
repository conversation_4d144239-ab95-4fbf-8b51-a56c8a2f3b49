<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';

import { columns, searchItems } from './config';

// 状态定义
const loading = ref(false);
const searchParams = ref({});

// 表格数据
const tableData = ref<any[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    // TODO: 调用API获取数据
    // const res = await getChannelList({
    //   page: page.value,
    //   pageSize: pageSize.value,
    //   ...searchParams.value,
    // });
    // if (res.code === 1) {
    //   tableData.value = res.data.rows;
    //   total.value = res.data.total;
    // }
  } catch (error) {
    console.error('获取列表失败:', error);
    message.error('获取列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理表格变化
const handleTableChange = (pagination: any) => {
  page.value = pagination.current;
  pageSize.value = pagination.pageSize;
  loadData();
};

// 处理搜索
const handleSearch = () => {
  page.value = 1;
  loadData();
};

// 处理重置
const handleReset = () => {
  page.value = 1;
  searchParams.value = {};
  loadData();
};

// 处理新增
const handleAdd = () => {
  // TODO: 实现新增逻辑
};

// 处理编辑
const handleEdit = (record: any) => {
  // TODO: 实现编辑逻辑
};

// 处理删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该通道吗？',
    async onOk() {
      try {
        // TODO: 调用删除API
        // const res = await deleteChannel(record.id);
        // if (res.code === 1) {
        //   message.success('删除成功');
        //   loadData();
        // }
      } catch {
        message.error('删除失败');
      }
    },
  });
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    text: '新增通道',
    type: 'primary' as const,
    icon: MdiPlus,
    onClick: handleAdd,
  },
];

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="shop-channel p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :custom-buttons="toolbarButtons"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="{
          total,
          current: page,
          pageSize,
          showSizeChanger: true,
          showQuickJumper: true,
        }"
        @change="handleTableChange"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.shop-channel {
  background-color: var(--background-deep);
}
</style>
