import type { AddGoods, ProductItem } from '#/api/core/shop';
import type { FormGroup } from '#/hooks/useAnsheng/types';
import type { UseImageSelectorOptions } from '#/hooks/web/useImageSelector';

import { defineComponent, h, onMounted, ref, watch } from 'vue';

import { Card, Col, Form, Input, InputNumber, Select } from 'ant-design-vue';

import {
  addGoods,
  getCategoryList,
  getGoodsImgInfo,
  getMallChannelList,
  updateGoods,
} from '#/api/core/shop';
import RichTextEditor from '#/components/RichTextEditor/index.vue';
import { useAnsheng } from '#/hooks/useAnsheng';
import { useImageSelector } from '#/hooks/web/useImageSelector';

// 商品表单接口定义
export interface ProductFormData {
  id?: number;
  goods__name: string;
  goods__goods_type: '1' | '2';
  goods__wages_type?: '1' | '2';
  goods__main: string;
  goods__classification_id?: string;
  goods__channel_id?: string;
  goods__sales: string;
  goods__postage: string;
  goods__description: string;
  goods__stock: string;
  goods__sort: string;
  status: '1' | '2';
  goods__encoding: string;
  selling: string;
  cost: string;
  rebates?: string;
  goods__main_path?: string;
  goods__detail_images?: string[];
  goods__detail_image_paths?: string[];
}

// useProductModal hook 选项
export interface UseProductModalOptions {
  onSuccess?: () => void;
  onCancel?: () => void;
}

// useProductModal hook 定义
export function useProductModal(options: UseProductModalOptions = {}) {
  // 基础选项配置
  const categoryOptions = ref<{ label: string; value: string }[]>([]);
  const channelOptions = ref<{ label: string; value: string }[]>([]);
  const goodsTypeOptions = [
    { label: '普通商品', value: '1' },
    { label: '号卡产品', value: '2' },
  ];
  const wagesTypeOptions = [
    { label: '下单立返', value: '1' },
    { label: '次月返', value: '2' },
  ];
  const statusOptions = [
    { label: '上架', value: '1' },
    { label: '下架', value: '2' },
  ];

  // 保存当前编辑的商品对象
  const currentEditProduct = ref<null | ProductItem>(null);

  // 主图选择器
  const {
    renderImageSelector: renderMainImageSelector,
    ImagePreviewModal: MainImagePreviewModal,
    imageUrl: mainImageUrl,
    selectedImageObject: _mainImageObject,
  } = useImageSelector({
    mode: 'gallery',
    multiple: false,
    defaultImageUrl: '',
  } as UseImageSelectorOptions);

  // 详情图选择器
  const {
    renderImageSelector: renderDetailImagesSelector,
    ImagePreviewModal: DetailImagesPreviewModal,
    selectedImageObjects: selectedDetailImageObjects,
    imageUrls: detailImageUrls,
  } = useImageSelector({
    mode: 'gallery',
    multiple: true,
    maxCount: 10,
    defaultImageUrls: [],
  } as UseImageSelectorOptions);

  // 重置图片选择器
  const resetImageSelectors = () => {
    // 重置主图
    mainImageUrl.value = '';
    // 重置详情图
    selectedDetailImageObjects.value = [];
    detailImageUrls.value = [];
  };

  // 使用useAnsheng中的form部分
  const {
    form: {
      formData,
      visible,
      isEdit,
      show,
      renderFormModal,
      setFieldVisibility,
      renderField,
      setRules,
      isFullscreen,
      toggleFullscreen,
      loading,
    },
  } = useAnsheng<ProductFormData>({
    formOptions: {
      title: (isEdit) => (isEdit ? '编辑商品' : '添加商品'),
      width: 900,
      defaultValues: {
        goods__name: '',
        goods__goods_type: '1',
        goods__wages_type: '1',
        goods__classification_id: undefined,
        goods__channel_id: undefined,
        goods__sales: '0',
        goods__postage: '0',
        goods__description: '',
        goods__stock: '0',
        goods__sort: '0',
        status: '1',
        goods__encoding: '',
        selling: '0',
        cost: '0',
        rebates: '0',
        goods__main: '',
        goods__main_path: '',
        goods__detail_images: [],
        goods__detail_image_paths: [],
      },
      // 创建方法
      create: async (params) => {
        try {
          // 处理主图和详情图，合并为一个字符串
          const mainImageId = params.goods__main || '';
          const detailImageIds = params.goods__detail_images || [];

          // 合并主图ID和详情图ID，确保主图在前
          const allImageIds = [mainImageId, ...detailImageIds]
            .filter(Boolean)
            .join(',');

          const data = {
            ...params,
            goods__main: allImageIds, // 覆盖原始的goods__main值
          } as unknown as AddGoods;

          const res = await addGoods(data);
          return res;
        } catch (error) {
          console.error('添加商品出错:', error);
          return { code: 0, msg: '添加失败，请重试' };
        }
      },
      // 更新方法
      update: async (id, params) => {
        try {
          // 处理主图和详情图，合并为一个字符串
          const mainImageId = params.goods__main || '';
          const detailImageIds = params.goods__detail_images || [];

          console.log(mainImageId, '1111');
          // 合并主图ID和详情图ID，确保主图在前
          const allImageIds = mainImageId
            ? [mainImageId, ...detailImageIds].filter(Boolean).join(',')
            : detailImageIds.join(',');

          const data = {
            ...params,
            goods__main: allImageIds, // 覆盖原始的goods__main值
            goods_id: currentEditProduct?.value?.goods_id || id, // 使用安全访问避免null错误
          } as unknown as AddGoods;

          const res = await updateGoods(data);
          return res;
        } catch (error) {
          console.error('编辑商品出错:', error);
          return { code: 0, msg: '编辑失败，请重试' };
        }
      },
      onSuccess: options.onSuccess,
      onCancel: () => {
        // 清除图片选择器中的数据
        resetImageSelectors();
        // 清除当前编辑的商品对象
        currentEditProduct.value = null;
        // 清除表单数据
        formData.goods__main = '';
        formData.goods__main_path = '';
        formData.goods__detail_images = [];
        formData.goods__detail_image_paths = [];

        options.onCancel?.();
      },
      fullscreenable: true,
      draggable: true,
    },
    // 提供空的tableOptions以满足类型要求
    tableOptions: {
      api: async () => ({ code: 1, data: { rows: [], total: 0 }, msg: 'ok' }),
      columns: [],
    },
  });

  // 在其他位置保存一个_isFullscreen引用，但不使用它
  const _isFullscreen = isFullscreen;

  // 自定义show方法，支持传入完整的商品对象
  const enhancedShow = (idOrProduct?: number | ProductItem) => {
    if (idOrProduct === undefined) {
      // 新增模式
      show();
    } else if (typeof idOrProduct === 'number') {
      // 编辑模式 - 通过ID（但在本例中没有getDetail API）
      console.warn(
        '警告：使用ID进行编辑时缺少getDetail API。建议传递完整的商品对象。',
      );
      show(idOrProduct);
    } else {
      // 编辑模式 - 传入完整商品对象
      // 保存商品对象以便后续使用
      currentEditProduct.value = idOrProduct;

      // 1. 先调用原始show方法，这会设置isEdit=true并显示表单
      show(idOrProduct.id);

      // 2. 手动填充表单数据
      setTimeout(() => {
        // 设置基本信息
        formData.id = idOrProduct.id;
        formData.goods__name = idOrProduct.goods__name;
        formData.goods__goods_type = String(idOrProduct.goods__goods_type) as
          | '1'
          | '2';
        formData.goods__wages_type = idOrProduct.goods__wages_type
          ? (String(idOrProduct.goods__wages_type) as '1' | '2')
          : undefined;
        // formData.goods__main = idOrProduct.goods__main;
        formData.goods__classification_id = idOrProduct.goods__classification_id
          ? String(idOrProduct.goods__classification_id)
          : undefined;
        formData.goods__channel_id = idOrProduct.goods__channel__id
          ? String(idOrProduct.goods__channel__id)
          : undefined;
        formData.goods__sales = String(idOrProduct.goods__sales);
        formData.goods__postage = idOrProduct.goods__postage;
        formData.goods__description = idOrProduct.goods__description;
        formData.goods__stock = String(idOrProduct.goods__stock);
        formData.goods__sort = String(idOrProduct.goods__sort);
        formData.status = String(idOrProduct.status) as '1' | '2';
        formData.goods__encoding = idOrProduct.goods__encoding || '';
        formData.selling = idOrProduct.selling;
        formData.cost = idOrProduct.cost;
        formData.goods__main_path = idOrProduct.goods__main_path;

        // 3. 加载图片信息
        if (idOrProduct.goods__main && idOrProduct.goods__main_path) {
          getGoodsImgInfo(idOrProduct.goods__main, idOrProduct.goods__main_path)
            .then((imgRes) => {
              if (imgRes.code === 1 && imgRes.data) {
                const { main, deputy } = imgRes.data;

                // 设置主图
                if (main && main.length > 0) {
                  const mainImg = main[0];
                  formData.goods__main = String(mainImg.id);
                  formData.goods__main_path = mainImg.file_path;
                  mainImageUrl.value = mainImg.file_path;
                }

                // 设置详情图片
                if (deputy && deputy.length > 0) {
                  formData.goods__detail_images = deputy.map(
                    (img: Record<string, any>) => String(img.id),
                  );
                  formData.goods__detail_image_paths = deputy.map(
                    (img: Record<string, any>) => img.file_path,
                  );

                  detailImageUrls.value = [
                    ...(formData.goods__detail_image_paths || []),
                  ];

                  selectedDetailImageObjects.value = deputy.map(
                    (img: Record<string, any>) => ({
                      id: img.id,
                      src: img.file_path,
                    }),
                  );
                }
              }
            })
            .catch((error) => {
              console.error('获取商品图片信息出错:', error);
            });
        }
      }, 100); // 使用短延时确保表单已加载
    }
  };

  // 设置表单验证规则
  setRules({
    goods__name: [
      { required: true, message: '请输入商品名称', trigger: 'blur' },
    ],
    goods__goods_type: [
      { required: true, message: '请选择商品类型', trigger: 'change' },
    ],
    goods__main: [
      { required: true, message: '请上传商品主图', trigger: 'change' },
    ],
    selling: [{ required: true, message: '请输入售价', trigger: 'blur' }],
    cost: [{ required: true, message: '请输入成本价', trigger: 'blur' }],
    goods__stock: [{ required: true, message: '请输入库存', trigger: 'blur' }],
    status: [{ required: true, message: '请选择上架状态', trigger: 'change' }],
  });

  // 设置主图变更回调
  watch(mainImageUrl, (url) => {
    formData.goods__main_path = url;
  });

  // 设置详情图变更回调
  watch(detailImageUrls, (urls) => {
    formData.goods__detail_image_paths = urls;
  });

  // 设置图片选择回调
  watch(_mainImageObject, (image) => {
    console.log(image, '1111');
    if (image) {
      formData.goods__main = String(image.id || '');
      formData.goods__main_path = image.src;
    }
  });

  // 设置详情图选择回调
  watch(selectedDetailImageObjects, (images) => {
    if (images.length > 0) {
      formData.goods__detail_images = images.map((img) => String(img.id || ''));
      formData.goods__detail_image_paths = images.map((img) => img.src);
    }
  });

  // 加载分类列表
  const loadCategories = async () => {
    try {
      const res = await getCategoryList({ page: 1, pageSize: 100 });
      if (res.code === 1) {
        categoryOptions.value = res.data.rows.map((item) => ({
          label: item.name,
          value: String(item.id),
        }));
      }
    } catch (error) {
      console.error('加载分类列表出错:', error);
    }
  };

  // 加载通道列表
  const loadChannels = async () => {
    try {
      const res = await getMallChannelList({ page: 1, pageSize: 100 });
      if (res.code === 1) {
        channelOptions.value = res.data.rows.map((item: any) => ({
          label: item.name,
          value: String(item.id),
        }));
      }
    } catch (error) {
      console.error('加载通道列表出错:', error);
    }
  };

  // 商品类型变更时的联动处理
  watch(
    () => formData.goods__goods_type,
    (newVal) => {
      formData.goods__wages_type = newVal === '2' ? '1' : undefined;

      // 设置佣金方式字段的可见性
      setFieldVisibility('goods__wages_type', {
        visible: (data) => data.goods__goods_type === '2',
        dependencies: ['goods__goods_type'],
      });

      // 设置返佣金额字段的可见性 - 移除条件使其始终可见
      // 移除对rebates字段的可见性限制，使其始终可见
      // setFieldVisibility('rebates', {
      //   visible: (data) => data.goods__goods_type === '2',
      //   dependencies: ['goods__goods_type'],
      // });
    },
    { immediate: true },
  );

  // 初始化数据
  onMounted(() => {
    loadCategories();
    loadChannels();
  });

  // 定义表单组
  const formGroups: FormGroup[] = [
    {
      title: '基本信息',
      content: (formData) => [
        // 商品名称
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '商品名称',
              name: 'goods__name',
              rules: [{ required: true, message: '请输入商品名称' }],
            },
            [
              h(Input, {
                placeholder: '请输入商品名称',
                value: formData.goods__name,
                'onUpdate:value': (val) => (formData.goods__name = val),
              }),
            ],
          ),
        ]),
        // 商品类型
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '商品类型',
              name: 'goods__goods_type',
              rules: [{ required: true, message: '请选择商品类型' }],
            },
            [
              h(Select, {
                placeholder: '请选择商品类型',
                options: goodsTypeOptions,
                value: formData.goods__goods_type,
                'onUpdate:value': (val) => (formData.goods__goods_type = val),
              }),
            ],
          ),
        ]),
        // 佣金方式 (仅号卡产品显示)
        h(Col, { span: 12 }, [
          renderField(
            'goods__wages_type',
            h(
              Form.Item,
              {
                label: '佣金方式',
                name: 'goods__wages_type',
              },
              [
                h(Select, {
                  placeholder: '请选择佣金方式',
                  options: wagesTypeOptions,
                  value: formData.goods__wages_type,
                  'onUpdate:value': (val) => (formData.goods__wages_type = val),
                }),
              ],
            ),
            (data) => data.goods__goods_type === '2',
          ),
        ]),
        // 商品分类
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '商品分类',
              name: 'goods__classification_id',
            },
            [
              h(Select, {
                placeholder: '请选择商品分类',
                options: categoryOptions.value,
                value: formData.goods__classification_id,
                'onUpdate:value': (val) =>
                  (formData.goods__classification_id = val),
                allowClear: true,
              }),
            ],
          ),
        ]),
        // 通道选择
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '商品通道',
              name: 'goods__channel_id',
            },
            [
              h(Select, {
                placeholder: '请选择商品通道',
                options: channelOptions.value,
                value: formData.goods__channel_id,
                'onUpdate:value': (val) => (formData.goods__channel_id = val),
                allowClear: true,
              }),
            ],
          ),
        ]),
      ],
    },
    {
      title: '价格库存',
      content: (formData) => [
        // 售价
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '售价',
              name: 'selling',
              rules: [{ required: true, message: '请输入售价' }],
            },
            [
              h(InputNumber, {
                placeholder: '请输入售价',
                min: 0,
                precision: 2,
                style: 'width: 100%',
                value: formData.selling,
                'onUpdate:value': (val) =>
                  (formData.selling = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 成本价
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '成本价',
              name: 'cost',
              rules: [{ required: true, message: '请输入成本价' }],
            },
            [
              h(InputNumber, {
                placeholder: '请输入成本价',
                min: 0,
                precision: 2,
                style: 'width: 100%',
                value: formData.cost,
                'onUpdate:value': (val) => (formData.cost = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 返佣金额 (修改成对所有商品类型都显示)
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '返佣金额',
              name: 'rebates',
            },
            [
              h(InputNumber, {
                placeholder: '请输入返佣金额',
                min: 0,
                precision: 2,
                style: 'width: 100%',
                value: formData.rebates,
                'onUpdate:value': (val) =>
                  (formData.rebates = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 库存
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '库存',
              name: 'goods__stock',
              rules: [{ required: true, message: '请输入库存' }],
            },
            [
              h(InputNumber, {
                placeholder: '请输入库存',
                min: 0,
                precision: 0,
                style: 'width: 100%',
                value: formData.goods__stock,
                'onUpdate:value': (val) =>
                  (formData.goods__stock = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 初始销量
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '初始销量',
              name: 'goods__sales',
            },
            [
              h(InputNumber, {
                placeholder: '请输入初始销量',
                min: 0,
                precision: 0,
                style: 'width: 100%',
                value: formData.goods__sales,
                'onUpdate:value': (val) =>
                  (formData.goods__sales = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 邮费
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '邮费',
              name: 'goods__postage',
            },
            [
              h(InputNumber, {
                placeholder: '请输入邮费',
                min: 0,
                precision: 2,
                style: 'width: 100%',
                value: formData.goods__postage,
                'onUpdate:value': (val) =>
                  (formData.goods__postage = String(val || 0)),
              }),
            ],
          ),
        ]),
      ],
    },
    {
      title: '其他信息',
      content: (formData) => [
        // 排序
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '排序',
              name: 'goods__sort',
            },
            [
              h(InputNumber, {
                placeholder: '请输入排序',
                min: 0,
                precision: 0,
                style: 'width: 100%',
                value: formData.goods__sort,
                'onUpdate:value': (val) =>
                  (formData.goods__sort = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 对接编码
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '对接编码',
              name: 'goods__encoding',
            },
            [
              h(Input, {
                placeholder: '请输入对接编码',
                value: formData.goods__encoding,
                'onUpdate:value': (val) => (formData.goods__encoding = val),
              }),
            ],
          ),
        ]),
        // 商品状态
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '商品状态',
              name: 'status',
              rules: [{ required: true, message: '请选择商品状态' }],
            },
            [
              h(Select, {
                placeholder: '请选择商品状态',
                options: statusOptions,
                value: formData.status,
                'onUpdate:value': (val) => (formData.status = val),
              }),
            ],
          ),
        ]),
        // 商品描述
        h(Col, { span: 24 }, [
          h(Card, {}, [
            h(RichTextEditor, {
              modelValue: formData.goods__description,
              'onUpdate:modelValue': (val: string) =>
                (formData.goods__description = val),
            }),
          ]),
        ]),
      ],
    },
    {
      title: '商品图片',
      content: () => [
        // 商品主图
        h(Col, { span: 24 }, [
          h(
            Form.Item,
            {
              label: '商品主图',
              name: 'goods__main',
              rules: [{ required: true, message: '请上传商品主图' }],
            },
            () => [renderMainImageSelector()],
          ),
        ]),
        // 商品详情图
        h(Col, { span: 24 }, [
          h(
            Form.Item,
            {
              label: '商品详情图',
              name: 'goods__detail_images',
            },
            () => [renderDetailImagesSelector()],
          ),
        ]),
      ],
    },
  ];

  // 创建表单弹窗组件
  const ProductFormModal = defineComponent({
    setup() {
      return () => renderFormModal(formGroups);
    },
  });

  return {
    // 表单数据
    formData,

    // 表单状态
    visible,
    loading,
    isEdit,

    // 选项数据
    categoryOptions,
    channelOptions,
    goodsTypeOptions,
    wagesTypeOptions,
    statusOptions,

    // 图片相关
    mainImageUrl,
    detailImageUrls,

    // 方法
    show: enhancedShow,
    toggleFullscreen,
    resetImageSelectors,

    // 组件
    ProductFormModal,
    MainImagePreviewModal,
    DetailImagesPreviewModal,
  };
}
