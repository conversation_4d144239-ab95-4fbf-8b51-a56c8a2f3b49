import type { AddGoods, ProductItem } from '#/api/core/shop';
import type { FormGroup } from '#/hooks/useAnsheng/types';
import type { UseImageSelectorOptions } from '#/hooks/web/useImageSelector';

import { computed, defineComponent, h, onMounted, ref, watch } from 'vue';

import {
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Select,
} from 'ant-design-vue';

import {
  addGoods,
  getCategoryList,
  getGoodsImgInfo,
  getMallChannelList,
  updateGoods,
} from '#/api/core/shop';
import RichTextEditor from '#/components/RichTextEditor/index.vue';
import { useAnsheng } from '#/hooks/useAnsheng';
import { useImageSelector } from '#/hooks/web/useImageSelector';

// 商品图片处理结果接口
interface ProcessedProductImages {
  allImageIds: string;
  mainImageId: string;
  detailImageIds: string[];
}

// 商品表单接口定义
export interface ProductFormData {
  id?: number;
  goods__name: string;
  goods__goods_type: '1' | '2';
  goods__wages_type?: '1' | '2';
  goods__main: string;
  goods__classification_id?: string;
  goods__channel_id?: string;
  goods__sales: string;
  goods__postage: string;
  goods__description: string;
  goods__stock: string;
  goods__sort: string;
  status: '1' | '2';
  goods__encoding: string;
  selling: string;
  cost: string;
  rebates?: string;
  goods__main_path?: string;
  goods__detail_images?: string[];
  goods__detail_image_paths?: string[];
}

// useProductModal hook 选项
export interface UseProductModalOptions {
  onSuccess?: () => void;
  onCancel?: () => void;
  // 可以添加日志选项，用于替代console输出
  logger?: {
    error: (message: string, error?: any) => void;
    warn: (message: string) => void;
  };
}

// useProductModal hook 定义
export function useProductModal(options: UseProductModalOptions = {}) {
  // 处理日志记录函数，如果没有提供则使用默认行为
  const logger = options.logger || {
    error: (message: string, error?: any) => console.error(message, error),
    warn: (message: string) => console.warn(message),
  };

  // 添加主图修改标记
  const mainImageModified = ref(false);

  // 基础选项配置
  const categoryOptions = ref<{ label: string; value: string }[]>([]);
  const channelOptions = ref<{ label: string; value: string }[]>([]);

  // 使用常量定义选项，提高可维护性
  const goodsTypeOptions = [
    { label: '普通商品', value: '1' },
    { label: '号卡产品', value: '2' },
  ];
  const wagesTypeOptions = [
    { label: '下单立返', value: '1' },
    { label: '次月返', value: '2' },
  ];
  const statusOptions = [
    { label: '上架', value: '1' },
    { label: '下架', value: '2' },
  ];

  // 保存当前编辑的商品对象
  const currentEditProduct = ref<null | ProductItem>(null);

  // 主图选择器
  const {
    renderImageSelector: renderMainImageSelector,
    ImagePreviewModal: MainImagePreviewModal,
    imageUrl: mainImageUrl,
    selectedImageObject: mainImageObject,
  } = useImageSelector({
    mode: 'gallery',
    multiple: false,
    defaultImageUrl: '',
  } as UseImageSelectorOptions);

  // 详情图选择器
  const {
    renderImageSelector: renderDetailImagesSelector,
    ImagePreviewModal: DetailImagesPreviewModal,
    selectedImageObjects: selectedDetailImageObjects,
    imageUrls: detailImageUrls,
  } = useImageSelector({
    mode: 'gallery',
    multiple: true,
    maxCount: 10,
    defaultImageUrls: [],
  } as UseImageSelectorOptions);

  // 重置图片选择器
  const resetImageSelectors = () => {
    mainImageUrl.value = '';
    selectedDetailImageObjects.value = [];
    detailImageUrls.value = [];
  };

  // 处理商品图片，统一处理图片ID格式化逻辑
  const processProductImages = (
    mainId: string = '',
    detailIds: string[] = [],
  ): ProcessedProductImages => {
    // 过滤空值并合并ID
    const filteredDetailIds = detailIds.filter(Boolean);

    // 在编辑模式下，只有当主图被修改时才包含主图ID
    const shouldIncludeMainId = !isEdit.value || mainImageModified.value;

    // 根据主图是否被修改决定是否包含主图ID
    const allImageIds =
      shouldIncludeMainId && mainId
        ? [mainId, ...filteredDetailIds].filter(Boolean).join(',')
        : filteredDetailIds.join(',');

    return {
      allImageIds,
      mainImageId: mainId,
      detailImageIds: filteredDetailIds,
    };
  };

  // 使用useAnsheng中的form部分
  const {
    form: {
      formData,
      visible,
      isEdit,
      show,
      renderFormModal,
      setFieldVisibility,
      renderField,
      setRules,
      isFullscreen,
      toggleFullscreen,
      loading,
    },
  } = useAnsheng<ProductFormData>({
    formOptions: {
      title: (isEdit) => (isEdit ? '编辑商品' : '添加商品'),
      width: 900,
      defaultValues: {
        goods__name: '',
        goods__goods_type: '1',
        goods__wages_type: '1',
        goods__classification_id: undefined,
        goods__channel_id: undefined,
        goods__sales: '0',
        goods__postage: '0',
        goods__description: '',
        goods__stock: '0',
        goods__sort: '0',
        status: '1',
        goods__encoding: '',
        selling: '0',
        cost: '0',
        rebates: '0',
        goods__main: '',
        goods__main_path: '',
        goods__detail_images: [],
        goods__detail_image_paths: [],
      },
      // 创建方法
      create: async (params) => {
        try {
          // 使用共享函数处理图片
          const { allImageIds } = processProductImages(
            params.goods__main,
            params.goods__detail_images,
          );

          const data = {
            ...params,
            goods__main: allImageIds,
          } as unknown as AddGoods;

          return await addGoods(data);
        } catch (error) {
          logger.error('添加商品出错:', error);
          return { code: 0, msg: '添加失败，请重试' };
        }
      },
      // 更新方法
      update: async (id, params) => {
        try {
          // 使用共享函数处理图片
          const { allImageIds } = processProductImages(
            params.goods__main,
            params.goods__detail_images,
          );

          const data = {
            ...params,
            goods__main: allImageIds,
            goods_id: currentEditProduct.value?.goods_id || id,
          } as unknown as AddGoods;

          return await updateGoods(data);
        } catch (error) {
          logger.error('编辑商品出错:', error);
          return { code: 0, msg: '编辑失败，请重试' };
        }
      },
      onSuccess: () => {
        // 重置主图修改标记
        mainImageModified.value = false;
        options.onSuccess?.();
      },
      onCancel: () => {
        // 清理所有相关数据
        resetImageSelectors();
        currentEditProduct.value = null;

        // 重置主图修改标记
        mainImageModified.value = false;

        // 清除表单数据中的图片字段
        formData.goods__main = '';
        formData.goods__main_path = '';
        formData.goods__detail_images = [];
        formData.goods__detail_image_paths = [];

        options.onCancel?.();
      },
      fullscreenable: true,
      draggable: true,
    },
    // 提供空的tableOptions以满足类型要求
    tableOptions: {
      api: async () => ({ code: 1, data: { rows: [], total: 0 }, msg: 'ok' }),
      columns: [],
    },
  });

  // 填充商品基本信息到表单
  const fillProductBasicInfo = (product: ProductItem) => {
    formData.id = product.id;
    formData.goods__name = product.goods__name;
    formData.goods__goods_type = String(product.goods__goods_type) as '1' | '2';
    formData.goods__wages_type = product.goods__wages_type
      ? (String(product.goods__wages_type) as '1' | '2')
      : undefined;
    formData.goods__classification_id = product.goods__classification_id
      ? String(product.goods__classification_id)
      : undefined;
    formData.goods__channel_id = product.goods__channel__id
      ? String(product.goods__channel__id)
      : undefined;
    formData.goods__sales = String(product.goods__sales);
    formData.goods__postage = product.goods__postage;
    formData.goods__description = product.goods__description;
    formData.goods__stock = String(product.goods__stock);
    formData.goods__sort = String(product.goods__sort);
    formData.status = String(product.status) as '1' | '2';
    formData.goods__encoding = product.goods__encoding || '';
    formData.selling = product.selling;
    formData.cost = product.cost;
    formData.goods__main_path = product.goods__main_path;
  };

  // 加载并设置商品图片信息
  const loadProductImages = async (mainId: string, mainPath: string) => {
    try {
      const imgRes = await getGoodsImgInfo(mainId, mainPath);

      if (imgRes.code === 1 && imgRes.data) {
        const { main, deputy } = imgRes.data;

        // 设置主图
        if (main?.length > 0) {
          const mainImg = main[0];
          formData.goods__main = String(mainImg.id);
          formData.goods__main_path = mainImg.file_path;
          mainImageUrl.value = mainImg.file_path;
        }

        // 设置详情图片
        if (deputy?.length > 0) {
          // 处理详情图片数据
          const detailImages = deputy.map((img: Record<string, any>) =>
            String(img.id),
          );
          console.log('detailImages', detailImages);
          const detailImagePaths = deputy.map(
            (img: Record<string, any>) => img.file_path,
          );

          // 设置表单数据
          formData.goods__detail_images = detailImages;
          formData.goods__detail_image_paths = detailImagePaths;

          // 同步UI状态
          detailImageUrls.value = [...detailImagePaths];
          selectedDetailImageObjects.value = deputy.map(
            (img: Record<string, any>) => ({
              id: img.id,
              src: img.file_path,
            }),
          );
        }
      }
    } catch (error) {
      logger.error('获取商品图片信息出错:', error);
      message.error('获取商品图片信息失败');
    }
  };

  // 自定义show方法，支持传入完整的商品对象
  // 自定义show方法，支持传入完整的商品对象
  const enhancedShow = (idOrProduct?: number | ProductItem) => {
    // 重置主图修改标记
    mainImageModified.value = false;

    if (idOrProduct === undefined) {
      // 新增模式
      show();
    } else if (typeof idOrProduct === 'number') {
      // 编辑模式 - 通过ID（但在本例中没有getDetail API）
      logger.warn(
        '警告：使用ID进行编辑时缺少getDetail API。建议传递完整的商品对象。',
      );
      show(idOrProduct);
    } else {
      // 编辑模式 - 传入完整商品对象
      currentEditProduct.value = idOrProduct;

      // 调用原始show方法，设置isEdit=true并显示表单
      show(idOrProduct.id);

      // 填充基本表单数据
      fillProductBasicInfo(idOrProduct);

      // 加载图片信息（如果有）
      if (idOrProduct.goods__main && idOrProduct.goods__main_path) {
        loadProductImages(
          idOrProduct.goods__main,
          idOrProduct.goods__main_path,
        );
      }
    }
  };

  // 设置表单验证规则
  setRules({
    goods__name: [
      { required: true, message: '请输入商品名称', trigger: 'blur' },
    ],
    goods__goods_type: [
      { required: true, message: '请选择商品类型', trigger: 'change' },
    ],
    goods__main: [
      { required: true, message: '请上传商品主图', trigger: 'change' },
    ],
    selling: [{ required: true, message: '请输入售价', trigger: 'blur' }],
    cost: [{ required: true, message: '请输入成本价', trigger: 'blur' }],
    goods__stock: [{ required: true, message: '请输入库存', trigger: 'blur' }],
    status: [{ required: true, message: '请选择上架状态', trigger: 'change' }],
  });

  // 设置图片相关的监听器，优化为更高效的watch方式
  watch(mainImageUrl, (url, oldUrl) => {
    if (url !== formData.goods__main_path) {
      formData.goods__main_path = url;

      // 如果URL发生变化，标记主图已修改
      if (url !== oldUrl && oldUrl !== '') {
        mainImageModified.value = true;
      }
    }
  });

  watch(mainImageObject, (image) => {
    if (image) {
      formData.goods__main = String(image.id || '');
      formData.goods__main_path = image.src;

      // 当选择了新的主图，标记主图已修改
      mainImageModified.value = true;
    }
  });

  watch(detailImageUrls, (urls) => {
    formData.goods__detail_image_paths = [...urls];
  });

  watch(selectedDetailImageObjects, (images) => {
    if (images.length > 0) {
      formData.goods__detail_images = images.map((img) => String(img.id || ''));
      formData.goods__detail_image_paths = images.map((img) => img.src);
    } else {
      formData.goods__detail_images = [];
      formData.goods__detail_image_paths = [];
    }
  });

  // 使用computed优化联动逻辑
  const shouldShowWagesType = computed(
    () => formData.goods__goods_type === '2',
  );

  // 加载分类列表
  const loadCategories = async () => {
    try {
      const res = await getCategoryList({ page: 1, pageSize: 100 });
      if (res.code === 1) {
        categoryOptions.value = res.data.rows.map((item) => ({
          label: item.name,
          value: String(item.id),
        }));
      }
    } catch (error) {
      logger.error('加载分类列表出错:', error);
      message.error('加载分类列表失败');
    }
  };

  // 加载通道列表
  const loadChannels = async () => {
    try {
      const res = await getMallChannelList({ page: 1, pageSize: 100 });
      if (res.code === 1) {
        channelOptions.value = res.data.rows.map((item: any) => ({
          label: item.name,
          value: String(item.id),
        }));
      }
    } catch (error) {
      logger.error('加载通道列表出错:', error);
      message.error('加载通道列表失败');
    }
  };

  // 商品类型变更时的联动处理
  watch(
    () => formData.goods__goods_type,
    (newVal) => {
      formData.goods__wages_type = newVal === '2' ? '1' : undefined;

      // 设置佣金方式字段的可见性
      setFieldVisibility('goods__wages_type', {
        visible: (data) => data.goods__goods_type === '2',
        dependencies: ['goods__goods_type'],
      });
    },
    { immediate: true },
  );

  // 初始化数据
  onMounted(() => {
    loadCategories();
    loadChannels();
  });

  // 定义表单组
  const formGroups: FormGroup[] = [
    {
      title: '基本信息',
      content: (formData) => [
        // 商品名称
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '商品名称',
              name: 'goods__name',
              rules: [{ required: true, message: '请输入商品名称' }],
            },
            [
              h(Input, {
                placeholder: '请输入商品名称',
                value: formData.goods__name,
                'onUpdate:value': (val) => (formData.goods__name = val),
              }),
            ],
          ),
        ]),
        // 商品类型
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '商品类型',
              name: 'goods__goods_type',
              rules: [{ required: true, message: '请选择商品类型' }],
            },
            [
              h(Select, {
                placeholder: '请选择商品类型',
                options: goodsTypeOptions,
                value: formData.goods__goods_type,
                'onUpdate:value': (val) => (formData.goods__goods_type = val),
              }),
            ],
          ),
        ]),
        // 佣金方式 (仅号卡产品显示)
        h(Col, { span: 12 }, [
          renderField(
            'goods__wages_type',
            h(
              Form.Item,
              {
                label: '佣金方式',
                name: 'goods__wages_type',
              },
              [
                h(Select, {
                  placeholder: '请选择佣金方式',
                  options: wagesTypeOptions,
                  value: formData.goods__wages_type,
                  'onUpdate:value': (val) => (formData.goods__wages_type = val),
                }),
              ],
            ),
            (data) => data.goods__goods_type === '2',
          ),
        ]),
        // 商品分类
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '商品分类',
              name: 'goods__classification_id',
            },
            [
              h(Select, {
                placeholder: '请选择商品分类',
                options: categoryOptions.value,
                value: formData.goods__classification_id,
                'onUpdate:value': (val) =>
                  (formData.goods__classification_id = val),
                allowClear: true,
              }),
            ],
          ),
        ]),
        // 通道选择
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '商品通道',
              name: 'goods__channel_id',
            },
            [
              h(Select, {
                placeholder: '请选择商品通道',
                options: channelOptions.value,
                value: formData.goods__channel_id,
                'onUpdate:value': (val) => (formData.goods__channel_id = val),
                allowClear: true,
              }),
            ],
          ),
        ]),
      ],
    },
    {
      title: '价格库存',
      content: (formData) => [
        // 售价
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '售价',
              name: 'selling',
              rules: [{ required: true, message: '请输入售价' }],
            },
            [
              h(InputNumber, {
                placeholder: '请输入售价',
                min: 0,
                precision: 2,
                style: 'width: 100%',
                value: formData.selling,
                'onUpdate:value': (val) =>
                  (formData.selling = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 成本价
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '成本价',
              name: 'cost',
              rules: [{ required: true, message: '请输入成本价' }],
            },
            [
              h(InputNumber, {
                placeholder: '请输入成本价',
                min: 0,
                precision: 2,
                style: 'width: 100%',
                value: formData.cost,
                'onUpdate:value': (val) => (formData.cost = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 返佣金额 (对所有商品类型都显示)
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '返佣金额',
              name: 'rebates',
            },
            [
              h(InputNumber, {
                placeholder: '请输入返佣金额',
                min: 0,
                precision: 2,
                style: 'width: 100%',
                value: formData.rebates,
                'onUpdate:value': (val) =>
                  (formData.rebates = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 库存
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '库存',
              name: 'goods__stock',
              rules: [{ required: true, message: '请输入库存' }],
            },
            [
              h(InputNumber, {
                placeholder: '请输入库存',
                min: 0,
                precision: 0,
                style: 'width: 100%',
                value: formData.goods__stock,
                'onUpdate:value': (val) =>
                  (formData.goods__stock = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 初始销量
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '初始销量',
              name: 'goods__sales',
            },
            [
              h(InputNumber, {
                placeholder: '请输入初始销量',
                min: 0,
                precision: 0,
                style: 'width: 100%',
                value: formData.goods__sales,
                'onUpdate:value': (val) =>
                  (formData.goods__sales = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 邮费
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '邮费',
              name: 'goods__postage',
            },
            [
              h(InputNumber, {
                placeholder: '请输入邮费',
                min: 0,
                precision: 2,
                style: 'width: 100%',
                value: formData.goods__postage,
                'onUpdate:value': (val) =>
                  (formData.goods__postage = String(val || 0)),
              }),
            ],
          ),
        ]),
      ],
    },
    {
      title: '其他信息',
      content: (formData) => [
        // 排序
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '排序',
              name: 'goods__sort',
            },
            [
              h(InputNumber, {
                placeholder: '请输入排序',
                min: 0,
                precision: 0,
                style: 'width: 100%',
                value: formData.goods__sort,
                'onUpdate:value': (val) =>
                  (formData.goods__sort = String(val || 0)),
              }),
            ],
          ),
        ]),
        // 对接编码
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '对接编码',
              name: 'goods__encoding',
            },
            [
              h(Input, {
                placeholder: '请输入对接编码',
                value: formData.goods__encoding,
                'onUpdate:value': (val) => (formData.goods__encoding = val),
              }),
            ],
          ),
        ]),
        // 商品状态
        h(Col, { span: 12 }, [
          h(
            Form.Item,
            {
              label: '商品状态',
              name: 'status',
              rules: [{ required: true, message: '请选择商品状态' }],
            },
            [
              h(Select, {
                placeholder: '请选择商品状态',
                options: statusOptions,
                value: formData.status,
                'onUpdate:value': (val) => (formData.status = val),
              }),
            ],
          ),
        ]),
        // 商品描述
        h(Col, { span: 24 }, [
          h(Card, {}, [
            h(RichTextEditor, {
              modelValue: formData.goods__description,
              'onUpdate:modelValue': (val: string) =>
                (formData.goods__description = val),
            }),
          ]),
        ]),
      ],
    },
    {
      title: '商品图片',
      content: () => [
        // 商品主图
        h(Col, { span: 24 }, [
          h(
            Form.Item,
            {
              label: '商品主图',
              name: 'goods__main',
              rules: [{ required: true, message: '请上传商品主图' }],
            },
            () => [renderMainImageSelector()],
          ),
        ]),
        // 商品详情图
        h(Col, { span: 24 }, [
          h(
            Form.Item,
            {
              label: '商品详情图',
              name: 'goods__detail_images',
            },
            () => [renderDetailImagesSelector()],
          ),
        ]),
      ],
    },
  ];

  // 创建表单弹窗组件 - 使用函数式组件风格
  const ProductFormModal = defineComponent(() => {
    return () => renderFormModal(formGroups);
  });

  return {
    // 表单数据
    formData,

    // 表单状态
    visible,
    loading,
    isEdit,

    // 选项数据
    categoryOptions,
    channelOptions,
    goodsTypeOptions,
    wagesTypeOptions,
    statusOptions,

    // 图片相关
    mainImageUrl,
    detailImageUrls,

    // 方法
    show: enhancedShow,
    toggleFullscreen,
    resetImageSelectors,

    // 组件
    ProductFormModal,
    MainImagePreviewModal,
    DetailImagesPreviewModal,
  };
}
