<script lang="ts" setup>
import type { ProductItem } from '#/api/core/shop';

import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import { deleteGoods, getProductList } from '#/api/core/shop';
// import BasicTable from '#/components/BasicTable/index.vue';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { columns, searchItems } from './config';
// 导入新的useProductModal hook
import { useProductModal } from './hooks/useProductModal';

const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getProductList,
  defaultParams: {},
});

// 是否为编辑模式
const isEditMode = ref(false);

// 添加/编辑成功回调
const handleSuccess = () => {
  message.success(`${isEditMode.value ? '编辑' : '添加'}商品成功`);
  getList();
};

// 使用商品弹窗hook
const {
  show: showProductModal,
  ProductFormModal,
  MainImagePreviewModal,
  DetailImagesPreviewModal,
} = useProductModal({
  onSuccess: handleSuccess,
});

// 处理新增
const handleAdd = () => {
  isEditMode.value = false;
  showProductModal();
};

// 处理编辑
const handleEdit = (record: ProductItem) => {
  isEditMode.value = true;
  showProductModal(record);
};

// 处理删除
const handleDelete = (record: ProductItem) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该商品吗？',
    async onOk() {
      try {
        // 调用删除API
        const res = await deleteGoods(record.goods_id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除商品出错:', error);
        message.error('删除失败');
      }
    },
  });
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    text: '新增商品',
    type: 'primary' as const,
    icon: MdiPlus,
    onClick: handleAdd,
  },
];

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="shop-product p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :custom-buttons="toolbarButtons"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 使用组合式API渲染的表单弹窗组件 -->
    <component :is="ProductFormModal" />
    <component :is="MainImagePreviewModal" />
    <component :is="DetailImagesPreviewModal" />
  </div>
</template>

<style lang="less" scoped>
.shop-product {
  background-color: var(--background-deep);
}
</style>
