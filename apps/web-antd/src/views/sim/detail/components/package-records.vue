<script setup lang="ts">
import { h, ref, watch } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Button, Tag } from 'ant-design-vue';

import { getPackByCadId } from '#/api/core/sim';
import { BasicTable } from '#/hooks/useAnsheng/components';
// import { usePackageActions } from '#/hooks/usePackageActions';
import { formatDateTime } from '#/utils/format';

import { usePackageActions } from '../../../../hooks/ usePackageActions';
import PackageModal from './package-modal.vue';

const props = defineProps<{
  cardId?: number;
}>();

const loading = ref(false);
const records = ref([]);
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});

const loadData = async () => {
  if (!props.cardId) return;

  loading.value = true;
  try {
    const res = await getPackByCadId({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      cardId: props.cardId,
    });

    if (res.code === 1) {
      records.value = res.data.rows;
      pagination.value.total = res.data.total;
    }
  } catch (error) {
    console.error('获取套餐记录失败:', error);
  } finally {
    loading.value = false;
  }
};

const {
  loading: actionLoading,
  modalVisible,
  currentRecord,
  handleAddPackage,
  handleEditPackage,
  handleDeletePackage,
  handleSubmit,
} = usePackageActions(loadData);

// 操作按钮配置
const actionButtons = [
  {
    key: 'edit',
    text: '编辑',
    permission: 1,
    onClick: (record) => handleEditPackage(record),
  },
  {
    key: 'delete',
    text: '删除',
    permission: 1,
    danger: true,
    onClick: (record) => handleDeletePackage(record),
  },
];

const columns = [
  {
    title: '套餐名称',
    dataIndex: 'packageName',
    width: 120,
    align: 'center',
  },
  {
    title: '关联单号',
    dataIndex: 'systemOrdernumber',
    width: 180,
    align: 'center',
  },
  {
    title: '套餐类型',
    dataIndex: 'packageType',
    width: 100,
    align: 'center',
    customRender: ({ text }) => {
      const types = {
        1: '基础套餐',
        2: '加油包',
        3: '加速宝',
        4: '体验包',
        5: '短信包',
        6: '语音包',
      };
      return types[text] || '未知';
    },
  },
  {
    title: '总流量',
    dataIndex: 'vTotalFlow',
    align: 'center',
    domainPermission: 1,
    customRender: ({ text }) => `${(text / 1024).toFixed(2)}GB`,
  },
  {
    title: '总流量(实)',
    dataIndex: 'totalFlow',
    align: 'center',
    permission: 1,
    customRender: ({ text }) => `${(text / 1024).toFixed(2)}GB`,
  },
  {
    title: '已用流量(实)',
    dataIndex: 'usedFlow',
    align: 'center',
    permission: 1,
    customRender: ({ text }) => `${(text / 1024).toFixed(2)}GB`,
  },
  {
    title: '已用流量',
    dataIndex: 'vUsedFlow',
    align: 'center',
    domainPermission: 1,
    customRender: ({ text }) => `${(text / 1024).toFixed(2)}GB`,
  },
  {
    title: '剩余流量(实)',
    dataIndex: 'residualFlow',
    align: 'center',
    permission: 1,
    customRender: ({ text }) => `${(text / 1024).toFixed(2)}GB`,
  },
  {
    title: '剩余流量',
    dataIndex: 'vResidualFlow',
    align: 'center',
    domainPermission: 1,
    customRender: ({ text }) => `${(text / 1024).toFixed(2)}GB`,
  },
  {
    title: '状态',
    dataIndex: 'packageState',
    width: 100,
    align: 'center',
    customRender: ({ text }) => {
      const states = {
        1: { text: '使用中', color: 'success' },
        2: { text: '未使用', color: 'warning' },
        3: { text: '已用完', color: 'error' },
        4: { text: '已过期', color: 'error' },
        5: { text: '已退款', color: 'default' },
      };
      const state = states[text] || { text: '未知', color: 'default' };
      return h(Tag, { color: state.color }, { default: () => state.text });
    },
  },
  {
    title: '近期使用',
    dataIndex: 'recentUse',
    width: 90,
    align: 'center',
    customRender: ({ text }) => {
      return h(
        Tag,
        { color: text === 1 ? 'success' : 'default' },
        {
          default: () => (text === 1 ? '是' : '否'),
        },
      );
    },
  },
  {
    title: '生效时间',
    dataIndex: 'takeeffectTime',
    width: 150,
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => formatDateTime(text),
  },
  {
    title: '到期时间',
    dataIndex: 'expirationTime',
    width: 150,
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => formatDateTime(text),
  },
  {
    title: '充值时间',
    dataIndex: 'rechargeTime',
    width: 150,
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => formatDateTime(text),
  },
];

const handleTableChange = (page) => {
  pagination.value.current = page.current;
  pagination.value.pageSize = page.pageSize;
  loadData();
};

// 监听 cardId 变化
watch(
  () => props.cardId,
  (newId) => {
    if (newId) {
      // 重置分页
      pagination.value.current = 1;
      loadData();
    } else {
      records.value = [];
    }
  },
  { immediate: true },
);

defineExpose({
  loadData,
});
</script>

<template>
  <div class="package-records">
    <div class="mb-4 flex justify-end">
      <Button
        type="primary"
        :loading="actionLoading"
        @click="handleAddPackage"
        class="flex items-center"
        v-permission="1"
      >
        <MdiPlus class="mr-1" />
        <span>添加套餐包</span>
      </Button>
    </div>
    <PackageModal
      v-model:visible="modalVisible"
      :title="currentRecord ? '修改套餐包' : '添加套餐包'"
      :record="currentRecord"
      :card-id="props.cardId"
      @submit="handleSubmit"
    />
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="records"
      :pagination="pagination"
      :scroll="{ x: 'max-content' }"
      :row-key="(record) => record.id"
      :show-action="true"
      :action-buttons="actionButtons"
      @change="handleTableChange"
    />
  </div>
</template>
