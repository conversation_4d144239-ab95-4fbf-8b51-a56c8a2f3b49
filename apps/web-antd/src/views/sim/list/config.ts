import type { TableColumnType } from 'ant-design-vue';

import type { SimCardItem } from '#/api/core/sim';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';
import { useRouter } from 'vue-router';

import { MdiInformationOutline } from '@vben/icons';

import { Button, Tag } from 'ant-design-vue';

import {
  getChannelListApi,
  getPackageRuleListApi,
  getUserListApi,
} from '#/api/core/order';
import { statusOptions as simStatusOptions } from '#/constants/card-status';
import { useCopyable } from '#/hooks/web/useCopyable';
import { formatDateTime } from '#/utils/format';

// 定义选项接口
export interface ChannelOption {
  label: string;
  value: number;
}

export interface PackageRuleOption {
  label: string;
  value: number;
}

export interface UserOption {
  label: string;
  value: number;
}

// 卡片状态选项
export const StatusOptions = simStatusOptions;

// 实名状态选项
export const NameStatusOptions = [
  { label: '未知', value: 1, color: 'default' },
  { label: '已实名', value: 2, color: 'success' },
  { label: '未实名', value: 3, color: 'warning' },
  { label: '无需实名', value: 4, color: 'info' },
];

// 基础搜索项配置
export const basicItems: SearchItemConfig[] = [
  {
    label: '开始卡号',
    field: 'cardNoBegin',
    component: 'Input',
    props: {
      placeholder: '请输入开始卡号',
      allowClear: true,
    },
  },
  {
    label: '结束卡号',
    field: 'cardNoEnd',
    component: 'Input',
    props: {
      placeholder: '请输入结束卡号',
      allowClear: true,
    },
  },
  {
    label: '所在地',
    field: 'cardAddress',
    component: 'Input',
    props: {
      placeholder: '请输入所在地',
      allowClear: true,
    },
  },
  {
    label: '断网状态',
    field: 'apnStatus',
    component: 'Select',
    props: {
      placeholder: '请选择断网状态',
      allowClear: true,
    },
    options: [
      { label: '正常', value: 1 },
      { label: '断网', value: 2 },
    ],
  },
  {
    label: '优享状态',
    field: 'youXiangStatus',
    component: 'Select',
    props: {
      placeholder: '请选择优享状态',
      allowClear: true,
    },
    options: [
      { label: '加速中', value: 1 },
      { label: '未加速', value: 2 },
    ],
  },
];

// 获取高级搜索项配置
export const advancedItems = [
  {
    label: '卡片筛选',
    items: [
      {
        label: '归属代理',
        field: 'userId',
        component: 'Select',
        remote: {
          api: getUserListApi,
          transform: (data: any) => {
            return data.data.map((item: any) => ({
              label: item.name,
              value: item.id,
            }));
          },
        },
        props: {
          placeholder: '请选择归属代理',
          allowClear: true,
          showSearch: true,
        },
      },
      {
        label: '通道',
        field: 'configId',
        component: 'Select',
        remote: {
          api: getChannelListApi,
          transform: (data: any) => {
            return data.data.map((item: any) => ({
              label: item.name,
              value: item.id,
            }));
          },
        },
        props: {
          placeholder: '请选择通道',
          allowClear: true,
        },
      },
      {
        label: '是否在线',
        field: 'onLineStatus',
        component: 'Select',
        props: {
          placeholder: '请选择是否在线',
          allowClear: true,
        },
        options: [
          { label: '在线', value: 1 },
          { label: '离线', value: 2 },
          { label: '不支持', value: 3 },
        ],
      },
      {
        label: '套餐规则',
        field: 'groupId',
        component: 'Select',
        remote: {
          api: getPackageRuleListApi,
          transform: (data: any) => {
            return data.data.map((item: any) => ({
              label: item.name,
              value: item.id,
            }));
          },
        },
        props: {
          placeholder: '请选择套餐规则',
          allowClear: true,
        },
      },
      {
        label: '系统状态',
        field: 'systemStatus',
        component: 'Select',
        props: {
          placeholder: '请选择系统状态',
          allowClear: true,
        },
        options: [
          { label: '正常', value: 1 },
          { label: '异常', value: 2 },
        ],
      },
      {
        label: '卡片状态',
        field: 'status',
        component: 'Select',
        props: {
          placeholder: '请选择卡片状态',
          allowClear: true,
        },
        options: StatusOptions,
      },
      {
        label: '实名状态',
        field: 'nameStatus',
        component: 'Select',
        props: {
          placeholder: '请选择实名状态',
          allowClear: true,
        },
        options: NameStatusOptions,
      },
    ],
  },
  {
    label: '其他',
    items: [
      {
        label: '流量剩余百分比',
        field: 'remainingBfb',
        component: 'Input',
        props: {
          placeholder: '请输入流量剩余百分比',
          allowClear: true,
        },
      },
      {
        label: '多卡查询',
        field: 'cardNos',
        component: 'Textarea',
        props: {
          placeholder: '请输入卡号用,分割',
          allowClear: true,
        },
      },
      {
        label: '创建日期范围',
        field: 'createTimeRange',
        component: 'RangePicker',
        props: {
          placeholder: '请选择日期范围',
          allowClear: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
      },
    ],
  },
];

// 获取表格列配置
export const getColumns = (): TableColumnType<SimCardItem>[] => {
  const { createCopyableCell } = useCopyable();
  const router = useRouter ? useRouter() : null;

  const goToDetail = (cardNo: string) => {
    if (router) {
      router.push({
        path: '/sim/detail',
        query: { cardNo },
      });
    }
  };

  const createCellWithSearch = (text: string) => {
    return h('span', { class: 'flex items-center justify-center gap-1' }, [
      createCopyableCell(text),
      h(
        Button,
        {
          type: 'link',
          size: 'small',
          onClick: (e: Event) => {
            e.stopPropagation();
            goToDetail(text);
          },
          style: { padding: '0 4px', minWidth: 'auto' },
        },
        () =>
          h(MdiInformationOutline, {
            style: { fontSize: '14px' },
          }),
      ),
    ]);
  };

  return [
    {
      title: 'MSISDN',
      dataIndex: 'msisdnNumber',
      width: 180,
      align: 'center',
      customRender: ({ text }) => createCellWithSearch(text),
    },
    {
      title: 'ICCID',
      dataIndex: 'iccidNumber',
      width: 180,
      align: 'center',
      customRender: ({ text }) => createCellWithSearch(text),
    },
    {
      title: '虚拟卡号',
      dataIndex: 'voidNumber',
      width: 180,
      align: 'center',
      customRender: ({ text }) => createCellWithSearch(text),
    },
    // apiName
    {
      title: '归属通道',
      dataIndex: 'apiName',
      width: 180,
      align: 'center',
      ellipsis: true,
      permission: 1,
    },
    // groupName
    {
      title: '套餐规则',
      dataIndex: 'groupName',
      width: 180,
      align: 'center',
      permission: 1,
    },
    {
      title: '归属代理',
      dataIndex: 'userAccount',
      width: 180,
      align: 'center',
    },
    {
      title: '实名状态',
      dataIndex: 'nameStatus',
      width: 100,
      align: 'center',
      customRender: ({ text }) => {
        const status = NameStatusOptions.find((item) => item.value === text);
        return h(Tag, { color: status?.color }, () => status?.label || text);
      },
    },
    {
      title: '卡片状态',
      dataIndex: 'status',
      width: 100,
      align: 'center',
      customRender: ({ text }) => {
        const status = StatusOptions.find((item) => item.value === text);
        return h(Tag, { color: status?.color }, () => status?.label || text);
      },
    },
    {
      title: '总流量(实际)',
      dataIndex: 'totalFlow',
      width: 120,
      align: 'center',
      permission: 1,
      customRender: ({ text }: { text: any }) => {
        const value = Number(text) || 0;
        return h('div', { class: 'flex items-center justify-center gap-1' }, [
          h('span', { class: 'mr-1' }, `${text}MB`),
          h('span', `(${(value / 1024).toFixed(2)}GB)`),
        ]);
      },
    },
    {
      title: '总流量',
      dataIndex: 'voidTotalFlow',
      width: 120,
      align: 'center',
      domainPermission: 1,
      customRender: ({ text }: { text: any }) => {
        const value = Number(text) || 0;
        return h('div', { class: 'flex items-center justify-center gap-1' }, [
          h('span', { class: 'mr-1' }, `${text}MB`),
          h('span', `(${(value / 1024).toFixed(2)}GB)`),
        ]);
      },
    },
    {
      title: '已用流量(实际)',
      dataIndex: 'usedFlow',
      width: 150,
      align: 'center',
      permission: 1,
      customRender: ({ text }: { text: any }) => {
        const value = Number(text) || 0;
        return h('div', { class: 'flex items-center justify-center gap-1' }, [
          h('span', { class: 'mr-1' }, `${text}MB`),
          h('span', `(${(value / 1024).toFixed(2)}GB)`),
        ]);
      },
    },
    {
      title: '已用流量',
      dataIndex: 'voidUsedFlow',
      width: 120,
      align: 'center',
      domainPermission: 1,
      customRender: ({ text }: { text: any }) => {
        const value = Number(text) || 0;
        return h('div', { class: 'flex items-center justify-center gap-1' }, [
          h('span', { class: 'mr-1' }, `${text}MB`),
          h('span', `(${(value / 1024).toFixed(2)}GB)`),
        ]);
      },
    },
    {
      title: '剩余流量(实际)',
      dataIndex: 'residualFlow',
      width: 150,
      align: 'center',
      permission: 1,
      customRender: ({ text }: { text: any }) => {
        const value = Number(text) || 0;
        return h('div', { class: 'flex items-center justify-center gap-1' }, [
          h('span', { class: 'mr-1' }, `${text}MB`),
          h('span', `(${(value / 1024).toFixed(2)}GB)`),
        ]);
      },
    },
    {
      title: '剩余流量',
      dataIndex: 'voidResidualFlow',
      width: 120,
      align: 'center',
      domainPermission: 1,
      customRender: ({ text }: { text: any }) => {
        const value = Number(text) || 0;
        return h('div', { class: 'flex items-center justify-center gap-1' }, [
          h('span', { class: 'mr-1' }, `${text}MB`),
          h('span', `(${(value / 1024).toFixed(2)}GB)`),
        ]);
      },
    },
    {
      title: '备注',
      dataIndex: 'notes',
      width: 120,
      align: 'center',
      customRender: ({ text }) => createCopyableCell(text),
    },
    {
      title: '到期时间',
      dataIndex: 'becomedueDatetime',
      width: 180,
      align: 'center',
      customRender: ({ text }) => formatDateTime(text),
    },
    {
      title: '创建时间',
      dataIndex: 'creationTime',
      width: 180,
      align: 'center',
      customRender: ({ text }) => formatDateTime(text),
    },
    // updateTime
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 180,
      align: 'center',
      customRender: ({ text }) => formatDateTime(text),
    },
  ];
};

// 卡片操作按钮配置
export const getActionButtons = ({
  admin = true,
}: { admin?: boolean } = {}) => {
  return [
    {
      key: 'view',
      text: '查看',
      type: 'link',
    },
    {
      key: 'refresh',
      text: '刷新',
      type: 'link',
      permission: 0, // 所有人可见
    },
    {
      key: 'recharge',
      text: '充值',
      type: 'link',
      permission: 1, // 需要权限
      show: () => admin,
    },
    {
      key: 'restart',
      text: '复机',
      type: 'link',
      permission: 1, // 需要权限
      show: (record: SimCardItem) => record.status === 4 && admin,
    },
    {
      key: 'delete',
      text: '删除',
      type: 'link',
      danger: true,
      permission: 1, // 需要权限
      show: () => admin,
    },
    {
      key: 'balance',
      text: '修改余额',
      type: 'link',
      permission: 1, // 需要权限
      show: () => admin,
    },
  ];
};

// 批量操作按钮配置
export const batchOperationButtons = [
  {
    key: 'batchDelete',
    text: '批量删除',
    type: 'primary',
    danger: true,
    permission: 1, // 需要权限
  },
  {
    key: 'batchRecharge',
    text: '批量充值',
    type: 'primary',
    permission: 1, // 需要权限
  },
  {
    key: 'batchRestart',
    text: '批量复机',
    type: 'primary',
    permission: 1, // 需要权限
  },
  {
    key: 'batchStop',
    text: '批量停机',
    type: 'primary',
    permission: 1, // 需要权限
  },
  {
    key: 'batchUpdateBalance',
    text: '批量修改余额',
    type: 'primary',
    permission: 1, // 需要权限
  },
  {
    key: 'export',
    text: '导出',
    type: 'primary',
    permission: 0, // 所有人可见
    domainPermission: 1,
  },
];
