<script lang="ts" setup>
import type { CardAnnouncementItem } from '#/api/core/cardAnnouncement';

import { h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  deleteCardAnnouncement,
  getCardAnnouncementList,
} from '#/api/core/cardAnnouncement';
import { getPackageRuleListApi } from '#/api/core/order';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import CardAnnouncementModal from './components/card-announcement-modal.vue';
// 引入配置
import { basicSearchItems, createColumns, packageRuleOptions } from './config';

// 状态定义
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getCardAnnouncementList,
  defaultParams: {},
});
// 使用导入的配置
const columns = createColumns();

// 弹窗控制
const modalVisible = ref(false);
const currentRecord = ref<CardAnnouncementItem>();

// 获取套餐规则列表
const getPackageRuleList = async () => {
  try {
    const res = await getPackageRuleListApi();
    if (res.code === 1 && res.data) {
      // 转换数据格式
      packageRuleOptions.value = res.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    } else {
      throw new Error(res.msg || '获取套餐规则列表失败');
    }
  } catch (error) {
    console.error('获取套餐规则列表失败:', error);
    message.error('获取套餐规则列表失败');
  }
};
// 处理新增
const handleAdd = () => {
  currentRecord.value = undefined;
  modalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: CardAnnouncementItem) => {
  currentRecord.value = record;
  modalVisible.value = true;
};

// 处理删除
const handleDelete = (record: CardAnnouncementItem) => {
  Modal.confirm({
    title: '提示',
    content: '确定删除该公告吗？',
    onOk: async () => {
      await deleteCardAnnouncement(record.id);
      message.success('删除成功');
      getList();
    },
  });
};

// // 处理发布/取消发布
// const handlePublish = (_record: any) => {
//   // TODO: 实现发布/取消发布逻辑
// };

// 组件挂载时加载数据
onMounted(async () => {
  await getPackageRuleList(); // 先获取套餐规则列表
  getList(); // 再获取表格数据
});
</script>

<template>
  <div class="card-announcement p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :loading="loading"
        :custom-buttons="[
          {
            text: '新增',
            icon: h(MdiPlus),
            type: 'primary',
            onClick: handleAdd,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          // {
          //   key: 'publish',
          //   text: '发布',
          //   type: 'link',
          //   onClick: handlePublish,
          // },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 新增/编辑弹窗 -->
    <CardAnnouncementModal
      v-model:visible="modalVisible"
      :record="currentRecord"
      @success="getList"
    />
  </div>
</template>

<style lang="less" scoped>
.card-announcement {
  background-color: var(--background-deep);
}
</style>
