<script lang="ts" setup>
import type { CardAnnouncementItem } from '#/api/core/cardAnnouncement';

import { ref, watch } from 'vue';

import { Input, message, Modal, Select } from 'ant-design-vue';

import {
  addCardAnnouncement,
  updateCardAnnouncement,
} from '#/api/core/cardAnnouncement';
import RichTextEditor from '#/components/RichTextEditor/index.vue';

import { packageRuleOptions } from '../config';

const props = defineProps<{
  record?: CardAnnouncementItem;
  visible: boolean;
}>();

const emit = defineEmits<{
  success: [];
  'update:visible': [value: boolean];
}>();

const loading = ref(false);
const title = ref('');
const content = ref('<p><br></p>');
const groupId = ref<number>();
const tag = ref<string>();
// 重置表单
const resetForm = () => {
  title.value = '';
  content.value = '<p><br></p>';
  groupId.value = undefined;
  tag.value = undefined;
};

// 处理取消
const handleCancel = () => {
  resetForm();
  emit('update:visible', false);
};

// 处理确认
const handleOk = async () => {
  // 检查内容是否为空或只有空标签
  const isEmpty =
    !content.value ||
    content.value === '<p><br></p>' ||
    content.value.trim() === '';
  if (isEmpty) {
    message.warning('请输入公告内容');
    return;
  }

  if (!title.value) {
    message.warning('请输入公告标题');
    return;
  }

  if (!groupId.value) {
    message.warning('请选择套餐规则');
    return;
  }

  if (!tag.value) {
    message.warning('请选择公告标签');
    return;
  }

  try {
    loading.value = true;
    const data = {
      title: title.value,
      detail: content.value,
      group_id: groupId.value,
      tag: tag.value,
    };

    if (props.record) {
      await updateCardAnnouncement({
        id: props.record.id,
        ...data,
        group__group_name: props.record.group__group_name,
      });
      message.success('编辑成功');
    } else {
      await addCardAnnouncement(data);
      message.success('添加成功');
    }

    emit('success');
    handleCancel();
  } catch (error) {
    console.error('操作失败:', error);
    message.error(error instanceof Error ? error.message : '操作失败');
  } finally {
    loading.value = false;
  }
};

// 监听弹窗显示
watch(
  () => props.visible,
  (val) => {
    if (val && props.record) {
      title.value = props.record.title;
      content.value = props.record.detail || '<p><br></p>';
      groupId.value = props.record.group_id;
      tag.value = props.record.tag;
    } else {
      resetForm();
    }
  },
  { immediate: true },
);
</script>

<template>
  <Modal
    :title="props.record ? '编辑公告' : '新增公告'"
    :visible="visible"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    width="800px"
    :destroy-on-close="true"
  >
    <div class="mt-6 space-y-4">
      <Input v-model:value="title" placeholder="请输入公告标题" />
      <Select
        v-model:value="groupId"
        placeholder="请选择套餐规则"
        :options="packageRuleOptions"
        style="width: 100%"
      />
      <!-- 1 活动  2通知 3 系统 -->
      <Select
        v-model:value="tag"
        placeholder="请选择公告标签"
        :options="[
          { label: '活动', value: '1' },
          { label: '通知', value: '2' },
          { label: '系统', value: '3' },
        ]"
        style="width: 100%"
      />
      <RichTextEditor
        v-model="content"
        height="500"
        placeholder="请输入公告内容..."
      />
    </div>
  </Modal>
</template>
