<script lang="ts" setup>
import type { DeviceAnnouncementItem } from '#/api/core/deviceAnnouncement';

import { ref, watch } from 'vue';

import { Form, Input, message, Modal, Select } from 'ant-design-vue';

import {
  addDeviceAnnouncement,
  updateDeviceAnnouncement,
} from '#/api/core/deviceAnnouncement';
import { useImagePreviewModal } from '#/hooks/web/useImagePreviewModal';
import { getFileUrl } from '#/utils/file';

const props = defineProps<{
  record?: DeviceAnnouncementItem;
  visible: boolean;
}>();

const emit = defineEmits<{
  success: [];
  'update:visible': [value: boolean];
}>();

const loading = ref(false);
const title = ref('');
const content = ref('');
const acType = ref<number>();
const jumpUrl = ref('');
const selectedImage = ref('');
const tag = ref<string>();

// 使用图片预览 hook
const { showModal, ImagePreviewModal } = useImagePreviewModal();

// 重置表单
const resetForm = () => {
  title.value = '';
  content.value = '';
  acType.value = undefined;
  jumpUrl.value = '';
  selectedImage.value = '';
  tag.value = undefined;
};

// 处理取消
const handleCancel = () => {
  resetForm();
  emit('update:visible', false);
};

// 处理选择图片
const handleSelectImage = () => {
  showModal({
    title: '选择海报',
    onSelect: (image) => {
      selectedImage.value = getFileUrl(image.src);
      content.value = image.src;
    },
    modalProps: {
      zIndex: 2000,
    },
  });
};

// 处理确认
const handleOk = async () => {
  if (!title.value) {
    message.warning('请输入公告标题');
    return;
  }

  if (!acType.value) {
    message.warning('请选择公告类型');
    return;
  }

  if (!content.value) {
    message.warning('请选择展示海报');
    return;
  }

  if (!tag.value) {
    message.warning('请选择公告标签');
    return;
  }

  try {
    loading.value = true;
    const data = {
      title: title.value,
      ac_type: acType.value,
      content: content.value,
      jump_url: jumpUrl.value || '',
      tag: tag.value,
    };

    if (props.record) {
      await updateDeviceAnnouncement({
        id: props.record.id,
        ...data,
        creation_time: props.record.creation_time,
        update_time: props.record.update_time,
      });
      message.success('编辑成功');
    } else {
      await addDeviceAnnouncement(data);
      message.success('添加成功');
    }

    emit('success');
    handleCancel();
  } catch (error) {
    console.error('操作失败:', error);
    message.error(error instanceof Error ? error.message : '操作失败');
  } finally {
    loading.value = false;
  }
};

// 监听弹窗显示
watch(
  () => props.visible,
  (val) => {
    if (val && props.record) {
      title.value = props.record.title;
      content.value = props.record.content;
      acType.value = props.record.ac_type;
      jumpUrl.value = props.record.jump_url;
      selectedImage.value = getFileUrl(props.record.content);
      tag.value = props.record.tag;
    } else {
      resetForm();
    }
  },
  { immediate: true },
);
</script>

<template>
  <Modal
    :title="props.record ? '编辑公告' : '新增公告'"
    :visible="visible"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    width="800px"
    :destroy-on-close="true"
    :z-index="1000"
  >
    <Form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
      <Form.Item label="公告标题" required>
        <Input v-model:value="title" placeholder="请输入公告标题" />
      </Form.Item>

      <Form.Item label="公告类型" required>
        <Select v-model:value="acType" placeholder="请选择公告类型">
          <Select.Option :value="1">全局公告</Select.Option>
          <Select.Option :value="2">单点公告</Select.Option>
        </Select>
      </Form.Item>
      <Form.Item label="公告标签" required>
        <!-- 1 活动  2通知 3 系统 -->
        <Select
          v-model:value="tag"
          placeholder="请选择公告标签"
          :options="[
            { label: '活动', value: '1' },
            { label: '通知', value: '2' },
            { label: '系统', value: '3' },
          ]"
          style="width: 100%"
        />
      </Form.Item>

      <Form.Item label="跳转链接">
        <Input v-model:value="jumpUrl" placeholder="请输入跳转链接" />
      </Form.Item>

      <Form.Item label="展示海报" required>
        <div class="flex items-center gap-4">
          <div
            class="hover:border-primary relative flex h-32 w-32 cursor-pointer items-center justify-center rounded border border-dashed border-gray-300 transition-colors"
            @click="handleSelectImage"
          >
            <img
              v-if="selectedImage"
              :src="selectedImage"
              alt="海报"
              class="absolute inset-0 h-full w-full object-contain"
            />
            <div
              v-if="selectedImage"
              class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 transition-opacity hover:opacity-100"
            >
              <span class="text-white">点击更换</span>
            </div>
            <span v-else class="text-gray-400">点击选择图片</span>
          </div>
        </div>
      </Form.Item>
    </Form>
  </Modal>

  <!-- 图片选择弹窗 -->
  <ImagePreviewModal />
</template>

<style scoped>
.upload-icon {
  font-size: 32px;
  color: #999;
}

.ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
