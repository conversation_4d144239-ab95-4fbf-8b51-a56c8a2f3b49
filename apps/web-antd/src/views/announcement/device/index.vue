<script lang="ts" setup>
import type { DeviceAnnouncementItem } from '#/api/core/deviceAnnouncement';

import { h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  deleteDeviceAnnouncement,
  getDeviceAnnouncementList,
} from '#/api/core/deviceAnnouncement';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import DeviceAnnouncementModal from './components/device-announcement-modal.vue';
import { basicSearchItems, createColumns } from './config';

// 状态定义
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getDeviceAnnouncementList,
  defaultParams: {},
});
// 弹窗控制
const modalVisible = ref(false);
const currentRecord = ref<DeviceAnnouncementItem>();

// 使用导入的配置
const columns = createColumns();

// 处理删除
const handleDelete = (record: DeviceAnnouncementItem) => {
  Modal.confirm({
    title: '提示',
    content: '确定删除该公告吗？',
    onOk: async () => {
      await deleteDeviceAnnouncement(record.id);
      message.success('删除成功');
      getList();
    },
  });
};

// 处理新增
const handleAdd = () => {
  currentRecord.value = undefined;
  modalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: DeviceAnnouncementItem) => {
  currentRecord.value = record;
  modalVisible.value = true;
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="device-announcement p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :loading="loading"
        :custom-buttons="[
          {
            text: '新增',
            icon: h(MdiPlus),
            type: 'primary',
            onClick: handleAdd,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          // 编辑
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 新增/编辑弹窗 -->
    <DeviceAnnouncementModal
      v-model:visible="modalVisible"
      :record="currentRecord"
      @success="getList"
    />
  </div>
</template>

<style lang="less" scoped>
.device-announcement {
  background-color: var(--background-deep);
}
</style>
