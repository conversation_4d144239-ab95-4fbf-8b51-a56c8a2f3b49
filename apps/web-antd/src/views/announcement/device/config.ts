import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Image } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getFileUrl } from '#/utils/file';

// 基础搜索配置
export const basicSearchItems: SearchItemConfig[] = [
  {
    field: 'ac_type',
    label: '公告类型',
    component: 'Select',
    props: {
      placeholder: '请选择公告类型',
      allowClear: true,
      options: [
        { label: '全局公告', value: 1 },
        { label: '单点公告', value: 2 },
      ],
    },
  },
  {
    field: 'jump_url',
    label: '跳转链接',
    component: 'Input',
    props: {
      placeholder: '请输入跳转链接',
      allowClear: true,
    },
  },
  {
    field: 'title',
    label: '公告标题',
    component: 'Input',
    props: {
      placeholder: '请输入公告标题',
      allowClear: true,
    },
  },
];

// 表格列配置
export const createColumns = () => [
  {
    title: '公告标题',
    dataIndex: 'title',
    align: 'center' as AlignType,
    width: 200,
  },
  {
    title: '公告类型',
    dataIndex: 'ac_type',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }: { text: number }) => {
      const types: Record<number, string> = {
        1: '全局公告',
        2: '单点公告',
      };
      return types[text] || '未知';
    },
  },
  {
    title: '海报',
    dataIndex: 'content',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }: { text: string }) => {
      return text
        ? h(Image, {
            src: getFileUrl(text),
            style: {
              width: '40px',
              height: '40px',
            },
          })
        : '-';
    },
    // customRender: ({ text }: { text: string }) => {
    //   // h函数渲染
    //   return h('Image', {
    //     src: getFileUrl(text),
    //     alt: '海报',
    //   });
    // },
  },
  {
    title: '跳转链接',
    dataIndex: 'jump_url',
    align: 'center' as AlignType,
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'creation_time',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  {
    title: '更新时间',
    dataIndex: 'update_time',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
];
