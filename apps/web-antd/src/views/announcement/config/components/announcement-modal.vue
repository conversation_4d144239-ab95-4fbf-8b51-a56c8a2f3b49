<script lang="ts" setup>
import type { AnnouncementItem } from '#/api/core/announcement';

import { ref, watch } from 'vue';

import { Card, Input, message, Modal, Select } from 'ant-design-vue';

import { addAnnouncement, updateAnnouncement } from '#/api/core/announcement';
import RichTextEditor from '#/components/RichTextEditor/index.vue';

const props = defineProps<{
  record?: AnnouncementItem;
  visible: boolean;
}>();

const emit = defineEmits<{
  success: [];
  'update:visible': [value: boolean];
}>();

const loading = ref(false);
const title = ref('');
const content = ref('<p><br></p>');
const type = ref<number>();
const tag = ref<string>();
// 重置表单
const resetForm = () => {
  title.value = '';
  content.value = '<p><br></p>';
  type.value = undefined;
};

// 处理取消
const handleCancel = () => {
  resetForm();
  emit('update:visible', false);
};

// 处理确认
const handleOk = async () => {
  // 检查内容是否为空或只有空标签
  const isEmpty =
    !content.value ||
    content.value === '<p><br></p>' ||
    content.value.trim() === '';
  if (isEmpty) {
    message.warning('请输入公告内容');
    return;
  }

  if (!title.value) {
    message.warning('请输入公告标题');
    return;
  }

  if (!type.value) {
    message.warning('请选择公告类型');
    return;
  }

  if (!tag.value) {
    message.warning('请选择公告标签');
    return;
  }

  try {
    loading.value = true;
    const data = {
      title: title.value,
      detail: content.value,
      type: type.value,
      status: 0,
      channel_id: null,
      tag: tag.value,
    };

    if (props.record) {
      await updateAnnouncement({
        id: props.record.id,
        ...data,
        creation_time: props.record.creation_time,
        update_time: new Date().toISOString(),
      });
      message.success('编辑成功');
    } else {
      await addAnnouncement({
        title: title.value,
        detail: content.value,
        type: type.value,
        tag: tag.value,
      });
      message.success('添加成功');
    }

    emit('success');
    handleCancel();
  } catch (error) {
    console.error('操作失败:', error);
    message.error(error instanceof Error ? error.message : '操作失败');
  } finally {
    loading.value = false;
  }
};

// 监听弹窗显示
watch(
  () => props.visible,
  (val) => {
    if (val && props.record) {
      title.value = props.record.title;
      content.value = props.record.detail || '<p><br></p>';
      type.value = props.record.type;
      tag.value = props.record.tag;
    } else {
      resetForm();
    }
  },
  { immediate: true },
);
</script>

<template>
  <Modal
    :title="props.record ? '编辑公告' : '新增公告'"
    :visible="visible"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    width="800px"
    :destroy-on-close="true"
  >
    <div class="space-y-4">
      <Input v-model:value="title" placeholder="请输入公告标题" />
      <Select
        v-model:value="type"
        placeholder="请选择公告类型"
        :options="[
          { label: '后台公告', value: 1 },
          { label: '充值端公告', value: 2 },
        ]"
        style="width: 100%"
      />
      <!-- 1 活动  2通知 3 系统 -->
      <Select
        v-model:value="tag"
        placeholder="请选择公告标签"
        :options="[
          { label: '活动', value: '1' },
          { label: '通知', value: '2' },
          { label: '系统', value: '3' },
        ]"
        style="width: 100%"
      />

      <Card>
        <RichTextEditor
          v-model="content"
          height="500"
          placeholder="请输入公告内容..."
        />
      </Card>
    </div>
  </Modal>
</template>

<style>
.w-e-text-container {
  min-height: 300px !important;
}
</style>
