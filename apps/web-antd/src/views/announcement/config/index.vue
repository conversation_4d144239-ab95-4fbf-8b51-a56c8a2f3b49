<script lang="ts" setup>
import type { AnnouncementItem } from '#/api/core/announcement';

import { h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  deleteAnnouncement,
  getAnnouncementList,
} from '#/api/core/announcement';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import AnnouncementModal from './components/announcement-modal.vue'; // 添加这行导入
import { basicSearchItems, createColumns } from './config';

// 状态定义
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getAnnouncementList,
  defaultParams: {},
});
// 弹窗控制
const modalVisible = ref(false);
const currentRecord = ref<AnnouncementItem>();

// 使用导入的配置
const columns = createColumns();

// 处理新增
const handleAdd = () => {
  currentRecord.value = undefined;
  modalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: AnnouncementItem) => {
  currentRecord.value = record;
  modalVisible.value = true;
};

// 处理删除
const handleDelete = (record: AnnouncementItem) => {
  Modal.confirm({
    title: '提示',
    content: '确定删除该公告吗？',
    onOk: async () => {
      await deleteAnnouncement(record.id);
      message.success('删除成功');
      getList();
    },
  });
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="announcement p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :loading="loading"
        :custom-buttons="[
          {
            text: '新增',
            icon: h(MdiPlus),
            type: 'primary',
            onClick: handleAdd,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 新增/编辑弹窗 -->
    <AnnouncementModal
      v-model:visible="modalVisible"
      :record="currentRecord"
      @success="getList"
    />
  </div>
</template>

<style lang="less" scoped>
.announcement {
  background-color: var(--background-deep);
}
</style>
