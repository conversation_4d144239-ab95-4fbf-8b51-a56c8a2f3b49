<script lang="ts" setup>
import type { SingleCardAnnouncementItem } from '#/api/core/singleCardAnnouncement';

import { h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  deleteSingleCardAnnouncement,
  getSingleCardAnnouncementList,
} from '#/api/core/singleCardAnnouncement';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import SingleCardAnnouncementModal from './components/single-card-announcement-modal.vue';
import { basicSearchItems, createColumns } from './config';

// 状态定义
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getSingleCardAnnouncementList,
  defaultParams: {},
});
// 弹窗控制
const modalVisible = ref(false);
const currentRecord = ref<SingleCardAnnouncementItem>();

// 使用导入的配置
const columns = createColumns();
// 处理删除
const handleDelete = (record: SingleCardAnnouncementItem) => {
  Modal.confirm({
    title: '提示',
    content: '确定删除该公告吗？',
    onOk: async () => {
      await deleteSingleCardAnnouncement(record.card_id);
      message.success('删除成功');
      getList();
    },
  });
};

// 处理新增
const handleAdd = () => {
  currentRecord.value = undefined;
  modalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: SingleCardAnnouncementItem) => {
  currentRecord.value = record;
  modalVisible.value = true;
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="single-card-announcement p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :loading="loading"
        :custom-buttons="[
          {
            text: '新增',
            icon: h(MdiPlus),
            type: 'primary',
            onClick: handleAdd,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 新增/编辑弹窗 -->
    <SingleCardAnnouncementModal
      v-model:visible="modalVisible"
      :record="currentRecord"
      @success="getList"
    />
  </div>
</template>

<style lang="less" scoped>
.single-card-announcement {
  background-color: var(--background-deep);
}
</style>
