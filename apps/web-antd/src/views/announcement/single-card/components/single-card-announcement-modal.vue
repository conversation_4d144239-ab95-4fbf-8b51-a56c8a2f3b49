<script lang="ts" setup>
import type { UploadProps } from 'ant-design-vue';

import type { SingleCardAnnouncementItem } from '#/api/core/singleCardAnnouncement';

import { ref, watch } from 'vue';

import {
  Button,
  Card,
  Input,
  message,
  Modal,
  Select,
  Upload,
} from 'ant-design-vue';

import {
  addSingleCardAnnouncement,
  downloadAnnouncementTemplate,
  updateSingleCardAnnouncement,
} from '#/api/core/singleCardAnnouncement';
import RichTextEditor from '#/components/RichTextEditor/index.vue';

const props = defineProps<{
  record?: SingleCardAnnouncementItem;
  visible: boolean;
}>();

const emit = defineEmits<{
  success: [];
  'update:visible': [value: boolean];
}>();

const loading = ref(false);
const title = ref('');
const content = ref('<p><br></p>');
const fileList = ref<any[]>([]);
const tag = ref<string>();

// 重置表单
const resetForm = () => {
  title.value = '';
  content.value = '<p><br></p>';
  fileList.value = [];
  tag.value = undefined;
};

// 处理取消
const handleCancel = () => {
  resetForm();
  emit('update:visible', false);
};

// 处理文件上传改变
// const handleChange = (info: UploadChangeParam) => {
//   fileList.value = info.fileList;

//   if (info.file.status === 'done') {
//     const response = info.file.response;
//     if (response?.code === 1) {
//       message.success(`${info.file.name} 上传成功`);
//     } else {
//       message.error(
//         `${info.file.name} 上传失败: ${response?.msg || '未知错误'}`,
//       );
//     }
//   } else if (info.file.status === 'error') {
//     message.error(`${info.file.name} 上传失败`);
//   }
// };

// 上传前检查
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 这里可以添加文件类型和大小的检查
  const isExcel =
    file.type === 'application/vnd.ms-excel' ||
    file.type ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
  if (!isExcel) {
    message.error('只能上传 Excel 文件!');
    return Upload.LIST_IGNORE;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('文件必须小于 2MB!');
    return Upload.LIST_IGNORE;
  }
  return true;
};

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    const response = await downloadAnnouncementTemplate();
    // 创建 Blob 对象
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' });
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    // 创建临时 a 标签并触发下载
    const link = document.createElement('a');
    link.href = url;
    link.download = '单卡公告导入模板.xls';
    // 为了避免跳转到其他页面，我们使用 window.location.href 来直接下载文件
    window.location.href = url;
    message.success('下载成功');
    // 清理
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败');
  }
};

// 处理确认
const handleOk = async () => {
  // 检查内容是否为空或只有空标签
  const isEmpty =
    !content.value ||
    content.value === '<p><br></p>' ||
    content.value.trim() === '';
  if (isEmpty) {
    message.warning('请输入公告内容');
    return;
  }

  if (!title.value) {
    message.warning('请输入公告标题');
    return;
  }

  if (fileList.value.length === 0) {
    message.warning('请上传文件');
    return;
  }

  if (!tag.value) {
    message.warning('请选择公告标签');
    return;
  }

  try {
    loading.value = true;
    const formData = new FormData();
    formData.append('title', title.value);
    formData.append('detail', content.value);
    formData.append('tag', tag.value);

    // 如果有文件，添加到 FormData
    if (fileList.value[0]?.originFileObj) {
      formData.append('file', fileList.value[0].originFileObj);
    }

    if (props.record) {
      await updateSingleCardAnnouncement({
        card_id: props.record.card_id,
        card__void_number: props.record.card__void_number,
        title: title.value,
        detail: content.value,
        creation_time: props.record.creation_time,
        tag: tag.value,
      });
      message.success('编辑成功');
    } else {
      const res = await addSingleCardAnnouncement(formData);
      if (res.code === 1) {
        Modal.success({
          title: '提交成功',
          content: '任务已提交，请前往任务中心查看结果',
          onOk: () => {
            emit('success');
            handleCancel();
          },
        });
        return;
      }
    }

    emit('success');
    handleCancel();
  } catch (error) {
    console.error('操作失败:', error);
    message.error(error instanceof Error ? error.message : '操作失败');
  } finally {
    loading.value = false;
  }
};

// 监听弹窗显示
watch(
  () => props.visible,
  (val) => {
    if (val && props.record) {
      title.value = props.record.title;
      content.value = props.record.detail || '<p><br></p>';
      // 如果有文件，添加到文件列表
      if (props.record.file_url) {
        fileList.value = [
          {
            uid: '-1',
            name: '已上传文件.xlsx',
            status: 'done',
            url: props.record.file_url,
          },
        ];
      }
    } else {
      resetForm();
    }
  },
  { immediate: true },
);
</script>

<template>
  <Modal
    :title="props.record ? '编辑公告' : '新增公告'"
    :visible="visible"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    width="800px"
    :destroy-on-close="true"
  >
    <div class="mt-6 space-y-4">
      <Input v-model:value="title" placeholder="请输入公告标题" />
      <!-- 1 活动  2通知 3 系统 -->
      <Select
        v-model:value="tag"
        placeholder="请选择公告标签"
        :options="[
          { label: '活动', value: '1' },
          { label: '通知', value: '2' },
          { label: '系统', value: '3' },
        ]"
        style="width: 100%"
      />

      <div class="flex items-center gap-4">
        <Upload
          v-model:file-list="fileList"
          name="file"
          :action="null"
          :before-upload="beforeUpload"
          :max-count="1"
          :show-upload-list="true"
          :custom-request="() => {}"
        >
          <Button type="primary">上传文件</Button>
        </Upload>
        <Button type="link" @click="handleDownloadTemplate">下载模板</Button>
      </div>
      <Card>
        <RichTextEditor
          v-model="content"
          height="500"
          placeholder="请输入公告内容..."
        />
      </Card>
    </div>
  </Modal>
</template>

<style scoped>
.upload-icon {
  font-size: 32px;
  color: #999;
}

.ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
