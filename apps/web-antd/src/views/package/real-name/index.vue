<script lang="ts" setup>
import type { RealName } from '#/api/core/device';

import { computed, h, onMounted } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import { deleteRealName, getRealNameList } from '#/api/core/device';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { advancedSearchSchema, columns, searchSchema } from './config';
import { useRealNameForm } from './hooks/useRealNameForm';

const {
  tableData,
  loading,
  pagination,
  searchParams,
  handleTableChange,
  handleSearch,
  handleReset,
  getList,
} = useTable<RealName, { page: number; pageSize: number }>({
  api: getRealNameList,
  defaultParams: {},
});

const handleAdd = () => {
  showRealNameModal();
};

const handleEdit = (record: any) => {
  showRealNameModal(record);
};

const handleDelete = (record: any) => {
  Modal.confirm({
    title: '删除确认',
    content: `确定要删除规则 ${record.name} 吗？此操作不可恢复！`,
    type: 'warning',
    okType: 'danger',
    async onOk() {
      try {
        const res = await deleteRealName(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          throw new Error(res.msg || '删除失败');
        }
      } catch (error) {
        message.error(error instanceof Error ? error.message : '删除失败');
      }
    },
  });
};

const actionButtons = [
  {
    key: 'edit',
    text: '编辑',
    type: 'link' as const,
    onClick: handleEdit,
  },
  {
    key: 'delete',
    text: '删除',
    type: 'link' as const,
    danger: true,
    onClick: handleDelete,
  },
];

const customButtons = computed(() => {
  const buttons = [
    {
      key: 'add',
      text: '新增规则',
      icon: h(MdiPlus),
      onClick: handleAdd,
    },
  ];
  return buttons;
});

const { RealNameFormModal, show: showRealNameModal } = useRealNameForm(() => {
  getList();
});

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="p-2">
    <Card>
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchSchema"
        :advanced-items="advancedSearchSchema"
        :custom-buttons="customButtons"
        @reset="handleReset"
        @search="handleSearch"
        @update:model-value="(val) => (formData = val)"
      />

      <BasicTable
        class="mt-4"
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :action-buttons="actionButtons"
        @change="handleTableChange"
        row-key="id"
        show-action
      />
    </Card>

    <!-- 添加/编辑实名规则弹窗 -->
    <component :is="RealNameFormModal()" />
  </div>
</template>
