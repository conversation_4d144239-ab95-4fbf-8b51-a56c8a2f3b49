import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed, ref, watch } from 'vue';

import { addRealName, updateRealName } from '#/api/core/device';
import { useForm } from '#/hooks/useAnsheng';

// 定义RealName接口，确保与API期望的类型匹配
interface RealName {
  id?: number;
  name: string;
  realNameRules: number;
  realNameLike?: number;
  likeAddress?: string;
  requestAddress?: string;
  apiAccount?: string;
  apiPwd?: string;
  apiKey?: string;
  creationTime?: string;
  updateTime?: string;
}

// 表单状态接口，用于内部formData类型
interface RealNameFormState {
  name: string;
  realNameRules: number;
  realNameLike?: number;
  likeAddress?: string;
  requestAddress?: string;
  apiAccount?: string;
  apiPwd?: string;
  apiKey?: string;
}

export function useRealNameForm(callback?: () => void) {
  // 使用ref跟踪表单显示逻辑所需的字段
  const realNameRules = ref<number>(1);
  const realNameLike = ref<number>(1);

  // 使用useForm创建表单
  const form = useForm<RealNameFormState>({
    title: (isEdit) => (isEdit ? '编辑实名规则' : '新增实名规则'),
    width: 600,
    draggable: true,
    simpleLayout: {
      showGroupTitle: true, // 不显示分组标题
      padding: 0, // 更小的内边距
    },
    // 创建方法
    create: async (params) => {
      // 构建符合RealName类型的请求数据
      const requestData: Omit<RealName, 'creationTime' | 'id' | 'updateTime'> =
        {
          name: params.name,
          realNameRules: params.realNameRules,
          realNameLike: params.realNameLike,
          likeAddress: params.likeAddress || '',
          requestAddress: params.requestAddress || '',
          apiAccount: params.apiAccount || '',
          apiPwd: params.apiPwd || '',
          apiKey: params.apiKey || '',
        };

      const res = await addRealName(requestData as any);
      if (res.code !== 1) {
        throw new Error(res.msg || '添加失败');
      }
      return res;
    },
    // 更新方法
    update: async (id, params) => {
      // 构建符合RealName类型的请求数据
      const requestData: Omit<RealName, 'creationTime' | 'updateTime'> = {
        id,
        name: params.name,
        realNameRules: params.realNameRules,
        realNameLike: params.realNameLike,
        likeAddress: params.likeAddress || '',
        requestAddress: params.requestAddress || '',
        apiAccount: params.apiAccount || '',
        apiPwd: params.apiPwd || '',
        apiKey: params.apiKey || '',
      };

      const res = await updateRealName(requestData as any);
      if (res.code !== 1) {
        throw new Error(res.msg || '修改失败');
      }
      return res;
    },
    onSuccess: callback,
    defaultValues: {
      name: '',
      realNameRules: 1,
      realNameLike: 1,
      likeAddress: '',
      requestAddress: '',
      apiAccount: '',
      apiPwd: '',
      apiKey: '',
    },
    // 表单验证规则
    rules: {
      name: [{ required: true, message: '请输入规则名称' }],
      realNameRules: [{ required: true, message: '请选择实名规则' }],
      realNameLike: [
        {
          validator: (_, value) => {
            if (realNameRules.value !== 1) {
              return value
                ? Promise.resolve()
                : Promise.reject(new Error('请选择实名链接'));
            }
            return Promise.resolve();
          },
        },
      ],
      likeAddress: [
        {
          validator: (_, value) => {
            if (realNameRules.value !== 1 && realNameLike.value === 2) {
              return value
                ? Promise.resolve()
                : Promise.reject(new Error('请输入自定义地址'));
            }
            return Promise.resolve();
          },
        },
      ],
      requestAddress: [
        {
          validator: (_, value) => {
            if (realNameRules.value !== 1 && realNameLike.value === 4) {
              return value
                ? Promise.resolve()
                : Promise.reject(new Error('请输入请求地址'));
            }
            return Promise.resolve();
          },
        },
      ],
      apiAccount: [
        {
          validator: (_, value) => {
            if (realNameRules.value !== 1 && realNameLike.value === 4) {
              return value
                ? Promise.resolve()
                : Promise.reject(new Error('请输入对接账户'));
            }
            return Promise.resolve();
          },
        },
      ],
      apiPwd: [
        {
          validator: (_, value) => {
            if (realNameRules.value !== 1 && realNameLike.value === 4) {
              return value
                ? Promise.resolve()
                : Promise.reject(new Error('请输入对接密码'));
            }
            return Promise.resolve();
          },
        },
      ],
      apiKey: [
        {
          validator: (_, value) => {
            if (realNameRules.value !== 1 && realNameLike.value === 4) {
              return value
                ? Promise.resolve()
                : Promise.reject(new Error('请输入对接密钥'));
            }
            return Promise.resolve();
          },
        },
      ],
    },
  });

  // 监听表单数据变化以更新本地ref
  watch(
    () => form.formData.realNameRules,
    (val) => {
      realNameRules.value = val;

      // 如果选择了"无需实名"，重置相关字段
      if (val === 1) {
        form.formData.realNameLike = undefined;
        form.formData.likeAddress = '';
        form.formData.requestAddress = '';
        form.formData.apiAccount = '';
        form.formData.apiPwd = '';
        form.formData.apiKey = '';
      }
    },
  );

  watch(
    () => form.formData.realNameLike,
    (val) => {
      realNameLike.value = val;

      // 根据实名链接类型重置相关字段
      if (val !== 2) {
        form.formData.likeAddress = '';
      }

      if (val !== 4) {
        form.formData.requestAddress = '';
        form.formData.apiAccount = '';
        form.formData.apiPwd = '';
        form.formData.apiKey = '';
      }
    },
  );

  // 使用声明式表单配置
  const formGroups = computed((): FormGroup[] => [
    {
      title: '基本信息',
      fields: [
        // 规则名称（始终显示）
        {
          name: 'name',
          label: '规则名称',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入规则名称',
            allowClear: true,
          },
        },
        // 实名规则（始终显示）
        {
          name: 'realNameRules',
          label: '实名规则',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择实名规则',
            options: [
              { label: '无需实名', value: 1 },
              { label: '先充值再实名', value: 2 },
              { label: '先实名再充值', value: 3 },
            ],
            allowClear: true,
          },
        },
        // 实名链接类型（条件显示）
        {
          name: 'realNameLike',
          label: '实名链接',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择实名链接',
            options: [
              { label: '系统默认', value: 1 },
              { label: '自定义填写', value: 2 },
              { label: '自适应跳转', value: 3 },
              { label: '自定义对接', value: 4 },
            ],
            allowClear: true,
          },
          show: () => form.formData.realNameRules !== 1,
        },
        // 自定义地址（条件显示）
        {
          name: 'likeAddress',
          label: '自定义地址',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入自定义地址',
            allowClear: true,
          },
          show: () =>
            form.formData.realNameRules !== 1 &&
            form.formData.realNameLike === 2,
        },
        // 请求地址（条件显示）
        {
          name: 'requestAddress',
          label: '请求地址',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入请求地址',
            allowClear: true,
          },
          show: () =>
            form.formData.realNameRules !== 1 &&
            form.formData.realNameLike === 4,
        },
        // 对接账户（条件显示）
        {
          name: 'apiAccount',
          label: '对接账户',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入对接账户',
            allowClear: true,
          },
          show: () =>
            form.formData.realNameRules !== 1 &&
            form.formData.realNameLike === 4,
        },
        // 对接密码（条件显示）
        {
          name: 'apiPwd',
          label: '对接密码',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入对接密码',
            type: 'password',
            allowClear: true,
          },
          show: () =>
            form.formData.realNameRules !== 1 &&
            form.formData.realNameLike === 4,
        },
        // 对接密钥（条件显示）
        {
          name: 'apiKey',
          label: '对接密钥',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入对接密钥',
            allowClear: true,
          },
          show: () =>
            form.formData.realNameRules !== 1 &&
            form.formData.realNameLike === 4,
        },
      ],
    },
  ]);

  // 自定义show方法，优化表单初始化
  const show = async (record?: any) => {
    if (record?.id) {
      // 编辑模式
      await form.show(record);
    } else {
      // 新增模式
      await form.show();
    }
  };

  // 渲染表单弹窗
  const RealNameFormModal = () => form.renderFormModal(formGroups.value);

  return {
    form,
    show,
    RealNameFormModal,
  };
}
