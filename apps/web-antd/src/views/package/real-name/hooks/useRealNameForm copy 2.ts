import type { FormGroup } from '#/hooks/common/useFormModal';

import { h } from 'vue';

import { Col, Form, Input, Select } from 'ant-design-vue';

import { addRealName, updateRealName } from '#/api/core/device';
// import { useFormModal } from '#/hooks/common/useFormModal';
import { useForm } from '#/hooks/useAnsheng/useForm';

const FormItem = Form.Item;

// 定义RealName接口，确保与API期望的类型匹配
interface RealName {
  id?: number;
  name: string;
  realNameRules: number;
  realNameLike?: number;
  likeAddress?: string;
  requestAddress?: string;
  apiAccount?: string;
  apiPwd?: string;
  apiKey?: string;
  creationTime?: string;
  updateTime?: string;
}

// 表单状态接口，用于内部formData类型
interface RealNameFormState {
  name: string;
  realNameRules: number;
  realNameLike?: number;
  likeAddress?: string;
  requestAddress?: string;
  apiAccount?: string;
  apiPwd?: string;
  apiKey?: string;
}

export function useRealNameForm(callback?: () => void) {
  const formGroups: FormGroup[] = [
    {
      title: '基本信息',
      // title: ' ',
      content: (formData) => {
        const formItems = [
          // 规则名称（始终显示）
          h(Col, { span: 24 }, [
            h(
              FormItem,
              {
                label: '规则名称',
                name: 'name',
                rules: [{ required: true, message: '请输入规则名称' }],
              },
              () =>
                h(Input, {
                  placeholder: '请输入规则名称',
                  value: formData.name,
                  'onUpdate:value': (val) => (formData.name = val),
                  allowClear: true,
                }),
            ),
          ]),
          // 实名规则（始终显示）
          h(Col, { span: 24 }, [
            h(
              FormItem,
              {
                label: '实名规则',
                name: 'realNameRules',
                rules: [{ required: true, message: '请选择实名规则' }],
              },
              () =>
                h(Select, {
                  placeholder: '请选择实名规则',
                  value: formData.realNameRules,
                  'onUpdate:value': (val) => {
                    formData.realNameRules = val;
                    // 重置相关字段
                    if (val === 1) {
                      formData.realNameLike = undefined;
                      formData.likeAddress = '';
                      formData.requestAddress = '';
                      formData.apiAccount = '';
                      formData.apiPwd = '';
                      formData.apiKey = '';
                    }
                  },
                  options: [
                    { label: '无需实名', value: 1 },
                    { label: '先充值再实名', value: 2 },
                    { label: '先实名再充值', value: 3 },
                  ],
                  allowClear: true,
                }),
            ),
          ]),
        ];

        // 如果不是"无需实名"，显示实名链接类型
        if (formData.realNameRules !== 1) {
          formItems.push(
            h(Col, { span: 24 }, [
              h(
                FormItem,
                {
                  label: '实名链接',
                  name: 'realNameLike',
                  rules: [{ required: true, message: '请选择实名链接' }],
                },
                () =>
                  h(Select, {
                    placeholder: '请选择实名链接',
                    value: formData.realNameLike,
                    'onUpdate:value': (val) => {
                      formData.realNameLike = val;
                      // 重置相关字段
                      formData.likeAddress = '';
                      formData.requestAddress = '';
                      formData.apiAccount = '';
                      formData.apiPwd = '';
                      formData.apiKey = '';
                    },
                    options: [
                      { label: '系统默认', value: 1 },
                      { label: '自定义填写', value: 2 },
                      { label: '自适应跳转', value: 3 },
                      { label: '自定义对接', value: 4 },
                    ],
                    allowClear: true,
                  }),
              ),
            ]),
          );

          // 如果选择"自定义填写"，显示自定义地址输入框
          if (formData.realNameLike === 2) {
            formItems.push(
              h(Col, { span: 24 }, [
                h(
                  FormItem,
                  {
                    label: '自定义地址',
                    name: 'likeAddress',
                    rules: [{ required: true, message: '请输入自定义地址' }],
                  },
                  () =>
                    h(Input, {
                      placeholder: '请输入自定义地址',
                      value: formData.likeAddress,
                      'onUpdate:value': (val) => (formData.likeAddress = val),
                      allowClear: true,
                    }),
                ),
              ]),
            );
          }

          // 如果选择"自定义对接"，显示对接相关字段
          if (formData.realNameLike === 4) {
            formItems.push(
              h(Col, { span: 24 }, [
                h(
                  FormItem,
                  {
                    label: '请求地址',
                    name: 'requestAddress',
                    rules: [{ required: true, message: '请输入请求地址' }],
                  },
                  () =>
                    h(Input, {
                      placeholder: '请输入请求地址',
                      value: formData.requestAddress,
                      'onUpdate:value': (val) =>
                        (formData.requestAddress = val),
                      allowClear: true,
                    }),
                ),
              ]),
              h(Col, { span: 24 }, [
                h(
                  FormItem,
                  {
                    label: '对接账户',
                    name: 'apiAccount',
                    rules: [{ required: true, message: '请输入对接账户' }],
                  },
                  () =>
                    h(Input, {
                      placeholder: '请输入对接账户',
                      value: formData.apiAccount,
                      'onUpdate:value': (val) => (formData.apiAccount = val),
                      allowClear: true,
                    }),
                ),
              ]),
              h(Col, { span: 24 }, [
                h(
                  FormItem,
                  {
                    label: '对接密码',
                    name: 'apiPwd',
                    rules: [{ required: true, message: '请输入对接密码' }],
                  },
                  () =>
                    h(Input, {
                      placeholder: '请输入对接密码',
                      value: formData.apiPwd,
                      'onUpdate:value': (val) => (formData.apiPwd = val),
                      type: 'password',
                      allowClear: true,
                    }),
                ),
              ]),
              h(Col, { span: 24 }, [
                h(
                  FormItem,
                  {
                    label: '对接密钥',
                    name: 'apiKey',
                    rules: [{ required: true, message: '请输入对接密钥' }],
                  },
                  () =>
                    h(Input, {
                      placeholder: '请输入对接密钥',
                      value: formData.apiKey,
                      'onUpdate:value': (val) => (formData.apiKey = val),
                      allowClear: true,
                    }),
                ),
              ]),
            );
          }
        }

        return formItems;
      },
    },
  ];

  // 添加getDetail模拟函数，用于编辑模式
  const getDetail = async (
    id: number,
  ): Promise<{ code: number; data: RealNameFormState; msg: string }> => {
    // 这里应该调用实际的详情获取API
    // 这是一个示例实现，实际项目中应替换为真实API调用
    return {
      code: 1,
      data: {} as RealNameFormState, // 实际上这个值不会被使用，因为我们手动设置表单数据
      msg: 'success',
    };
  };

  const {
    formRef,
    formData,
    visible,
    loading,
    isEdit,
    currentId,
    show: originalShow,
    close,
    setFormData,
    renderFormModal,
  } = useForm<RealNameFormState>({
    title: (isEdit) => (isEdit ? '编辑实名规则' : '新增实名规则'),
    width: 600,
    draggable: true,
    // 添加getDetail函数以支持编辑模式
    getDetail,
    create: async (params) => {
      // 构建符合RealName类型的请求数据
      const requestData: Omit<RealName, 'creationTime' | 'id' | 'updateTime'> =
        {
          name: params.name,
          realNameRules: params.realNameRules,
          realNameLike: params.realNameLike,
          likeAddress: params.likeAddress || '',
          requestAddress: params.requestAddress || '',
          apiAccount: params.apiAccount || '',
          apiPwd: params.apiPwd || '',
          apiKey: params.apiKey || '',
        };

      const res = await addRealName(requestData as any);
      if (res.code !== 1) {
        throw new Error(res.msg || '添加失败');
      }
      return res;
    },
    update: async (id, params) => {
      // 构建符合RealName类型的请求数据
      const requestData: Omit<RealName, 'creationTime' | 'updateTime'> = {
        id,
        name: params.name,
        realNameRules: params.realNameRules,
        realNameLike: params.realNameLike,
        likeAddress: params.likeAddress || '',
        requestAddress: params.requestAddress || '',
        apiAccount: params.apiAccount || '',
        apiPwd: params.apiPwd || '',
        apiKey: params.apiKey || '',
      };

      const res = await updateRealName(requestData as any);
      if (res.code !== 1) {
        throw new Error(res.msg || '修改失败');
      }
      return res;
    },
    onSuccess: callback,
    defaultValues: {
      realNameRules: 1,
      realNameLike: 1,
      likeAddress: '',
      requestAddress: '',
      apiAccount: '',
      apiPwd: '',
      apiKey: '',
    },
  });

  // 自定义show方法，修复编辑模式问题
  const show = async (record?: any) => {
    if (record?.id) {
      // 编辑模式：先调用originalShow传入id以标记为编辑模式，然后再设置数据
      await originalShow(record.id);

      // 设置表单数据
      setFormData({
        name: record.name,
        realNameRules: record.realNameRules,
        realNameLike: record.realNameLike,
        likeAddress: record.likeAddress || '',
        requestAddress: record.requestAddress || '',
        apiAccount: record.apiAccount || '',
        apiPwd: record.apiPwd || '',
        apiKey: record.apiKey || '',
      });
    } else {
      // 新增模式：直接调用originalShow不传id
      await originalShow();
    }
  };

  return {
    formRef,
    formData,
    visible,
    loading,
    isEdit,
    currentId,
    show,
    close,
    setFormData,
    renderFormModal: () => renderFormModal(formGroups),
  };
}
