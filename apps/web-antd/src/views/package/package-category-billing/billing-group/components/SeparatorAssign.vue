<script lang="ts" setup>
import type { BillingGroupOption } from '#/api';

import { ref } from 'vue';

import { Form } from 'ant-design-vue';

const props = defineProps<{
  billingGroups: BillingGroupOption[];
}>();

const formRef = ref();
const form = ref({
  seriesId: undefined as number | undefined,
  cardNos: '',
});

const rules = {
  seriesId: [{ required: true, message: '请选择计费组' }],
  cardNos: [{ required: true, message: '请输入设备号码' }],
};

defineExpose({
  formRef,
  form,
  rules,
});
</script>

<template>
  <Form ref="formRef" :model="form" :rules="rules">
    <Form.Item label="计费组" name="seriesId">
      <a-select
        v-model:value="form.seriesId"
        placeholder="请选择计费组"
        :options="
          props.billingGroups.map((item) => ({
            label: item.name,
            value: item.id,
          }))
        "
      />
    </Form.Item>
    <Form.Item label="设备号码" name="cardNos">
      <a-textarea
        v-model:value="form.cardNos"
        placeholder="请输入设备号码，多个用逗号分隔"
        :rows="4"
      />
    </Form.Item>
  </Form>
</template>
