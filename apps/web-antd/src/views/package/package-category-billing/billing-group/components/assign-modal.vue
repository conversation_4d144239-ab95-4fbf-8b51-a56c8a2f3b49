<script lang="ts" setup>
import type { UploadProps } from 'ant-design-vue';

import type { BillingGroupOption } from '#/api';

import { computed, ref } from 'vue';

import { MdiUpload } from '@vben/icons';

import { Form, message, Modal } from 'ant-design-vue';

import {
  assignSeparatorBillingGroup,
  assignSequentialBillingGroup,
  importSeparatorBillingGroup,
} from '#/api';

const _props = defineProps<{
  billingGroups: BillingGroupOption[];
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}>();

const formRef = ref();
const activeKey = ref('separator');
const loading = ref(false);

// 分隔符分配表单
const separatorForm = ref({
  cardNos: '',
  seriesId: undefined as number | undefined,
});

// 连号分配表单
const sequentialForm = ref({
  startDeviceNo: '',
  endDeviceNo: '',
  seriesId: undefined as number | undefined,
});

// 文件上传相关
const fileList = ref<any[]>([]);
const uploadProps = computed<UploadProps>(() => ({
  name: 'file',
  multiple: false,
  fileList: fileList.value,
  beforeUpload: (file) => {
    fileList.value = [file];
    return false;
  },
  onRemove: () => {
    fileList.value = [];
  },
}));

// 表单验证规则
const rules = {
  seriesId: [{ required: true, message: '请选择计费组' }],
  cardNos: [{ required: true, message: '请输入设备号码' }],
  startDeviceNo: [{ required: true, message: '请输入开始设备号' }],
  endDeviceNo: [{ required: true, message: '请输入结束设备号' }],
};

// 处理取消
const handleCancel = () => {
  formRef.value?.resetFields();
  fileList.value = [];
  emit('update:visible', false);
};

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    loading.value = true;

    switch (activeKey.value) {
      case 'import': {
        if (fileList.value.length === 0) {
          message.error('请选择要上传的文件');
          return;
        }
        await importSeparatorBillingGroup({
          file: fileList.value[0],
          seriesId: separatorForm.value.seriesId!,
        });

        break;
      }
      case 'separator': {
        await assignSeparatorBillingGroup({
          cardNos: separatorForm.value.cardNos,
          seriesId: separatorForm.value.seriesId!,
        });

        break;
      }
      case 'sequential': {
        await assignSequentialBillingGroup({
          startDeviceNo: sequentialForm.value.startDeviceNo,
          endDeviceNo: sequentialForm.value.endDeviceNo,
          seriesId: sequentialForm.value.seriesId!,
        });

        break;
      }
      // No default
    }

    message.success('分配成功');
    emit('success');
    handleCancel();
  } catch (error: any) {
    message.error(error.message || '分配失败');
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <Modal
    :visible="_props.visible"
    title="分配计费组"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <Form
      ref="formRef"
      :model="activeKey === 'separator' ? separatorForm : sequentialForm"
      :rules="rules"
    >
      <a-tabs v-model:active-key="activeKey">
        <!-- 分隔符分配 -->
        <a-tab-pane key="separator" tab="分隔符分配">
          <Form.Item label="计费组" name="seriesId">
            <a-select
              v-model:value="separatorForm.seriesId"
              placeholder="请选择计费组"
              :options="
                _props.billingGroups.map((item) => ({
                  label: item.name,
                  value: item.id,
                }))
              "
            />
          </Form.Item>
          <Form.Item label="设备号码" name="cardNos">
            <a-textarea
              v-model:value="separatorForm.cardNos"
              placeholder="请输入设备号码，多个用逗号分隔"
              :rows="4"
            />
          </Form.Item>
        </a-tab-pane>

        <!-- 导入分配 -->
        <a-tab-pane key="import" tab="导入分配">
          <Form.Item label="计费组" name="seriesId">
            <a-select
              v-model:value="separatorForm.seriesId"
              placeholder="请选择计费组"
              :options="
                _props.billingGroups.map((item) => ({
                  label: item.name,
                  value: item.id,
                }))
              "
            />
          </Form.Item>
          <Form.Item label="导入文件">
            <a-upload-dragger v-bind="uploadProps">
              <p class="ant-upload-drag-icon">
                <MdiUpload />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">支持单个文件上传</p>
            </a-upload-dragger>
          </Form.Item>
        </a-tab-pane>

        <!-- 连号分配 -->
        <a-tab-pane key="sequential" tab="连号分配">
          <Form.Item label="计费组" name="seriesId">
            <a-select
              v-model:value="sequentialForm.seriesId"
              placeholder="请选择计费组"
              :options="
                _props.billingGroups.map((item) => ({
                  label: item.name,
                  value: item.id,
                }))
              "
            />
          </Form.Item>
          <Form.Item label="开始设备号" name="startDeviceNo">
            <a-input
              v-model:value="sequentialForm.startDeviceNo"
              placeholder="请输入开始设备号"
            />
          </Form.Item>
          <Form.Item label="结束设备号" name="endDeviceNo">
            <a-input
              v-model:value="sequentialForm.endDeviceNo"
              placeholder="请输入结束设备号"
            />
          </Form.Item>
        </a-tab-pane>
      </a-tabs>
    </Form>
  </Modal>
</template>

<style lang="less" scoped>
.ant-upload-drag-icon {
  margin-bottom: 8px;
  color: #40a9ff;
  font-size: 48px;
}

.ant-upload-text {
  margin-bottom: 4px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
}

.ant-upload-hint {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}
</style>
