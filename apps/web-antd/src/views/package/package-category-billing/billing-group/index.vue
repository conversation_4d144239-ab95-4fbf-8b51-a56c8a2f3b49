<script lang="ts" setup>
import { computed, h, onMounted } from 'vue';

import { MdiAccountGroup, MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import { deleteBillingGroup, getBillingGroupList } from '#/api';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { basicSearchItems, createColumns } from './config';
import { useBillingGroupModal } from './hooks/useAddBillingGroup';
import { useAssignBillingGroup } from './hooks/useAssignBillingGroup';

const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getBillingGroupList,
  defaultParams: {},
});

// 使用新的hook替换原来的模态框
const { openEditModal, BillingGroupModal } = useBillingGroupModal(() => {
  getList();
});

// 使用分配计费组hook
const { openModal: openAssignModal, AssignModal: AssignModalComponent } =
  useAssignBillingGroup(() => {
    getList();
  });

// 处理编辑
const handleEdit = (record: any) => {
  openEditModal(record);
};

// 处理删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该计费组吗？',
    async onOk() {
      try {
        const res = await deleteBillingGroup(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          throw new Error(res.msg || '删除失败');
        }
      } catch (error) {
        message.error(error instanceof Error ? error.message : '删除失败');
      }
    },
  });
};

// 处理新增
const handleAdd = () => {
  openEditModal();
};

// 处理分配
const handleAssign = () => {
  openAssignModal();
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    icon: h(MdiPlus),
    text: '新增计费组',
    type: 'primary' as const,
    onClick: handleAdd,
  },
  // 分配计费组
  {
    key: 'assign',
    icon: h(MdiAccountGroup),
    text: '分配计费组',
    onClick: handleAssign,
  },
];

// 表格列配置
const columns = computed(() => createColumns());

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="billing-group">
    <!-- <Card :bordered="false"> -->
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="basicSearchItems"
      :custom-buttons="toolbarButtons"
      :loading="loading"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :show-action="true"
      :action-buttons="[
        {
          key: 'edit',
          text: '编辑',
          type: 'link',
          onClick: handleEdit,
        },
        {
          key: 'delete',
          text: '删除',
          type: 'link',
          danger: true,
          onClick: handleDelete,
        },
      ]"
      :pagination="pagination"
      @change="handleTableChange"
    />

    <!-- 使用新的hook渲染弹窗组件 -->
    <component :is="BillingGroupModal" />
    <component :is="AssignModalComponent" />
    <!-- </Card> -->
  </div>
</template>

<style lang="less" scoped>
.billing-group {
  background-color: var(--background-deep);
}
</style>
