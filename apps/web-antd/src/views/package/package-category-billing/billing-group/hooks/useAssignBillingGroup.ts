import type { SelectValue } from 'ant-design-vue/es/select';

import type { BillingGroupOption } from '#/api';
import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed, ref, watch } from 'vue';

import { message } from 'ant-design-vue';

import {
  assignSeparatorBillingGroup,
  assignSequentialBillingGroup,
  downloadDeviceAssignTemplate,
  getBillingGroupOptions,
  importSeparatorBillingGroup,
} from '#/api';
import { useForm } from '#/hooks/useAnsheng';
import { handleFileDownload } from '#/utils/export';

interface FormData {
  seriesId: SelectValue;
  cardNos: string;
  startDeviceNo: string;
  endDeviceNo: string;
  file: File | null;
  assignType: 'import' | 'separator' | 'sequential';
}

export function useAssignBillingGroup(onSuccess?: () => void) {
  // 计费组选项
  const billingGroups = ref<BillingGroupOption[]>([]);
  // 当前选择的分配方式
  const assignType = ref<'import' | 'separator' | 'sequential'>('separator');

  // 使用 useForm 管理表单
  const form = useForm<FormData>({
    title: '分配计费组',
    width: 700,
    simpleLayout: {
      showGroupTitle: false,
      padding: 0,
    },
    defaultValues: {
      seriesId: undefined,
      cardNos: '',
      startDeviceNo: '',
      endDeviceNo: '',
      file: null,
      assignType: 'separator',
    },
    // 表单验证规则
    rules: {
      seriesId: [{ required: true, message: '请选择计费组' }],
      cardNos: [
        {
          validator: (_, value) => {
            if (assignType.value !== 'separator') return Promise.resolve();
            return value?.trim()
              ? Promise.resolve()
              : Promise.reject(new Error('请输入设备号码'));
          },
        },
      ],
      startDeviceNo: [
        {
          validator: (_, value) => {
            if (assignType.value !== 'sequential') return Promise.resolve();
            return value
              ? Promise.resolve()
              : Promise.reject(new Error('请输入开始设备号'));
          },
        },
      ],
      endDeviceNo: [
        {
          validator: (_, value) => {
            if (assignType.value !== 'sequential') return Promise.resolve();
            return value
              ? Promise.resolve()
              : Promise.reject(new Error('请输入结束设备号'));
          },
        },
      ],
      file: [
        {
          validator: (_, value) => {
            if (assignType.value !== 'import') return Promise.resolve();
            return value
              ? Promise.resolve()
              : Promise.reject(new Error('请选择导入文件'));
          },
        },
      ],
    },
    // 表单提交处理
    create: async (values) => {
      try {
        let res;
        switch (assignType.value) {
          case 'import': {
            if (!values.file) {
              throw new Error('请选择要上传的文件');
            }
            res = await importSeparatorBillingGroup({
              file: values.file,
              seriesId: values.seriesId,
            });
            break;
          }
          case 'separator': {
            if (!values.seriesId) {
              throw new Error('请选择计费组');
            }
            // 处理多种分隔符：将换行、中文逗号、英文逗号统一转为英文逗号
            const cardNos = values.cardNos
              .replaceAll('，', ',')
              .replaceAll('\n', ',')
              .split(',')
              .map((no) => no.trim())
              .filter(Boolean)
              .join(',');

            res = await assignSeparatorBillingGroup({
              seriesId: values.seriesId,
              cardNos,
            });
            break;
          }
          case 'sequential': {
            if (!values.seriesId) {
              throw new Error('请选择计费组');
            }
            res = await assignSequentialBillingGroup({
              seriesId: values.seriesId,
              startDeviceNo: values.startDeviceNo,
              endDeviceNo: values.endDeviceNo,
            });
            break;
          }
          default: {
            throw new Error('未知的分配方式');
          }
        }
        return res;
      } catch (error) {
        console.error('分配失败:', error);
        throw error;
      }
    },
    onSuccess: () => {
      message.success('分配成功');
      onSuccess?.();
    },
    // 关闭弹窗时重置
    onCancel: () => {
      assignType.value = 'separator';
      form.resetForm();
    },
  });

  // 监听分配方式变化
  watch(assignType, (newValue) => {
    form.formData.assignType = newValue;
  });

  // 处理分配方式变更
  const handleAssignTypeChange = (e: any) => {
    const value = e.target?.value || e;
    assignType.value = value as 'import' | 'separator' | 'sequential';

    // 清除其他分配方式的表单值
    switch (value) {
      case 'import': {
        form.formData.cardNos = '';
        form.formData.startDeviceNo = '';
        form.formData.endDeviceNo = '';
        break;
      }
      case 'separator': {
        form.formData.startDeviceNo = '';
        form.formData.endDeviceNo = '';
        form.formData.file = null;
        break;
      }
      case 'sequential': {
        form.formData.cardNos = '';
        form.formData.file = null;
        break;
      }
    }
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    const res = await downloadDeviceAssignTemplate();
    await handleFileDownload(res, '分配模板.xlsx');
  };

  // 表单分组配置
  const formGroups = computed((): FormGroup[] => [
    {
      title: '分配信息',
      fields: [
        {
          component: 'RadioGroup',
          label: '分配方式',
          col: { span: 24 },
          props: {
            options: [
              { label: '分隔符分配', value: 'separator' },
              { label: '连号分配', value: 'sequential' },
              { label: '导入分配', value: 'import' },
            ],
            value: assignType.value,
            onChange: handleAssignTypeChange,
          },
        },
        {
          name: 'seriesId',
          label: '计费组',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择计费组',
            // options: billingGroups.value.map((item) => ({
            //   label: item.name,
            //   value: item.id,
            // })),
          },
          remote: {
            api: getBillingGroupOptions,
            // params: {},
            transform: (res: any) =>
              res.data.map((item: any) => ({
                label: item.name,
                value: item.id,
              })),
          },
        },
        // 分隔符分配
        {
          name: 'cardNos',
          label: '设备号码',
          component: 'TextArea',
          col: { span: 24 },
          props: {
            placeholder: '请输入设备号码，每行一个',
            rows: 10,
          },
          show: () => assignType.value === 'separator',
        },
        // 连号分配
        {
          name: 'startDeviceNo',
          label: '开始设备号',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入开始设备号',
          },
          show: () => assignType.value === 'sequential',
        },
        {
          name: 'endDeviceNo',
          label: '结束设备号',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入结束设备号',
          },
          show: () => assignType.value === 'sequential',
        },
        // 导入分配
        {
          name: 'file',
          label: '导入文件',
          component: 'UploadBox',
          col: { span: 24 },
          props: {
            accept: '.xlsx,.xls,.csv',
            maxCount: 1,
            onUpload: (file: File) => {
              form.formData.file = file;
              return false; // 阻止自动上传
            },
            onDownload: handleDownloadTemplate,
            downloadText: '下载模板',
            uploadText: '点击或拖拽文件到此区域上传',
            uploadHint: '支持 .xlsx、.xls、.csv 格式',
          },
          show: () => assignType.value === 'import',
        },
      ],
    },
  ]);

  // 打开弹窗
  const openModal = async () => {
    try {
      // const res = await getBillingGroupOptions();
      // if (res.code === 1) {
      // billingGroups.value = res.data;
      // 重置分配方式
      assignType.value = 'separator';
      // 重置表单
      form.resetForm();
      // 显示表单
      form.show();
      // }
    } catch (error) {
      console.error('获取计费组选项失败:', error);
      message.error('获取计费组选项失败');
    }
  };

  // 渲染弹窗组件
  const AssignModal = () => form.renderFormModal(formGroups.value);

  return {
    form,
    openModal,
    AssignModal,
    assignType,
  };
}
