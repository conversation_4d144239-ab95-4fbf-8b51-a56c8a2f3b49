import type { SelectValue } from 'ant-design-vue/es/select';
import type {
  UploadChangeParam,
  UploadFile,
} from 'ant-design-vue/es/upload/interface';

import type { BillingGroupOption } from '#/api';

import { h, ref } from 'vue';

import { MdiUpload } from '@vben/icons';

import {
  Button,
  Col,
  Form,
  Input,
  message,
  Radio,
  Row,
  Select,
  Space,
  Typography,
  Upload,
} from 'ant-design-vue';

import {
  assignSeparatorBillingGroup,
  assignSequentialBillingGroup,
  downloadDeviceAssignTemplate,
  getBillingGroupOptions,
  importSeparatorBillingGroup,
} from '#/api';
import { useFormModal } from '#/hooks/common/useFormModal';
import { handleFileDownload } from '#/utils/export';

interface FormData {
  seriesId: SelectValue;
  cardNos: string;
  startDeviceNo: string;
  endDeviceNo: string;
  file?: UploadFile[];
}

export function useAssignBillingGroup(onSuccess?: () => void) {
  const billingGroups = ref<BillingGroupOption[]>([]);
  const assignType = ref<'import' | 'separator' | 'sequential'>('separator');

  // 文件列表
  const fileList = ref<UploadFile[]>([]);

  // 表单验证规则
  const rules = {
    seriesId: [{ required: true, message: '请选择计费组' }],
    cardNos: [{ required: true, message: '请输入设备号码' }],
    startDeviceNo: [{ required: true, message: '请输入开始设备号' }],
    endDeviceNo: [{ required: true, message: '请输入结束设备号' }],
    file: [{ required: true, message: '请选择导入文件' }],
  };

  // 使用 useFormModal 管理弹窗和表单
  const {
    formData: _formData,
    loading: _loading,
    show: _show,
    close: _close,
    renderFormModal,
  } = useFormModal<FormData>({
    draggable: true,
    title: '分配计费组',
    width: 550,
    defaultValues: {
      seriesId: undefined,
      cardNos: '',
      startDeviceNo: '',
      endDeviceNo: '',
      file: undefined,
    },
    onSuccess: () => onSuccess?.(),
    onCancel: () => {
      fileList.value = [];
    },
    create: async (values) => {
      try {
        let res;
        switch (assignType.value) {
          case 'import': {
            if (fileList.value.length === 0) {
              message.error('请选择要上传的文件');
              return { code: 0, msg: '请选择要上传的文件' };
            }
            res = await importSeparatorBillingGroup({
              file: fileList.value[0],
              seriesId: values.seriesId,
            });
            break;
          }
          case 'separator': {
            if (!values.seriesId) {
              message.error('请选择计费组');
              return { code: 0, msg: '请选择计费组' };
            }
            res = await assignSeparatorBillingGroup({
              seriesId: values.seriesId,
              cardNos: values.cardNos,
            });
            break;
          }
          case 'sequential': {
            if (!values.seriesId) {
              message.error('请选择计费组');
              return { code: 0, msg: '请选择计费组' };
            }
            res = await assignSequentialBillingGroup({
              seriesId: values.seriesId,
              startDeviceNo: values.startDeviceNo,
              endDeviceNo: values.endDeviceNo,
            });
            break;
          }
        }

        return res;
        // if (res?.code === 1) {
        //   message.success('分配成功');
        //   return { code: 1, msg: '分配成功' };
        // } else {
        //   throw new Error(res?.msg || '分配失败');
        // }
      } catch (error) {
        // message.error(error instanceof Error ? error.message : '分配失败');
        console.error(error);
      }
    },
  });

  // 打开弹窗
  const openModal = async () => {
    try {
      const res = await getBillingGroupOptions();
      if (res.code === 1) {
        billingGroups.value = res.data;
        _show();
      }
    } catch (error) {
      console.error('获取计费组选项失败:', error);
    }
  };

  // 动态生成 formGroups
  const formGroups = [
    {
      title: '',
      content: () => [
        // 分配方式选择
        h(Row, { gutter: 16, class: 'mb-4' }, [
          h(
            Col,
            { span: 24 },
            h(
              Radio.Group,
              {
                value: assignType.value,
                'onUpdate:value': (
                  val: 'import' | 'separator' | 'sequential',
                ) => (assignType.value = val),
              },
              [
                h(Radio, { value: 'separator' }, '分隔符分配'),
                h(Radio, { value: 'import' }, '导入分配'),
                h(Radio, { value: 'sequential' }, '连号分配'),
              ],
            ),
          ),
        ]),
        // 表单内容
        h(Row, { gutter: 16 }, [
          h(Col, { span: 24 }, [
            (() => {
              switch (assignType.value) {
                case 'import': {
                  return [
                    h(Row, { gutter: 16 }, [
                      h(
                        Col,
                        { span: 24 },
                        h(
                          Form.Item,
                          {
                            label: '计费组',
                            name: 'seriesId',
                            rules: rules.seriesId,
                          },
                          [
                            h(Select, {
                              value: _formData.seriesId,
                              'onUpdate:value': (val: SelectValue) =>
                                (_formData.seriesId = val),
                              placeholder: '请选择计费组',
                              options: billingGroups.value.map((item) => ({
                                label: item.name,
                                value: item.id,
                              })),
                              style: { width: '100%' },
                            }),
                          ],
                        ),
                      ),
                      h(
                        Col,
                        { span: 24 },
                        h(
                          Form.Item,
                          {
                            label: '导入文件',
                            name: 'file',
                            rules: rules.file,
                            valuePropName: 'fileList',
                            getValueFromEvent: (e: UploadChangeParam) => {
                              if (Array.isArray(e)) {
                                return e;
                              }
                              return e?.fileList;
                            },
                          },
                          [
                            h('div', {}, [
                              // 添加下载模板按钮
                              h(
                                Button,
                                {
                                  onClick: async () => {
                                    const res =
                                      await downloadDeviceAssignTemplate();
                                    await handleFileDownload(
                                      res,
                                      '分配模板.xlsx',
                                    );
                                  },
                                  style: { marginBottom: '8px' },
                                },
                                '下载模板',
                              ),
                              h(
                                Upload.Dragger,
                                {
                                  name: 'file',
                                  multiple: false,
                                  fileList: fileList.value,
                                  beforeUpload: (file) => {
                                    fileList.value = [file];
                                    _formData.file = fileList.value;
                                    return false;
                                  },
                                  onRemove: () => {
                                    fileList.value = [];
                                    _formData.file = undefined;
                                  },
                                },
                                [
                                  h(
                                    Space,
                                    { direction: 'vertical', align: 'center' },
                                    [
                                      h(
                                        'div',
                                        { class: 'ant-upload-drag-icon' },
                                        [h(MdiUpload)],
                                      ),
                                      h(
                                        Typography.Text,
                                        { type: 'secondary' },
                                        ['点击或拖拽文件到此区域上传'],
                                      ),
                                      h(
                                        Typography.Text,
                                        { type: 'secondary' },
                                        ['支持单个文件上传'],
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ]),
                          ],
                        ),
                      ),
                    ]),
                  ];
                }
                case 'separator': {
                  return [
                    h(Row, { gutter: 16 }, [
                      h(
                        Col,
                        { span: 24 },
                        h(
                          Form.Item,
                          {
                            label: '计费组',
                            name: 'seriesId',
                            rules: rules.seriesId,
                          },
                          [
                            h(Select, {
                              value: _formData.seriesId,
                              'onUpdate:value': (val: SelectValue) =>
                                (_formData.seriesId = val),
                              placeholder: '请选择计费组',
                              options: billingGroups.value.map((item) => ({
                                label: item.name,
                                value: item.id,
                              })),
                              style: { width: '100%' },
                            }),
                          ],
                        ),
                      ),
                      h(
                        Col,
                        { span: 24 },
                        h(
                          Form.Item,
                          {
                            label: '设备号码',
                            name: 'cardNos',
                            rules: rules.cardNos,
                          },
                          [
                            h(Input.TextArea, {
                              value: _formData.cardNos,
                              'onUpdate:value': (val: string) =>
                                (_formData.cardNos = val),
                              placeholder: '请输入设备号码，多个用逗号分隔',
                              rows: 4,
                            }),
                          ],
                        ),
                      ),
                    ]),
                  ];
                }
                case 'sequential': {
                  return [
                    h(Row, { gutter: 16 }, [
                      h(
                        Col,
                        { span: 24 },
                        h(
                          Form.Item,
                          {
                            label: '计费组',
                            name: 'seriesId',
                            rules: rules.seriesId,
                          },
                          [
                            h(Select, {
                              value: _formData.seriesId,
                              'onUpdate:value': (val: SelectValue) =>
                                (_formData.seriesId = val),
                              placeholder: '请选择计费组',
                              options: billingGroups.value.map((item) => ({
                                label: item.name,
                                value: item.id,
                              })),
                              style: { width: '100%' },
                            }),
                          ],
                        ),
                      ),
                    ]),
                    h(Row, { gutter: 16 }, [
                      h(
                        Col,
                        { span: 24 },
                        h(
                          Form.Item,
                          {
                            label: '开始设备号',
                            name: 'startDeviceNo',
                            rules: rules.startDeviceNo,
                          },
                          [
                            h(Input, {
                              value: _formData.startDeviceNo,
                              'onUpdate:value': (val: string) =>
                                (_formData.startDeviceNo = val),
                              placeholder: '请输入开始设备号',
                            }),
                          ],
                        ),
                      ),
                      h(
                        Col,
                        { span: 24 },
                        h(
                          Form.Item,
                          {
                            label: '结束设备号',
                            name: 'endDeviceNo',
                            rules: rules.endDeviceNo,
                          },
                          [
                            h(Input, {
                              value: _formData.endDeviceNo,
                              'onUpdate:value': (val: string) =>
                                (_formData.endDeviceNo = val),
                              placeholder: '请输入结束设备号',
                            }),
                          ],
                        ),
                      ),
                    ]),
                  ];
                }
              }
            })(),
          ]),
        ]),
      ],
    },
  ];

  return {
    openModal,
    AssignModal: () => renderFormModal(formGroups),
  };
}
