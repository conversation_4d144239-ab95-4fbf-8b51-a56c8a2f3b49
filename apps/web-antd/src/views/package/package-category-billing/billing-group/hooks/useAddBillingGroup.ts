import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed } from 'vue';

import { createBillingGroup, editBillingGroup } from '#/api';
import { useForm } from '#/hooks/useAnsheng';

export interface BillingGroupFormData {
  id?: number;
  seriesName: string;
}

export function useBillingGroupModal(callback?: () => void) {
  // 使用 useForm 创建表单弹窗
  const form = useForm<BillingGroupFormData>({
    title: (isEdit) => (isEdit ? '编辑计费组' : '新增计费组'),
    width: 550,
    // 简化布局
    simpleLayout: {
      showGroupTitle: false,
      padding: 0,
    },
    defaultValues: {
      seriesName: '',
    },
    // 创建方法
    create: async (params: BillingGroupFormData) => {
      return await createBillingGroup(params);
    },
    // 更新方法
    update: async (id: number, params: BillingGroupFormData) => {
      // 确保添加id到请求参数
      return await editBillingGroup({ ...params, id });
    },
    // 表单验证规则
    rules: {
      seriesName: [{ required: true, message: '请输入计费组名称' }],
    },
    // 在内部成功回调中调用自定义回调
    onSuccess: callback,
  });

  // 使用声明式表单配置
  const formGroups = computed((): FormGroup[] => [
    {
      title: '',
      fields: [
        {
          name: 'seriesName',
          label: '计费组名称',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入计费组名称',
            allowClear: true,
          },
        },
      ],
    },
  ]);

  // 打开编辑弹窗的方法
  const openEditModal = (record?: any) => {
    if (record) {
      // 编辑模式：使用传入的record
      form.show(record);
    } else {
      // 新增模式：使用默认值
      form.show();
    }
  };

  // 渲染弹窗组件
  const BillingGroupModal = () => form.renderFormModal(formGroups.value);

  return {
    form,
    openEditModal,
    BillingGroupModal,
  };
}
