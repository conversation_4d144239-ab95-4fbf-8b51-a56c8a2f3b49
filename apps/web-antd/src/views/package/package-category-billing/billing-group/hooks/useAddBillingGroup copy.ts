import { h } from 'vue';

import { Col, Form, Input } from 'ant-design-vue';

import { createBillingGroup, editBillingGroup } from '#/api';
import { useFormModal } from '#/hooks/common/useFormModal';

export interface BillingGroupFormData {
  id?: number;
  seriesName: string;
}

export function useBillingGroupModal(callback?: () => void) {
  // 使用useFormModal创建表单弹窗
  const { formData, visible, loading, isEdit, show, close, renderFormModal } =
    useFormModal<BillingGroupFormData>({
      title: (isEdit: boolean) => (isEdit ? '编辑计费组' : '新增计费组'),
      width: 550,
      defaultValues: {
        seriesName: '',
      },
      // 创建方法
      create: async (params: BillingGroupFormData) => {
        return await createBillingGroup(params);
      },
      // 更新方法
      update: async (id: number, params: BillingGroupFormData) => {
        // 确保添加id到请求参数
        return await editBillingGroup({ ...params, id });
      },
      // 表单验证规则
      rules: {
        seriesName: [{ required: true, message: '请输入计费组名称' }],
      },
      // 在内部成功回调中调用自定义回调
      onSuccess: callback,
    });

  // 渲染表单内容
  const formGroups = [
    {
      title: '', // 提供一个空标题以满足类型要求
      content: () => [
        h(Col, { span: 24 }, [
          h(
            Form.Item,
            {
              label: '计费组名称',
              name: 'seriesName',
            },
            [
              h(Input, {
                value: formData.seriesName,
                'onUpdate:value': (val) => (formData.seriesName = val),
                placeholder: '请输入计费组名称',
                allowClear: true,
              }),
            ],
          ),
        ]),
      ],
    },
  ];

  // 打开编辑弹窗的方法
  const openEditModal = (record?: any) => {
    if (record) {
      // 编辑模式：直接使用传入的record设置表单数据
      isEdit.value = true;
      // 在调用show前设置currentId
      show(record.id).then(() => {
        // 手动设置表单数据
        formData.id = record.id;
        formData.seriesName = record.seriesName;
      });
    } else {
      // 新增模式：使用默认值
      isEdit.value = false;
      show();
    }
  };

  // 渲染弹窗组件
  const BillingGroupModal = () => renderFormModal(formGroups);

  return {
    formData,
    visible,
    loading,
    isEdit,
    openEditModal,
    close,
    BillingGroupModal,
  };
}
