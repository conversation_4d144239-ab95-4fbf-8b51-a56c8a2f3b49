<script setup lang="ts">
import { ref } from 'vue';

import { Card, Tabs } from 'ant-design-vue';

import BillingGroup from './billing-group/index.vue';
import Category from './category/index.vue';

const activeKey = ref('category');

// Tab 配置
const tabs = [
  // 套餐分类
  {
    key: 'category',
    tab: '套餐分类',
    component: Category,
  },
  // 计费组
  {
    key: 'billing-group',
    tab: '计费规则',
    component: BillingGroup,
  },
];
</script>

<template>
  <div class="package-category-billing p-2">
    <Card>
      <Tabs v-model:active-key="activeKey">
        <Tabs.TabPane v-for="item in tabs" :key="item.key" :tab="item.tab">
          <component :is="item.component" />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  </div>
</template>
