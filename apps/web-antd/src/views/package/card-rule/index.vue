<script lang="ts" setup>
import { computed, h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import {
  copyPackageGroup,
  deletePackageGroup,
  getCardPackageRuleList,
} from '#/api';
import { getPackageRuleListApi } from '#/api/core/order';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
// 使用 useTable hook
import { useTable } from '#/hooks/useTable';

// import BatchAssignModal from './components/batch-assign-modal.vue';
import NotificationModal from './components/notification-modal.vue';
import { basicSearchItems, createColumns } from './config';
import { useBatchAssign } from './hooks/useBatchAssignForm';
import { usePackageRuleForm } from './hooks/usePackageRuleForm';

const {
  loading,
  searchParams,
  tableData,
  pagination,
  handleSearch,
  handleReset,
  handleTableChange,
  getList,
} = useTable({
  api: getCardPackageRuleList,
  defaultParams: {},
});

// 搜索配置
const searchConfig = ref(basicSearchItems);

// 使用表单 hook
const { show: showModal, renderFormModal } = usePackageRuleForm(() => {
  // 成功回调，重新加载数据
  getList();
});

const { batchAssignModal, form } = useBatchAssign(() => {
  getList();
});

// 添加状态
const batchAssignVisible = ref(false);
const notificationVisible = ref(false);
const currentRecord = ref<any>(null);

// 添加套餐规则选项状态
const ruleOptions = ref<{ label: string; value: number }[]>([]);

// 处理新增
const handleAdd = () => {
  showModal();
};

// 处理编辑
const handleEdit = async (record: any) => {
  try {
    // 直接传递record.id到showModal，让hook内部处理详情获取
    showModal(record.id);
  } catch (error) {
    console.error('打开编辑失败:', error);
    message.error(error instanceof Error ? error.message : '操作失败');
  }
};

// 处理删除
const handleDelete = async (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该套餐规则吗？',
    async onOk() {
      try {
        const res = await deletePackageGroup(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          throw new Error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('Delete failed:', error);
        message.error(error instanceof Error ? error.message : '删除失败');
      }
    },
  });
};

// 添加处理函数
const handleBatchAssign = () => {
  // batchAssignVisible.value = true;
  form.show();
};

// 处理下架通知
const handleNotification = (record: any) => {
  currentRecord.value = record;
  notificationVisible.value = true;
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    icon: h(MdiPlus),
    text: '新增规则',
    type: 'primary',
    onClick: handleAdd,
  },
  {
    key: 'batchAssign',
    icon: h(MdiPlus),
    text: '批量分配',
    type: 'primary',
    onClick: handleBatchAssign,
  },
];

// 表格列配置
const columns = computed(() => createColumns());

// 加载套餐规则选项
const loadRuleOptions = async () => {
  try {
    const res = await getPackageRuleListApi();
    if (res.code === 1) {
      ruleOptions.value = res.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error('获取套餐规则失败:', error);
    message.error('获取套餐规则失败');
  }
};

// 组件挂载时加载数据和通道选项
onMounted(() => {
  getList();
  loadRuleOptions();
});

// 在 setup 中添加复制处理函数
const handleCopy = async (record: any) => {
  try {
    const res = await copyPackageGroup(record.id);
    if (res.code === 1) {
      message.success('复制成功');
      getList();
    } else {
      throw new Error(res.msg || '复制失败');
    }
  } catch (error) {
    console.error('Copy failed:', error);
    message.error(error instanceof Error ? error.message : '复制失败');
  }
};

// 在 actionHandlers 中添加复制处理器
const actionHandlers = {
  onEdit: handleEdit,
  onDelete: handleDelete,
  onCopy: handleCopy,
};

// 添加到操作按钮列表
const actionButtons = [
  // ... 其他按钮
  {
    key: 'notification',
    text: '下架通知',
    type: 'link',
    onClick: handleNotification,
  },
];
</script>

<template>
  <div class="package-card-rule">
    <!-- <Card> -->
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="searchConfig"
      :custom-buttons="toolbarButtons"
      :loading="loading"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :show-action="true"
      :action-buttons="[
        {
          key: 'edit',
          text: '编辑',
          type: 'link',
          onClick: handleEdit,
        },
        {
          key: 'delete',
          text: '删除',
          type: 'link',
          danger: true,
          onClick: handleDelete,
        },
        {
          key: 'copy',
          text: '通道复制',
          type: 'link',
          onClick: handleCopy,
        },
        {
          key: 'notification',
          text: '下架通知',
          type: 'link',
          onClick: handleNotification,
        },
      ]"
      :pagination="pagination"
      @change="handleTableChange"
    />
    <!-- </Card> -->

    <!-- 渲染表单模态框 - 移到 Card 外面 -->
    <component :is="renderFormModal()" />
    <component :is="batchAssignModal()" />

    <!-- <BatchAssignModal
      v-model:visible="batchAssignVisible"
      :rule-options="ruleOptions"
      @success="getList"
    /> -->

    <NotificationModal
      v-model:visible="notificationVisible"
      :record="currentRecord"
      @success="getList"
    />
  </div>
</template>

<style lang="less" scoped>
.package-card-rule {
  // background-color: var(--background-deep);
}
</style>
