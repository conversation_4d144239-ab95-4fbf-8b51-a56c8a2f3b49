import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed, ref, watch } from 'vue';

import { message } from 'ant-design-vue';

import {
  addPackageGroup,
  getPackageGroupDetail,
  OperatorEnum,
  updatePackageGroup,
} from '#/api';
import { getApiVersions } from '#/api/core/channel';
import { getChannelListApi } from '#/api/core/order';
import { useForm } from '#/hooks/useAnsheng';

// 表单状态接口
interface PackageRuleFormState {
  id?: number;
  groupName: string; // 组名称
  groupRemarks: string; // 组备注
  groupOperator: OperatorEnum; // 组运营商
  configId: number; // 通道id
  realNameRules: number; // 实名规则
  realNameAddressRules: number; // 实名地址规则
  realNameAddress: string; // 自定义实名地址
  realNameRequestAddress: string; // 对接实名请求域名地址
  realNameRequestAddress2: string; // 对接实名请求域名地址2
  realNameRequestAccount: string; // 对接账号
  realNameRequestPassword: string; // 对接密码
  realNameApiPlatform: string; // 对接平台标识
  realNameApiVersion: string; // 平台版本
  realNameRequestKey: string; // 对接密钥
  realNameRequestKey2: string; // 对接密钥2
  realNameCardType: number; // api递交卡号
  bindPhoneNumber: number; // 绑定手机号
  automaticRenewalRules: number; // 自动续费规则
  renewalThreshold: number; // 自动续费阈值
  renewalArrears: number; // 自动续费可欠费金额
  inAdvanceStop: number; // 提前停机
  realNameMsg: string; // 实名说明
  activationRestrictions: number; // 限制激活
  notification: string; // 通知
  groupStatus?: number; // 添加组状态字段，使其与API返回一致
}

export function usePackageRuleForm(callback?: () => void) {
  // 通道选项数据
  const channelOptions = ref<{ label: string; value: number }[]>([]);
  const channelLoading = ref(false);

  // 监听的字段值
  const realNameAddressRules = ref<number>(1);
  const realNameApiPlatform = ref<string>('');
  const realNameApiVersion = ref<string>('');
  const automaticRenewalRules = ref<number>(0);

  // 加载通道选项
  const loadChannelOptions = async () => {
    try {
      channelLoading.value = true;
      const res = await getChannelListApi();
      if (
        res &&
        typeof res === 'object' &&
        'code' in res &&
        res.code === 1 &&
        'data' in res
      ) {
        channelOptions.value = (res.data as any[]).map((item: any) => ({
          label: item.name,
          value: item.id,
        }));
      }
    } catch (error) {
      console.error('Failed to load channel options:', error);
      message.error('加载通道选项失败');
    } finally {
      channelLoading.value = false;
    }
  };

  // 使用useForm创建表单
  const form = useForm<PackageRuleFormState>({
    title: (isEdit) => (isEdit ? '编辑套餐规则' : '新增套餐规则'),
    width: 1000,
    fullscreenable: true,
    draggable: true,
    defaultValues: {
      groupName: '',
      groupRemarks: '',
      groupOperator: undefined as unknown as OperatorEnum,
      configId: undefined as unknown as number,
      groupStatus: 1,
      inAdvanceStop: 1,
      realNameRules: 1,
      realNameAddressRules: 1,
      realNameAddress: '',
      realNameRequestAddress: '',
      realNameRequestAddress2: '',
      realNameRequestAccount: '',
      realNameRequestPassword: '',
      realNameApiPlatform: '',
      realNameApiVersion: '',
      realNameRequestKey: '',
      realNameRequestKey2: '',
      bindPhoneNumber: 1,
      realNameCardType: 1,
      automaticRenewalRules: 0,
      renewalThreshold: 0,
      renewalArrears: 0,
      activationRestrictions: 0,
      realNameMsg: '',
      notification: '',
    },
    // 表单验证规则
    rules: {
      groupName: [{ required: true, message: '请输入套餐规则名' }],
      groupOperator: [{ required: true, message: '请选择套餐规则运营商' }],
      configId: [{ required: true, message: '请选择归属通道' }],
      realNameRules: [{ required: true, message: '请选择实名规则' }],
      realNameAddressRules: [{ required: true, message: '请选择实名规则' }],
      realNameAddress: [
        {
          validator: (_, value) => {
            if (realNameAddressRules.value === 3 && !value) {
              return Promise.reject(new Error('请输入自定义实名地址'));
            }
            return Promise.resolve();
          },
        },
      ],
      realNameApiPlatform: [
        {
          validator: (_, value) => {
            if (
              (realNameAddressRules.value === 2 ||
                realNameAddressRules.value === 5) &&
              !value
            ) {
              return Promise.reject(new Error('请输入平台标识'));
            }
            return Promise.resolve();
          },
        },
      ],
      realNameApiVersion: [
        {
          validator: (_, value) => {
            if (
              (realNameAddressRules.value === 2 ||
                realNameAddressRules.value === 5) &&
              realNameApiPlatform.value &&
              !value
            ) {
              return Promise.reject(new Error('请选择平台版本'));
            }
            return Promise.resolve();
          },
        },
      ],
      realNameRequestAddress: [
        {
          validator: (_, value) => {
            if (
              (realNameAddressRules.value === 2 ||
                realNameAddressRules.value === 5) &&
              realNameApiVersion.value &&
              !value
            ) {
              return Promise.reject(new Error('请输入实名请求地址'));
            }
            return Promise.resolve();
          },
        },
      ],
      realNameRequestAccount: [
        {
          validator: (_, value) => {
            if (
              (realNameAddressRules.value === 2 ||
                realNameAddressRules.value === 5) &&
              realNameApiVersion.value &&
              !value
            ) {
              return Promise.reject(new Error('请输入对接账号'));
            }
            return Promise.resolve();
          },
        },
      ],
      realNameRequestPassword: [
        {
          validator: (_, value) => {
            if (
              (realNameAddressRules.value === 2 ||
                realNameAddressRules.value === 5) &&
              realNameApiVersion.value &&
              !value
            ) {
              return Promise.reject(new Error('请输入对接密码'));
            }
            return Promise.resolve();
          },
        },
      ],
      realNameCardType: [{ required: true, message: '请选择API递交卡号' }],
      bindPhoneNumber: [{ required: true, message: '请选择绑定手机号规则' }],
      automaticRenewalRules: [
        { required: true, message: '请选择自动续费规则' },
      ],
      renewalThreshold: [
        {
          validator: (_, value) => {
            if (
              (automaticRenewalRules.value === 1 ||
                automaticRenewalRules.value === 2) &&
              value === undefined
            ) {
              return Promise.reject(new Error('请输入续费阈值'));
            }
            return Promise.resolve();
          },
        },
      ],
      renewalArrears: [
        {
          validator: (_, value) => {
            if (
              (automaticRenewalRules.value === 1 ||
                automaticRenewalRules.value === 2) &&
              value === undefined
            ) {
              return Promise.reject(new Error('请输入可欠费金额'));
            }
            return Promise.resolve();
          },
        },
      ],
      inAdvanceStop: [{ required: true, message: '请输入提前停机时间' }],
      activationRestrictions: [
        { required: true, message: '请选择是否限制激活' },
      ],
    },
    // 创建方法
    create: async (params) => {
      // 进行表单验证
      if (params.realNameAddressRules === 3 && !params.realNameAddress) {
        throw new Error('请输入自定义实名地址');
      }
      if (
        params.realNameAddressRules === 2 &&
        (!params.realNameRequestAddress ||
          !params.realNameRequestAccount ||
          !params.realNameRequestPassword ||
          !params.realNameApiPlatform ||
          !params.realNameApiVersion)
      ) {
        throw new Error('请填写完整的对接信息');
      }
      if (
        (params.automaticRenewalRules === 1 ||
          params.automaticRenewalRules === 2) &&
        (params.renewalThreshold === undefined ||
          params.renewalArrears === undefined)
      ) {
        throw new Error('请填写续费阈值和可欠费金额');
      }

      return await addPackageGroup(params);
    },
    // 更新方法
    update: async (id, params) => {
      // 同样的验证逻辑
      if (params.realNameAddressRules === 3 && !params.realNameAddress) {
        throw new Error('请输入自定义实名地址');
      }
      if (
        params.realNameAddressRules === 2 &&
        (!params.realNameRequestAddress ||
          !params.realNameRequestAccount ||
          !params.realNameRequestPassword ||
          !params.realNameApiPlatform ||
          !params.realNameApiVersion)
      ) {
        throw new Error('请填写完整的对接信息');
      }
      if (
        (params.automaticRenewalRules === 1 ||
          params.automaticRenewalRules === 2) &&
        (params.renewalThreshold === undefined ||
          params.renewalArrears === undefined)
      ) {
        throw new Error('请填写续费阈值和可欠费金额');
      }

      return await updatePackageGroup({ ...params, id });
    },
    getDetail: async (id) => {
      try {
        const res = await getPackageGroupDetail(id as number);
        if (res.code === 1 && res.data) {
          return { code: 1, data: res.data, msg: res.msg };
        }
        throw new Error(res.msg || '获取详情失败');
      } catch (error) {
        console.error('获取详情失败:', error);
        message.error(error instanceof Error ? error.message : '获取详情失败');
        throw error;
      }
    },
    onSuccess: callback,
  });

  // 监听关键字段变化
  watch(
    () => form.formData.realNameAddressRules,
    (val) => {
      realNameAddressRules.value = val;
    },
  );

  watch(
    () => form.formData.realNameApiPlatform,
    (val) => {
      realNameApiPlatform.value = val;
    },
  );

  watch(
    () => form.formData.realNameApiVersion,
    (val) => {
      realNameApiVersion.value = val;
    },
  );

  watch(
    () => form.formData.automaticRenewalRules,
    (val) => {
      automaticRenewalRules.value = val;
    },
  );

  // 使用声明式表单配置
  const formGroups = computed((): FormGroup[] => [
    {
      title: '基本信息',
      fields: [
        {
          name: 'groupName',
          label: '规则名称',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入套餐规则名',
            allowClear: true,
          },
        },
        {
          name: 'groupRemarks',
          label: '规则备注',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入套餐规则备注',
            allowClear: true,
          },
        },
        {
          name: 'groupOperator',
          label: '规则运营商',
          component: 'Select',
          col: { span: 12 },
          props: {
            placeholder: '请选择套餐规则运营商',
            options: [
              { label: '中国电信', value: OperatorEnum.TELECOM },
              { label: '中国联通', value: OperatorEnum.UNICOM },
              { label: '中国移动', value: OperatorEnum.MOBILE },
              { label: '中国广电', value: OperatorEnum.CCTC },
              { label: '其他', value: OperatorEnum.OTHER },
            ],
            allowClear: true,
          },
        },
        {
          name: 'configId',
          label: '归属通道',
          component: 'Select',
          col: { span: 12 },
          props: {
            placeholder: '请选择归属通道',
            options: channelOptions.value,
            loading: channelLoading.value,
            allowClear: true,
          },
        },
      ],
    },
    {
      title: '实名配置',
      fields: [
        {
          name: 'realNameRules',
          label: '实名规则',
          component: 'Select',
          col: { span: 12 },
          props: {
            placeholder: '请选择实名规则',
            options: [
              { label: '无需实名', value: 1 },
              { label: '先充值再实名', value: 2 },
              { label: '先实名再充值', value: 3 },
            ],
            allowClear: true,
          },
        },
        {
          name: 'realNameAddressRules',
          label: '实名规则',
          component: 'Select',
          col: { span: 12 },
          props: {
            placeholder: '请选择实名规则',
            options: [
              { label: '系统默认地址', value: 1 },
              { label: '对接获取', value: 2 },
              { label: '自定义填写', value: 3 },
              { label: '规则通道参数获取', value: 4 },
              { label: '短号实名', value: 5 },
              { label: '卡片通道参数获取', value: 6 },
            ],
            allowClear: true,
          },
        },
        {
          name: 'realNameCardType',
          label: 'API递交卡号',
          component: 'Select',
          col: { span: 12 },
          props: {
            placeholder: '请选择API递交卡号',
            options: [
              { label: 'ICCID', value: 1 },
              { label: 'MIS', value: 2 },
              { label: '虚拟号', value: 3 },
            ],
            allowClear: true,
          },
        },
        {
          name: 'bindPhoneNumber',
          label: '绑定手机号',
          component: 'Select',
          col: { span: 12 },
          props: {
            placeholder: '请选择绑定手机号规则',
            options: [
              { label: '无需绑定', value: 1 },
              { label: '强制绑定需要验证码', value: 2 },
              { label: '强制绑定无需验证码', value: 3 },
              { label: '无限制绑定需验证码', value: 4 },
              { label: '无限制绑定无需验证码', value: 5 },
            ],
            allowClear: true,
          },
        },
        {
          name: 'realNameAddress',
          label: '自定义实名地址',
          component: 'Input',
          col: { span: 24 },
          labelCol: { span: 4 },
          wrapperCol: { span: 19 },
          props: {
            placeholder: '请输入自定义实名地址',
            allowClear: true,
          },
          show: () => form.formData.realNameAddressRules === 3,
        },
        {
          name: 'realNameApiPlatform',
          label: '平台标识',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入平台标识',
            allowClear: true,
          },
          show: () =>
            form.formData.realNameAddressRules === 2 ||
            form.formData.realNameAddressRules === 5,
        },
        {
          name: 'realNameApiVersion',
          label: '平台版本',
          component: 'Select',
          col: { span: 12 },
          props: {
            placeholder: '请输入关键词搜索版本',
            allowClear: true,
          },
          show: () =>
            (form.formData.realNameAddressRules === 2 ||
              form.formData.realNameAddressRules === 5) &&
            form.formData.realNameApiPlatform,
          remote: {
            api: (params) => {
              const platform = form.formData.realNameApiPlatform;
              if (!platform) {
                return Promise.resolve({ code: 1, data: [] });
              }
              return getApiVersions(platform);
            },
            transform: (res) => {
              if (res.code === 1 && Array.isArray(res.data)) {
                return res.data.map((item) => ({
                  label: item.versionName,
                  value: item.platformIdentifying,
                }));
              }
              return [];
            },
          },
        },
        {
          name: 'realNameRequestAddress',
          label: '实名地址',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入实名请求地址',
            allowClear: true,
          },
          show: () =>
            (form.formData.realNameAddressRules === 2 ||
              form.formData.realNameAddressRules === 5) &&
            form.formData.realNameApiVersion,
        },
        {
          name: 'realNameRequestAddress2',
          label: '实名地址2',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入实名请求地址2',
            allowClear: true,
          },
          show: () =>
            (form.formData.realNameAddressRules === 2 ||
              form.formData.realNameAddressRules === 5) &&
            form.formData.realNameApiVersion,
        },
        {
          name: 'realNameRequestAccount',
          label: '对接账号',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入对接账号',
            allowClear: true,
          },
          show: () =>
            form.formData.realNameAddressRules === 2 ||
            form.formData.realNameAddressRules === 5,
        },
        {
          name: 'realNameRequestPassword',
          label: '对接密码',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入对接密码',
            allowClear: true,
          },
          show: () =>
            form.formData.realNameAddressRules === 2 ||
            form.formData.realNameAddressRules === 5,
        },
        {
          name: 'realNameRequestKey',
          label: '对接密钥',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入对接密钥',
            allowClear: true,
          },
          show: () =>
            form.formData.realNameAddressRules === 2 ||
            form.formData.realNameAddressRules === 5,
        },
        {
          name: 'realNameRequestKey2',
          label: '对接密钥2',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入对接密钥2',
            allowClear: true,
          },
          show: () =>
            form.formData.realNameAddressRules === 2 ||
            form.formData.realNameAddressRules === 5,
        },
        {
          name: 'realNameMsg',
          label: '实名说明',
          component: 'TextArea',
          col: { span: 24 },
          labelCol: { span: 3 },
          wrapperCol: { span: 20 },
          props: {
            placeholder: '请输入实名说明',
            rows: 4,
            allowClear: true,
          },
        },
      ],
    },
    {
      title: '续费配置',
      fields: [
        {
          name: 'automaticRenewalRules',
          label: '自动续费规则',
          component: 'Select',
          col: { span: 12 },
          props: {
            placeholder: '请选择自动续费规则',
            options: [
              { label: '关闭功能', value: 0 },
              { label: '强制续费', value: 1 },
              { label: '选择续费', value: 2 },
              { label: '选择续费(杰)', value: 3 },
            ],
            allowClear: true,
          },
        },
        {
          name: 'renewalThreshold',
          label: '续费阈值(%)',
          component: 'InputNumber',
          col: { span: 12 },
          props: {
            placeholder: '请输入续费阈值',
            min: 0,
            max: 100,
            style: { width: '100%' },
          },
          show: () =>
            form.formData.automaticRenewalRules === 1 ||
            form.formData.automaticRenewalRules === 2,
        },
        {
          name: 'renewalArrears',
          label: '可欠费金额',
          component: 'InputNumber',
          col: { span: 12 },
          props: {
            placeholder: '请输入可欠费金额',
            min: 0,
            style: { width: '100%' },
          },
          show: () =>
            form.formData.automaticRenewalRules === 1 ||
            form.formData.automaticRenewalRules === 2,
        },
        {
          name: 'inAdvanceStop',
          label: '提前停机',
          component: 'InputNumber',
          col: { span: 12 },
          props: {
            placeholder: '请输入提前停机时间',
            min: 0,
            style: { width: '100%' },
          },
        },
      ],
    },
    {
      title: '其他配置',
      fields: [
        {
          name: 'activationRestrictions',
          label: '限制激活',
          component: 'Select',
          col: { span: 12 },
          props: {
            placeholder: '请选择是否限制激活',
            options: [
              { label: '关闭限制', value: 0 },
              { label: '限制新卡', value: 1 },
              { label: '所有卡', value: 2 },
            ],
            allowClear: true,
          },
        },
      ],
    },
  ]);

  // 自定义show方法
  const show = async (recordOrId?: any) => {
    try {
      await form.show(recordOrId);
      // 加载通道选项
      await loadChannelOptions();
    } catch (error) {
      console.error('Failed to show form:', error);
      message.error('加载表单失败');
    }
  };

  // 渲染表单弹窗
  const renderFormModal = () => form.renderFormModal(formGroups.value);

  return {
    form,
    formData: form.formData,
    show,
    close: form.close,
    renderFormModal,
  };
}
