import type { FormGroup } from '#/hooks/common/useFormModal';

import { h, ref } from 'vue';

import { Col, Form, Input, InputNumber, message, Select } from 'ant-design-vue';

import {
  addPackageGroup,
  getPackageGroupDetail,
  OperatorEnum,
  updatePackageGroup,
} from '#/api';
import { getApiVersions } from '#/api/core/channel';
import { getChannelListApi } from '#/api/core/order';
import { useFormModal } from '#/hooks/common/useFormModal';

const FormItem = Form.Item;

// 表单状态接口
interface PackageRuleFormState {
  groupName: string; // 组名称
  groupRemarks: string; // 组备注
  groupOperator: OperatorEnum; // 组运营商
  configId: number; // 通道id
  realNameRules: number; // 实名规则
  realNameAddressRules: number; // 实名地址规则
  realNameAddress: string; // 自定义实名地址
  realNameRequestAddress: string; // 对接实名请求域名地址
  realNameRequestAddress2: string; // 对接实名请求域名地址2
  realNameRequestAccount: string; // 对接账号
  realNameRequestPassword: string; // 对接密码
  realNameApiPlatform: string; // 对接平台标识
  realNameApiVersion: string; // 平台版本
  realNameRequestKey: string; // 对接密钥
  realNameRequestKey2: string; // 对接密钥2
  realNameCardType: number; // api递交卡号
  bindPhoneNumber: number; // 绑定手机号
  automaticRenewalRules: number; // 自动续费规则
  renewalThreshold: number; // 自动续费阈值
  renewalArrears: number; // 自动续费可欠费金额
  inAdvanceStop: number; // 提前停机
  realNameMsg: string; // 实名说明
  activationRestrictions: number; // 限制激活
  notification: string; // 通知
  groupStatus?: number; // 添加组状态字段，使其与API返回一致
}

export function usePackageRuleForm(callback?: () => void) {
  // 选项数据
  const channelOptions = ref<{ label: string; value: number }[]>([]);
  const channelLoading = ref(false);

  // 加载通道选项
  const loadChannelOptions = async () => {
    try {
      channelLoading.value = true;
      const res = await getChannelListApi();
      if (
        res &&
        typeof res === 'object' &&
        'code' in res &&
        res.code === 1 &&
        'data' in res
      ) {
        channelOptions.value = (res.data as any[]).map((item: any) => ({
          label: item.name,
          value: item.id,
        }));
      }
    } catch (error) {
      console.error('Failed to load channel options:', error);
    } finally {
      channelLoading.value = false;
    }
  };

  // 使用通用表单 hook
  const {
    formRef,
    formData,
    visible,
    loading,
    isEdit,
    currentId,
    show: originalShow,
    close,
    setFormData,
    renderFormModal,
    createRemoteSelect,
    saveFormData,
    clearFormData,
  } = useFormModal<PackageRuleFormState>({
    title: (isEdit) => (isEdit ? '编辑套餐规则' : '新增套餐规则'),
    width: 1000,
    fullscreenable: true,
    draggable: true,
    selectApis: {
      apiVersions: {
        api: (params) => {
          const platform = params?.platform || '';
          if (!platform) {
            return Promise.resolve({ code: 1, data: [] });
          }
          return getApiVersions(platform);
        },
        valueField: 'platformIdentifying',
        labelField: 'versionName',
        remote: true,
        searchField: 'keyword',
        debounce: 300,
      },
    },
    create: async (params) => {
      // 根据条件处理必填字段
      if (params.realNameAddressRules === 3 && !params.realNameAddress) {
        throw new Error('请输入自定义实名地址');
      }
      if (
        params.realNameAddressRules === 2 &&
        (!params.realNameRequestAddress ||
          !params.realNameRequestAccount ||
          !params.realNameRequestPassword ||
          !params.realNameApiPlatform ||
          !params.realNameApiVersion)
      ) {
        throw new Error('请填写完整的对接信息');
      }
      if (
        (params.automaticRenewalRules === 1 ||
          params.automaticRenewalRules === 2) &&
        (params.renewalThreshold === undefined ||
          params.renewalArrears === undefined)
      ) {
        throw new Error('请填写续费阈值和可欠费金额');
      }

      return await addPackageGroup(params);
    },
    update: async (id, params) => {
      // 同样的验证逻辑
      if (params.realNameAddressRules === 3 && !params.realNameAddress) {
        throw new Error('请输入自定义实名地址');
      }
      if (
        params.realNameAddressRules === 2 &&
        (!params.realNameRequestAddress ||
          !params.realNameRequestAccount ||
          !params.realNameRequestPassword ||
          !params.realNameApiPlatform ||
          !params.realNameApiVersion)
      ) {
        throw new Error('请填写完整的对接信息');
      }
      if (
        (params.automaticRenewalRules === 1 ||
          params.automaticRenewalRules === 2) &&
        (params.renewalThreshold === undefined ||
          params.renewalArrears === undefined)
      ) {
        throw new Error('请填写续费阈值和可欠费金额');
      }

      return await updatePackageGroup({ ...params, id });
    },
    onSuccess: callback,
    defaultValues: {
      groupStatus: 1,
      inAdvanceStop: 1,
      realNameRules: 1,
      realNameAddressRules: 1,
      bindPhoneNumber: 1,
      realNameCardType: 1,
      automaticRenewalRules: 0,
      renewalThreshold: 0,
      renewalArrears: 0,
      activationRestrictions: 0,
    },
  });

  // 表单分组配置
  const formGroups: FormGroup[] = [
    {
      title: '基本信息',
      content: (formData) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '规则名称',
              name: 'groupName',
              rules: [{ required: true, message: '请输入套餐规则名' }],
            },
            () =>
              h(Input, {
                placeholder: '请输入套餐规则名',
                value: formData.groupName,
                'onUpdate:value': (val) => (formData.groupName = val),
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '规则备注',
              name: 'groupRemarks',
            },
            () =>
              h(Input, {
                placeholder: '请输入套餐规则备注',
                value: formData.groupRemarks,
                'onUpdate:value': (val) => (formData.groupRemarks = val),
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '规则运营商',
              name: 'groupOperator',
              rules: [{ required: true, message: '请选择套餐规则运营商' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择套餐规则运营商',
                value: formData.groupOperator,
                'onUpdate:value': (val) => (formData.groupOperator = val),
                options: [
                  { label: '中国电信', value: OperatorEnum.TELECOM },
                  { label: '中国联通', value: OperatorEnum.UNICOM },
                  { label: '中国移动', value: OperatorEnum.MOBILE },
                  { label: '中国广电', value: OperatorEnum.CCTC },
                  { label: '其他', value: OperatorEnum.OTHER },
                ],
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '归属通道',
              name: 'configId',
              rules: [{ required: true, message: '请选择归属通道' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择归属通道',
                value: formData.configId,
                'onUpdate:value': (val) => (formData.configId = val),
                options: channelOptions.value,
                loading: channelLoading.value,
                allowClear: true,
              }),
          ),
        ]),
      ],
    },
    {
      title: '实名配置',
      content: (formData) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '实名规则',
              name: 'realNameRules',
              rules: [{ required: true, message: '请选择实名规则' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择实名规则',
                value: formData.realNameRules,
                'onUpdate:value': (val) => (formData.realNameRules = val),
                options: [
                  { label: '无需实名', value: 1 },
                  { label: '先充值再实名', value: 2 },
                  { label: '先实名再充值', value: 3 },
                ],
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '实名规则',
              name: 'realNameAddressRules',
              rules: [{ required: true, message: '请选择实名规则' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择实名规则',
                value: formData.realNameAddressRules,
                'onUpdate:value': (val) =>
                  (formData.realNameAddressRules = val),
                options: [
                  { label: '系统默认地址', value: 1 },
                  { label: '对接获取', value: 2 },
                  { label: '自定义填写', value: 3 },
                  { label: '规则通道参数获取', value: 4 },
                  { label: '短号实名', value: 5 },
                  { label: '卡片通道参数获取', value: 6 },
                ],
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API递交卡号',
              name: 'realNameCardType',
              rules: [{ required: true, message: '请选择API递交卡号' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择API递交卡号',
                value: formData.realNameCardType,
                'onUpdate:value': (val) => (formData.realNameCardType = val),
                options: [
                  { label: 'ICCID', value: 1 },
                  { label: 'MIS', value: 2 },
                  { label: '虚拟号', value: 3 },
                ],
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '绑定手机号',
              name: 'bindPhoneNumber',
              rules: [{ required: true, message: '请选择绑定手机号规则' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择绑定手机号规则',
                value: formData.bindPhoneNumber,
                'onUpdate:value': (val) => (formData.bindPhoneNumber = val),
                options: [
                  { label: '无需绑定', value: 1 },
                  { label: '强制绑定需要验证码', value: 2 },
                  { label: '强制绑定无需验证码', value: 3 },
                  { label: '无限制绑定需验证码', value: 4 },
                  { label: '无限制绑定无需验证码', value: 5 },
                ],
                allowClear: true,
              }),
          ),
        ]),
        ...(formData.realNameAddressRules === 3
          ? [
              h(Col, { span: 24 }, [
                h(
                  FormItem,
                  {
                    label: '自定义实名地址',
                    name: 'realNameAddress',
                    labelCol: { span: 4 },
                    wrapperCol: { span: 19 },
                    rules: [
                      { required: true, message: '请输入自定义实名地址' },
                    ],
                  },
                  () =>
                    h(Input, {
                      placeholder: '请输入自定义实名地址',
                      value: formData.realNameAddress,
                      'onUpdate:value': (val) =>
                        (formData.realNameAddress = val),
                      allowClear: true,
                    }),
                ),
              ]),
            ]
          : []),
        ...(formData.realNameAddressRules === 2 ||
        formData.realNameAddressRules === 5
          ? [
              h(Col, { span: 12 }, [
                h(
                  FormItem,
                  {
                    label: '平台标识',
                    name: 'realNameApiPlatform',
                    rules: [{ required: true, message: '请输入平台标识' }],
                  },
                  () =>
                    h(Input, {
                      placeholder: '请输入平台标识',
                      value: formData.realNameApiPlatform,
                      'onUpdate:value': (val) => {
                        formData.realNameApiPlatform = val;
                      },
                      allowClear: true,
                    }),
                ),
              ]),
              formData.realNameApiPlatform &&
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: '平台版本',
                      name: 'realNameApiVersion',
                      rules: [{ required: true, message: '请选择平台版本' }],
                    },
                    () =>
                      createRemoteSelect('apiVersions', {
                        placeholder: '请输入关键词搜索版本',
                        value: formData.realNameApiVersion,
                        'onUpdate:value': (val: string) =>
                          (formData.realNameApiVersion = val),
                        style: { width: '100%' },
                        params: { platform: formData.realNameApiPlatform },
                        allowClear: true,
                      }),
                  ),
                ]),
              formData.realNameApiVersion && [
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: '实名地址',
                      name: 'realNameRequestAddress',
                      rules: [
                        { required: true, message: '请输入实名请求地址' },
                      ],
                    },
                    () =>
                      h(Input, {
                        placeholder: '请输入实名请求地址',
                        value: formData.realNameRequestAddress,
                        'onUpdate:value': (val) =>
                          (formData.realNameRequestAddress = val),
                        allowClear: true,
                      }),
                  ),
                ]),
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: '实名地址2',
                      name: 'realNameRequestAddress2',
                    },
                    () =>
                      h(Input, {
                        placeholder: '请输入实名请求地址2',
                        value: formData.realNameRequestAddress2,
                        'onUpdate:value': (val) =>
                          (formData.realNameRequestAddress2 = val),
                        allowClear: true,
                      }),
                  ),
                ]),
              ],
              h(Col, { span: 12 }, [
                h(
                  FormItem,
                  {
                    label: '对接账号',
                    name: 'realNameRequestAccount',
                    rules: [{ required: true, message: '请输入对接账号' }],
                  },
                  () =>
                    h(Input, {
                      placeholder: '请输入对接账号',
                      value: formData.realNameRequestAccount,
                      'onUpdate:value': (val) =>
                        (formData.realNameRequestAccount = val),
                      allowClear: true,
                    }),
                ),
              ]),
              h(Col, { span: 12 }, [
                h(
                  FormItem,
                  {
                    label: '对接密码',
                    name: 'realNameRequestPassword',
                    rules: [{ required: true, message: '请输入对接密码' }],
                  },
                  () =>
                    h(Input, {
                      placeholder: '请输入对接密码',
                      value: formData.realNameRequestPassword,
                      'onUpdate:value': (val) =>
                        (formData.realNameRequestPassword = val),
                      allowClear: true,
                    }),
                ),
              ]),
              h(Col, { span: 12 }, [
                h(
                  FormItem,
                  {
                    label: '对接密钥',
                    name: 'realNameRequestKey',
                  },
                  () =>
                    h(Input, {
                      placeholder: '请输入对接密钥',
                      value: formData.realNameRequestKey,
                      'onUpdate:value': (val) =>
                        (formData.realNameRequestKey = val),
                      allowClear: true,
                    }),
                ),
              ]),
              h(Col, { span: 12 }, [
                h(
                  FormItem,
                  {
                    label: '对接密钥2',
                    name: 'realNameRequestKey2',
                  },
                  () =>
                    h(Input, {
                      placeholder: '请输入对接密钥2',
                      value: formData.realNameRequestKey2,
                      'onUpdate:value': (val) =>
                        (formData.realNameRequestKey2 = val),
                      allowClear: true,
                    }),
                ),
              ]),
            ]
          : []),
        h(Col, { span: 24 }, [
          h(
            FormItem,
            {
              label: '实名说明',
              name: 'realNameMsg',
              labelCol: { span: 3 },
              wrapperCol: { span: 20 },
            },
            () =>
              h(Input.TextArea, {
                placeholder: '请输入实名说明',
                value: formData.realNameMsg,
                'onUpdate:value': (val) => (formData.realNameMsg = val),
                rows: 4,
                allowClear: true,
              }),
          ),
        ]),
      ],
    },
    {
      title: '续费配置',
      content: (formData) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '自动续费规则',
              name: 'automaticRenewalRules',
              rules: [{ required: true, message: '请选择自动续费规则' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择自动续费规则',
                value: formData.automaticRenewalRules,
                'onUpdate:value': (val) =>
                  (formData.automaticRenewalRules = val),
                options: [
                  { label: '关闭功能', value: 0 },
                  { label: '强制续费', value: 1 },
                  { label: '选择续费', value: 2 },
                  { label: '选择续费(杰)', value: 3 },
                ],
                allowClear: true,
              }),
          ),
        ]),
        ...(formData.automaticRenewalRules === 1 ||
        formData.automaticRenewalRules === 2
          ? [
              h(Col, { span: 12 }, [
                h(
                  FormItem,
                  {
                    label: '续费阈值(%)',
                    name: 'renewalThreshold',
                    rules: [{ required: true, message: '请输入续费阈值' }],
                  },
                  () =>
                    h(InputNumber, {
                      placeholder: '请输入续费阈值',
                      value: formData.renewalThreshold,
                      'onUpdate:value': (val) =>
                        (formData.renewalThreshold = val),
                      min: 0,
                      max: 100,
                      style: { width: '100%' },
                    }),
                ),
              ]),
              h(Col, { span: 12 }, [
                h(
                  FormItem,
                  {
                    label: '可欠费金额',
                    name: 'renewalArrears',
                    rules: [{ required: true, message: '请输入可欠费金额' }],
                  },
                  () =>
                    h(InputNumber, {
                      placeholder: '请输入可欠费金额',
                      value: formData.renewalArrears,
                      'onUpdate:value': (val) =>
                        (formData.renewalArrears = val),
                      min: 0,
                      style: { width: '100%' },
                    }),
                ),
              ]),
            ]
          : []),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '提前停机',
              name: 'inAdvanceStop',
              rules: [{ required: true, message: '请输入提前停机时间' }],
            },
            () =>
              h(InputNumber, {
                placeholder: '请输入提前停机时间',
                value: formData.inAdvanceStop,
                'onUpdate:value': (val) => (formData.inAdvanceStop = val),
                min: 0,
                style: { width: '100%' },
              }),
          ),
        ]),
      ],
    },
    {
      title: '其他配置',
      content: (formData) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '限制激活',
              name: 'activationRestrictions',
              rules: [{ required: true, message: '请选择是否限制激活' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择是否限制激活',
                value: formData.activationRestrictions,
                'onUpdate:value': (val) =>
                  (formData.activationRestrictions = val),
                options: [
                  { label: '关闭限制', value: 0 },
                  { label: '限制新卡', value: 1 },
                  { label: '所有卡', value: 2 },
                ],
                allowClear: true,
              }),
          ),
        ]),
      ],
    },
  ];

  // 加载详情数据
  const loadDetail = async (id: number) => {
    try {
      loading.value = true;
      const res = await getPackageGroupDetail(id);
      if (res.code === 1 && res.data) {
        // 设置表单数据
        setFormData({
          groupName: res.data.groupName,
          groupRemarks: res.data.groupRemarks || '',
          groupOperator: res.data.groupOperator,
          configId: res.data.configId,
          realNameRules: res.data.realNameRules,
          realNameAddressRules: res.data.realNameAddressRules,
          realNameAddress: res.data.realNameAddress || '',
          realNameRequestAddress: res.data.realNameRequestAddress,
          realNameRequestAddress2: res.data.realNameRequestAddress2,
          realNameRequestAccount: res.data.realNameRequestAccount,
          realNameRequestPassword: res.data.realNameRequestPassword,
          realNameApiPlatform: res.data.realNameApiPlatform,
          realNameApiVersion: res.data.realNameApiVersion,
          realNameRequestKey: res.data.realNameRequestKey || '',
          realNameRequestKey2: res.data.realNameRequestKey2 || '',
          realNameCardType: res.data.realNameCardType,
          bindPhoneNumber: res.data.bindPhoneNumber,
          automaticRenewalRules: res.data.automaticRenewalRules,
          renewalThreshold: res.data.renewalThreshold,
          renewalArrears: res.data.renewalArrears,
          inAdvanceStop: res.data.inAdvanceStop,
          realNameMsg: res.data.realNameMsg || '',
          activationRestrictions: res.data.activationRestrictions,
          notification: res.data.notification || '',
          groupStatus: res.data.groupStatus,
        });

        return res.data;
      }
      throw new Error(res.msg || '获取详情失败');
    } catch (error) {
      console.error('获取详情失败:', error);
      message.error(error instanceof Error ? error.message : '获取详情失败');
    } finally {
      loading.value = false;
    }
  };

  // 显示表单 - 修改为使用ID参数而非record
  const show = async (recordOrId?: any) => {
    if (recordOrId?.id) {
      // 如果是编辑模式，先调用原始show并传入id，确保isEdit设置正确
      await originalShow(recordOrId.id);

      // 加载详情数据
      const detailData = await loadDetail(recordOrId.id);
      if (detailData) {
        // 使用saveFormData替代setFormData，记录原始数据
        saveFormData({
          groupName: detailData.groupName,
          groupRemarks: detailData.groupRemarks || '',
          groupOperator: detailData.groupOperator,
          configId: detailData.configId,
          realNameRules: detailData.realNameRules,
          realNameAddressRules: detailData.realNameAddressRules,
          realNameAddress: detailData.realNameAddress || '',
          realNameRequestAddress: detailData.realNameRequestAddress,
          realNameRequestAddress2: detailData.realNameRequestAddress2,
          realNameRequestAccount: detailData.realNameRequestAccount,
          realNameRequestPassword: detailData.realNameRequestPassword,
          realNameApiPlatform: detailData.realNameApiPlatform,
          realNameApiVersion: detailData.realNameApiVersion,
          realNameRequestKey: detailData.realNameRequestKey || '',
          realNameRequestKey2: detailData.realNameRequestKey2 || '',
          realNameCardType: detailData.realNameCardType,
          bindPhoneNumber: detailData.bindPhoneNumber,
          automaticRenewalRules: detailData.automaticRenewalRules,
          renewalThreshold: detailData.renewalThreshold,
          renewalArrears: detailData.renewalArrears,
          inAdvanceStop: detailData.inAdvanceStop,
          realNameMsg: detailData.realNameMsg || '',
          activationRestrictions: detailData.activationRestrictions,
          notification: detailData.notification || '',
          groupStatus: detailData.groupStatus,
        });
      }
    } else {
      // 新增模式
      await originalShow();
      // 清除表单数据，使用默认值
      clearFormData();
    }

    // 加载选项数据
    loadChannelOptions().catch((error) => {
      console.error('Failed to load options:', error);
      message.error('加载选项失败');
    });
  };

  return {
    formRef,
    formData,
    visible,
    loading,
    isEdit,
    currentId,
    show,
    close,
    setFormData,
    renderFormModal: () => renderFormModal(formGroups),
  };
}
