import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed, ref, watch } from 'vue';

import { message } from 'ant-design-vue';

import { getPackageRuleListApi } from '#/api/core/order';
import {
  assignPackageRuleByRange,
  downloadAllocationTemplate,
  importPackageRuleAssign,
} from '#/api/core/package';
import { useForm } from '#/hooks/useAnsheng';
import { handleFileDownload } from '#/utils/export';

interface FormData {
  startCardNo: string;
  endCardNo: string;
  file: File | null;
  ruleId: number | undefined;
  assignType: 'import' | 'serial';
}

export interface UseBatchAssignOptions {
  onSuccess?: () => void;
}

export function useBatchAssign(options: UseBatchAssignOptions) {
  const { onSuccess } = options;

  // 当前选择的分配方式
  const assignType = ref<'import' | 'serial'>('serial');

  // 使用 useForm 管理表单
  const form = useForm<FormData>({
    title: '批量分配',
    width: 600,
    simpleLayout: {
      showGroupTitle: false,
      padding: 0,
    },
    defaultValues: {
      startCardNo: '',
      endCardNo: '',
      ruleId: undefined,
      file: null,
      assignType: 'serial',
    },
    // 表单验证规则
    rules: {
      ruleId: [{ required: true, message: '请选择套餐规则' }],
      startCardNo: [
        {
          validator: (_, value) => {
            if (assignType.value !== 'serial') return Promise.resolve();
            return value?.trim()
              ? Promise.resolve()
              : Promise.reject(new Error('请输入起始SIM NO'));
          },
        },
      ],
      endCardNo: [
        {
          validator: (_, value) => {
            if (assignType.value !== 'serial') return Promise.resolve();
            return value?.trim()
              ? Promise.resolve()
              : Promise.reject(new Error('请输入结束SIM NO'));
          },
        },
      ],
      file: [
        {
          validator: (_, value) => {
            if (assignType.value !== 'import') return Promise.resolve();
            return value
              ? Promise.resolve()
              : Promise.reject(new Error('请选择导入文件'));
          },
        },
      ],
    },
    // 表单提交处理
    create: async (values) => {
      try {
        let res;
        switch (assignType.value) {
          case 'import': {
            if (!values.ruleId) {
              throw new Error('请选择套餐规则');
            }
            if (!values.file) {
              throw new Error('请选择要上传的文件');
            }

            res = await importPackageRuleAssign({
              file: values.file,
              packageGroupId: values.ruleId,
            });
            break;
          }
          case 'serial': {
            if (!values.ruleId) {
              throw new Error('请选择套餐规则');
            }
            if (!values.startCardNo || !values.endCardNo) {
              throw new Error('请输入起始和结束SIM NO');
            }

            res = await assignPackageRuleByRange({
              groupId: values.ruleId,
              startCardNo: values.startCardNo,
              endCardNo: values.endCardNo,
            });
            break;
          }
          default: {
            throw new Error('未知的分配方式');
          }
        }
        return res;
      } catch (error) {
        console.error('分配失败:', error);
        throw error;
      }
    },
    onSuccess: () => {
      message.success('分配成功');
      onSuccess?.();
    },
    // 关闭弹窗时重置
    onCancel: () => {
      assignType.value = 'serial';
      form.resetForm();
    },
  });

  // 监听分配方式变化
  watch(assignType, (newValue) => {
    form.formData.assignType = newValue;
  });

  // 处理分配方式变更
  const handleAssignTypeChange = (e: any) => {
    const value = e.target?.value || e;
    assignType.value = value as 'import' | 'serial';

    // 清除其他分配方式的表单值
    switch (value) {
      case 'import': {
        form.formData.startCardNo = '';
        form.formData.endCardNo = '';
        break;
      }
      case 'serial': {
        form.formData.file = null;
        break;
      }
    }
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      const res = await downloadAllocationTemplate();
      await handleFileDownload(res, `批量分配模板${Date.now()}.xlsx`);
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  // 文件上传处理
  const handleFileUpload = (file: File) => {
    form.formData.file = file;
    return false; // 阻止自动上传
  };

  // 表单分组配置
  const formGroups = computed((): FormGroup[] => [
    {
      title: '分配信息',
      fields: [
        {
          component: 'RadioGroup',
          label: '分配方式',
          col: { span: 24 },
          props: {
            options: [
              { label: '连号分配', value: 'serial' },
              { label: '导入分配', value: 'import' },
            ],
            value: assignType.value,
            onChange: handleAssignTypeChange,
          },
        },
        {
          name: 'ruleId',
          label: '套餐规则',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择套餐规则',
            allowClear: true,
          },
          remote: {
            api: getPackageRuleListApi,
            transform: (res: any) =>
              res.data.map((item: any) => ({
                label: item.name,
                value: item.id,
              })),
          },
        },
        // 连号分配
        {
          name: 'startCardNo',
          label: '起始SIM NO',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入起始SIM NO',
            allowClear: true,
          },
          show: () => assignType.value === 'serial',
        },
        {
          name: 'endCardNo',
          label: '结束SIM NO',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入结束SIM NO',
            allowClear: true,
          },
          show: () => assignType.value === 'serial',
        },
        // 导入分配
        {
          name: 'file',
          label: '导入文件',
          component: 'UploadBox',
          col: { span: 24 },
          props: {
            accept: '.xlsx,.xls,.csv',
            maxCount: 1,
            onUpload: handleFileUpload,
            onDownload: handleDownloadTemplate,
            downloadText: '模板下载',
            uploadText: '请点击上传文件',
            uploadHint: '支持 .xlsx、.xls、.csv 格式',
          },
          show: () => assignType.value === 'import',
        },
      ],
    },
  ]);

  // 打开弹窗
  const openModal = async () => {
    try {
      // 重置分配方式
      assignType.value = 'serial';
      // 重置表单
      form.resetForm();
      // 显示表单
      form.show();
    } catch (error) {
      console.error('打开弹窗失败:', error);
      message.error('打开弹窗失败');
    }
  };

  // 渲染弹窗组件
  const batchAssignModal = () => form.renderFormModal(formGroups.value);

  return {
    form,
    openModal,
    batchAssignModal,
    assignType,
  };
}
