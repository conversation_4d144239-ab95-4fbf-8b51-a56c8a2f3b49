<script setup lang="ts">
import { ref } from 'vue';

import { Card, Tabs } from 'ant-design-vue';

import CardRules from '../card-rule/index.vue';
import DeviceRules from '../device-rule/index.vue';

const activeKey = ref('card');

// Tab 配置
const tabs = [
  // 卡片规则
  {
    key: 'card',
    tab: '卡片规则',
    component: CardRules,
  },
  // 设备规则
  {
    key: 'device',
    tab: '设备规则',
    component: DeviceRules,
  },
];
</script>

<template>
  <div class="package-rules p-2">
    <Card>
      <Tabs v-model:active-key="activeKey">
        <Tabs.TabPane v-for="item in tabs" :key="item.key" :tab="item.tab">
          <component :is="item.component" />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  </div>
</template>
