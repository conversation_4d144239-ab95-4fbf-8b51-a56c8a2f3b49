import type { TableColumnType } from 'ant-design-vue';

import type {
  SearchGroup,
  SearchItemConfig,
} from '#/components/SearchToolbar/types';

import { formatDateTime } from '#/utils/format';

// 基础搜索配置
export const searchSchema: SearchItemConfig[] = [
  {
    field: 'name',
    label: '规则名称',
    component: 'Input',
    props: {
      placeholder: '请输入规则名称',
      allowClear: true,
    },
  },
  {
    field: 'realNameRules',
    label: '实名规则',
    component: 'Select',
    props: {
      placeholder: '请选择实名规则',
      allowClear: true,
      options: [
        { label: '无需实名', value: 1 },
        { label: '先充值再实名', value: 2 },
        { label: '先实名再充值', value: 3 },
      ],
    },
  },
  {
    field: 'realNameLike',
    label: '实名链接',
    component: 'Select',
    props: {
      placeholder: '请选择实名链接',
      allowClear: true,
      options: [
        { label: '系统默认', value: 1 },
        { label: '自定义填写', value: 2 },
        { label: '自适应跳转', value: 3 },
        { label: '自定义对接', value: 4 },
      ],
    },
  },
];

// 高级搜索配置
export const advancedSearchSchema: SearchGroup[] = [
  {
    label: '链接配置',
    items: [
      {
        field: 'likeAddress',
        label: '自定义地址',
        component: 'Input',
        props: {
          placeholder: '请输入自定义地址',
          allowClear: true,
        },
      },
      {
        field: 'requestAddress',
        label: '请求地址',
        component: 'Input',
        props: {
          placeholder: '请输入请求地址',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '对接配置',
    items: [
      {
        field: 'apiAccount',
        label: '对接账号',
        component: 'Input',
        props: {
          placeholder: '请输入对接账号',
          allowClear: true,
        },
      },
      {
        field: 'apiPwd',
        label: '对接密码',
        component: 'Input',
        props: {
          placeholder: '请输入对接密码',
          allowClear: true,
        },
      },
      {
        field: 'apiKey',
        label: '对接密钥',
        component: 'Input',
        props: {
          placeholder: '请输入对接密钥',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '时间信息',
    items: [
      {
        field: 'creationTimeBegin',
        label: '创建开始时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          format: 'YYYY-MM-DD',
        },
      },
      {
        field: 'creationTimeEnd',
        label: '创建结束时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          format: 'YYYY-MM-DD',
        },
      },
      {
        field: 'updateTimeBegin',
        label: '更新开始时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          format: 'YYYY-MM-DD',
        },
      },
      {
        field: 'updateTimeEnd',
        label: '更新结束时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          format: 'YYYY-MM-DD',
        },
      },
    ],
  },
];

// 表格列定义
export const columns: TableColumnType[] = [
  {
    title: '规则名称',
    dataIndex: 'name',
    width: 180,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '实名规则',
    dataIndex: 'realNameRules',
    width: 120,
    align: 'center',
    customRender: ({ text }: { text: number }) => {
      const rules: Record<number, string> = {
        1: '无需实名',
        2: '先充值再实名',
        3: '先实名再充值',
      };
      return rules[text] || '未知';
    },
  },
  {
    title: '实名链接',
    dataIndex: 'realNameLike',
    width: 120,
    align: 'center',
    customRender: ({ text }: { text: number }) => {
      const types: Record<number, string> = {
        1: '系统默认',
        2: '自定义填写',
        3: '自适应跳转',
        4: '自定义对接',
      };
      return types[text] || '未知';
    },
  },
  {
    title: '自定义地址',
    dataIndex: 'likeAddress',
    width: 200,
    align: 'center',
  },
  {
    title: '请求地址',
    dataIndex: 'requestAddress',
    width: 200,
    align: 'center',
  },
  {
    title: '对接账户',
    dataIndex: 'apiAccount',
    width: 150,
    align: 'center',
  },
  {
    title: '对接密码',
    dataIndex: 'apiPwd',
    width: 150,
    align: 'center',
  },
  {
    title: '对接密钥',
    dataIndex: 'apiKey',
    width: 150,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
    width: 180,
    align: 'center',
    customRender: ({ text }) => formatDateTime(text),
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 180,
    align: 'center',
    customRender: ({ text }) => formatDateTime(text),
  },
];
