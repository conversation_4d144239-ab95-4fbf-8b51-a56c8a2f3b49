import type { RealName } from '#/api/core/device';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';
import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed, h } from 'vue';

import { MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import {
  addRealName,
  deleteRealName,
  getRealNameList,
  updateRealName,
} from '#/api/core/device';
import { useAnsheng } from '#/hooks/useAnsheng';

import { advancedSearchSchema, columns, searchSchema } from '../config';

/**
 * 实名规则管理业务逻辑
 */
export function useRealName() {
  // 声明handleDelete函数
  let handleDelete: (record: RealName) => void;

  // 使用useAnsheng的集成方案
  const {
    // 子模块
    table,
    form,
    initialize,
    // 方法
    searchToolbarBind,
    handleTableChange,

    tableBind: origTableBind,
  } = useAnsheng({
    // 表单配置
    formOptions: {
      draggable: true,
      title: (isEdit) => (isEdit ? '编辑实名规则' : '新增实名规则'),
      simpleLayout: {
        showGroupTitle: false,
        padding: 0,
      },
      // 表单宽度
      width: 700,
      // 默认表单值
      defaultValues: {
        name: '',
        realNameRules: undefined,
        realNameLike: undefined,
        likeAddress: '',
        requestAddress: '',
        apiAccount: '',
        apiPwd: '',
        apiKey: '',
      },
      // 创建
      create: (params) => addRealName(params as RealName),
      // 更新
      update: (id, params) => updateRealName({ ...params, id } as RealName),
      // 表单规则
      rules: {
        name: [{ required: true, message: '请输入规则名称' }],
        realNameRules: [{ required: true, message: '请选择实名规则' }],
        realNameLike: [{ required: true, message: '请选择实名链接' }],
        likeAddress: [{ required: true, message: '请输入自定义地址' }],
        requestAddress: [{ required: true, message: '请输入请求地址' }],
        apiAccount: [{ required: true, message: '请输入对接账户' }],
        apiPwd: [{ required: true, message: '请输入对接密码' }],
        apiKey: [{ required: true, message: '请输入对接密钥' }],
      },
      // 成功回调
      onSuccess: () => {
        message.success('操作成功');
        table.getList(); // 刷新表格数据
      },
    },

    // 表格配置
    tableOptions: {
      api: getRealNameList,
      columns,
      defaultParams: {
        // page: 1,
        // pageSize: 10,
      },
      actionButtons: [
        {
          key: 'edit',
          text: '编辑',
          type: 'link',
          onClick: (record) => form.show(record),
        },
        {
          key: 'delete',
          text: '删除',
          type: 'link',
          danger: true,
          onClick: (record) => handleDelete(record),
        },
      ],
    },

    // 搜索配置
    searchOptions: {
      basicItems: searchSchema as SearchItemConfig[],
      advancedItems: advancedSearchSchema,
      customButtons: [
        {
          text: '新增规则',
          icon: h(MdiPlus),
          type: 'primary',
          onClick: () => form.show(),
        },
      ],
    },
  });

  // 处理删除函数实现
  handleDelete = (record: RealName) => {
    Modal.confirm({
      title: '删除确认',
      content: `确定要删除规则 ${record.name} 吗？此操作不可恢复！`,
      type: 'warning',
      okType: 'danger',
      async onOk() {
        try {
          const res = await deleteRealName(record.id);
          if (res.code === 1) {
            message.success('删除成功');
            table.getList();
          } else {
            throw new Error(res.msg || '删除失败');
          }
        } catch (error) {
          message.error(error instanceof Error ? error.message : '删除失败');
        }
      },
    });
  };

  // 表格绑定对象
  const tableBind = computed(() => ({
    ...origTableBind.value,
    onChange: handleTableChange,
  }));

  // 表单分组
  const formGroups: FormGroup[] = [
    {
      title: '基本信息',
      fields: [
        // 规则名称（始终显示）
        {
          name: 'name',
          label: '规则名称',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入规则名称',
            allowClear: true,
          },
        },
        // 实名规则（始终显示）
        {
          name: 'realNameRules',
          label: '实名规则',
          component: 'Select',
          col: { span: 12 },
          props: {
            placeholder: '请选择实名规则',
            options: [
              { label: '无需实名', value: 1 },
              { label: '先充值再实名', value: 2 },
              { label: '先实名再充值', value: 3 },
            ],
            allowClear: true,
            onChange: (val: number) => {
              // 如果选择无需实名，重置相关字段
              if (val === 1) {
                form.setFormData({
                  realNameLike: undefined,
                  likeAddress: '',
                  requestAddress: '',
                  apiAccount: '',
                  apiPwd: '',
                  apiKey: '',
                });
              }
            },
          },
        },
        // 实名链接（仅在非"无需实名"时显示）
        {
          name: 'realNameLike',
          label: '实名链接',
          component: 'Select',
          col: { span: 12 },
          props: {
            placeholder: '请选择实名链接',
            options: [
              { label: '系统默认', value: 1 },
              { label: '自定义填写', value: 2 },
              { label: '自适应跳转', value: 3 },
              { label: '自定义对接', value: 4 },
            ],
            allowClear: true,
            onChange: (_val: number) => {
              // 重置相关字段
              form.setFormData({
                likeAddress: '',
                requestAddress: '',
                apiAccount: '',
                apiPwd: '',
                apiKey: '',
              });
            },
          },
          // 仅在非"无需实名"时显示
          show: (formData) => formData.realNameRules !== 1,
        },
        // 自定义地址（仅在选择"自定义填写"时显示）
        {
          name: 'likeAddress',
          label: '自定义地址',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入自定义地址',
            allowClear: true,
          },
          // 仅在选择"自定义填写"时显示
          show: (formData) =>
            formData.realNameRules !== 1 && formData.realNameLike === 2,
        },
        // 请求地址（仅在选择"自定义对接"时显示）
        {
          name: 'requestAddress',
          label: '请求地址',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入请求地址',
            allowClear: true,
          },
          // 仅在选择"自定义对接"时显示
          show: (formData) =>
            formData.realNameRules !== 1 && formData.realNameLike === 4,
        },
        // 对接账户（仅在选择"自定义对接"时显示）
        {
          name: 'apiAccount',
          label: '对接账户',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入对接账户',
            allowClear: true,
          },
          // 仅在选择"自定义对接"时显示
          show: (formData) =>
            formData.realNameRules !== 1 && formData.realNameLike === 4,
        },
        // 对接密码（仅在选择"自定义对接"时显示）
        {
          name: 'apiPwd',
          label: '对接密码',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入对接密码',
            type: 'password',
            allowClear: true,
          },
          // 仅在选择"自定义对接"时显示
          show: (formData) =>
            formData.realNameRules !== 1 && formData.realNameLike === 4,
        },
        // 对接密钥（仅在选择"自定义对接"时显示）
        {
          name: 'apiKey',
          label: '对接密钥',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入对接密钥',
            allowClear: true,
          },
          // 仅在选择"自定义对接"时显示
          show: (formData) =>
            formData.realNameRules !== 1 && formData.realNameLike === 4,
        },
      ],
    },
  ];

  return {
    // 表格相关
    table,
    tableBind,
    searchToolbarBind,

    // 表单相关
    form,
    formGroups,

    // 方法
    initialize,
  };
}
