<script lang="ts" setup>
import { onMounted } from 'vue';

import { Card } from 'ant-design-vue';

import { BasicTable, SearchToolbar } from '#/hooks/useAnsheng';

import { useRealName } from './hooks/useRealName';

defineOptions({ name: 'RealNameList' });

const { tableBind, searchToolbarBind, initialize, form, formGroups } =
  useRealName();

// 页面加载时获取数据
onMounted(() => {
  initialize();
});
</script>

<template>
  <div class="real-name p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar v-bind="searchToolbarBind" />

      <!-- 表格 -->
      <BasicTable v-bind="tableBind" />
    </Card>

    <!-- 表单弹窗 -->
    <component :is="form.renderFormModal(formGroups)" />
  </div>
</template>

<style lang="less" scoped>
.real-name {
  background-color: var(--background-deep);
}
</style>
