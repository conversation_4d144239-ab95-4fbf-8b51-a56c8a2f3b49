<script lang="ts" setup>
import { ref, watch } from 'vue';

import { useUserStore } from '@vben/stores';

import { Form, InputNumber, message, Modal } from 'ant-design-vue';

import { updatePackagePriceApi } from '#/api/core/package';

const props = defineProps<{
  record?: any;
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}>();

const formRef = ref();
const loading = ref(false);
const formState = ref({
  packageId: undefined as number | undefined,
  packagePrice: undefined as number | undefined,
});

const userStore = useUserStore();

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  formState.value = {
    packageId: undefined,
    packagePrice: undefined,
  };
  formRef.value?.resetFields();
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (val: boolean) => {
    if (val && props.record) {
      formState.value = {
        packageId: props.record.id,
        packagePrice: props.record.packagePrice,
      };
    }
  },
);

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;
    const params = {
      packageId: formState.value.packageId!,
      packagePrice: formState.value.packagePrice!,
      userid: userStore.userInfo?.id,
    };
    const res = await updatePackagePriceApi(params);
    if (res.code === 1) {
      message.success('修改售价成功');
      emit('success');
      handleClose();
    } else {
      throw new Error(res.msg || '修改售价失败');
    }
  } catch (error) {
    console.error('修改售价失败:', error);
    message.error(error instanceof Error ? error.message : '修改售价失败');
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <Modal
    :visible="visible"
    title="修改售价"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleClose"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <Form
      ref="formRef"
      :model="formState"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <Form.Item
        label="套餐售价"
        name="packagePrice"
        :rules="[{ required: true, message: '请输入套餐售价' }]"
      >
        <InputNumber
          v-model:value="formState.packagePrice"
          placeholder="请输入套餐售价"
          :min="0"
          :precision="2"
          style="width: 100%"
        />
      </Form.Item>
    </Form>
  </Modal>
</template>
