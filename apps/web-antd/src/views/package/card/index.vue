<script lang="ts" setup>
import type { PackageItem, PackageListParams } from '#/api/core/package';

import { computed, h, onMounted, ref } from 'vue';

import { MdiDelete, MdiDownload, MdiImport, MdiPlus } from '@vben/icons';
import { useUserStore } from '@vben/stores';

import { Button, Card, message, Modal, Upload } from 'ant-design-vue';

import {
  copyPackage,
  deletePackage,
  downloadPackageTemplate,
  getPackageList,
  importPackage,
  resetPackageCode,
  setPackagePriceApi,
  updatePackage,
} from '#/api/core/package';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';
import { handleFileDownload } from '#/utils/export';

import ModifyPriceModal from './components/modify-price-modal.vue';
import PriceModal from './components/price-modal.vue';
import { advancedItems, basicSearchItems, createColumns } from './config';
import { usePackageForm } from './hooks/usePackageForm';

const userStore = useUserStore();
// 使用 useTable hook
const {
  loading,
  searchParams,
  tableData,
  pagination,
  handleSearch,
  handleReset,
  handleTableChange,
  getList,
  handleCellSave,
} = useTable<PackageItem, PackageListParams>({
  api: getPackageList,
  defaultParams: {},
  onSave: {
    default: async (record, value, key) => {
      const res = await updatePackage({
        ...record,
        [key]: value,
      });
      if (res.code !== 1) {
        throw new Error(res.msg || '保存失败');
      }
    },
    packagePrice: async (record, value) => {
      const res = await setPackagePriceApi({
        packageId: record.id,
        price: value,
        packagePrice: value,
        userId: userStore.userInfo?.id,
        packageCost: record.packageCost,
      });
      if (res.code !== 1) {
        throw new Error(res.msg || '保存价格失败');
      }
    },
  },
});

// 添加选择相关的状态
const selectedRowKeys = ref<number[]>([]);
const selectedRows = ref<any[]>([]);

// 添加状态
const showSelection = ref(false); // 添加显示选择状态

// 添加导入相关的状态
const importVisible = ref(false);
const importLoading = ref(false);
const fileList = ref<any[]>([]);

// 添加快捷改价弹窗状态
const priceModalVisible = ref(false);
const currentRecord = ref<any>(null);

// 添加修改售价弹窗状态
const modifyPriceModalVisible = ref(false);
const currentModifyRecord = ref<any>(null);

// 使用表单 hook
const { show, renderForm } = usePackageForm(() => {
  // 成功回调中刷新列表
  getList();
});

// 处理新增
const handleAdd = () => {
  show();
};

// 处理编辑
const handleEdit = (record: any) => {
  show({
    id: record.id,
  });
};

// 处理快捷改价
const handleQuickPrice = (record: any) => {
  currentRecord.value = record;
  priceModalVisible.value = true;
};

// 处理修改售价
const handleModifyPrice = (record: any) => {
  currentModifyRecord.value = record;
  modifyPriceModalVisible.value = true;
};

// 处理修改售价成功
const handleModifyPriceSuccess = () => {
  getList();
};

// 处理改价成功
const handlePriceSuccess = () => {
  getList();
};

// 处理删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该套餐吗？',
    async onOk() {
      try {
        const res = await deletePackage(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          throw new Error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error(error instanceof Error ? error.message : '删除失败');
      }
    },
  });
};

// 处理复制
const handleCopy = async (record: any) => {
  try {
    const res = await copyPackage(record.id);
    if (res.code === 1) {
      message.success('复制成功');
      getList();
    } else {
      throw new Error(res.msg || '复制失败');
    }
  } catch (error) {
    console.error('复制失败:', error);
    message.error(error instanceof Error ? error.message : '复制失败');
  }
};

// 处理重置编码
const handleResetCode = async (record: any) => {
  Modal.confirm({
    title: '确认重置',
    content: '确定要重置该套餐的编码吗？',
    async onOk() {
      try {
        const res = await resetPackageCode(record.id);
        if (res.code === 1) {
          message.success('重置成功');
          getList();
        } else {
          throw new Error(res.msg || '重置失败');
        }
      } catch (error) {
        console.error('重置失败:', error);
        message.error(error instanceof Error ? error.message : '重置失败');
      }
    },
  });
};

// 处理选择变化
const handleSelectionChange = (keys: number[], rows: any[]) => {
  selectedRowKeys.value = keys;
  selectedRows.value = rows;
};

// 处理取消选择
const handleCancelSelection = () => {
  showSelection.value = false;
  selectedRowKeys.value = [];
  selectedRows.value = [];
};

// 修改批量删除处理
const handleBatchDelete = () => {
  if (!showSelection.value) {
    showSelection.value = true;
    return;
  }

  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的套餐');
    return;
  }

  Modal.confirm({
    title: '批量删除确认',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个套餐吗？此操作不可恢复！`,
    type: 'warning',
    okType: 'danger',
    async onOk() {
      try {
        // 串行删除，确保每个都能正确处理
        for (const id of selectedRowKeys.value) {
          const res = await deletePackage(id);
          if (res.code !== 1) {
            throw new Error(res.msg || '删除失败');
          }
        }
        message.success('批量删除成功');
        selectedRowKeys.value = [];
        selectedRows.value = [];
        showSelection.value = false;
        getList();
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error(error instanceof Error ? error.message : '批量删除失败');
      }
    },
  });
};

// 处理导入
const handleImport = () => {
  importVisible.value = true;
};

// 处理下载模板
const handleDownloadTemplate = async () => {
  try {
    const res = await downloadPackageTemplate();
    await handleFileDownload(res, '套餐导入模板.xlsx');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error(error instanceof Error ? error.message : '下载模板失败');
  }
};

// 处理导入提交
const handleImportSubmit = async () => {
  if (fileList.value.length === 0) {
    message.error('请选择要导入的文件');
    return;
  }

  try {
    importLoading.value = true;
    // 添加空值检查
    const fileObj = fileList.value[0]?.originFileObj;
    if (!fileObj) {
      throw new Error('文件对象无效');
    }
    const res = await importPackage(fileObj);
    if (res.code === 1) {
      message.success('导入成功');
      importVisible.value = false;
      fileList.value = [];
      getList();
    } else {
      throw new Error(res.msg || '导入失败');
    }
  } catch (error) {
    console.error('导入失败:', error);
    message.error(error instanceof Error ? error.message : '导入失败');
  } finally {
    importLoading.value = false;
  }
};

// 修改工具栏按钮配置为计算属性
const toolbarButtons = computed(() => {
  const allButtons = [
    {
      key: 'add',
      icon: h(MdiPlus),
      text: '新增套餐',
      type: 'primary',
      onClick: handleAdd,
      permission: 1, // 只有管理员可见
    },
    {
      key: 'import',
      icon: h(MdiImport),
      text: '导入入库',
      onClick: handleImport,
      permission: 1, // 只有管理员可见
    },
    {
      key: 'batchDelete',
      text: showSelection.value ? '确认删除' : '批量删除',
      danger: true,
      icon: h(MdiDelete),
      onClick: handleBatchDelete,
      permission: 1, // 只有管理员可见
    },
  ];

  // 如果显示选择，添加取消选择按钮
  if (showSelection.value) {
    allButtons.push({
      key: 'cancelSelection',
      text: '取消选择',
      icon: h(MdiDelete),
      onClick: handleCancelSelection,
      permission: 1, // 只有管理员可见
    });
  }

  // 过滤掉没有权限的按钮
  const authority = userStore.userInfo?.authority;
  return allButtons.filter((btn) => {
    if (!btn.permission) return true;
    return authority === 1; // 只有管理员可见的按钮，只有当用户是管理员时才显示
  });
});

// 表格列配置
const columns = computed(() => createColumns(getList));

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="package-card p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedItems"
        :custom-buttons="toolbarButtons"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :editable-columns="['packageName', 'packagePrice', 'packageTotal']"
        @cell-save="handleCellSave"
        :action-buttons="[
          // 修改售价
          {
            key: 'modifyPrice',
            text: '修改售价',
            type: 'link',
            onClick: handleModifyPrice,
            // 权限显示
          },
          // 快捷改价
          {
            key: 'quickPrice',
            text: '快捷改价',
            type: 'link',
            permission: 1,
            onClick: handleQuickPrice,
          },
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            permission: 1,
            onClick: handleEdit,
          },
          {
            key: 'copy',
            text: '复制套餐',
            type: 'link',
            permission: 1,
            onClick: handleCopy,
          },
          {
            key: 'resetCode',
            text: '重置编码',
            type: 'link',
            permission: 1,
            onClick: handleResetCode,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            permission: 1,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        :show-selection="showSelection"
        v-model:selected-row-keys="selectedRowKeys"
        @selection-change="handleSelectionChange"
        row-key="id"
        @change="handleTableChange"
      />

      <!-- 渲染表单 -->
      <component :is="renderForm" />

      <!-- 导入入库弹窗 -->
      <Modal
        v-model:visible="importVisible"
        title="导入入库"
        @ok="handleImportSubmit"
        :confirm-loading="importLoading"
      >
        <div class="p-4">
          <Upload
            v-model:file-list="fileList"
            :max-count="1"
            :before-upload="() => false"
          >
            <Button>选择文件</Button>
          </Upload>
          <div class="mt-4 flex items-center justify-between">
            <span class="text-gray-500">
              请上传Excel文件，文件大小不超过10MB
            </span>
            <Button
              type="link"
              class="flex items-center"
              @click="handleDownloadTemplate"
            >
              <MdiDownload class="mr-1" />
              下载模板
            </Button>
          </div>
        </div>
      </Modal>
    </Card>

    <!-- 快捷改价弹窗 -->
    <PriceModal
      v-model:visible="priceModalVisible"
      :record="currentRecord"
      @success="handlePriceSuccess"
    />

    <!-- 修改售价弹窗 -->
    <ModifyPriceModal
      v-model:visible="modifyPriceModalVisible"
      :record="currentModifyRecord"
      @success="handleModifyPriceSuccess"
    />
  </div>
</template>

<style lang="less" scoped>
.package-card {
  background-color: var(--background-deep);
}
</style>
