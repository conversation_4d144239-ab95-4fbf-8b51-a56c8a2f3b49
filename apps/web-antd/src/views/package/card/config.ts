import type { Ref } from 'vue';

import { h, ref } from 'vue';

import { MdiContentCopy, MdiRefresh } from '@vben/icons';

import { Button, message, Modal, Switch, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  resetPackageCode,
  updatePackageShelfStatus,
  updatePackageStatus,
} from '#/api';
import { getPackageRuleListApi } from '#/api/core/order';
import { getCycleLimitOptions } from '#/api/core/package';
import { getSpeedLimitOptions, getVoidLimitOptions } from '#/api/core/sim';

// 基础搜索配置
export const basicSearchItems = [
  {
    field: 'packageName',
    label: '套餐名称',
    component: 'Input',
    props: {
      placeholder: '请输入套餐名',
      allowClear: true,
    },
  },
  {
    field: 'packageType',
    label: '套餐类型',
    component: 'Select',
    props: {
      placeholder: '请选择套餐类型',
      allowClear: true,
      options: [
        { label: '基础套餐', value: 1 },
        { label: '加油包', value: 2 },
        { label: '加速宝', value: 3 },
        { label: '体验包', value: 4 },
        { label: '短信包', value: 5 },
        { label: '语音包', value: 6 },
      ],
    },
  },
  {
    field: 'packageStatus',
    label: '套餐状态',
    component: 'Select',
    props: {
      placeholder: '请选择套餐状态',
      allowClear: true,
      options: [
        { label: '上架', value: 1 },
        { label: '下架', value: 2 },
      ],
    },
  },
];

// 高级搜索配置
export const advancedItems = [
  {
    label: '套餐规则',
    items: [
      {
        field: 'groupId',
        label: '套餐规则',
        component: 'Select',
        remote: {
          api: getPackageRuleListApi,
          transform: (data: any) => {
            return data.data.map((item: any) => ({
              label: item.name,
              value: item.id,
            }));
          },
        },
        props: {
          placeholder: '请选择套餐规则',
          allowClear: true,
        },
      },
      {
        field: 'stackingRules',
        label: '叠加规则',
        component: 'Select',
        props: {
          placeholder: '请选择叠加规则',
          allowClear: true,
          options: [
            { label: '不叠加', value: 1 },
            { label: '有效期内无限叠加', value: 2 },
            { label: '按充值日期后延充值', value: 3 },
          ],
        },
      },
      {
        field: 'orderingRules',
        label: '订购规则',
        component: 'Select',
        props: {
          placeholder: '请选择订购规则',
          allowClear: true,
          options: [
            { label: '有次月选项', value: 1 },
            { label: '无次月选项多次订购后延', value: 2 },
            { label: '禁止订购次月', value: 3 },
          ],
        },
      },
      {
        field: 'packageValidity',
        label: '有效期类型',
        component: 'Select',
        props: {
          placeholder: '请选择有效类型',
          allowClear: true,
          options: [
            { label: '月底清零', value: 1 },
            { label: '26号清零', value: 2 },
            { label: '按天自定义', value: 3 },
          ],
        },
      },
    ],
  },
  {
    label: '模板配置',
    items: [
      {
        field: 'speedLimitId',
        label: '限速模板',
        component: 'Select',
        remote: {
          api: getSpeedLimitOptions,
          transform: (data: any) => {
            return data.data.map((item: any) => ({
              label: item.name,
              value: item.id,
            }));
          },
        },
        props: {
          placeholder: '请选择限速模板',
          allowClear: true,
        },
      },
      {
        field: 'voidId',
        label: '虚量模板',
        component: 'Select',
        remote: {
          api: getVoidLimitOptions,
          transform: (data: any) => {
            return data.data.map((item: any) => ({
              label: item.name,
              value: item.id,
            }));
          },
        },
        props: {
          placeholder: '请选择虚量模板',
          allowClear: true,
        },
      },
      {
        field: 'cycleId',
        label: '周期模板',
        component: 'Select',
        remote: {
          api: getCycleLimitOptions,
          transform: (data: any) => {
            return data.data.map((item: any) => ({
              label: item.name,
              value: item.id,
            }));
          },
        },
        props: {
          placeholder: '请选择周期模板',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '时间范围',
    items: [
      {
        field: ['creationBegin', 'creationEnd'],
        label: '创建时间',
        component: 'RangePicker',
        props: {
          placeholder: ['开始时间', '结束时间'],
          allowClear: true,
        },
      },
      {
        field: ['updateBegin', 'updateEnd'],
        label: '修改时间',
        component: 'RangePicker',
        props: {
          placeholder: ['开始时间', '结束时间'],
          allowClear: true,
        },
      },
    ],
  },
];

// 创建 Switch 渲染函数
const createSwitchRender = (
  field: 'automaticRenewal' | 'defaultRenewal' | 'packageStatus',
  labels: { off: string; on: string },
) => {
  // 使用 Map 存储每个记录的 loading 状态
  const loadingMap = new Map<number, Ref<boolean>>();

  return ({ text, record }: { record: any; text: number }) => {
    // 如果该记录还没有 loading 状态，则创建一个
    if (!loadingMap.has(record.id)) {
      loadingMap.set(record.id, ref(false));
    }
    const loading = loadingMap.get(record.id) || ref(false);

    const handleChange = async (checked: boolean) => {
      loading.value = true;
      try {
        const res = await (field === 'packageStatus'
          ? updatePackageShelfStatus({
              packageId: record.id,
              status: checked ? 1 : 2,
            })
          : updatePackageStatus({
              id: record.id,
              [field]: checked ? 1 : 2,
            }));

        if (res.code === 1) {
          message.success('修改成功');
          record[field] = checked ? 1 : 2;
        } else {
          throw new Error(res.msg || '修改失败');
        }
      } catch (error) {
        message.error(error instanceof Error ? error.message : '修改失败');
      } finally {
        loading.value = false;
      }
    };

    return h(Switch, {
      checked: text === 1,
      checkedChildren: labels.on,
      unCheckedChildren: labels.off,
      loading: loading.value,
      onChange: handleChange,
    });
  };
};

// 创建表格列配置
export const createColumns = (getList: () => void) => [
  {
    title: '套餐名称',
    dataIndex: 'packageName',
    align: 'center',
    width: 200,
  },
  {
    title: '套餐类型',
    dataIndex: 'packageType',
    align: 'center',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      const typeMap = {
        1: { color: 'processing', label: '基础套餐' },
        2: { color: 'success', label: '加油包' },
        3: { color: 'warning', label: '加速宝' },
        4: { color: 'cyan', label: '体验包' },
        5: { color: 'blue', label: '短信包' },
        6: { color: 'geekblue', label: '语音包' },
      };
      return h(Tag, { color: typeMap[text]?.color }, typeMap[text]?.label);
    },
  },
  {
    title: '叠加规则',
    dataIndex: 'stackingRules',
    align: 'center',
    width: 150,
    customRender: ({ text }: { text: number }) => {
      const stackingMap = {
        1: { color: 'default', label: '不叠加' },
        2: { color: 'processing', label: '有效期内无限叠加' },
        3: { color: 'warning', label: '按充值日期后延充值' },
      };
      return h(
        Tag,
        { color: stackingMap[text]?.color },
        stackingMap[text]?.label,
      );
    },
  },
  {
    title: '下游对接编码',
    dataIndex: 'systemPackageCode',
    align: 'center',
    width: 180,
    customRender: ({ text, record }: { record: any; text: string }) => {
      const handleCopy = () => {
        navigator.clipboard.writeText(text).then(() => {
          message.success('复制成功');
        });
      };

      const handleRefresh = () => {
        Modal.confirm({
          title: '确认重置',
          content: '确定要重置该套餐的编码吗？',
          async onOk() {
            try {
              const res = await resetPackageCode(record.id);
              if (res.code === 1) {
                message.success('重置成功');
                getList();
              } else {
                throw new Error(res.msg || '重置失败');
              }
            } catch (error) {
              console.error('重置失败:', error);
              message.error(
                error instanceof Error ? error.message : '重置失败',
              );
            }
          },
        });
      };

      return h('div', { class: 'inline-flex items-center gap-1' }, [
        h('span', { class: 'truncate' }, text),
        h(
          Button,
          {
            type: 'link',
            size: 'small',
            onClick: handleCopy,
            style: { padding: '0 4px', minWidth: 'auto' },
          },
          () => h(MdiContentCopy, { style: { fontSize: '14px' } }),
        ),
        h(
          Button,
          {
            type: 'link',
            size: 'small',
            onClick: handleRefresh,
            style: { padding: '0 4px', minWidth: 'auto' },
          },
          () => h(MdiRefresh, { style: { fontSize: '14px' } }),
        ),
      ]);
    },
  },
  {
    title: '套餐价格',
    dataIndex: 'packagePrice',
    align: 'center',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return `¥${Number(text).toFixed(2)}`;
    },
  },
  {
    title: '成本价',
    dataIndex: 'packageCost',
    align: 'center',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return `¥${text.toFixed(2)}`;
    },
  },
  {
    title: '总流量',
    dataIndex: 'packageTotal',
    align: 'center',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return `${text}MB`;
    },
  },
  {
    title: '有效期类型',
    dataIndex: 'packageValidity',
    align: 'center',
    width: 120,
    customRender: ({ text, record }: { record: any; text: number }) => {
      const validityMap = {
        1: { color: 'processing', label: '月底清零' },
        2: { color: 'success', label: '26号清零' },
        3: { color: 'warning', label: `${record.validityDays}天` },
      };
      return h(
        Tag,
        { color: validityMap[text]?.color },
        validityMap[text]?.label,
      );
    },
  },
  {
    title: '自动续费',
    dataIndex: 'automaticRenewal',
    align: 'center',
    width: 100,
    customRender: createSwitchRender('automaticRenewal', {
      on: '开启',
      off: '关闭',
    }),
    permission: 1,
  },
  {
    title: '默认续费',
    dataIndex: 'defaultRenewal',
    align: 'center',
    width: 100,
    customRender: createSwitchRender('defaultRenewal', {
      on: '开启',
      off: '关闭',
    }),
    permission: 1,
  },
  {
    title: '状态',
    dataIndex: 'packageStatus',
    align: 'center',
    width: 100,
    customRender: createSwitchRender('packageStatus', {
      on: '上架',
      off: '下架',
    }),
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
    align: 'center',
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
];
