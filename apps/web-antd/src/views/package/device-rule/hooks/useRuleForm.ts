import type { FormGroup } from '#/hooks/common/useFormModal';

import { h, ref } from 'vue';

import {
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Select,
  Spin,
  Switch,
} from 'ant-design-vue';

import {
  addDeviceRule,
  getDeviceChannelOptions,
  getDeviceRealNameOption,
  updateDeviceRule,
} from '#/api/core/device';
import { cardSlotNetwork, networkOptions } from '#/constants/device-status';
import { useFormModal } from '#/hooks/common/useFormModal';
import { useImageSelector } from '#/hooks/web/useImageSelector';

const FormItem = Form.Item;

interface RuleFormState {
  name: string;
  groupImg: string;
  msg: string;
  apiConfigId: number;
  deviceType: number;
  cardType: number;
  supports5g: number;
  networkSwitching: number;
  masterCard: number;
  powerDisplay: number;
  signaling: number;
  wifiName: number;
  wifiPwd: number;
  wifiStatus: number;
  restoreFactory: number;
  restart: number;
  clearCache: number;
  nextRptTime: number;
  deviceAddress: string;
  tutorialAddress: string;
  trafficSharing: number;
  cardSlot1: number;
  cardSlot2?: number;
  cardSlot3?: number;
  cardSlot4?: number;
  realName1Id: number;
  realName2Id?: number;
  realName3Id?: number;
  realName4Id?: number;
  status: number;
  inAdvanceStop: number;
  speedLimitRules: number;
  maximumDosage1: number;
  maximumDosage2: number;
  maximumDosage3: number;
  maximumDosage4: number;
  reconstructionRules: number;
}

export function useRuleForm(callback?: () => void) {
  // 先定义状态变量
  const channelOptions = ref<{ label: string; value: number }[]>([]);
  const channelLoading = ref(false);

  const realNameOptions = ref<{ label: string; value: number }[]>([]);
  const realNameLoading = ref(false);

  // 创建表单实例
  const {
    formRef,
    formData,
    visible,
    loading,
    isEdit,
    currentId,
    show: originalShow,
    close,
    setFormData,
    saveFormData,
    resetToOriginal,
    isFormModified,
    clearFormData,
    renderFormModal,
  } = useFormModal<RuleFormState>({
    title: (isEdit) => (isEdit ? '编辑设备规则' : '新增设备规则'),
    width: 900,
    create: async (params) => {
      const requestData = {
        apiConfigId: params.apiConfigId,
        cardSlot1: params.cardSlot1,
        cardSlot2: params.cardSlot2,
        cardSlot3: params.cardSlot3,
        cardSlot4: params.cardSlot4,
        cardType: params.cardType,
        clearCache: params.clearCache,
        deviceAddress: params.deviceAddress,
        deviceType: params.deviceType,
        groupImg: params.groupImg,
        inAdvanceStop: String(params.inAdvanceStop),
        masterCard: params.masterCard,
        msg: params.msg,
        name: params.name,
        networkSwitching: params.networkSwitching,
        nextRptTime: params.nextRptTime,
        powerDisplay: params.powerDisplay,
        realName1Id: params.realName1Id,
        realName2Id: params.realName2Id,
        realName3Id: params.realName3Id,
        realName4Id: params.realName4Id,
        restart: params.restart,
        restoreFactory: params.restoreFactory,
        signaling: params.signaling,
        status: params.status,
        supports5g: params.supports5g,
        trafficSharing: params.trafficSharing,
        tutorialAddress: params.tutorialAddress,
        wifiName: params.wifiName,
        wifiPwd: params.wifiPwd,
        wifiStatus: params.wifiStatus,
        speedLimitRules: params.speedLimitRules,
        maximumDosage1: params.maximumDosage1,
        maximumDosage2: params.maximumDosage2,
        maximumDosage3: params.maximumDosage3,
        maximumDosage4: params.maximumDosage4,
        reconstructionRules: params.reconstructionRules,
      };
      return await addDeviceRule(requestData);
    },
    update: async (id, params) => {
      const requestData = {
        id,
        apiConfigId: params.apiConfigId,
        cardSlot1: params.cardSlot1,
        cardSlot2: params.cardSlot2,
        cardSlot3: params.cardSlot3,
        cardSlot4: params.cardSlot4,
        cardType: params.cardType,
        clearCache: params.clearCache,
        deviceAddress: params.deviceAddress,
        deviceType: params.deviceType,
        groupImg: params.groupImg,
        inAdvanceStop: String(params.inAdvanceStop),
        masterCard: params.masterCard,
        msg: params.msg,
        name: params.name,
        networkSwitching: params.networkSwitching,
        nextRptTime: params.nextRptTime,
        powerDisplay: params.powerDisplay,
        realName1Id: params.realName1Id,
        realName2Id: params.realName2Id,
        realName3Id: params.realName3Id,
        realName4Id: params.realName4Id,
        restart: params.restart,
        restoreFactory: params.restoreFactory,
        signaling: params.signaling,
        status: params.status,
        supports5g: params.supports5g,
        trafficSharing: params.trafficSharing,
        tutorialAddress: params.tutorialAddress,
        wifiName: params.wifiName,
        wifiPwd: params.wifiPwd,
        wifiStatus: params.wifiStatus,
        speedLimitRules: params.speedLimitRules,
        maximumDosage1: params.maximumDosage1,
        maximumDosage2: params.maximumDosage2,
        maximumDosage3: params.maximumDosage3,
        maximumDosage4: params.maximumDosage4,
        reconstructionRules: params.reconstructionRules,
      };
      return await updateDeviceRule(requestData);
    },
    onSuccess: callback,
    defaultValues: {
      supports5g: 1,
      networkSwitching: 1,
      masterCard: 1,
      powerDisplay: 1,
      signaling: 1,
      wifiName: 1,
      wifiPwd: 1,
      wifiStatus: 1,
      restoreFactory: 1,
      restart: 1,
      clearCache: 1,
      nextRptTime: 30,
      cardType: 1,
      trafficSharing: 1,
      status: 1,
      inAdvanceStop: 5,
      deviceType: 1,
      cardSlot1: undefined,
      cardSlot2: undefined,
      cardSlot3: undefined,
      cardSlot4: undefined,
      realName1Id: undefined,
      realName2Id: undefined,
      realName3Id: undefined,
      realName4Id: undefined,
      speedLimitRules: 1,
      maximumDosage1: undefined,
      maximumDosage2: undefined,
      maximumDosage3: undefined,
      maximumDosage4: undefined,
      reconstructionRules: 1,
    },
  });

  // 使用图片选择器，放在formData初始化之后
  const { renderImageSelector, ImagePreviewModal, imageUrl } = useImageSelector(
    {
      mode: 'gallery', // 默认使用图库模式
      previewSize: 100,
      onImageChange: (url, _isLocalFile) => {
        if (formData) {
          // 如果是本地文件，直接保存二进制URL
          // 如果是图库图片，则保存相对路径
          formData.groupImg = url;
        }
      },
    },
  );

  // 表单分组配置
  const formGroups: FormGroup[] = [
    {
      title: '基本信息',
      content: (formData) => [
        h(
          Col,
          { span: 12 },
          {
            default: () => [
              h(
                FormItem,
                {
                  label: '规则名称',
                  name: 'name',
                  rules: [{ required: true, message: '请输入规则名称' }],
                },
                {
                  default: () =>
                    h(Input, {
                      placeholder: '请输入规则名称',
                      value: formData.name,
                      'onUpdate:value': (val) => (formData.name = val),
                      allowClear: true,
                    }),
                },
              ),
            ],
          },
        ),
        h(
          Col,
          { span: 12 },
          {
            default: () => [
              h(
                FormItem,
                {
                  label: '厂家通道',
                  name: 'apiConfigId',
                  rules: [{ required: true, message: '请选择厂家通道' }],
                },
                {
                  default: () =>
                    h(
                      Spin,
                      { spinning: channelLoading.value },
                      {
                        default: () => [
                          h(Select, {
                            placeholder: '请选择厂家通道',
                            value: formData.apiConfigId,
                            'onUpdate:value': (val) =>
                              (formData.apiConfigId = val),
                            options: channelOptions.value,
                            allowClear: true,
                            showSearch: true,
                            filterOption: (input: string, option: any) =>
                              option?.label
                                ?.toLowerCase()
                                .includes(input.toLowerCase()),
                          }),
                        ],
                      },
                    ),
                },
              ),
            ],
          },
        ),
        h(
          Col,
          { span: 12 },
          {
            default: () => [
              h(
                FormItem,
                {
                  label: '设备类型',
                  name: 'deviceType',
                  rules: [{ required: true, message: '请选择设备类型' }],
                },
                {
                  default: () =>
                    h(Select, {
                      placeholder: '请选择设备类型',
                      value: formData.deviceType,
                      'onUpdate:value': (val) => {
                        formData.deviceType = val;
                        // 根据设备类型重置卡槽和实名规则
                        if (val < 4) {
                          // 如果不是四网，重置多余的卡槽和实名规则
                          for (let i = val + 1; i <= 4; i++) {
                            formData[`cardSlot${i}`] = undefined;
                            formData[`realName${i}Id`] = undefined;
                          }
                        }
                      },
                      options: cardSlotNetwork,
                      allowClear: true,
                    }),
                },
              ),
            ],
          },
        ),
        h(
          Col,
          { span: 12 },
          {
            default: () => [
              h(
                FormItem,
                {
                  label: '卡片类型',
                  name: 'cardType',
                  rules: [{ required: true, message: '请选择卡片类型' }],
                },
                {
                  default: () =>
                    h(Select, {
                      placeholder: '请选择卡片类型',
                      value: formData.cardType,
                      'onUpdate:value': (val) => (formData.cardType = val),
                      options: [
                        { label: '自定义导入', value: 1 },
                        { label: '云卡无需导入', value: 2 },
                      ],
                      allowClear: true,
                    }),
                },
              ),
            ],
          },
        ),
        h(
          Col,
          { span: 12 },
          {
            default: () => [
              h(
                FormItem,
                {
                  label: '规则状态',
                  name: 'status',
                  rules: [{ required: true, message: '请选择规则状态' }],
                },
                {
                  default: () =>
                    h(Select, {
                      placeholder: '请选择规则状态',
                      value: formData.status,
                      'onUpdate:value': (val) => (formData.status = val),
                      options: [
                        { label: '下架', value: 1 },
                        { label: '上架', value: 2 },
                      ],
                      allowClear: true,
                    }),
                },
              ),
            ],
          },
        ),
        h(
          Col,
          { span: 12 },
          {
            default: () => [
              h(
                FormItem,
                {
                  label: '提前停机',
                  name: 'inAdvanceStop',
                  rules: [{ required: true, message: '请输入提前停机时间' }],
                },
                {
                  default: () =>
                    h(InputNumber, {
                      placeholder: '请输入提前停机时间',
                      value: formData.inAdvanceStop,
                      'onUpdate:value': (val) => (formData.inAdvanceStop = val),
                      style: { width: '100%' },
                      min: 0,
                    }),
                },
              ),
            ],
          },
        ),
        h(
          Col,
          { span: 24 },
          {
            default: () => [
              h(
                FormItem,
                {
                  label: '规则主图',
                  name: 'groupImg',
                  labelCol: { span: 3 },
                  wrapperCol: { span: 20 },
                },
                {
                  default: () => renderImageSelector(),
                },
              ),
            ],
          },
        ),
      ],
    },
    {
      title: '网络配置',
      content: (formData) => {
        if (!formData.deviceType) return [];

        const slots = [];
        const maxSlots = formData.deviceType;

        for (let i = 1; i <= maxSlots; i++) {
          slots.push(
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: `卡槽${i}网络`,
                  name: `cardSlot${i}`,
                  rules: [{ required: true, message: `请选择卡槽${i}网络` }],
                },
                () =>
                  h(Select, {
                    placeholder: '请选择网络',
                    value: formData[`cardSlot${i}`],
                    'onUpdate:value': (val) => (formData[`cardSlot${i}`] = val),
                    options: networkOptions,
                    allowClear: true,
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: `实名规则${i}`,
                  name: `realName${i}Id`,
                  rules: [{ required: true, message: `请选择实名规则${i}` }],
                },
                () =>
                  h(Spin, { spinning: realNameLoading.value }, [
                    h(Select, {
                      placeholder: '请选择实名规则',
                      value: formData[`realName${i}Id`],
                      'onUpdate:value': (val) =>
                        (formData[`realName${i}Id`] = val),
                      options: realNameOptions.value,
                      allowClear: true,
                    }),
                  ]),
              ),
            ]),
          );
        }
        return slots;
      },
    },
    {
      title: '功能配置',
      content: (formData) => [
        h(Col, { span: 12 }, [
          h(FormItem, { label: '网络切换', name: 'networkSwitching' }, () =>
            h(Select, {
              placeholder: '请选择网络切换',
              value: formData.networkSwitching,
              'onUpdate:value': (val) => (formData.networkSwitching = val),
              options: [
                { label: '不支持', value: 1 },
                { label: '支持', value: 2 },
                { label: '虚假', value: 3 },
                { label: '内置卡切换', value: 4 },
              ],
              allowClear: true,
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '主卡网络', name: 'masterCard' }, () =>
            h(Select, {
              placeholder: '请选择主卡网络',
              value: formData.masterCard,
              'onUpdate:value': (val) => (formData.masterCard = val),
              options: Array.from(
                { length: formData.deviceType || 0 },
                (_, i) => ({
                  label: `卡槽${i + 1}`,
                  value: i + 1,
                }),
              ),
              allowClear: true,
              disabled: !formData.deviceType, // 如果没有选择设备类型则禁用
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '5G网络', name: 'supports5g' }, () =>
            h(Switch, {
              checked: formData.supports5g === 2,
              'onUpdate:checked': (val) => (formData.supports5g = val ? 2 : 1),
              checkedChildren: '支持',
              unCheckedChildren: '不支持',
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '电量显示', name: 'powerDisplay' }, () =>
            h(Switch, {
              checked: formData.powerDisplay === 2,
              'onUpdate:checked': (val) =>
                (formData.powerDisplay = val ? 2 : 1),
              checkedChildren: '显示',
              unCheckedChildren: '隐藏',
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '信号显示', name: 'signaling' }, () =>
            h(Switch, {
              checked: formData.signaling === 2,
              'onUpdate:checked': (val) => (formData.signaling = val ? 2 : 1),
              checkedChildren: '显示',
              unCheckedChildren: '隐藏',
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '名称修改', name: 'wifiName' }, () =>
            h(Switch, {
              checked: formData.wifiName === 2,
              'onUpdate:checked': (val) => (formData.wifiName = val ? 2 : 1),
              checkedChildren: '允许',
              unCheckedChildren: '不允许',
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '密码修改', name: 'wifiPwd' }, () =>
            h(Switch, {
              checked: formData.wifiPwd === 2,
              'onUpdate:checked': (val) => (formData.wifiPwd = val ? 2 : 1),
              checkedChildren: '允许',
              unCheckedChildren: '不允许',
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '隐藏状态', name: 'wifiStatus' }, () =>
            h(Switch, {
              checked: formData.wifiStatus === 2,
              'onUpdate:checked': (val) => (formData.wifiStatus = val ? 2 : 1),
              checkedChildren: '允许',
              unCheckedChildren: '不允许',
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '重置设备', name: 'restoreFactory' }, () =>
            h(Switch, {
              checked: formData.restoreFactory === 2,
              'onUpdate:checked': (val) =>
                (formData.restoreFactory = val ? 2 : 1),
              checkedChildren: '允许',
              unCheckedChildren: '不允许',
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '重启设备', name: 'restart' }, () =>
            h(Switch, {
              checked: formData.restart === 2,
              'onUpdate:checked': (val) => (formData.restart = val ? 2 : 1),
              checkedChildren: '允许',
              unCheckedChildren: '不允许',
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '清除缓存', name: 'clearCache' }, () =>
            h(Switch, {
              checked: formData.clearCache === 2,
              'onUpdate:checked': (val) => (formData.clearCache = val ? 2 : 1),
              checkedChildren: '允许',
              unCheckedChildren: '不允许',
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '上报时间',
              name: 'nextRptTime',
              rules: [{ required: true, message: '请输入上报时间' }],
            },
            () =>
              h(InputNumber, {
                placeholder: '请输入上报时间(秒)',
                value: formData.nextRptTime,
                'onUpdate:value': (val) => (formData.nextRptTime = val),
                style: { width: '100%' },
                min: 0,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '设备地址',
              name: 'deviceAddress',
              rules: [{ required: true, message: '请输入设备地址' }],
            },
            () =>
              h(Input, {
                placeholder: '请输入设备地址',
                value: formData.deviceAddress,
                'onUpdate:value': (val) => (formData.deviceAddress = val),
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '教程地址',
              name: 'tutorialAddress',
              rules: [{ required: true, message: '请输入教程地址' }],
            },
            () =>
              h(Input, {
                placeholder: '请输入教程地址',
                value: formData.tutorialAddress,
                'onUpdate:value': (val) => (formData.tutorialAddress = val),
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 24 }, [
          h(
            FormItem,
            {
              label: '激活描述',
              name: 'msg',
              labelCol: { span: 3 },
              wrapperCol: { span: 20 },
            },
            () =>
              h(Input.TextArea, {
                placeholder: '请输入激活描述',
                value: formData.msg,
                'onUpdate:value': (val) => (formData.msg = val),
                rows: 4,
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '流量共享', name: 'trafficSharing' }, () =>
            h(Switch, {
              checked: formData.trafficSharing === 2,
              'onUpdate:checked': (val) =>
                (formData.trafficSharing = val ? 2 : 1),
              checkedChildren: '共享',
              unCheckedChildren: '不共享',
            }),
          ),
        ]),
      ],
    },
    {
      title: '功能配置',
      content: (formData) => [
        // speedLimitRules: number; // 限速规则 1卡片限速 2设备限速 必填默认卡片限速
        // maximumDosage1: number; // 自然月限制用量卡1(mb)
        // maximumDosage2: number; // 自然月限制用量卡2(mb)
        // maximumDosage3: number; // 自然月限制用量卡3(mb)
        // maximumDosage4: number; // 自然月限制用量卡4(mb)
        // reconstructionRules: number; // 复机规则 1仅主卡  2所有卡
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '限速规则',
              name: 'speedLimitRules',
              rules: [{ required: true, message: '请选择限速规则' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择限速规则',
                value: formData.speedLimitRules,
                'onUpdate:value': (val) => (formData.speedLimitRules = val),
                options: [
                  { label: '卡片限速', value: 1 },
                  { label: '设备限速', value: 2 },
                ],
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '限制用量卡1', name: 'maximumDosage1' }, () =>
            h(Input, {
              placeholder: '请输入自然月限制用量卡1（MB）',
              value: formData.maximumDosage1,
              'onUpdate:value': (val) => (formData.maximumDosage1 = val),
              style: { width: '100%' },
              min: 0,
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '限制用量卡2', name: 'maximumDosage2' }, () =>
            h(Input, {
              placeholder: '请输入自然月限制用量卡2（MB）',
              value: formData.maximumDosage2,
              'onUpdate:value': (val) => (formData.maximumDosage2 = val),
              style: { width: '100%' },
              min: 0,
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '限制用量卡3', name: 'maximumDosage3' }, () =>
            h(Input, {
              placeholder: '请输入自然月限制用量卡3（MB）',
              value: formData.maximumDosage3,
              'onUpdate:value': (val) => (formData.maximumDosage3 = val),
              style: { width: '100%' },
              min: 0,
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '限制用量卡4', name: 'maximumDosage4' }, () =>
            h(Input, {
              placeholder: '请输入自然月限制用量卡4（MB）',
              value: formData.maximumDosage4,
              'onUpdate:value': (val) => (formData.maximumDosage4 = val),
              style: { width: '100%' },
              min: 0,
            }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(FormItem, { label: '复机规则', name: 'reconstructionRules' }, () =>
            h(Select, {
              placeholder: '请选择复机规则',
              value: formData.reconstructionRules,
              'onUpdate:value': (val) => (formData.reconstructionRules = val),
              options: [
                { label: '仅主卡', value: 1 },
                { label: '所有卡', value: 2 },
              ],
              allowClear: true,
            }),
          ),
        ]),
      ],
    },
  ];

  // 方法定义
  const loadChannelOptions = async () => {
    try {
      channelLoading.value = true;
      const res = await getDeviceChannelOptions();
      if (res.code === 1) {
        channelOptions.value = res.data.map((item: any) => ({
          label: item.name,
          value: item.id,
        }));
      }
    } catch (error) {
      console.error('Failed to load channel options:', error);
    } finally {
      channelLoading.value = false;
    }
  };

  const loadRealNameOptions = async () => {
    try {
      realNameLoading.value = true;
      const res = await getDeviceRealNameOption();
      if (res.code === 1) {
        realNameOptions.value = res.data.map((item: any) => ({
          label: item.name,
          value: item.id,
        }));
      }
    } catch (error) {
      console.error('Failed to load real name options:', error);
    } finally {
      realNameLoading.value = false;
    }
  };

  const show = async (record?: any) => {
    if (record?.id) {
      // 如果是编辑模式，先调用原始show并传入id，确保isEdit设置正确
      await originalShow(record.id);

      // 使用saveFormData替代setFormData以保存原始数据（用于重置和检测修改）
      saveFormData({
        name: record.name,
        groupImg: record.groupImg,
        msg: record.msg,
        apiConfigId: record.apiConfigId,
        deviceType: record.deviceType,
        cardType: record.cardType,
        supports5g: record.supports5g,
        networkSwitching: record.networkSwitching,
        masterCard: record.masterCard,
        powerDisplay: record.powerDisplay,
        signaling: record.signaling,
        wifiName: record.wifiName,
        wifiPwd: record.wifiPwd,
        wifiStatus: record.wifiStatus,
        restoreFactory: record.restoreFactory,
        restart: record.restart,
        clearCache: record.clearCache,
        nextRptTime: record.nextRptTime,
        deviceAddress: record.deviceAddress,
        tutorialAddress: record.tutorialAddress,
        trafficSharing: record.trafficSharing,
        cardSlot1: record.cardSlot1,
        cardSlot2: record.cardSlot2,
        cardSlot3: record.cardSlot3,
        cardSlot4: record.cardSlot4,
        realName1Id: record.realName1Id,
        realName2Id: record.realName2Id,
        realName3Id: record.realName3Id,
        realName4Id: record.realName4Id,
        status: record.status,
        inAdvanceStop: record.inAdvanceStop,
        speedLimitRules: record.speedLimitRules,
        maximumDosage1: record.maximumDosage1,
        maximumDosage2: record.maximumDosage2,
        maximumDosage3: record.maximumDosage3,
        maximumDosage4: record.maximumDosage4,
        reconstructionRules: record.reconstructionRules,
      });

      // 设置图片URL以显示预览
      imageUrl.value = record.groupImg;
    } else {
      // 新增模式
      await originalShow();
      // 清除表单数据，保留默认值
      clearFormData();
      // 清空图片
      imageUrl.value = '';
    }

    // 后台加载数据
    Promise.all([loadChannelOptions(), loadRealNameOptions()]).catch(
      (error) => {
        console.error('Failed to load options:', error);
        message.error('加载选项失败');
      },
    );
  };

  return {
    formRef,
    formData,
    visible,
    loading,
    isEdit,
    currentId,
    show,
    close,
    setFormData,
    saveFormData,
    resetToOriginal,
    isFormModified,
    renderFormModal: () => renderFormModal(formGroups),
    ImagePreviewModal,
  };
}
