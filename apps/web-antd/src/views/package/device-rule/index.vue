<script lang="ts" setup>
import type { DeviceCard, DeviceRule } from '#/api/core/device';

import { h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import { deleteDeviceRule, getDeviceRuleList } from '#/api/core/device';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { advancedSearchItems, basicSearchItems, columns } from './config';
import { useRuleForm } from './hooks/useRuleForm';

// 设备通道选项
const deviceChannelOptions = ref<{ label: string; value: number }[]>([]);
const selectedRows = ref<DeviceCard[]>([]);
const showSelection = ref(false);
const selectedRowKeys = ref<(number | string)[]>([]);

// 表格和搜索状态管理
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable<DeviceRule, { page: number; pageSize: number }>({
  api: getDeviceRuleList,
  defaultParams: {},
});

// 从useRuleForm获取需要的函数和状态
const {
  renderFormModal,
  show: showRuleModal,
  ImagePreviewModal,
} = useRuleForm(() => {
  getList();
});

// 基础搜索项配置（带选项数据）
const computedBasicItems = ref(basicSearchItems);

const handleAdd = () => {
  showRuleModal();
};

const handleEdit = (record: any) => {
  showRuleModal(record);
};

const handleConfirmModal = ({
  title,
  content,
  onOk,
  type = 'info',
  okType = 'primary',
  okText = '确定',
  cancelText = '取消',
}: {
  cancelText?: string;
  content: (() => any) | string;
  okText?: string;
  okType?: 'danger' | 'primary';
  onOk: () => Promise<void>;
  title: string;
  type?: 'error' | 'info' | 'success' | 'warning';
}) => {
  Modal.confirm({
    title,
    content: typeof content === 'function' ? content() : content,
    type,
    okType,
    okText,
    cancelText,
    async onOk() {
      try {
        loading.value = true;
        await onOk();
      } catch (error: any) {
        if (error && error.errorFields) {
          return;
        }
        throw error;
      } finally {
        loading.value = false;
      }
    },
  });
};
const handleDelete = (record: any) => {
  handleConfirmModal({
    title: '删除确认',
    content: `确定要删除规则 ${record.name} 吗？此操作不可恢复！`,
    type: 'warning',
    okType: 'danger',
    async onOk() {
      const res = await deleteDeviceRule(record.id);
      if (res.code === 1) {
        message.success('删除成功');
        getList();
      } else {
        message.error(res.msg || '删除失败');
      }
    },
  });
};

const handleSelectionChange = (
  keys: (number | string)[],
  rows: DeviceCard[],
) => {
  selectedRowKeys.value = keys;
  selectedRows.value = rows;
};

const actionButtons = [
  {
    key: 'edit',
    text: '编辑',
    // type: 'primary' as const,
    // type: 'default',
    // ghost: true,
    onClick: handleEdit,
  },
  {
    key: 'delete',
    text: '删除',
    type: 'link' as const,
    danger: true,
    onClick: handleDelete,
  },
];

const customButtons = [
  {
    key: 'addRule',
    text: '新增规则',
    icon: h(MdiPlus),
    onClick: handleAdd,
  },
];

onMounted(() => {
  getList();
});
</script>

<template>
  <div>
    <!-- <Card> -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="computedBasicItems"
      :advanced-items="advancedSearchItems"
      :custom-buttons="customButtons"
      @reset="handleReset"
      @search="handleSearch"
    />

    <BasicTable
      class="mt-4"
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      :action-buttons="actionButtons"
      :show-selection="showSelection"
      v-model:selected-row-keys="selectedRowKeys"
      @selection-change="handleSelectionChange"
      @change="handleTableChange"
      row-key="id"
      :show-action="true"
    />
    <!-- </Card> -->
    <!-- 添加规则表单弹窗 -->
    <component :is="renderFormModal()" />

    <!-- 添加图片选择弹窗 -->
    <component :is="ImagePreviewModal()" />
  </div>
</template>

<!-- <style scoped></style> -->
