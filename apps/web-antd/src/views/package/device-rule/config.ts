import type { TableColumnType } from 'ant-design-vue/es/table';

import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { getDeviceChannelOptions } from '#/api';
import {
  cardSlotNetwork,
  clearCache,
  networkOptions,
  networkSwitch,
  powerDisplay,
  restart,
  restoreFactory,
  signaling,
  wifiName,
  wifiPwd,
  wifiStatus,
} from '#/constants/device-status';
import { formatDateTime } from '#/utils/format';

// 状态选项
const statusOptions = [
  { label: '下架', value: 1 },
  { label: '上架', value: 2 },
];

// 流量共享选项
const trafficSharingOptions = [
  { label: '不共享', value: 1 },
  { label: '共享', value: 2 },
];

// 基础搜索配置
export const basicSearchItems: SearchItemConfig[] = [
  {
    field: 'name',
    label: '规则名称',
    component: 'Input',
    props: {
      placeholder: '请输入规则名称',
      allowClear: true,
    },
  },
  {
    field: 'apiConfigIds',
    label: '厂家通道',
    component: 'Select',
    remote: {
      api: getDeviceChannelOptions,
      transform: (data: any) =>
        data.data.map((item: any) => ({
          label: `${item.name}`, // 组合显示
          value: item.id,
        })),
    },
    props: {
      placeholder: '请选择厂家通道',
      allowClear: true,
      optionFilterProp: 'label',
    },
  },
  {
    field: 'deviceType',
    label: '设备类型',
    component: 'Select',
    props: {
      placeholder: '请选择设备类型',
      allowClear: true,
    },
    options: cardSlotNetwork,
  },
  {
    field: 'status',
    label: '规则状态',
    component: 'Select',
    props: {
      placeholder: '请选择规则状态',
      allowClear: true,
    },
    options: statusOptions,
  },
];

// 高级搜索配置
export const advancedSearchItems: {
  items: SearchItemConfig[];
  label: string;
}[] = [
  {
    label: '网络设置',
    items: [
      {
        field: 'networkSwitching',
        label: '网络切换',
        component: 'Select',
        props: {
          placeholder: '请选择网络切换',
          allowClear: true,
        },
        options: networkSwitch,
      },
      {
        field: 'masterCard',
        label: '主卡网络',
        component: 'Select',
        props: {
          placeholder: '请选择主卡网络',
          allowClear: true,
        },
        options: networkOptions,
      },
      {
        field: 'trafficSharing',
        label: '流量共享',
        component: 'Select',
        props: {
          placeholder: '请选择是否共享流量',
          allowClear: true,
        },
        options: trafficSharingOptions,
      },
    ],
  },
  {
    label: '卡槽设置',
    items: [
      {
        field: 'cardSlot1',
        label: '卡槽网络1',
        component: 'Select',
        props: {
          placeholder: '请选择卡槽1网络',
          allowClear: true,
        },
        options: networkOptions,
      },
      {
        field: 'cardSlot2',
        label: '卡槽网络2',
        component: 'Select',
        props: {
          placeholder: '请选择卡槽2网络',
          allowClear: true,
        },
        options: networkOptions,
      },
      {
        field: 'cardSlot3',
        label: '卡槽网络3',
        component: 'Select',
        props: {
          placeholder: '请选择卡槽3网络',
          allowClear: true,
        },
        options: networkOptions,
      },
      {
        field: 'cardSlot4',
        label: '卡槽网络4',
        component: 'Select',
        props: {
          placeholder: '请选择卡槽4网络',
          allowClear: true,
        },
        options: networkOptions,
      },
    ],
  },
  {
    label: '设备显示',
    items: [
      {
        field: 'powerDisplay',
        label: '电量显示',
        component: 'Select',
        props: {
          placeholder: '请选择电量显示',
          allowClear: true,
        },
        options: powerDisplay,
      },
      {
        field: 'signaling',
        label: '信号显示',
        component: 'Select',
        props: {
          placeholder: '请选择信号显示',
          allowClear: true,
        },
        options: signaling,
      },
    ],
  },
  {
    label: 'WIFI设置',
    items: [
      {
        field: 'wifiName',
        label: '名称修改',
        component: 'Select',
        props: {
          placeholder: '请选择名称修改',
          allowClear: true,
        },
        options: wifiName,
      },
      {
        field: 'wifiPwd',
        label: '密码修改',
        component: 'Select',
        props: {
          placeholder: '请选择密码修改',
          allowClear: true,
        },
        options: wifiPwd,
      },
      {
        field: 'wifiStatus',
        label: '隐藏状态',
        component: 'Select',
        props: {
          placeholder: '请选择隐藏状态',
          allowClear: true,
        },
        options: wifiStatus,
      },
    ],
  },
  {
    label: '设备控制',
    items: [
      {
        field: 'restoreFactory',
        label: '重置设备',
        component: 'Select',
        props: {
          placeholder: '请选择重置设备',
          allowClear: true,
        },
        options: restoreFactory,
      },
      {
        field: 'restart',
        label: '重启设备',
        component: 'Select',
        props: {
          placeholder: '请选择重启设备',
          allowClear: true,
        },
        options: restart,
      },
      {
        field: 'clearCache',
        label: '清除缓存',
        component: 'Select',
        props: {
          placeholder: '请选择清除缓存',
          allowClear: true,
        },
        options: clearCache,
      },
    ],
  },
  {
    label: '地址信息',
    items: [
      {
        field: 'deviceAddress',
        label: '设备地址',
        component: 'Input',
        props: {
          placeholder: '请输入设备地址',
          allowClear: true,
        },
      },
      {
        field: 'tutorialAddress',
        label: '教程地址',
        component: 'Input',
        props: {
          placeholder: '请输入教程地址',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '时间区间',
    items: [
      {
        field: 'creationTimeBegin',
        label: '创建时间(开始)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'creationTimeEnd',
        label: '创建时间(结束)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'updateTimeBegin',
        label: '修改时间(开始)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'updateTimeEnd',
        label: '修改时间(结束)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
];

// 图片预览配置
export const imagePreviewConfig = {
  // 图片列表
  images: [
    {
      src: 'https://img.picui.cn/free/2025/02/05/67a3715791fe5.jpeg',
    },
    {
      src: 'https://img.picui.cn/free/2025/02/05/67a3715791fe5.jpeg',
    },
    {
      src: 'https://img.picui.cn/free/2025/02/05/67a3715791fe5.jpeg',
    },
    {
      src: 'https://img.picui.cn/free/2025/02/05/67a3715791fe5.jpeg',
    },
    {
      src: 'https://img.picui.cn/free/2025/02/05/67a3715791fe5.jpeg',
    },
    {
      src: 'https://img.picui.cn/free/2025/02/05/67a3715791fe5.jpeg',
    },
    {
      src: 'https://img.picui.cn/free/2025/02/05/67a3715791fe5.jpeg',
    },
    {
      src: 'https://img.picui.cn/free/2025/02/05/67a3715791fe5.jpeg',
    },
    {
      src: 'https://img.picui.cn/free/2025/02/05/67a3715791fe5.jpeg',
    },
    {
      src: 'https://img.picui.cn/free/2025/02/05/67a3715791fe5.jpeg',
    },
  ],
};

// 表格列定义
export const columns: TableColumnType[] = [
  {
    title: '规则名称',
    dataIndex: 'name',
    width: 180,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备类型',
    dataIndex: 'deviceType',
    width: 120,
    align: 'center',
    customRender: ({ text }) => {
      const types = {
        1: '单网',
        2: '双网',
        3: '三网',
        4: '四网',
      };
      return types[text] || '未知';
    },
  },
  {
    title: '卡片类型',
    dataIndex: 'cardType',
    width: 120,
    align: 'center',
    customRender: ({ text }) => {
      const types = {
        1: '自定义导入',
        2: '云卡无需导入',
      };
      return types[text] || '未知';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    align: 'center',
    customRender: ({ text }) => {
      const statuses = {
        1: '下架',
        2: '上架',
      };
      return statuses[text] || '未知';
    },
  },
  {
    title: '充值地址',
    dataIndex: 'id',
    width: 300,
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => {
      const url = `${window.location.origin}/device_aw?deviceModel=${text}`;
      return h(
        'a',
        {
          href: url,
          target: '_blank',
        },
        url,
      );
    },
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
    width: 180,
    align: 'center',
    customRender: ({ text }) => formatDateTime(text),
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 180,
    align: 'center',
    customRender: ({ text }) => formatDateTime(text),
  },
];

// 向下兼容旧的搜索配置
export const searchSchema = basicSearchItems;
