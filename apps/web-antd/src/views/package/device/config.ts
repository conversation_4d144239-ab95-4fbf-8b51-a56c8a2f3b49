import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

// 基础搜索配置
export const basicSearchItems = [
  {
    field: 'name',
    label: '套餐名称',
    component: 'Input',
    props: {
      placeholder: '请输入套餐名称',
      allowClear: true,
    },
  },
  {
    field: 'packageType',
    label: '套餐类型',
    component: 'Select',
    props: {
      placeholder: '请选择套餐类型',
      allowClear: true,
      options: [
        { label: '基础套餐', value: 1 },
        { label: '加油包', value: 2 },
        { label: '加速宝', value: 3 },
        { label: '体验包', value: 4 },
        { label: '短信包', value: 5 },
        { label: '语音包', value: 6 },
      ],
    },
  },
  {
    field: 'packageStatus',
    label: '套餐状态',
    component: 'Select',
    props: {
      placeholder: '请选择套餐状态',
      allowClear: true,
      options: [
        { label: '上架', value: 1 },
        { label: '下架', value: 2 },
      ],
    },
  },
  {
    field: 'systemPackageCode',
    label: '系统套餐编码',
    component: 'Input',
    props: {
      placeholder: '请输入系统套餐编码',
      allowClear: true,
    },
  },
  {
    field: 'packageCode',
    label: '对接上游编码',
    component: 'Input',
    props: {
      placeholder: '请输入对接上游编码',
      allowClear: true,
    },
  },
];

// 高级搜索配置
export const advancedSearchItems = [
  {
    label: '规则配置',
    items: [
      {
        field: 'voidType',
        label: '虚量模式',
        component: 'Select',
        props: {
          placeholder: '请选择虚量模式',
          allowClear: true,
        },
        options: [
          { label: '统一虚量', value: 1 },
          { label: '阶梯虚量', value: 2 },
        ],
      },
      {
        field: 'cycleRule',
        label: '周期规则',
        component: 'Select',
        props: {
          placeholder: '请选择周期规则',
          allowClear: true,
        },
        options: [
          { label: '自定义填写', value: 1 },
          { label: '模板规则', value: 2 },
        ],
      },
      {
        field: 'orderRule',
        label: '订购规则',
        component: 'Select',
        props: {
          placeholder: '请选择订购规则',
          allowClear: true,
        },
        options: [
          { label: '常规订购', value: 1 },
          { label: '无次月选项', value: 2 },
          { label: '禁止次月', value: 3 },
        ],
      },
      {
        field: 'stackingRules',
        label: '叠加规则',
        component: 'Select',
        props: {
          placeholder: '请选择叠加规则',
          allowClear: true,
        },
        options: [
          { label: '不叠加', value: 1 },
          { label: '有效期内无限叠加', value: 2 },
          { label: '按充值日期后延充值', value: 3 },
        ],
      },
      {
        field: 'packageValidity',
        label: '有效期类型',
        component: 'Select',
        props: {
          placeholder: '请选择有效期类型',
          allowClear: true,
        },
        options: [
          { label: '月底清零', value: 1 },
          { label: '26号清零', value: 2 },
          { label: '按天自定义', value: 3 },
        ],
      },
    ],
  },
  {
    label: '编码配置',
    items: [
      {
        field: 'useAreaId',
        label: '额外对接编码',
        component: 'Input',
        props: {
          placeholder: '请输入额外对接编码',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '流量配置',
    items: [
      {
        field: 'packageTotalMin',
        label: '总流量最小值',
        component: 'InputNumber',
        props: {
          placeholder: '请输入总流量最小值',
          min: 0,
          style: { width: '100%' },
        },
      },
      {
        field: 'packageTotalMax',
        label: '总流量最大值',
        component: 'InputNumber',
        props: {
          placeholder: '请输入总流量最大值',
          min: 0,
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '创建时间',
    items: [
      {
        field: 'creationTimeStart',
        label: '开始时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          style: { width: '100%' },
        },
      },
      {
        field: 'creationTimeEnd',
        label: '结束时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '修改时间',
    items: [
      {
        field: 'updateTimeStart',
        label: '开始时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          style: { width: '100%' },
        },
      },
      {
        field: 'updateTimeEnd',
        label: '结束时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          style: { width: '100%' },
        },
      },
    ],
  },
];

// 时间范围搜索配置
export const timeRangeSearchItems = [
  {
    field: 'creationTime',
    label: '创建时间',
    component: 'RangePicker',
    props: {
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
      style: { width: '100%' },
    },
  },
  {
    field: 'updateTime',
    label: '修改时间',
    component: 'RangePicker',
    props: {
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
      style: { width: '100%' },
    },
  },
];

// 创建表格列配置
export const createColumns = () => [
  {
    title: '套餐名称',
    dataIndex: 'name',
    align: 'center',
    width: 200,
  },
  {
    title: '套餐类型',
    dataIndex: 'packageType',
    align: 'center',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      const typeMap = {
        1: { color: 'processing', label: '基础套餐' },
        2: { color: 'success', label: '加油包' },
        3: { color: 'warning', label: '加速宝' },
        4: { color: 'cyan', label: '体验包' },
        5: { color: 'blue', label: '短信包' },
        6: { color: 'geekblue', label: '语音包' },
      };
      return h(Tag, { color: typeMap[text]?.color }, typeMap[text]?.label);
    },
  },
  {
    title: '计费组',
    dataIndex: 'seriesName',
    align: 'center',
    width: 150,
  },
  {
    title: '套餐分类',
    dataIndex: 'className',
    align: 'center',
    width: 120,
  },
  {
    title: '套餐价格',
    dataIndex: 'packagePrice',
    align: 'center',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return `¥${text.toFixed(2)}`;
    },
  },
  {
    title: '成本价',
    dataIndex: 'packageCost',
    align: 'center',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return `¥${text.toFixed(2)}`;
    },
  },
  {
    title: '总流量',
    dataIndex: 'packageTotal',
    align: 'center',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return `${text}MB`;
    },
  },
  {
    title: '状态',
    dataIndex: 'packageStatus',
    align: 'center',
    width: 100,
    customRender: ({ text }: { text: number }) => {
      return h(
        Tag,
        { color: text === 1 ? 'success' : 'default' },
        text === 1 ? '上架' : '下架',
      );
    },
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
    align: 'center',
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
];
