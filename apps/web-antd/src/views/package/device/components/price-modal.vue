<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

import { Form, InputNumber, message, Modal } from 'ant-design-vue';

import { updateDevicePackagePrice } from '#/api/core/package';
import { useUserPermission } from '#/hooks/useUserPermission';

interface PriceModalProps {
  visible: boolean;
  record: {
    id: number;
    packageCost: number;
    packagePrice: number;
  };
}

const props = defineProps<PriceModalProps>();

const emit = defineEmits(['update:visible', 'success']);

// permission: 1,
const { isAdmin } = useUserPermission();

const loading = ref(false);
const formData = ref({
  price: 0,
  cost: 0,
});

// 监听记录变化，更新表单数据
watch(
  () => props.record,
  (val) => {
    if (val) {
      formData.value = {
        price: val.packagePrice || 0,
        cost: val.packageCost || 0,
      };
    }
  },
  { immediate: true },
);

// 计算利润
const profit = computed(() => {
  return formData.value.price - formData.value.cost;
});

// 计算利润率
const profitRate = computed(() => {
  if (formData.value.cost === 0) return 0;
  return ((profit.value / formData.value.cost) * 100).toFixed(2);
});

// 处理提交
const handleSubmit = async () => {
  try {
    loading.value = true;
    await updateDevicePackagePrice({
      packageId: props.record.id,
      packagePrice: formData.value.price,
      packageCost: formData.value.cost,
    });
    message.success('价格修改成功');
    emit('success');
    emit('update:visible', false);
  } catch (error) {
    console.error('修改价格失败:', error);
    message.error('修改价格失败');
  } finally {
    loading.value = false;
  }
};

// 关闭弹窗
const handleCancel = () => {
  emit('update:visible', false);
};
</script>

<template>
  <Modal
    title="快捷改价"
    :visible="visible"
    :confirm-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    width="500px"
  >
    <Form :model="formData" layout="vertical" class="price-form">
      <Form.Item label="套餐价格" required>
        <InputNumber
          v-model:value="formData.price"
          :min="0"
          :precision="2"
          :step="0.01"
          style="width: 100%"
          placeholder="请输入套餐价格"
        />
        <div class="original-value">
          原价：{{ props.record.packagePrice }} 元
        </div>
      </Form.Item>
      <Form.Item label="成本价" required>
        <InputNumber
          v-model:value="formData.cost"
          :min="0"
          :precision="2"
          :step="0.01"
          :disabled="!isAdmin"
          style="width: 100%"
          placeholder="请输入成本价"
        />
        <div class="original-value">
          原价：{{ props.record.packageCost }} 元
        </div>
      </Form.Item>
      <div class="profit-info">
        <div class="profit-item">
          <span class="label">利润：</span>
          <span class="value">{{ profit }} 元</span>
        </div>
        <div class="profit-item">
          <span class="label">利润率：</span>
          <span class="value">{{ profitRate }}%</span>
        </div>
      </div>
    </Form>
  </Modal>
</template>

<style lang="less" scoped>
.price-form {
  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
}

.original-value {
  font-size: 12px;
  // color: #999;
  margin-top: 4px;
}

.profit-info {
  padding: 12px 16px;
  border-radius: 4px;
  margin-top: 8px;

  .profit-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      // color: #666;
    }

    .value {
      font-weight: 500;
      // color: #333;
    }
  }
}
</style>
