<script lang="ts" setup>
import type { DevicePackageItem } from '#/api/core/package';

import { computed, h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  copyDevicePackage,
  deleteDevicePackage,
  getDevicePackageListApi,
} from '#/api/core/package';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
// import SearchToolbar from '#/components/SearchToolbar/index.vue';
import SearchToolbar from '#/hooks/useAnsheng/components/SearchToolbar.vue';
import { useTable } from '#/hooks/useTable';

import PriceModal from './components/price-modal.vue';
import {
  advancedSearchItems,
  basicSearchItems,
  createColumns,
  timeRangeSearchItems,
} from './config';
import { useDevicePackageForm } from './hooks/useDevicePackageForm';

const {
  loading,
  searchParams,
  tableData,
  pagination,
  handleSearch,
  handleReset,
  handleTableChange,
  getList,
} = useTable<
  DevicePackageItem,
  {
    name?: string;
    packageStatus?: number;
    packageType?: number;
    page: number;
    pageSize: number;
  }
>({
  api: getDevicePackageListApi,
  defaultParams: {},
});

// 使用表单 hook
const { show, renderForm } = useDevicePackageForm(() => {
  // 成功回调中刷新列表
  getList();
});

// 快捷改价弹窗
const priceModalVisible = ref(false);
const currentRecord = ref<any>(null);

// 处理新增
const handleAdd = () => {
  show();
};

// 处理编辑
const handleEdit = (record: any) => {
  show(record);
};

// 处理快捷改价
const handlePrice = (record: DevicePackageItem) => {
  currentRecord.value = {
    id: record.id,
    packagePrice: record.packagePrice,
    packageCost: record.packageCost,
  };
  priceModalVisible.value = true;
};

// 处理复制
const handleCopy = async (record: any) => {
  try {
    const res = await copyDevicePackage(record.id);
    if (res.code === 1) {
      message.success('复制成功');
      getList();
    } else {
      throw new Error(res.msg || '复制失败');
    }
  } catch (error) {
    message.error(error instanceof Error ? error.message : '复制失败');
  }
};

// 处理删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该套餐吗？',
    async onOk() {
      try {
        const res = await deleteDevicePackage(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          throw new Error(res.msg || '删除失败');
        }
      } catch (error) {
        message.error(error instanceof Error ? error.message : '删除失败');
      }
    },
  });
};

// 工具栏按钮配置
const toolbarButtons = computed(() => [
  {
    key: 'add',
    icon: h(MdiPlus),
    text: '新增套餐',
    type: 'primary',
    onClick: handleAdd,
    permission: 1, // 只有管理员可见
  },
]);

// 表格列配置
const columns = computed(() => createColumns());

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="package-device p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedSearchItems"
        :time-range-items="timeRangeSearchItems"
        :custom-buttons="toolbarButtons"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'price',
            text: '快捷改价',
            type: 'link',
            onClick: handlePrice,
          },
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
            permission: 1,
          },
          {
            key: 'copy',
            text: '复制',
            type: 'link',
            onClick: handleCopy,
            permission: 1,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
            permission: 1,
          },
        ]"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      />

      <!-- 渲染表单 -->
      <component :is="renderForm" />
    </Card>

    <PriceModal
      :visible="priceModalVisible"
      :record="currentRecord"
      @update:visible="priceModalVisible = $event"
      @success="getList"
    />
  </div>
</template>

<style lang="less" scoped>
.package-device {
  background-color: var(--background-deep);
}
</style>
