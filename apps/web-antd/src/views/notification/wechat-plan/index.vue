<script lang="ts" setup>
import type { ActionButton } from './config';

import type { WechatPlan } from '#/api/core/wechat';

import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  deleteWechatPlan,
  getWechatPlanList,
  getWechatPublicList,
  getWechatTemplateOptions,
} from '#/api/core/wechat';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

// 导入模态框组件
import EditModal from './components/edit-modal.vue';
import { advancedSearchItems, columns, searchItems } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getWechatPlanList,
  defaultParams: {},
});

// 添加选项加载状态
const optionsLoading = ref(false);

// 模态框状态
const modalVisible = ref(false);
const currentRecord = ref<Partial<WechatPlan>>();

// 编辑
const handleEdit = (record: WechatPlan) => {
  currentRecord.value = record;
  modalVisible.value = true;
};

// 删除
const handleDelete = (record: WechatPlan) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该计划吗？',
    onOk: async () => {
      try {
        const res = await deleteWechatPlan(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        message.error('删除失败');
        console.error('删除出错:', error);
      }
    },
  });
};

// 修改 initSelectOptions 函数，添加加载状态
const initSelectOptions = async () => {
  optionsLoading.value = true;
  try {
    // 获取公众号列表
    const publicRes = await getWechatPublicList();
    if (publicRes.data?.rows) {
      // 更新公众号选择器的选项
      const options = publicRes.data.rows.map((item) => ({
        label: item.name,
        value: item.id,
      }));
      if (searchItems[1]?.props) {
        searchItems[1].props.options = options;
      }
    }

    // 获取模板选项
    const templateRes = await getWechatTemplateOptions();
    if (templateRes.code === 1 && templateRes.data) {
      const options = templateRes.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));

      // 更新所有模板选择器的选项
      if (advancedSearchItems[0]?.items?.[0]?.props) {
        advancedSearchItems[0].items[0].props.options = options;
      }
      if (advancedSearchItems[0]?.items?.[1]?.props) {
        advancedSearchItems[0].items[1].props.options = options;
      }
      if (advancedSearchItems[1]?.items?.[0]?.props) {
        advancedSearchItems[1].items[0].props.options = options;
      }
      if (advancedSearchItems[1]?.items?.[1]?.props) {
        advancedSearchItems[1].items[1].props.options = options;
      }
    }
  } catch (error) {
    message.error('获取选项数据失败');
    console.error('获取选项数据出错:', error);
  } finally {
    optionsLoading.value = false;
  }
};

// 添加新增计划的处理函数
const handleAdd = () => {
  currentRecord.value = undefined;
  modalVisible.value = true;
};

// 处理保存成功
const handleSuccess = () => {
  getList();
};

// 修改 onMounted 钩子，确保先获取选项数据
onMounted(async () => {
  await initSelectOptions(); // 先获取选项数据
  getList(); // 再加载表格数据
});
</script>

<template>
  <div class="notification-wechat-plan p-2">
    <Card>
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :advanced-items="advancedSearchItems"
        :loading="loading || optionsLoading"
        :custom-buttons="[
          {
            text: '新增计划',
            type: 'primary' as const,
            icon: MdiPlus,
            onClick: handleAdd,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="
          [
            {
              key: 'edit',
              text: '编辑',
              type: 'link',
              onClick: handleEdit,
            },
            {
              key: 'delete',
              text: '删除',
              type: 'link',
              danger: true,
              onClick: handleDelete,
            },
          ] as ActionButton[]
        "
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 新增/编辑模态框 -->
    <EditModal
      v-model:visible="modalVisible"
      :record="currentRecord"
      @success="handleSuccess"
    />
  </div>
</template>

<style lang="less" scoped>
.notification-wechat-plan {
  background-color: var(--background-deep);
}
</style>
