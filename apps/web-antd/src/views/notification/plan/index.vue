<script lang="ts" setup>
import type { NotificationPlan } from '#/api/core/plan';

import { h, onMounted, ref } from 'vue';

import { MdiExport, MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getChannelListApi } from '#/api/core/order';
import {
  addNotificationPlan,
  exportNotificationPlan,
  getNotificationPlanList,
  updateNotificationPlan,
} from '#/api/core/plan';
import { getUserApiList } from '#/api/core/user';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';
import { handleFileDownload } from '#/utils/export';

import EditModal from './components/edit-modal.vue';
import {
  advancedSearchItems,
  basicSearchItems,
  columns,
  updateChannelOptions,
  updateCustomerOptions,
} from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getNotificationPlanList,
  defaultParams: {},
});

// 编辑弹窗状态
const editVisible = ref(false);
const editData = ref<NotificationPlan>();

// 加载通道数据
const loadChannelData = async () => {
  try {
    const res = await getChannelListApi();
    if (res.code === 1) {
      updateChannelOptions(res.data);
    } else {
      message.error(res.msg || '获取通道列表失败');
    }
  } catch (error) {
    console.error('获取通道列表失败:', error);
    message.error('获取通道列表失败');
  }
};

// 加载客户数据
const loadCustomerData = async () => {
  try {
    const res = await getUserApiList();
    if (res.code === 1) {
      updateCustomerOptions(res.data);
    } else {
      message.error(res.msg || '获取API客户列表失败');
    }
  } catch (error) {
    console.error('获取API客户列表失败:', error);
    message.error('获取API客户列表失败');
  }
};

// 初始化数据
const initData = async () => {
  try {
    loading.value = true;
    await Promise.all([loadChannelData(), loadCustomerData(), getList()]);
  } finally {
    loading.value = false;
  }
};

// 编辑
const handleEdit = async (record: NotificationPlan) => {
  editData.value = record;
  editVisible.value = true;
};

// 删除
const handleDelete = (record: NotificationPlan) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该通知计划吗？',
    onOk: async () => {
      try {
        // TODO: 调用API删除数据
        console.log('删除:', record);
      } catch {
        message.error('删除失败');
      }
    },
  });
};

// 切换状态
const handleToggleStatus = async (record: NotificationPlan) => {
  try {
    // TODO: 调用API更新状态
    console.log('切换状态:', record);
  } catch {
    message.error('操作失败');
  }
};

// 新增
const handleAdd = async () => {
  editData.value = undefined;
  editVisible.value = true;
};

// 导出
const handleExport = async () => {
  try {
    const res = await exportNotificationPlan(searchParams.value);
    await handleFileDownload(
      res,
      `通知计划列表_${dayjs().format('YYYY-MM-DD')}`,
    );
  } catch (error) {
    console.error('导出失败:', error);
  }
};
// 处理编辑提交
const handleEditSubmit = async (values: NotificationPlan) => {
  try {
    if (values.id) {
      // 编辑
      const params = {
        globalopen: values.globalopen,
        sms_id: values.sms_id,
        flow_valve: values.flow_valve,
        balance_valve: values.balance_valve,
        flow_time: values.flow_time,
        status: values.status,
        phone_type: values.phone_type,
      };

      // 如果是局部配置，添加通道和客户参数
      if (values.globalopen === 2) {
        if (values.api) {
          params.api = Number(values.api);
        }
        if (values.user) {
          params.user = Number(values.user);
        }
      }

      const res = await updateNotificationPlan(params);
      if (res.code === 1) {
        message.success('更新成功');
        editVisible.value = false;
        getList();
      } else {
        message.error(res.msg || '更新失败');
      }
    } else {
      // 新增
      const params = {
        globalopen: values.globalopen,
        sms_id: values.sms_id,
        flow_valve: values.flow_valve,
        balance_valve: values.balance_valve,
        flow_time: values.flow_time,
        status: values.status,
        phone_type: values.phone_type,
      };

      // 如果是局部配置，添加通道和客户参数
      if (values.globalopen === 2) {
        if (values.api) {
          params.api = Number(values.api);
        }
        if (values.user) {
          params.user = Number(values.user);
        }
      }

      const res = await addNotificationPlan(params);
      if (res.code === 1) {
        message.success('新增成功');
        editVisible.value = false;
        getList();
      } else {
        message.error(res.msg || '新增失败');
      }
    }
  } catch (error) {
    console.error(values.id ? '更新失败:' : '新增失败:', error);
    message.error(values.id ? '更新失败' : '新增失败');
  }
};

onMounted(() => {
  initData();
});
</script>

<template>
  <div class="notification-plan p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedSearchItems"
        :custom-buttons="[
          {
            text: '新增',
            type: 'primary',
            icon: h(MdiPlus),
            onClick: handleAdd,
          },
          {
            text: '导出',
            icon: h(MdiExport),
            onClick: handleExport,
          },
        ]"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 编辑弹窗 -->
    <EditModal
      v-model:visible="editVisible"
      :data="editData"
      @submit="handleEditSubmit"
    />
  </div>
</template>

<style lang="less" scoped>
.notification-plan {
  background-color: var(--background-deep);
}
</style>
