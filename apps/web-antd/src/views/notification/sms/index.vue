<script lang="ts" setup>
import type { SmsTemplate } from '#/api/core/sms';

import { onMounted, ref } from 'vue';

import { MdiExport, MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  addSmsTemplate,
  deleteSmsTemplate,
  exportSmsTemplate,
  getSmsTemplateList,
  updateSmsTemplate,
} from '#/api/core/sms';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';
import { handleFileDownload } from '#/utils/export';

import EditModal from './components/edit-modal.vue';
import { advancedSearchItems, basicSearchItems, columns } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getSmsTemplateList,
  defaultParams: {},
});

// 编辑弹窗状态
const editVisible = ref(false);
const editData = ref<SmsTemplate>();

// 处理编辑
const handleEdit = (row: SmsTemplate) => {
  editData.value = row;
  editVisible.value = true;
};

// 处理编辑提交
const handleEditSubmit = async (values: SmsTemplate) => {
  try {
    let res;
    if (values.id) {
      // 编辑
      res = await updateSmsTemplate(values);
    } else {
      // 新增
      res = await addSmsTemplate(values);
    }

    if (res.code === 1) {
      message.success(values.id ? '更新成功' : '新增成功');
      editVisible.value = false;
      getList();
    } else {
      message.error(res.msg || (values.id ? '更新失败' : '新增失败'));
    }
  } catch (error) {
    console.error(values.id ? '更新失败:' : '新增失败:', error);
    message.error(values.id ? '更新失败' : '新增失败');
  }
};

// 处理删除
const handleDelete = (row: SmsTemplate) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该模板吗？',
    async onOk() {
      try {
        const res = await deleteSmsTemplate(row.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理导出
const handleExport = async () => {
  try {
    const res = await exportSmsTemplate(searchParams.value);
    await handleFileDownload(
      res,
      `短信模板列表_${dayjs().format('YYYY-MM-DD')}`,
    );
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
};

// 处理新增
const handleAdd = () => {
  editData.value = undefined;
  editVisible.value = true;
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="notification-sms p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedSearchItems"
        :custom-buttons="[
          {
            text: '新增',
            icon: MdiPlus,
            onClick: handleAdd,
          },
          {
            text: '导出短信通知',
            icon: MdiExport,
            onClick: handleExport,
          },
        ]"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 编辑弹窗 -->
    <EditModal
      v-model:visible="editVisible"
      :data="editData"
      @submit="handleEditSubmit"
    />
  </div>
</template>

<style lang="less" scoped>
.notification-sms {
  background-color: var(--background-deep);
}
</style>
