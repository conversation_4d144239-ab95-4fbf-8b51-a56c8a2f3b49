<script lang="ts" setup>
import type { WechatTemplate } from '#/api/core/wechat';

import { h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  addTemplate,
  deleteTemplate,
  getWechatTemplateList,
  updateTemplate,
} from '#/api/core/wechat';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import EditModal from './components/edit-modal.vue';
import { columns, searchItems } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getWechatTemplateList,
  defaultParams: {},
});

// 编辑弹窗状态
const editVisible = ref(false);

// 编辑数据
const editData = ref<WechatTemplate>();

// 编辑
const handleEdit = (record: WechatTemplate) => {
  editData.value = record;
  editVisible.value = true;
};

// 删除
const handleDelete = (_record: WechatTemplate) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该模板吗？',
    onOk: async () => {
      try {
        const res = await deleteTemplate(_record.id);
        if (res.code === 1) {
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch {
        message.error('删除失败');
      }
    },
  });
};

// 切换状态
const handleToggleStatus = async (_record: WechatTemplate) => {
  try {
    // TODO: 实现状态切换逻辑
    message.error('功能开发中');
  } catch {
    message.error('操作失败');
  }
};

// 新增
const handleAdd = () => {
  editData.value = undefined;
  editVisible.value = true;
};

// 处理编辑提交
const handleEditSubmit = async (values: any) => {
  try {
    const res = values.id
      ? await updateTemplate(values)
      : await addTemplate(values);

    if (res.code === 1) {
      message.success(values.id ? '编辑成功' : '新增成功');
      editVisible.value = false;
      getList();
    } else {
      message.error(res.msg || (values.id ? '编辑失败' : '新增失败'));
    }
  } catch (error) {
    console.error(values.id ? '编辑失败:' : '新增失败:', error);
    message.error(values.id ? '编辑失败' : '新增失败');
  }
};

// 导出
const handleExport = () => {
  // TODO: 实现导出逻辑
};

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="notification-wechat-template p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        :custom-buttons="[
          {
            text: '新增模板',
            type: 'primary',
            icon: h(MdiPlus),
            onClick: handleAdd,
          },
          // {
          //   text: '导出',
          //   icon: h(MdiExport),
          //   onClick: handleExport,
          // },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <!-- 编辑弹窗 -->
      <EditModal
        v-model:visible="editVisible"
        :data="editData"
        @submit="handleEditSubmit"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.notification-wechat-template {
  background-color: var(--background-deep);
}
</style>
