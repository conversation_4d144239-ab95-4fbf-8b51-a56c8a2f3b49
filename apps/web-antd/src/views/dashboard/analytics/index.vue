<script lang="ts" setup>
import type { AnalysisOverviewItem } from '@vben/common-ui';
import type { TabOption } from '@vben/types';

import type { OverviewData } from '#/api/core/dashboard';

import { computed, h, onMounted, ref } from 'vue';

import {
  AnalysisChartCard,
  AnalysisChartsTabs,
  AnalysisOverview,
} from '@vben/common-ui';
import {
  MdiAccountGroup,
  MdiCurrencyUsd,
  MdiFileDocument,
  MdiSimOutline,
} from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import {
  getAnnouncementList,
  getControlAnnouncementList,
  updateControlAnnouncementStatus,
} from '#/api/core/announcement';
import { getOverviewDataApi } from '#/api/core/dashboard';
import { checkSystemUpdateApi, updateBoot } from '#/api/core/system';
import { useUserPermission } from '#/hooks/useUserPermission';

import analyticsAccountInfo from './analytics-account-info.vue';
import AnalyticsOperatorStatus from './analytics-operator-status.vue';
import AnalyticsOperatorUsage from './analytics-operator-usage.vue';
import AnalyticsQuickEntry from './analytics-quick-entry.vue';
import AnalyticsServerStatus from './analytics-server-status.vue';

interface AnnouncementItem {
  id: number;
  title: string;
  detail: string;
  type: number;
  status: null | number;
  channel_id: null | number;
  creation_time: string;
  update_time: string;
}

type _AnnouncementListParams = {
  page?: number;
  pageSize?: number;
  type: number;
};

// 获取用户权限相关的功能
const { isAdmin } = useUserPermission();
const overviewData = ref<OverviewData>();
const currentAnnouncement = ref<AnnouncementItem | null>(null);

// 获取数据
const loadData = async () => {
  try {
    const res = await getOverviewDataApi();
    if (res.code === 1) {
      overviewData.value = res.data;
    }
  } catch (error) {
    console.error('获取概览数据失败:', error);
  }
};

// 获取公告
const loadAnnouncements = async () => {
  try {
    const res = await getAnnouncementList({
      type: 1,
      page: 1,
      pageSize: 1,
    });
    if (res.code === 1 && res.data.rows?.[0]) {
      // 如果有新公告，显示最新的一条
      const rawAnnouncement = res.data.rows[0];
      const announcement: AnnouncementItem = {
        id: rawAnnouncement.id ?? 0,
        title: rawAnnouncement.title ?? '系统公告',
        detail: rawAnnouncement.detail ?? '',
        type: rawAnnouncement.type ?? 1,
        status: rawAnnouncement.status ?? null,
        channel_id: rawAnnouncement.channel_id ?? null,
        creation_time:
          rawAnnouncement.creation_time ?? new Date().toISOString(),
        update_time: rawAnnouncement.update_time ?? new Date().toISOString(),
      };
      currentAnnouncement.value = announcement;

      Modal.info({
        title: announcement.title,
        width: 640,
        class: 'system-announcement-modal',
        content: h('div', {
          class: 'whitespace-pre-wrap',
          innerHTML: announcement.detail,
        }),
        okText: '我知道了',
        maskClosable: true,
        onOk: () => {
          currentAnnouncement.value = null;
        },
      });
    }
  } catch (error) {
    console.error('获取公告失败:', error);
  }
};

// 获取控制端公告
const loadControlAnnouncement = async () => {
  try {
    const res = await getControlAnnouncementList({
      page: 1,
      pageSize: 1,
    });

    if (res.code === 1 && res.data.rows?.[0]) {
      // 如果有新公告，显示最新的一条
      const rawAnnouncement = res.data.rows[0];

      Modal.info({
        title: rawAnnouncement.bulletin__title,
        width: 640,
        class: 'system-announcement-modal',
        content: h('div', {
          class: 'whitespace-pre-wrap',
          innerHTML: rawAnnouncement.bulletin__content,
        }),
        okText: '我知道了',
        maskClosable: true,
        onOk: async () => {
          await updateControlAnnouncementStatus({
            id: rawAnnouncement.id,
            op_type: 1,
          });
        },
      });
    }
  } catch (error) {
    console.error('获取控制端公告失败:', error);
  }
};

// 检查系统更新
const checkSystemUpdate = async () => {
  try {
    const res = await checkSystemUpdateApi();
    console.log('checkSystemUpdateApi', res);
    if (res.code === -1) {
      // -1 代表后台版本不一致
      if (!res.data) {
        message.error('获取更新信息失败');
        return;
      }

      Modal.confirm({
        title: '发现新版本',
        width: 500,
        class: 'system-update-modal',
        content: h('div', { class: 'space-y-2' }, [
          h('div', { class: 'text-base' }, [
            '当前版本：',
            h('span', { class: 'font-medium' }, res.data.thisVersion || '未知'),
          ]),
          h('div', { class: 'text-base' }, [
            '更新时间：',
            h('span', { class: 'font-medium' }, res.data.updateTime || '未知'),
          ]),
          h('div', { class: 'mt-4' }, [
            '更新内容：',
            h('div', {
              class: 'mt-2 p-3 bg-gray-50 dark:bg-black/10 rounded',
              innerHTML: res.data.updateMsg || '暂无更新说明',
            }),
          ]),
        ]),
        okText: '立即更新',
        cancelText: '稍后更新',
        async onOk() {
          try {
            const updateRes = await updateBoot();
            if (updateRes.code === 1) {
              message.success('系统更新成功，请刷新页面');
              setTimeout(() => {
                window.location.reload();
              }, 1500);
            } else {
              message.error(updateRes.msg || '更新失败，请稍后重试');
            }
          } catch (error) {
            message.error('更新失败，请稍后重试');
            console.error('系统更新失败:', error);
          }
        },
      });
    } else if (res.code === 1) {
      message.success(res.msg || '当前版本已是最新');
      // 1 代表后台版本一致
      // 版本一致，不需要更新
    }
  } catch (error) {
    console.error('检查系统更新失败:', error);
    message.error('检查更新失败，请稍后重试');
  }
};

// 计算展示数据
const overviewItems = computed<AnalysisOverviewItem[]>(() => {
  if (!overviewData.value) return [];

  return [
    {
      icon: MdiCurrencyUsd,
      title: '财务统计',
      value: overviewData.value.todaySIncome,
      valueTitle: '今日收入',
      totalValue: overviewData.value.totalIncome,
      totalTitle: '总收入',
      totalTitle2: '总发放',
      totalValue2: overviewData.value.totalDistribution,
      prefix: '￥',
      totalPrefix: '￥',
      total2Prefix: '￥',
    },
    {
      icon: MdiSimOutline,
      title: 'SIM卡统计',
      value: overviewData.value.numberOfNetworks,
      valueTitle: '在网数',
      totalValue: overviewData.value.totalNumberOfCards,
      totalTitle: '总数量',
      totalTitle2: '已出库',
      totalValue2: overviewData.value.outbound,
    },
    {
      icon: MdiAccountGroup,
      title: '客户统计',
      value: overviewData.value.ordinaryCustomers,
      valueTitle: '普通代理',
      totalValue: overviewData.value.totalNumberOfCustomers,
      totalTitle: '总客户',
      totalTitle2: 'API客户',
      totalValue2: overviewData.value.capableCustomers,
    },
    {
      icon: MdiFileDocument,
      title: '订单统计',
      value: overviewData.value.cardPreStorage,
      valueTitle: '预存总订单',
      totalValue: overviewData.value.totalNumberOfPackageOrders,
      totalTitle: '总订单',
      totalTitle2: '商品总订单',
      totalValue2: overviewData.value.totalNumberOfProductOrders,
    },
  ];
});

onMounted(() => {
  loadData();

  if (isAdmin.value) {
    checkSystemUpdate();
    loadControlAnnouncement();
  } else {
    loadAnnouncements();
  }
});

const chartTabs: TabOption[] = [
  {
    label: '中国移动',
    value: 'mobile',
  },
  {
    label: '中国电信',
    value: 'telecom',
  },
  {
    label: '中国联通',
    value: 'unicom',
  },
  {
    label: '中国广电',
    value: 'radio',
  },
];
</script>

<template>
  <div class="p-2">
    <AnalysisOverview :items="overviewItems" />
    <div class="mt-2 w-full md:flex">
      <AnalysisChartsTabs
        :tabs="chartTabs"
        class="mt-2 md:mr-2 md:mt-0 md:w-2/3"
      >
        <template #mobile>
          <AnalyticsOperatorUsage v-domain-permission="1" operator="mobile" />
        </template>
        <template #telecom>
          <AnalyticsOperatorUsage v-domain-permission="1" operator="telecom" />
        </template>
        <template #unicom>
          <AnalyticsOperatorUsage v-domain-permission="1" operator="unicom" />
        </template>
        <template #radio>
          <AnalyticsOperatorUsage v-domain-permission="1" operator="radio" />
        </template>
      </AnalysisChartsTabs>

      <!-- <AnalysisChartCard
        class="mt-5 md:mr-4 md:mt-0 md:w-1/3"
        title="服务器负载"
      >
        <AnalyticsServerLoad />
      </AnalysisChartCard> -->

      <AnalysisChartCard class="mt-2 md:mt-0 md:w-1/3" title="快捷方式">
        <AnalyticsQuickEntry />
      </AnalysisChartCard>
    </div>
    <!-- <div class="mt-5 w-full md:flex">
      <AnalysisChartCard
        class="mt-5 md:mr-4 md:mt-0 md:w-2/3"
        title="近七日收款"
      >
        <AnalyticsRecentPayment />
      </AnalysisChartCard>
      <AnalysisChartCard class="mt-5 md:mt-0 md:w-1/3" title="SIM卡分布">
        <AnalyticsVisitsSource />
      </AnalysisChartCard>
    </div> -->
    <div class="mt-2 w-full md:flex">
      <!-- <AnalysisChartCard
        class="mt-5 md:mr-4 md:mt-0 md:w-1/2"
        title="服务器负载"
      > -->
      <div class="mt-5 md:mr-2 md:mt-0 md:w-1/2">
        <analyticsAccountInfo />
      </div>
      <!-- </AnalysisChartCard> -->
      <!-- <AnalysisChartCard class="mt-5 md:mt-0 md:w-1/2" title="年度订单分析"> -->
      <!-- <AnalyticsYearlyOrders /> -->
      <div class="mt-5 md:mt-0 md:w-1/2">
        <AnalyticsServerStatus />
      </div>
      <!-- </AnalysisChartCard> -->
    </div>
    <div class="mt-2">
      <AnalyticsOperatorStatus />
    </div>
  </div>
</template>
