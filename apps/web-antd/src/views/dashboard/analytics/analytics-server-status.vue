<script lang="ts" setup>
import {
  computed,
  onActivated,
  onDeactivated,
  onMounted,
  onUnmounted,
  ref,
} from 'vue';

import { MdiCpu, MdiHarddisk, MdiMemory, MdiServer } from '@vben/icons';
import { usePreferences } from '@vben/preferences';

import { Card, Progress } from 'ant-design-vue';

import { getServerStatusApi } from '#/api/core/monitor';

const { isDark } = usePreferences();
const loading = ref(true);
const worker = ref<null | Worker>(null);

interface ServerData {
  cpu: number;
  memory: number;
  disk: number;
}

const serverData = ref<ServerData>();

const loadData = async () => {
  try {
    const res = await getServerStatusApi();

    if (res.code === 1 && res.data) {
      const { disk_total, disk_free, memory_percent, cpu_total } = res.data;

      serverData.value = {
        cpu: Number((cpu_total / 2).toFixed(1)), // CPU使用率 = 总负载/核心数
        memory: Number(memory_percent.toFixed(1)),
        disk: Number(
          (((disk_total - disk_free) / disk_total) * 100).toFixed(1),
        ),
      };
    }
  } catch (error) {
    console.error('获取服务器状态失败:', error);
  } finally {
    loading.value = false;
  }
};

// 初始化 Web Worker
const initWorker = () => {
  if (typeof Worker !== 'undefined') {
    worker.value = new Worker(
      new URL('#/workers/serverMonitor.worker.ts', import.meta.url),
      { type: 'module' },
    );

    // 开始轮询
    worker.value.addEventListener('message', async (event) => {
      if (event.data.type === 'fetch') {
        await loadData();
      }
    });

    worker.value.postMessage({ type: 'start' });
    loading.value = false;
  }
};

// 清理 Worker
const cleanupWorker = () => {
  if (worker.value) {
    worker.value.postMessage({ type: 'stop' });
    worker.value.terminate();
    worker.value = null;
  }
};

const themeColors = computed(() => ({
  success: isDark.value ? '#49aa19' : '#52c41a',
  warning: isDark.value ? '#d89614' : '#faad14',
  error: isDark.value ? '#a61d24' : '#ff4d4f',
}));

const statusItems = computed(() => [
  {
    icon: MdiCpu,
    label: 'CPU使用率',
    value: serverData.value?.cpu || 0,
    color: getStatusColor(serverData.value?.cpu || 0),
    suffix: '%',
  },
  {
    icon: MdiMemory,
    label: '内存使用率',
    value: serverData.value?.memory || 0,
    color: getStatusColor(serverData.value?.memory || 0),
    suffix: '%',
  },
  {
    icon: MdiHarddisk,
    label: '磁盘使用率',
    value: serverData.value?.disk || 0,
    color: getStatusColor(serverData.value?.disk || 0),
    suffix: '%',
  },
]);

function getStatusColor(value: number) {
  if (value >= 80) return themeColors.value.error;
  if (value >= 60) return themeColors.value.warning;
  return themeColors.value.success;
}

onMounted(() => {
  initWorker();
});

// 组件激活时
onActivated(() => {
  console.log('服务器状态组件激活');
  // 重新初始化Worker，恢复轮询
  loadData(); // 先加载一次最新数据
  initWorker(); // 然后启动worker进行轮询
});
// 组件停用时
onDeactivated(() => {
  console.log('服务器状态组件停用');
  // isComponentActive.value = false;
  cleanupWorker();
});

onUnmounted(() => {
  console.log('unmounted');
  cleanupWorker();
});
</script>

<template>
  <Card title="服务器状态" :loading="loading">
    <template #extra>
      <MdiServer class="text-primary h-5 w-5" />
    </template>
    <div class="flex flex-col gap-4">
      <div
        v-for="item in statusItems"
        :key="item.label"
        class="status-card"
        :class="{ 'is-dark': isDark }"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <component
              :is="item.icon"
              class="h-5 w-5"
              :style="{ color: item.color }"
            />
            <span class="text-sm font-medium">{{ item.label }}</span>
          </div>
          <span class="text-lg font-semibold" :style="{ color: item.color }">
            {{ item.value }}{{ item.suffix }}
          </span>
        </div>
        <div class="mt-2">
          <Progress
            :percent="item.value"
            :stroke-color="item.color"
            :track-color="
              isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)'
            "
            :show-info="false"
            :stroke-width="8"
            :size="[400, 8]"
          />
        </div>
      </div>
    </div>
  </Card>
</template>

<style lang="scss" scoped>
.status-card {
  @apply rounded-lg bg-white p-4 shadow-sm transition-all hover:shadow-md;

  &.is-dark {
    @apply bg-[#141414];
  }
}
</style>
