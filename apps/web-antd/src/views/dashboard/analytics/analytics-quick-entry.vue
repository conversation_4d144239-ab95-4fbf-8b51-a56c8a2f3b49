<script lang="ts" setup>
import type { ShortcutItem } from './types';

import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';

import {
  MdiAccountCircle,
  MdiAccountConvert,
  MdiAccountGroup,
  MdiAccountMultiplePlus,
  MdiCardBulleted,
  MdiCash,
  MdiCashMultiple,
  MdiCog,
  MdiCogBox,
  MdiCreditCard,
  MdiCreditCardPlus,
  MdiCurrencyUsd,
  MdiDevices,
  MdiFileDocument,
  MdiPackageVariant,
  MdiShieldAccount,
  MdiStore,
  MdiWallet,
} from '@vben/icons';

import { message } from 'ant-design-vue';

// 引入弹窗组件
import { APIBalanceBuyModal } from '#/components';
import { RechargeMode } from '#/components/BalanceRechargeModal';
import { useUserPermission } from '#/hooks/useUserPermission';
import UserCreate from '#/views/user/list/components/user-create.vue';

const modeType = ref(RechargeMode.Control);

// 对快捷方式进行分类
const categories = [
  {
    name: '客户管理',
    icon: MdiAccountCircle,
    items: ['新增客户', '客户管理', '角色管理', '用户管理'],
  },
  {
    name: '业务管理',
    icon: MdiCardBulleted,
    items: [
      '购买短信',
      '套餐管理',
      '新增卡片',
      '卡片管理',
      '余额充值',
      '订单管理',
      '商店管理',
    ],
  },
  {
    name: '财务管理',
    icon: MdiCurrencyUsd,
    items: ['充值记录', '财务管理', '支付配置'],
  },
  {
    name: '系统管理',
    icon: MdiCogBox,
    items: ['系统设置', '设备管理'],
  },
];

const router = useRouter();

// 短信购买弹窗可见性
const smsModalVisible = ref(false);

// API余额购买弹窗可见性
const apiBalanceBuyModalVisible = ref(false);

// 余额充值弹窗可见性
const balanceRechargeModalVisible = ref(false);

const { isAdmin, userId } = useUserPermission();

// 显示短信购买弹窗
const showSmsModal = async () => {
  smsModalVisible.value = true;
};

// 显示余额充值弹窗
const showBalanceRechargeModal = (mode) => {
  balanceRechargeModalVisible.value = true;
  modeType.value = mode;
};

// 显示API余额购买弹窗
const showAPIBalanceBuyModal = () => {
  apiBalanceBuyModalVisible.value = true;
};

// // 处理购买成功
// const handleBuySuccess = (packageInfo: SmsPackage) => {
//   message.success(`成功购买"${packageInfo.name}"短信套餐！`);
// };

// 处理余额充值成功
// const handleRechargeSuccess = (packageInfo: any) => {
//   console.log(packageInfo, 'packageInfo');
//   message.success(
//     `充值成功，已充值 ${packageInfo.collectionBalance} 元，赠送 ${packageInfo.giftBalance} 元`,
//   );
// };

// 处理API余额充值成功
const handleApiRechargeSuccess = (packageInfo: any) => {
  message.success(
    `充值成功，已充值 ${packageInfo.amount} 元，赠送 ${packageInfo.giveAmount} 元`,
  );
};

// 在types.ts中添加category字段
interface ShortcutItemWithCategory extends ShortcutItem {
  category: string;
}

const createModalVisible = ref(false);

const handleGoto = (path: any) => {
  router.push(path);
  // message.success('排列中🐛');
};

const allShortcutItems = computed<ShortcutItemWithCategory[]>(() => [
  // 客户管理类
  {
    icon: MdiAccountMultiplePlus,
    label: '新增客户',
    path: '/user/list',
    // action: (path: string) => handleGoto(path),
    action: () => (createModalVisible.value = true),
    category: '客户管理',
  },
  {
    icon: MdiAccountConvert,
    label: '客户管理',
    path: '/user/list',
    action: (path: string) => handleGoto(path),
    category: '客户管理',
  },
  {
    icon: MdiShieldAccount,
    label: '角色管理',
    path: '/user/role',
    action: (path: string) => handleGoto(path),
    category: '客户管理',
  },
  {
    icon: MdiAccountGroup,
    label: '用户管理',
    path: '/user/list',
    action: (path: string) => handleGoto(path),
    category: '客户管理',
  },

  // 业务管理类
  // {
  //   icon: MdiMessageText,
  //   label: '购买短信',
  //   path: '#',
  //   action: () => showSmsModal(),
  //   category: '业务管理',
  // },
  {
    icon: MdiPackageVariant,
    label: '套餐管理',
    path: '/package/list',
    action: (path: string) => handleGoto(path),
    category: '业务管理',
  },
  {
    icon: MdiCreditCardPlus,
    label: '新增卡片',
    path: '/sim/list',
    action: (path: string) => handleGoto(path),
    category: '业务管理',
  },
  {
    icon: MdiCardBulleted,
    label: '卡片管理',
    path: '/sim/list',
    action: (path: string) => handleGoto(path),
    category: '业务管理',
  },
  // {
  //   icon: MdiWallet,
  //   label: '余额充值',
  //   path: '#',
  //   action: () => showBalanceRechargeModal(RechargeMode.Control),
  //   category: '业务管理',
  // },
  {
    icon: MdiWallet,
    label: 'API预存充值',
    path: '#',
    // action: () => showBalanceRechargeModal(RechargeMode.Agent),
    action: () => showAPIBalanceBuyModal(),
    category: '业务管理',
  },
  {
    icon: MdiFileDocument,
    label: '订单管理',
    path: '/order/list',
    action: (path: string) => handleGoto(path),
    category: '业务管理',
  },
  {
    icon: MdiStore,
    label: '商店管理',
    path: '/shop/list',
    action: (path: string) => handleGoto(path),
    category: '业务管理',
  },

  // 财务管理类
  {
    icon: MdiCashMultiple,
    label: '充值记录',
    path: '/finance/recharge',
    action: (path: string) => handleGoto(path),
    category: '财务管理',
  },
  {
    icon: MdiCash,
    label: '财务管理',
    path: '/finance/manage',
    action: (path: string) => handleGoto(path),
    category: '财务管理',
  },
  {
    icon: MdiCreditCard,
    label: '支付配置',
    path: '/finance/payment',
    action: (path: string) => handleGoto(path),
    category: '财务管理',
  },

  // 系统管理类
  {
    icon: MdiCog,
    label: '系统设置',
    path: '/system/settings',
    action: (path: string) => handleGoto(path),
    category: '系统管理',
  },
  {
    icon: MdiDevices,
    label: '设备管理',
    path: '/device/list',
    action: (path: string) => handleGoto(path),
    category: '系统管理',
  },
]);

// 根据分类获取快捷方式项
const getItemsByCategory = (category: string) => {
  return allShortcutItems.value.filter((item) => item.category === category);
};

const handleClick = (item: ShortcutItem) => {
  item.action(item.path);
};

// 处理创建成功
const handleCreateSuccess = () => {
  createModalVisible.value = false;
};
</script>

<template>
  <div class="shortcut-container">
    <div
      v-for="category in categories"
      :key="category.name"
      class="category-section"
    >
      <div class="category-header">
        <component :is="category.icon" class="category-icon" />
        <div class="category-title">{{ category.name }}</div>
      </div>
      <div class="shortcut-list">
        <div
          v-for="item in getItemsByCategory(category.name)"
          :key="item.label"
          class="shortcut-item"
          @click="handleClick(item)"
        >
          <div class="icon-wrapper">
            <component :is="item.icon" />
          </div>
          <span class="label">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 使用独立的短信购买弹窗组件 -->
  <!-- <SmsBuyModal v-model:visible="smsModalVisible" @success="handleBuySuccess" /> -->

  <!-- 使用API余额购买弹窗组件 -->
  <APIBalanceBuyModal
    v-model:visible="apiBalanceBuyModalVisible"
    @success="(info) => handleApiRechargeSuccess(info)"
  />

  <!-- 新增用户弹窗 -->
  <!-- 新增用户弹窗 -->
  <UserCreate
    :visible="createModalVisible"
    :default-values="isAdmin ? undefined : { userId }"
    @success="handleCreateSuccess"
    @cancel="() => (createModalVisible = false)"
  />

  <!-- 使用余额充值弹窗组件 -->
  <!-- <BalanceRechargeModal
    v-model:visible="balanceRechargeModalVisible"
    :mode="modeType"
    @success="(info) => handleRechargeSuccess(info)"
  /> -->
</template>

<style lang="scss" scoped>
// 响应式调整
@media (max-width: 640px) {
  .shortcut-list {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
  }

  .shortcut-item {
    @apply p-1.5;

    .icon-wrapper {
      @apply mb-1 p-1.5 text-lg;
    }

    .label {
      @apply text-[10px];
    }
  }
}

.shortcut-container {
  @apply flex flex-col gap-4 overflow-y-auto;

  max-height: 340px;
}

.category-section {
  @apply flex flex-col gap-2 rounded-lg p-3;
  @apply border shadow-sm;
}

.category-header {
  @apply mb-2 flex items-center gap-2;
}

.category-icon {
  @apply text-primary-500 text-lg dark:text-gray-300;
}

.category-title {
  @apply px-1 text-sm font-medium text-gray-700 dark:text-gray-300;
}

.shortcut-list {
  @apply grid gap-2;

  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
}

.shortcut-item {
  @apply flex cursor-pointer flex-col items-center justify-center rounded-lg p-2 transition-all;
  @apply shadow-sm hover:shadow-md;
  @apply border;
  @apply hover:border-primary-500;
  @apply hover:text-primary-500;
  @apply hover:translate-y-[-2px];

  .icon-wrapper {
    @apply mb-1.5 rounded-full p-2 text-xl text-gray-600 dark:text-gray-300;
    @apply transition-colors;
  }

  // &:hover .icon-wrapper {
  //   @apply text-primary-500;
  // }

  .label {
    @apply text-xs font-medium text-gray-700 dark:text-gray-300;
    @apply w-full truncate px-1 text-center;
  }

  // &:hover .label {
  //   @apply text-primary-500;
  // }
}
</style>
