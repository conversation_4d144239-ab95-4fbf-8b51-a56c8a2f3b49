<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { usePreferences } from '@vben/preferences';

import { Progress } from 'ant-design-vue';

import { getCardStatusApi } from '#/api/core/dashboard';

import telecomLogo from '/static/dianxin.png';
import broadcastLogo from '/static/guangdian.png';
import unicomLogo from '/static/liantong.png';
import mobileLogo from '/static/yidong.png';

interface OperatorStatus {
  activated: number;
  inactivated: number;
  suspended: number;
  total: number;
}

interface OperatorData {
  mobile: OperatorStatus;
  telecom: OperatorStatus;
  unicom: OperatorStatus;
  broadcast: OperatorStatus;
}

const { isDark } = usePreferences();
const loading = ref(true);
const statusData = ref<OperatorData>({
  mobile: { activated: 0, inactivated: 0, suspended: 0, total: 0 },
  telecom: { activated: 0, inactivated: 0, suspended: 0, total: 0 },
  unicom: { activated: 0, inactivated: 0, suspended: 0, total: 0 },
  broadcast: { activated: 0, inactivated: 0, suspended: 0, total: 0 },
});

const operatorConfig = {
  mobile: {
    name: '中国移动',
    logo: mobileLogo,
    color: '#3366CC',
    type: 3,
  },
  telecom: {
    name: '中国电信',
    logo: telecomLogo,
    color: '#2F9DF4',
    type: 1,
  },
  unicom: {
    name: '中国联通',
    logo: unicomLogo,
    color: '#FF6B6B',
    type: 2,
  },
  broadcast: {
    name: '中国广电',
    logo: broadcastLogo,
    color: '#4CAF50',
    type: 4,
  },
};

const loadOperatorData = async () => {
  try {
    loading.value = true;
    const promises = Object.entries(operatorConfig).map(
      async ([key, config]) => {
        const res = await getCardStatusApi(config.type);
        if (res.code === 1 && res.data) {
          Object.assign(statusData.value[key as keyof OperatorData], {
            activated: Number(res.data.already_activate_count),
            inactivated: Number(res.data.not_activate_count),
            suspended: Number(res.data.shutdown_count),
            total: Number(res.data.card_count),
          });
        }
      },
    );

    await Promise.all(promises);
  } catch (error) {
    console.error('Failed to fetch operator status:', error);
  } finally {
    loading.value = false;
  }
};

const getActivationRate = (data: OperatorStatus) => {
  if (!data || data.total === 0) return 0;
  const rate = (data.activated / data.total) * 100;
  return Math.round(rate);
};

// 计算每个运营商的激活率
const activationRates = computed(() => {
  const rates: Record<keyof OperatorData, number> = {
    mobile: 0,
    telecom: 0,
    unicom: 0,
    broadcast: 0,
  };

  Object.entries(statusData.value).forEach(([key, data]) => {
    rates[key as keyof OperatorData] = getActivationRate(data);
  });

  return rates;
});

onMounted(() => {
  loadOperatorData();
});
</script>

<template>
  <div class="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-4">
    <div
      v-for="(data, operator) in statusData"
      :key="operator"
      class="operator-card"
      :class="{ 'is-dark': isDark }"
    >
      <!-- 头部区域 -->
      <div class="card-header">
        <div class="operator-info">
          <img
            :src="operatorConfig[operator].logo"
            :alt="operatorConfig[operator].name"
            class="operator-logo"
          />
          <div class="operator-title">
            <div class="operator-name">{{ operatorConfig[operator].name }}</div>
            <div class="activation-info">
              <Progress
                :percent="
                  loading ? 0 : ((data.activated / data.total) * 100).toFixed(2)
                "
                :stroke-color="operatorConfig[operator].color"
                :show-info="false"
                :stroke-width="4"
                class="activation-progress"
              />
              <span class="activation-text">{{
                loading
                  ? '加载中'
                  : data.total
                    ? `${((data.activated / data.total) * 100).toFixed(2)}%`
                    : '0%'
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 状态数据 -->
      <div class="status-list">
        <div class="status-item">
          <div class="status-label">已激活</div>
          <div
            class="status-value"
            :style="{ color: operatorConfig[operator].color }"
          >
            {{ loading ? '-' : data.activated }}
          </div>
        </div>
        <div class="status-item">
          <div class="status-label">未激活</div>
          <div class="status-value text-[#faad14]">
            {{ loading ? '-' : data.inactivated }}
          </div>
        </div>
        <div class="status-item">
          <div class="status-label">已停机</div>
          <div class="status-value text-[#ff4d4f]">
            {{ loading ? '-' : data.suspended }}
          </div>
        </div>
      </div>

      <!-- 底部总计 -->
      <div class="card-footer">
        <span class="total-label">设备总量</span>
        <span class="total-value">{{ loading ? '-' : data.total }}</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* 添加响应式调整 */
@media (max-width: 640px) {
  .operator-info {
    gap: 8px;
  }

  .operator-logo {
    width: 24px;
    height: 24px;
  }

  .operator-name {
    font-size: 14px;
  }

  .activation-progress {
    min-width: 40px;
  }

  .activation-text {
    min-width: 32px;
    font-size: 11px;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .operator-info {
    gap: 12px;
  }

  .operator-logo {
    width: 28px;
    height: 28px;
  }

  .operator-name {
    font-size: 15px;
  }

  .activation-progress {
    min-width: 50px;
  }

  .activation-text {
    min-width: 36px;
    font-size: 12px;
  }
}

.operator-card {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: hsl(var(--background));
  border: 1px solid #f0f0f0;
  border-radius: calc(var(--radius) + 4px);
  transition: all 0.3s ease;

  &.is-dark {
    background: hsl(var(--background));
    border-color: #303030;
  }

  &:hover {
    box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.operator-info {
  display: flex;
  gap: 12px;
  align-items: center;
  width: 100%;
}

.operator-logo {
  width: 32px;
  height: 32px;
  padding: 4px;
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;

  .is-dark & {
    background: hsl(var(--background));
    border-color: #303030;
  }
}

.operator-name {
  font-size: 16px;
  font-weight: 500;
  color: v-bind("isDark ? '#fff' : '#000'");
}

.operator-title {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 8px;
}

.activation-info {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
}

.activation-progress {
  flex: 1;
  min-width: 60px;
}

.activation-text {
  min-width: 40px;
  font-size: 12px;
  font-weight: 500;
  color: v-bind("isDark ? 'rgba(255, 255, 255, 0.65)' : 'rgba(0, 0, 0, 0.45)'");
  text-align: right;
}

.status-list {
  display: grid;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: v-bind(
    "isDark ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.02)'"
  );
  border-radius: 6px;
}

.status-label {
  font-size: 14px;
  color: v-bind("isDark ? 'rgba(255, 255, 255, 0.65)' : 'rgba(0, 0, 0, 0.45)'");
}

.status-value {
  font-size: 15px;
  font-weight: 500;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16px;
  border-top: 1px solid v-bind("isDark ? '#303030' : '#f0f0f0'");
}

.total-label {
  font-size: 14px;
  color: v-bind("isDark ? 'rgba(255, 255, 255, 0.45)' : 'rgba(0, 0, 0, 0.45)'");
}

.total-value {
  font-size: 16px;
  font-weight: 600;
  color: v-bind("isDark ? '#fff' : '#000'");
}
</style>
