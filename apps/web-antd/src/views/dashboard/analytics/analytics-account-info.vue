<script lang="ts" setup>
import { computed, ref } from 'vue';

import { <PERSON>di<PERSON><PERSON>nt<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>di<PERSON>ye<PERSON>ff, MdiLink } from '@vben/icons';
import { useUserStore } from '@vben/stores';

import { useClipboard } from '@vueuse/core';
import {
  Button,
  Card,
  Descriptions,
  Divider,
  message,
  Modal,
  Tag,
  Typography,
} from 'ant-design-vue';

const { Text } = Typography;
const { copy } = useClipboard();
const userStore = useUserStore();
const showSecret = ref(false);
const showLinksModal = ref(false);

// 获取当前域名
const currentDomain = window.location.origin;

const userInfo = computed(
  () =>
    userStore.userInfo || {
      userAccount: '-',
      userName: '',
      authority: 2,
      userStatus: 1,
      userRname: 2,
      userBalance: 0,
      userWage: 0,
      dockingAccounts: '-',
      userKey: '-',
      opennessApi: 1,
    },
);

// 请求域名列表
const apiEndpoints = [
  {
    title: '充值端1',
    path: '/app',
  },
  {
    title: '充值端2',
    path: '/app_sw',
  },
  {
    title: '充值端3',
    path: '/appAn',
  },
  {
    title: '设备端1',
    path: '/deviceAn',
  },
  {
    title: '商城端1',
    path: '/app/mall',
    query: (userInfo) => `?invitation_code=${userInfo.invitationCode || ''}`,
  },
];

// 常用链接列表
const commonLinks = [
  {
    title: 'API文档中心',
    url: 'https://doc.apipost.net/docs/detail/2aa0c24e3464000?target_id=4976a1d',
    description: '查看完整的API接口文档',
  },
  // {
  //   title: '开发者中心',
  //   url: `${currentDomain}/developer`,
  //   description: '获取开发资源和技术支持',
  // },
  // {
  //   title: '调试工具',
  //   url: `${currentDomain}/tools/debug`,
  //   description: 'API接口调试工具',
  // },
];

const handleCopy = async (text: string, type: string) => {
  try {
    await copy(text);
    message.success(`${type}已复制到剪贴板`);
  } catch {
    message.error('复制失败');
  }
};

const handleLinkClick = (url: string) => {
  window.open(url, '_blank');
};

const toggleSecret = () => {
  showSecret.value = !showSecret.value;
};

const openLinksModal = () => {
  showLinksModal.value = true;
};

// 获取完整的API地址
const getFullApiUrl = (endpoint: (typeof apiEndpoints)[0]) => {
  const query =
    typeof endpoint.query === 'function'
      ? endpoint.query(userInfo.value)
      : endpoint.query || '';
  return `${currentDomain}${endpoint.path}${query}`;
};
</script>

<template>
  <Card title="账号信息" :bordered="true" class="h-full">
    <div class="flex items-start justify-between">
      <div class="flex-1">
        <Descriptions :column="1" class="mb-4">
          <Descriptions.Item label="账号" class="!py-2">
            <div class="flex items-center">
              <span>{{ userInfo.userAccount }}</span>
              <Button
                type="link"
                size="small"
                class="ml-2"
                @click="handleCopy(userInfo.userAccount, '账号')"
              >
                <MdiContentCopy />
              </Button>
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="姓名" class="!py-2">
            <Text>{{ userInfo.userName || '暂未设置' }}</Text>
            <Tag
              :color="userInfo.userRname === 1 ? 'success' : 'warning'"
              class="ml-2"
            >
              {{ userInfo.userRname === 1 ? '已实名' : '未实名' }}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="账户类型" class="!py-2">
            <Text :type="userInfo.authority === 1 ? 'success' : 'secondary'">
              {{ userInfo.authority === 1 ? '管理员' : '普通用户' }}
            </Text>
            <Tag
              :color="userInfo.userStatus === 1 ? 'success' : 'error'"
              class="ml-2"
            >
              {{ userInfo.userStatus === 1 ? '正常' : '已禁用' }}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="账户余额" class="!py-2">
            <Text type="warning" class="mr-4">
              余额：¥ {{ userInfo.userBalance }}
            </Text>
            <Text type="warning"> 佣金：¥ {{ userInfo.userWage }} </Text>
          </Descriptions.Item>
        </Descriptions>

        <Divider class="!my-4" />

        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <Text strong>API对接信息</Text>
              <Tag
                :color="userInfo.opennessApi === 2 ? 'success' : 'default'"
                class="ml-2"
              >
                {{ userInfo.opennessApi === 2 ? 'API已开启' : 'API未开启' }}
              </Tag>
            </div>
            <Button type="link" @click="openLinksModal">
              <div class="flex items-center">
                <MdiLink class="mr-1" />
                常用链接
              </div>
            </Button>
          </div>

          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <Text type="secondary">对接账号：</Text>
              <div class="flex items-center">
                <Text>{{ userInfo.dockingAccounts || '-' }}</Text>
                <Button
                  type="link"
                  size="small"
                  @click="handleCopy(userInfo.dockingAccounts, 'API账号')"
                  :disabled="!userInfo.dockingAccounts"
                >
                  <MdiContentCopy />
                </Button>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <Text type="secondary">密钥：</Text>
              <div class="flex items-center">
                <Text>
                  {{ showSecret ? userInfo.userKey : '••••••••' }}
                </Text>
                <Button
                  type="link"
                  size="small"
                  @click="toggleSecret"
                  class="mr-1"
                  :disabled="!userInfo.userKey"
                >
                  <MdiEye v-if="!showSecret" />
                  <MdiEyeOff v-else />
                </Button>
                <Button
                  type="link"
                  size="small"
                  @click="handleCopy(userInfo.userKey, 'API密钥')"
                  :disabled="!userInfo.userKey"
                >
                  <MdiContentCopy />
                </Button>
              </div>
            </div>

            <div class="flex items-center justify-between">
              <Text type="secondary">API请求链接：</Text>
              <div class="flex items-center">
                <Text class="text-xs">{{ currentDomain }}</Text>
                <Button
                  type="link"
                  size="small"
                  @click="handleCopy(`${currentDomain}`, 'API请求链接')"
                >
                  <MdiContentCopy />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Card>

  <!-- 常用链接弹窗 -->
  <Modal
    v-model:visible="showLinksModal"
    title="常用链接"
    @cancel="showLinksModal = false"
    :footer="null"
    width="600px"
  >
    <div class="space-y-4">
      <!-- API文档链接 -->
      <div
        v-for="link in commonLinks"
        :key="link.url"
        class="cursor-pointer rounded bg-gray-50 p-4 transition-colors hover:bg-gray-100 dark:bg-black/10 dark:hover:bg-black/20"
        @click="handleLinkClick(link.url)"
      >
        <div class="flex items-center justify-between">
          <Text strong>{{ link.title }}</Text>
          <Button type="link" size="small">
            <div class="flex items-center">
              <MdiLink class="mr-1" />
              立即前往
            </div>
          </Button>
        </div>
        <Text type="secondary" class="mt-1 block">
          {{ link.description }}
        </Text>
      </div>

      <!-- 请求域名列表 -->
      <Divider>请求域名</Divider>
      <div
        v-for="endpoint in apiEndpoints"
        :key="endpoint.path"
        class="cursor-pointer rounded bg-gray-50 p-4 transition-colors hover:bg-gray-100 dark:bg-black/10 dark:hover:bg-black/20"
        @click="handleCopy(getFullApiUrl(endpoint), '链接')"
      >
        <div class="flex items-center justify-between">
          <Text strong>{{ endpoint.title }}</Text>
          <Button type="link" size="small">
            <div class="flex items-center">
              <MdiContentCopy class="mr-1" />
              复制链接
            </div>
          </Button>
        </div>
        <Text type="secondary" class="mt-1 block text-xs">
          {{ getFullApiUrl(endpoint) }}
        </Text>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
:deep(.ant-descriptions-item) {
  padding-bottom: 8px !important;
  padding-top: 8px !important;
}
</style>
