<script lang="ts" setup>
import { onMounted } from 'vue';

import { getUserOptionsApi } from '#/api';
import { getProxyActivationStatistics } from '#/api/core/dataStatistics';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

// 搜索条件配置
const basicSearchItems = [
  {
    field: 'user_id',
    label: '代理ID',
    component: 'Select' as const,
    remote: {
      api: getUserOptionsApi,
      transform: (data: any) =>
        data.data.map((item: any) => ({
          label: `${item.name}`, // 组合显示
          value: item.id,
        })),
    },
    props: {
      optionFilterProp: 'label',
      placeholder: '请选择代理ID',
      allowClear: true,
    },
  },
];

// 表格列配置
const columns = [
  {
    title: '代理名称',
    dataIndex: 'user_name',
    width: 150,
    align: 'center' as const,
  },
  // "card_activate": 1386, //SIM卡激活总数
  {
    title: 'SIM卡激活总数',
    dataIndex: 'card_activate',
    width: 150,
    align: 'center' as const,
  },
  {
    title: 'SIM卡在网数量',
    dataIndex: 'card_in_use',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '设备激活总数',
    dataIndex: 'device_activate',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '设备在网总数',
    dataIndex: 'device_in_use',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '停机总数',
    dataIndex: 'card_stop',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '总利润',
    dataIndex: 'total_profit',
    width: 150,
    align: 'center' as const,
  },
  {
    title: 'SIM卡总数',
    dataIndex: 'card_count',
    width: 150,
    align: 'center' as const,
  },
];

// 使用 useTable hook 管理表格数据和搜索
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getProxyActivationStatistics,
  defaultParams: {},
});

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="proxy-activation-stats">
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="basicSearchItems"
      :loading="loading"
      :compact="true"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 数据表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :pagination="pagination"
      :row-key="(record) => record.proxyId"
      @change="handleTableChange"
    />
  </div>
</template>

<style lang="less" scoped>
.proxy-activation-stats {
  :deep(.ant-card-body) {
    padding: 16px;
  }
}
</style>
