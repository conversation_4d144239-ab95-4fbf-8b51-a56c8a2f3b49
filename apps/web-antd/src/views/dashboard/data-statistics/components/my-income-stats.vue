<script lang="ts" setup>
import { onMounted } from 'vue';

import dayjs from 'dayjs';

import { getMyIncomeStatistics } from '#/api/core/dataStatistics';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

// 搜索条件配置
const basicSearchItems = [
  {
    field: 'userName',
    label: '返佣代理',
    component: 'Input',
    props: {
      placeholder: '请输入返佣代理',
      allowClear: true,
    },
  },
  // wageType 佣金类型  1返佣 2退款 3管理员
  {
    field: 'wageType',
    label: '佣金类型',
    component: 'Select',
    props: {
      placeholder: '请选择佣金类型',
      options: [
        { label: '返佣', value: 1 },
        { label: '退款', value: 2 },
      ],
    },
  },
];
// 表格列配置
const columns = [
  {
    title: '返佣金额',
    dataIndex: 'wageAmount',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '到账金额',
    dataIndex: 'wageAccountAmount',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '返佣时间',
    dataIndex: 'wageTime',
    width: 150,
    align: 'center' as const,
    // 不换行
    ellipsis: true,
    customRender: ({ text }) => {
      return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '返佣代理',
    dataIndex: 'userName',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '返佣前佣金',
    dataIndex: 'wageBefore',
    width: 150,
    align: 'center' as const,
    // 不换行
    ellipsis: true,
  },
  {
    title: '返佣备注',
    dataIndex: 'wageRemark',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '关联单号',
    dataIndex: 'systemOrdernumber',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '佣金类型',
    dataIndex: 'wageType',
    width: 150,
    align: 'center' as const,
    customRender: ({ text }) => {
      return text === 1 ? '返佣' : text === 2 ? '退款' : '管理员';
    },
  },
  {
    title: '手机号',
    dataIndex: 'userPhone',
    width: 150,
    align: 'center' as const,
  },
];

// 使用 useTable hook 管理表格数据和搜索
const {
  loading,
  searchParams,
  tableData,
  pagination,
  handleSearch,
  handleReset,
  handleTableChange,
  getList,
} = useTable({
  api: getMyIncomeStatistics,
  defaultParams: {},
});

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="my-income-stats">
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :loading="loading"
      :basic-items="basicSearchItems"
      :compact="true"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 数据表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :pagination="pagination"
      :row-key="(record) => record.id"
      @change="handleTableChange"
    />
  </div>
</template>

<style lang="less" scoped>
.my-income-stats {
  :deep(.ant-card-body) {
    padding: 16px;
  }
}
</style>
