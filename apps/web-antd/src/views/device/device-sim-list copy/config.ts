import type { TableColumnType } from 'ant-design-vue';

import type { DeviceCard } from '#/api';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import dayjs from 'dayjs';

import { statusOptions } from '#/constants/card-status';

// 实名状态选项
const nameStatusOptions = [
  { label: '未知', value: '1', color: 'default' },
  { label: '已实名', value: '2', color: 'success' },
  { label: '未实名', value: '3', color: 'warning' },
  { label: '无需实名', value: '4', color: 'default' },
];

// 卡片状态选项
// const statusOptions = [
//   { label: '未知', value: '1', color: 'default' },
//   { label: '待激活', value: '2', color: 'processing' },
//   { label: '已激活', value: '3', color: 'success' },
//   { label: '已停机', value: '4', color: 'warning' },
//   { label: '预销号', value: '5', color: 'error' },
//   { label: '已销号', value: '6', color: 'error' },
//   { label: '已拆机', value: '7', color: 'error' },
//   { label: '测试期', value: '8', color: 'default' },
//   { label: '沉默期', value: '9', color: 'default' },
//   { label: '库存', value: '10', color: 'default' },
//   { label: '已过户', value: '11', color: 'default' },
//   { label: '异常', value: '12', color: 'error' },
// ];

export const columns: TableColumnType<DeviceCard>[] = [
  {
    title: '通道名称',
    dataIndex: 'apiName',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: 'ICCID',
    dataIndex: 'iccidNumber',
    width: 150,
    align: 'center',
  },
  {
    title: '接入号',
    dataIndex: 'msisdnNumber',
    width: 150,
    align: 'center',
  },
  {
    title: '虚拟号',
    dataIndex: 'voidNumber',
    width: 150,
    align: 'center',
  },
  {
    title: '实名状态',
    dataIndex: 'nameStatus',
    width: 100,
    align: 'center',
    customRender: ({ text }) => {
      const option = nameStatusOptions.find(
        (item) => item.value === String(text),
      );
      return option ? option.label : '未知';
    },
  },
  {
    title: '上游状态',
    dataIndex: 'upperStatusMsg',
    width: 100,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '卡片状态',
    dataIndex: 'statusMsg',
    width: 100,
    align: 'center',
  },
  {
    title: '总流量(实际/MB)',
    dataIndex: 'totalFlow',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '已用流量(实际/MB)',
    dataIndex: 'usedFlow',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '剩余流量(实际/MB)',
    dataIndex: 'residualFlow',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '总流量(显示/MB)',
    dataIndex: 'vTotalFlow',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '已用流量(显示/MB)',
    dataIndex: 'vUsedFlow',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '剩余流量(显示/MB)',
    dataIndex: 'vResidualFlow',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '入库时间',
    dataIndex: 'creationTime',
    width: 160,
    align: 'center',
    ellipsis: true,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 160,
    align: 'center',
    ellipsis: true,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  {
    title: '激活时间',
    dataIndex: 'activationDatetime',
    width: 160,
    align: 'center',
    ellipsis: true,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
];

// 基础搜索配置
export const basicSearchItems: SearchItemConfig[] = [
  {
    field: 'iccidNumber',
    label: 'ICCID',
    component: 'Input',
    props: {
      placeholder: '请输入ICCID',
      allowClear: true,
    },
  },
  {
    field: 'msisdnNumber',
    label: 'MSISDN',
    component: 'Input',
    props: {
      placeholder: '请输入MSISDN',
      allowClear: true,
    },
  },
  {
    field: 'voidNumber',
    label: '虚拟号',
    component: 'Input',
    props: {
      placeholder: '请输入虚拟号',
      allowClear: true,
    },
  },
  {
    field: 'status',
    label: '卡片状态',
    component: 'Select',
    props: {
      placeholder: '请选择卡片状态',
      allowClear: true,
      options: statusOptions,
    },
  },
];

// 高级搜索配置
export const advancedSearchItems = [
  {
    label: '卡片信息',
    items: [
      {
        field: 'configIds',
        label: '通道ID',
        component: 'Input',
        props: {
          placeholder: '请输入通道ID，多个用逗号分隔',
          allowClear: true,
        },
      },
      {
        field: 'startCardNo',
        label: '开始卡号',
        component: 'Input',
        props: {
          placeholder: '请输入开始卡号',
          allowClear: true,
        },
      },
      {
        field: 'endCardNo',
        label: '结尾卡号',
        component: 'Input',
        props: {
          placeholder: '请输入结尾卡号',
          allowClear: true,
        },
      },
      {
        field: 'nameStatus',
        label: '实名状态',
        component: 'Select',
        props: {
          placeholder: '请选择实名状态',
          allowClear: true,
          options: nameStatusOptions,
        },
      },
    ],
  },
  {
    label: '时间区间',
    items: [
      {
        field: 'creationTimeBegin',
        label: '创建时间(开始)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'creationTimeEnd',
        label: '创建时间(结束)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'updateTimeBegin',
        label: '修改时间(开始)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'updateTimeEnd',
        label: '修改时间(结束)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'activationDatetimeBegin',
        label: '激活时间(开始)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'activationDatetimeEnd',
        label: '激活时间(结束)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
];
