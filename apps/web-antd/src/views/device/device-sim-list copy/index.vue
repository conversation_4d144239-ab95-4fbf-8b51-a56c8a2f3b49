<script lang="ts" setup>
import type { DeviceCard } from '#/api/core/device';

import { computed, h, onMounted, ref } from 'vue';

import { MdiCalculator, MdiChip, MdiDelete, MdiImport } from '@vben/icons';

import { Card, Form, message, Modal, Select } from 'ant-design-vue';

import {
  deleteCardByConfigId,
  deleteCardByIds,
  getDeviceCardList,
  updateCardInfo,
} from '#/api/core/device';
import { getChannelListApi } from '#/api/core/order';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import CalculateImportModal from './components/calculate-import-modal.vue';
import ImportStorageModal from './components/import-storage-modal.vue';
import { advancedSearchItems, basicSearchItems, columns } from './config';

const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable<DeviceCard, { page: number; pageSize: number }>({
  api: getDeviceCardList,
  defaultParams: {},
});

const formData = ref({});
const selectedRowKeys = ref<(number | string)[]>([]);
const selectedRows = ref<DeviceCard[]>([]);
const showSelection = ref(false);
const channelId = ref<string>();

// 计算导入相关
const calculateVisible = ref(false);
const calculateLoading = ref(false);

// 导入入库相关
const importVisible = ref(false);
const importLoading = ref(false);

interface Channel {
  id: number;
  name: string;
}

const channelList = ref<Channel[]>([]);
const channelLoading = ref(false);

const loadChannelList = async () => {
  try {
    channelLoading.value = true;
    const res = await getChannelListApi();
    channelList.value = res.data;
  } catch {
    message.error('获取通道列表失败');
  } finally {
    channelLoading.value = false;
  }
};

const handleConfirmModal = ({
  title,
  content,
  onOk,
  type = 'info',
  okType = 'primary',
  okText = '确定',
  cancelText = '取消',
}: {
  cancelText?: string;
  content: (() => any) | string;
  okText?: string;
  okType?: 'danger' | 'primary';
  onOk: () => Promise<void>;
  title: string;
  type?: 'error' | 'info' | 'success' | 'warning';
}) => {
  Modal.confirm({
    title,
    content: typeof content === 'function' ? content() : content,
    type,
    okType,
    okText,
    cancelText,
    async onOk() {
      try {
        loading.value = true;
        await onOk();
      } catch (error) {
        if (error?.errorFields) {
          return;
        }
        throw error;
      } finally {
        loading.value = false;
      }
    },
  });
};

const handleUpdate = async (record: DeviceCard) => {
  const res = await updateCardInfo(record.id);
  if (res.code === 1) {
    message.success(res.msg || '更新成功');
    getList();
  } else {
    message.error(res.msg || '更新失败');
  }
};

const handleDelete = (record: DeviceCard) => {
  handleConfirmModal({
    title: '删除确认',
    content: `确定要删除卡号 ${record.msisdnNumber} 吗？此操作不可恢复！`,
    type: 'warning',
    okType: 'danger',
    async onOk() {
      const res = await deleteCardByIds([record.id]);
      if (res.code === 1) {
        message.success('删除成功');
        getList();
      } else {
        message.error(res.msg || '删除失败');
      }
    },
  });
};

const handleCalculate = () => {
  calculateVisible.value = true;
  loadChannelList();
};

const handleImport = () => {
  importVisible.value = true;
  loadChannelList();
};

const handleCancelSelection = () => {
  showSelection.value = false;
  selectedRowKeys.value = [];
  selectedRows.value = [];
};

const handleUpdateChannel = () => {
  // TODO: 修改卡片通道
};

const handleBatchDelete = () => {
  if (!showSelection.value) {
    showSelection.value = true;
    return;
  }

  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的记录');
    return;
  }

  handleConfirmModal({
    title: '批量删除确认',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？此操作不可恢复！`,
    type: 'warning',
    okType: 'danger',
    async onOk() {
      const res = await deleteCardByIds(selectedRowKeys.value);
      if (res.code === 1) {
        message.success('删除成功');
        selectedRowKeys.value = [];
        selectedRows.value = [];
        showSelection.value = false;
        getList();
      } else {
        message.error(res.msg || '删除失败');
      }
    },
  });
};

const handleSelectionChange = (
  keys: (number | string)[],
  rows: DeviceCard[],
) => {
  selectedRowKeys.value = keys;
  selectedRows.value = rows;
};

const actionButtons = [
  {
    key: 'update',
    text: '更新',
    type: 'link' as const,
    onClick: handleUpdate,
  },
  {
    key: 'delete',
    text: '删除',
    type: 'link' as const,
    danger: true,
    onClick: handleDelete,
  },
];

const customButtons = computed(() => {
  const buttons = [
    {
      key: 'import',
      text: '导入入库',
      icon: h(MdiImport),
      onClick: handleImport,
    },
    {
      key: 'calculate',
      text: '计算入库',
      icon: h(MdiCalculator),
      onClick: handleCalculate,
    },
    // 修改卡片通道
    {
      key: 'updateChannel',
      text: '修改通道',
      icon: h(MdiChip),
      onClick: handleUpdateChannel,
    },
    {
      key: 'batchDelete',
      text: showSelection.value ? '确认删除' : '批量删除',
      danger: true,
      icon: h(MdiDelete),
      onClick: handleBatchDelete,
    },
  ];

  if (showSelection.value) {
    buttons.push({
      key: 'cancelSelection',
      text: '取消选择',
      icon: h(MdiDelete),
      onClick: handleCancelSelection,
    });
  }

  buttons.push({
    key: 'channelDelete',
    text: '通道删除',
    danger: true,
    icon: h(MdiDelete),
    onClick: () => {
      channelId.value = undefined;
      loadChannelList();

      handleConfirmModal({
        title: '通道删除',
        content: () =>
          h('div', [
            h('div', { class: 'mb-4' }, '请选择要删除的通道：'),
            h(
              Form,
              { layout: 'vertical' },
              {
                default: () => [
                  h(
                    Form.Item,
                    {
                      label: '通道名称',
                      required: true,
                    },
                    {
                      default: () =>
                        h(Select, {
                          placeholder: '请选择通道',
                          style: { width: '100%' },
                          options: channelList.value.map((item) => ({
                            label: item.name,
                            value: String(item.id),
                          })),
                          onChange: (value: any) => {
                            channelId.value =
                              typeof value === 'string' ? value : undefined;
                          },
                        }),
                    },
                  ),
                ],
              },
            ),
          ]),
        async onOk() {
          if (!channelId.value) {
            message.warning('请选择通道');
            throw new Error('请选择通道');
          }
          const res = await deleteCardByConfigId(channelId.value);
          if (res.code === 1) {
            message.success('删除成功');
            getList();
          } else {
            message.error(res.msg || '删除失败');
          }
        },
      });
    },
  });

  return buttons;
});

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="p-2">
    <!-- <Card class="mb-4">
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedSearchItems"
        :custom-buttons="customButtons"
        @reset="handleReset"
        @search="handleSearch"
        @update:model-value="(val) => (formData = val)"
      />
    </Card> -->
    <Card>
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedSearchItems"
        :custom-buttons="customButtons"
        @reset="handleReset"
        @search="handleSearch"
        @update:model-value="(val) => (formData = val)"
      />
      <BasicTable
        class="mt-4"
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :action-buttons="actionButtons"
        :show-selection="showSelection"
        v-model:selected-row-keys="selectedRowKeys"
        @selection-change="handleSelectionChange"
        @change="handleTableChange"
        row-key="id"
        show-action
      />
    </Card>

    <!-- 计算导入弹窗 -->
    <CalculateImportModal
      v-model:visible="calculateVisible"
      :loading="calculateLoading"
      :channel-list="channelList"
      :channel-loading="channelLoading"
      @success="getList"
    />

    <!-- 导入入库弹窗 -->
    <ImportStorageModal
      v-model:visible="importVisible"
      :loading="importLoading"
      :channel-list="channelList"
      :channel-loading="channelLoading"
      @success="getList"
    />
  </div>
</template>
