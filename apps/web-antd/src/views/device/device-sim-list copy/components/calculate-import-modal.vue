<script lang="ts" setup>
import { ref } from 'vue';

import { Form, Input, message, Modal, Select, Spin } from 'ant-design-vue';

import { calculateImportCard } from '#/api/core/device';

interface Channel {
  id: number;
  name: string;
}

interface CalculateForm {
  voidNumber: string;
  msisdnNumber: string;
  iccIdNumber: string;
  num: string;
  configId: string | undefined;
}

interface Props {
  visible: boolean;
  loading: boolean;
  channelList: Channel[];
  channelLoading: boolean;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  loading: false,
  channelList: () => [],
  channelLoading: false,
});

const emit = defineEmits<Emits>();

const calculateFormRef = ref();
const calculateForm = ref<CalculateForm>({
  voidNumber: '',
  msisdnNumber: '',
  iccIdNumber: '',
  num: '',
  configId: undefined,
});

const calculateLoading = ref(false);

const handleSubmit = async () => {
  try {
    calculateLoading.value = true;
    const values = await calculateFormRef.value?.validateFields();
    const res = await calculateImportCard(values);
    if (res.code === 1) {
      message.success(res.msg);
      closeModal();
      emit('success');
    } else {
      message.error(res.msg || '计算导入失败');
    }
  } catch (error: any) {
    if (error?.errorFields) {
      return;
    }
    message.error('计算导入失败');
  } finally {
    calculateLoading.value = false;
  }
};

const closeModal = () => {
  emit('update:visible', false);
  calculateForm.value = {
    voidNumber: '',
    msisdnNumber: '',
    iccIdNumber: '',
    num: '',
    configId: undefined,
  };
};
</script>

<template>
  <Modal
    :visible="visible"
    title="计算导入卡片"
    @ok="handleSubmit"
    :confirm-loading="calculateLoading || loading"
    @cancel="closeModal"
  >
    <Form ref="calculateFormRef" :model="calculateForm" layout="vertical">
      <Form.Item
        label="开始虚拟号"
        name="voidNumber"
        :rules="[{ required: true, message: '请输入开始虚拟号' }]"
      >
        <Input
          v-model:value="calculateForm.voidNumber"
          placeholder="请输入开始虚拟号"
          allow-clear
        />
      </Form.Item>
      <Form.Item
        label="开始接入号"
        name="msisdnNumber"
        :rules="[{ required: true, message: '请输入开始接入号' }]"
      >
        <Input
          v-model:value="calculateForm.msisdnNumber"
          placeholder="请输入开始接入号"
          allow-clear
        />
      </Form.Item>
      <Form.Item
        label="开始ICCID"
        name="iccIdNumber"
        :rules="[{ required: true, message: '请输入开始ICCID' }]"
      >
        <Input
          v-model:value="calculateForm.iccIdNumber"
          placeholder="请输入开始ICCID"
          allow-clear
        />
      </Form.Item>
      <Form.Item
        label="导入个数"
        name="num"
        :rules="[{ required: true, message: '请输入导入个数' }]"
      >
        <Input
          v-model:value="calculateForm.num"
          placeholder="请输入导入个数"
          allow-clear
        />
      </Form.Item>
      <Form.Item
        label="通道名称"
        name="configId"
        :rules="[{ required: true, message: '请选择通道名称' }]"
      >
        <Spin :spinning="channelLoading">
          <Select
            v-model:value="calculateForm.configId"
            placeholder="请选择通道名称"
            :options="
              channelList.map((item) => ({
                label: item.name,
                value: String(item.id),
              }))
            "
            allow-clear
            show-search
            :filter-option="
              (input, option) =>
                option?.label?.toLowerCase().includes(input.toLowerCase())
            "
          />
        </Spin>
      </Form.Item>
    </Form>
  </Modal>
</template>
