<script lang="ts" setup>
import { ref } from 'vue';

import {
  Button,
  Form,
  message,
  Modal,
  Select,
  Spin,
  Upload,
} from 'ant-design-vue';

import { importUpdateCardConfig } from '#/api/core/device';
import { downloadDeleteTemplate } from '#/api/core/sim';
import { handleFileDownload } from '#/utils/export';

interface Channel {
  id: number;
  name: string;
}

interface ImportForm {
  configId: string | undefined;
}

interface Props {
  visible: boolean;
  loading: boolean;
  channelList: Channel[];
  channelLoading: boolean;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  loading: false,
  channelList: () => [],
  channelLoading: false,
});

const emit = defineEmits<Emits>();

const importFormRef = ref();
const importForm = ref<ImportForm>({
  configId: undefined,
});
const fileList = ref([]);
const importLoading = ref(false);

// const handleDownloadTemplate = async () => {
//   try {
//     const res = await downloadStorageTemplateApi();
//     await handleFileDownload(res, '卡片入库模板.xlsx');
//   } catch (error) {
//     console.error('下载模板失败:', error);
//     message.error('下载模板失败');
//   }
// };

const handleDownloadTemplate = async () => {
  try {
    const res = await downloadDeleteTemplate();
    await handleFileDownload(res, '变更通道模板.xlsx');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败');
  }
};

const closeModal = () => {
  emit('update:visible', false);
  importForm.value = {
    configId: undefined,
  };
  fileList.value = [];
};

const handleSubmit = async () => {
  try {
    const values = await importFormRef.value?.validateFields();
    if (fileList.value.length === 0) {
      message.error('请选择要导入的文件');
      return;
    }
    importLoading.value = true;
    const res = await importUpdateCardConfig(
      values.configId,
      fileList.value[0].originFileObj,
    );
    if (res.code === 1) {
      message.success(res.msg);
      closeModal();
      emit('success');
    } else {
      message.error(res.msg || '导入失败');
    }
  } catch (error: any) {
    if (error?.errorFields) {
      return;
    }
    message.error('导入失败');
  } finally {
    importLoading.value = false;
  }
};
</script>

<template>
  <Modal
    :visible="visible"
    title="导入修改"
    @ok="handleSubmit"
    :confirm-loading="importLoading || loading"
    @cancel="closeModal"
  >
    <Form ref="importFormRef" :model="importForm" layout="vertical">
      <Form.Item
        label="通道名称"
        name="configId"
        :rules="[{ required: true, message: '请选择通道名称' }]"
      >
        <Spin :spinning="channelLoading">
          <Select
            v-model:value="importForm.configId"
            placeholder="请选择通道名称"
            :options="
              channelList.map((item) => ({
                label: item.name,
                value: String(item.id),
              }))
            "
            allow-clear
            show-search
            :filter-option="
              (input, option) =>
                option?.label?.toLowerCase().includes(input.toLowerCase())
            "
          />
        </Spin>
      </Form.Item>

      <Form.Item label="导入文件" required>
        <Upload
          v-model:file-list="fileList"
          :max-count="1"
          :before-upload="() => false"
        >
          <Button>选择文件</Button>
        </Upload>
        <div class="mt-2 flex items-center justify-between">
          <span class="text-gray-500">请上传Excel文件，文件大小不超过10MB
          </span>
          <Button
            type="link"
            class="flex items-center"
            @click="handleDownloadTemplate"
          >
            <MdiDownload class="mr-1" />
            下载模板
          </Button>
        </div>
      </Form.Item>
    </Form>
  </Modal>
</template>
