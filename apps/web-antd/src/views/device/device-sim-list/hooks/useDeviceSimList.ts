import type { CustomButton } from '#/hooks/useAnsheng/types';

import { computed, h, ref } from 'vue';

import { MdiCalculator, MdiChip, MdiDelete, MdiImport } from '@vben/icons';

import { Form, message, Modal, Select } from 'ant-design-vue';

import {
  deleteCardByConfigId,
  deleteCardByIds,
  getDeviceCardList,
  updateCardInfo,
} from '#/api/core/device';
import { getChannelListApi } from '#/api/core/order';
import { useAnsheng } from '#/hooks/useAnsheng';

import { advancedSearchItems, basicSearchItems, columns } from '../config';

/**
 * 卡片列表业务逻辑Hook
 */
export function useDeviceSimList() {
  // 刷新函数
  const refresh = ref(null);

  // 通道列表
  const channelList = ref([]);
  const channelLoading = ref(false);

  // 计算导入相关
  const calculateVisible = ref(false);
  const calculateLoading = ref(false);

  // 导入入库相关
  const importVisible = ref(false);
  const importLoading = ref(false);

  // 批量操作相关
  const showSelection = ref(false);
  const selectedRowKeys = ref<(number | string)[]>([]);
  const selectedRows = ref([]);

  // 通道删除相关
  const channelId = ref<string>();
  const channelDeleteLoading = ref(false);

  // 导入修改相关
  const importUpdateVisible = ref(false);
  // const importUpdateLoading = ref(false);

  // 声明initialize函数，后面会赋值
  let initialize: () => void;

  // 加载通道列表
  const loadChannelList = async () => {
    try {
      channelLoading.value = true;
      const res = await getChannelListApi();
      channelList.value = res.data;
    } catch {
      // message.error('获取通道列表失败');
    } finally {
      channelLoading.value = false;
    }
  };

  // 显示计算导入弹窗
  const handleCalculate = () => {
    calculateVisible.value = true;
    loadChannelList();
  };

  // 显示导入入库弹窗
  const handleImport = () => {
    importVisible.value = true;
    loadChannelList();
  };

  // 取消选择
  const handleCancelSelection = () => {
    showSelection.value = false;
    selectedRowKeys.value = [];
    selectedRows.value = [];
  };

  // 通道删除处理函数
  const handleChannelDelete = () => {
    channelId.value = undefined;
    loadChannelList();

    Modal.confirm({
      title: '通道删除',
      content: () =>
        h('div', [
          h('div', { class: 'mb-4' }, '请选择要删除的通道：'),
          h(
            Form,
            { layout: 'vertical' },
            {
              default: () => [
                h(
                  Form.Item,
                  {
                    label: '通道名称',
                    required: true,
                  },
                  {
                    default: () =>
                      h(Select, {
                        placeholder: '请选择通道',
                        style: { width: '100%' },
                        options: channelList.value.map((item: any) => ({
                          label: item.name,
                          value: String(item.id),
                        })),
                        onChange: (value: any) => {
                          channelId.value =
                            typeof value === 'string' ? value : undefined;
                        },
                      }),
                  },
                ),
              ],
            },
          ),
        ]),
      async onOk() {
        if (!channelId.value) {
          message.warning('请选择通道');
          throw new Error('请选择通道');
        }
        try {
          channelDeleteLoading.value = true;
          const res = await deleteCardByConfigId(channelId.value);
          if (res.code === 1) {
            message.success('删除成功');
            initialize();
          } else {
            message.error(res.msg || '删除失败');
          }
        } finally {
          channelDeleteLoading.value = false;
        }
      },
    });
  };

  // 更新卡片通道
  const handleUpdateChannel = () => {
    // TODO: 实现修改卡片通道功能
    importUpdateVisible.value = true;
    loadChannelList();
  };

  // 使用useAnsheng整合表格、搜索功能
  const {
    loading,
    initialize: initializeFunc,
    handleTableChange,
    searchToolbarBind,
    tableBind: origTableBind,
    table,
  } = useAnsheng({
    // 表格配置
    tableOptions: {
      api: getDeviceCardList,
      columns: columns as any,
      defaultParams: {},
      actionButtons: [
        {
          key: 'update',
          text: '更新',
          type: 'link' as const,
          onClick: async (record) => {
            try {
              loading.value = true;
              const res = await updateCardInfo(record.id);
              if (res.code === 1) {
                // message.success(res.msg || '更新成功');
                initialize();
              } else {
                // message.error(res.msg || '更新失败');
              }
            } finally {
              loading.value = false;
            }
          },
        },
        {
          key: 'delete',
          text: '删除',
          type: 'link' as const,
          danger: true,
          onClick: (record) => {
            Modal.confirm({
              title: '删除确认',
              content: `确定要删除卡号 ${record.msisdnNumber} 吗？此操作不可恢复！`,
              onOk: async () => {
                const res = await deleteCardByIds([record.id]);
                if (res.code === 1) {
                  message.success(res.msg || '删除成功');
                  initialize();
                } else {
                  message.error(res.msg || '删除失败');
                }
              },
            });
          },
        },
      ],
      actionWrap: false,
    },
    // 搜索配置
    searchOptions: {
      basicItems: basicSearchItems,
      advancedItems: advancedSearchItems,
      customButtons: computed(() => {
        const buttons = [
          {
            key: 'import',
            text: '导入入库',
            icon: h(MdiImport),
            onClick: handleImport,
          },
          {
            key: 'calculate',
            text: '计算入库',
            icon: h(MdiCalculator),
            onClick: handleCalculate,
          },
          // 修改卡片通道
          {
            key: 'updateChannel',
            text: '修改通道',
            icon: h(MdiChip),
            onClick: handleUpdateChannel,
          },
          {
            key: 'batchDelete',
            text: showSelection.value ? '确认删除' : '批量删除',
            danger: true,
            icon: h(MdiDelete),
            onClick: () => {
              if (!showSelection.value) {
                showSelection.value = true;
                return;
              }

              if (selectedRowKeys.value.length === 0) {
                message.warning('请选择要删除的记录');
              }

              Modal.confirm({
                title: '批量删除确认',
                content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？此操作不可恢复！`,
                onOk: async () => {
                  const res = await deleteCardByIds(selectedRowKeys.value);
                  if (res.code === 1) {
                    message.success('删除成功');
                    selectedRowKeys.value = [];
                    selectedRows.value = [];
                    showSelection.value = false;
                    initialize();
                  } else {
                    message.error(res.msg || '删除失败');
                  }
                },
              });
            },
          },
        ];

        if (showSelection.value) {
          buttons.push({
            key: 'cancelSelection',
            text: '取消选择',
            icon: h(MdiDelete),
            onClick: handleCancelSelection,
          });
        }

        buttons.push({
          key: 'channelDelete',
          text: '通道删除',
          danger: true,
          icon: h(MdiDelete),
          onClick: handleChannelDelete,
        });

        return buttons;
      }) as unknown as CustomButton[],
    },
    formOptions: {
      title: '卡片详情',
      defaultValues: {},
    },
  });

  // 给之前声明的initialize函数赋值
  initialize = initializeFunc;

  refresh.value = table as any;

  // 处理选择变更
  const handleSelectionChange = (keys: (number | string)[], rows: any[]) => {
    selectedRowKeys.value = keys;
    selectedRows.value = rows;
  };

  // 表格绑定对象 - 添加selection相关属性
  const tableBind = computed(() => ({
    ...origTableBind.value,
    onChange: handleTableChange,
    showSelection: showSelection.value,
    selectedRowKeys: selectedRowKeys.value,
    onSelectionChange: handleSelectionChange,
  }));

  return {
    // 状态
    loading,
    channelList,
    channelLoading,
    calculateVisible,
    calculateLoading,
    importVisible,
    importLoading,
    selectedRowKeys,
    selectedRows,
    channelId,
    showSelection,
    importUpdateVisible,
    channelDeleteLoading,

    // 方法
    initialize,
    loadChannelList,
    handleSelectionChange,
    handleCancelSelection,

    // 绑定对象
    searchToolbarBind,
    tableBind,
  };
}
