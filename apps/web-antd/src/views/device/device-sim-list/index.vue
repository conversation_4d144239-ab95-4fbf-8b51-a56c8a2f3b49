<script setup lang="ts">
import { onMounted } from 'vue';

import { Card } from 'ant-design-vue';

import { BasicTable, SearchToolbar } from '#/hooks/useAnsheng';

import CalculateImportModal from './components/calculate-import-modal.vue';
import ImportStorageModal from './components/import-storage-modal.vue';
import ImportUpdateModal from './components/import-update-modal.vue';
import { useDeviceSimList } from './hooks/useDeviceSimList';

// 使用卡片列表业务逻辑Hook
const {
  channelList,
  channelLoading,
  calculateVisible,
  calculateLoading,
  importVisible,
  importLoading,
  importUpdateVisible,
  importUpdateLoading,
  initialize,
  searchToolbarBind,
  tableBind,
} = useDeviceSimList();

// 页面加载时获取数据
onMounted(() => {
  initialize();
});
</script>

<template>
  <div class="p-2">
    <Card>
      <SearchToolbar v-bind="searchToolbarBind" />
      <BasicTable class="mt-4" v-bind="tableBind" />
    </Card>

    <!-- 计算导入弹窗 -->
    <CalculateImportModal
      v-model:visible="calculateVisible"
      :loading="calculateLoading"
      :channel-list="channelList"
      :channel-loading="channelLoading"
      @success="initialize"
    />

    <!-- 导入入库弹窗 -->
    <ImportStorageModal
      v-model:visible="importVisible"
      :loading="importLoading"
      :channel-list="channelList"
      :channel-loading="channelLoading"
      @success="initialize"
    />

    <!-- 导入修改弹窗 -->
    <ImportUpdateModal
      v-model:visible="importUpdateVisible"
      :loading="importUpdateLoading"
      :channel-list="channelList"
      :channel-loading="channelLoading"
      @success="initialize"
    />
  </div>
</template>
