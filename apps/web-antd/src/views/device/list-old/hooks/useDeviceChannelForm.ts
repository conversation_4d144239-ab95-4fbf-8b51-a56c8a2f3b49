import type { FormInstance } from 'ant-design-vue';
import type { SelectValue } from 'ant-design-vue/es/select';

import type { DeviceChannel } from '#/api';

import { h, ref } from 'vue';

import { MdiInformationOutline } from '@vben/icons';

import { useDebounceFn } from '@vueuse/core';
import {
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Select,
  Spin,
  Switch,
  Tooltip,
} from 'ant-design-vue';

import { addDeviceChannel, editApiConfigDevice } from '#/api';
import { getApiVersions } from '#/api/core/channel';

const FormItem = Form.Item;

export interface UseChannelFormOptions {
  onSuccess?: () => void;
}

export interface ChannelFormState {
  apiName: string; // 通道名称
  apiAccount: string; // API账号
  apiPassword: string; // API密码
  apiKey: string; // API密钥
  apiKey2?: string; // API密钥2
  requestAddress: string; // API请求地址
  apiType: string; // API类型
  apiSubmitCard: string; // API提交卡号
  apiSources: string; // 信息获取
  apiPlatform: string; // 对接平台
  apiVersion?: string; // API版本
  apiPolling: number; // 接口轮询
  singleCardSleep: number; // 单卡休眠时间(ms)
  apiOrderSubmission: number; // 订单递交
  notes?: string; // 备注
}

interface ApiVersionItem {
  versionName: string;
  platformIdentifying: string;
}

interface ApiVersionResponse {
  code: number;
  data: ApiVersionItem[];
}

export function useChannelForm(options: UseChannelFormOptions = {}) {
  const formRef = ref<FormInstance>();
  const visible = ref(false);
  const loading = ref(false);
  const versionLoading = ref(false);
  const versionOptions = ref<{ label: string; value: string }[]>([]);
  const isEdit = ref(false);
  const currentId = ref<number>();

  const initFormState = () => ({
    apiName: '',
    apiAccount: '',
    apiPassword: '',
    apiKey: '',
    apiKey2: '',
    requestAddress: '',
    apiType: '1',
    apiSubmitCard: '1',
    apiSources: '1',
    apiPlatform: '',
    apiVersion: undefined,
    apiPolling: 1,
    singleCardSleep: 1000,
    apiOrderSubmission: 0,
    notes: '',
  });

  const formState = ref<ChannelFormState>(initFormState());

  const debouncedLoadApiVersions = useDebounceFn(async (platform: string) => {
    if (!platform?.trim()) {
      versionOptions.value = [];
      return;
    }

    try {
      versionLoading.value = true;
      const res = (await getApiVersions(
        platform,
      )) as unknown as ApiVersionResponse;
      versionOptions.value = res?.data?.length
        ? res.data.map((item) => ({
            label: item.versionName,
            value: item.platformIdentifying,
          }))
        : [];
    } catch (error) {
      console.error('Failed to get API versions:', error);
      message.error('获取API版本失败');
      versionOptions.value = [];
    } finally {
      versionLoading.value = false;
    }
  }, 300);

  const apiTypeOptions = [
    { label: '套餐', value: '1' },
    { label: '前向流量池', value: '2' },
    { label: '后向流量池', value: '3' },
  ];

  const _apiSubmitCardOptions = [
    { label: 'ICCID', value: '1' },
    { label: 'MIS', value: '2' },
    { label: '虚拟号', value: '3' },
  ];

  const apiSourcesOptions = [
    { label: '上游获取', value: '1' },
    { label: '系统自定义', value: '2' },
  ];

  const loadChannelDetail = async (params: any) => {
    try {
      loading.value = true;
      // const res = await getChannelDetail(params.id);
      // if (res.code === 1 && res.data) {
      //   const detail = res.data;

      formState.value = {
        ...params,
        apiType: String(params.apiType),
        apiSources: String(params.apiSources),
        apiOrderSubmission: Number(params.apiOrderSubmission),
        singleCardSleep: Number(params.singleCardSleep),
        apiPolling: Number(params.apiPolling),
      };

      if (params.apiPlatform) {
        await debouncedLoadApiVersions(params.apiPlatform);
      }
      // } else {
      //   message.error(res.msg || '获取通道详情失败');
      // }
    } catch (error) {
      console.error('Failed to load channel detail:', error);
      message.error('获取通道详情失败');
    } finally {
      loading.value = false;
    }
  };

  const handleOk = async () => {
    try {
      loading.value = true;
      await formRef.value?.validate();

      const params: DeviceChannel = {
        apiName: formState.value.apiName,
        apiPlatform: formState.value.apiPlatform,
        apiVersion: formState.value.apiVersion,
        apiAccount: formState.value.apiAccount,
        apiPassword: formState.value.apiPassword,
        apiKey: formState.value.apiKey || undefined,
        apiKey2: formState.value.apiKey2 || undefined,
        apiSources: Number(formState.value.apiSources),
        apiType: Number(formState.value.apiType),
        apiCardType: Number(formState.value.apiSubmitCard),
        apiOrderSubmission: formState.value.apiOrderSubmission,
        apiPolling: formState.value.apiPolling,
        singleCardSleep: formState.value.singleCardSleep,
        notes: formState.value.notes,
        orderMachine: formState.value.orderMachine,
        requestAddress: formState.value.requestAddress,
      };

      if (isEdit.value && currentId.value) {
        const res = await editApiConfigDevice(currentId.value, params);
        if (res.code !== 1) {
          message.error(res.msg || '修改失败');
          return;
        }
        message.success(res.msg);
      } else {
        const res = await addDeviceChannel(params);
        if (res.code === 0) {
          if (res.msg === '该名称已存在') {
            message.error('通道名称已存在');
            return;
          }
          message.error(res.msg || '保存失败');
          return;
        }
        message.success(res.msg);
      }

      // message.success(isEdit.value ? '修改成功' : '添加成功');
      visible.value = false;
      options.onSuccess?.();
    } catch (error) {
      console.error('Save failed:', error);
    } finally {
      loading.value = false;
    }
  };

  const handleCancel = () => {
    formState.value = initFormState();
    versionOptions.value = [];
    visible.value = false;
    isEdit.value = false;
    currentId.value = undefined;
  };

  const renderForm = () => {
    return h(
      'div',
      {
        class:
          'channel-form-container max-h-[calc(100vh-200px)] overflow-y-auto',
      },
      [
        h(
          Form,
          {
            ref: formRef,
            model: formState.value,
            layout: 'horizontal',
            class: 'py-4',
            labelCol: { span: 6 },
            wrapperCol: { span: 16 },
          },
          () => [
            h('div', { class: 'px-6 mb-8' }, [
              h(
                'div',
                {
                  class: 'text-base font-medium mb-5 pb-2.5  border-gray-100',
                },
                '基本信息',
              ),
              h(Row, { gutter: 24 }, [
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: '通道名称',
                      name: 'apiName',
                      rules: [{ required: true, message: '请输入通道名称' }],
                    },
                    () =>
                      h(Input, {
                        value: formState.value.apiName,
                        'onUpdate:value': (val: string) =>
                          (formState.value.apiName = val),
                        placeholder: '请输入通道名称',
                        allowClear: true,
                      }),
                  ),
                ]),
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: '对接平台',
                      name: 'apiPlatform',
                      rules: [{ required: true, message: '请输入对接平台' }],
                    },
                    () =>
                      h(Input, {
                        value: formState.value.apiPlatform,
                        'onUpdate:value': (val: string) => {
                          formState.value.apiPlatform = val;
                          formState.value.apiVersion = undefined;
                          versionOptions.value = [];
                          debouncedLoadApiVersions(val);
                        },
                        placeholder: '请输入对接平台',
                        allowClear: true,
                      }),
                  ),
                ]),
              ]),
              h(Row, { gutter: 24 }, [
                h(Col, { span: 12 }, [
                  versionOptions.value.length > 0 &&
                    h(
                      FormItem,
                      {
                        label: '对接版本',
                        name: 'apiVersion',
                        rules: [],
                      },
                      () =>
                        h(Select, {
                          value: formState.value.apiVersion,
                          'onUpdate:value': (val: SelectValue): void => {
                            formState.value.apiVersion = String(val ?? '');
                          },
                          loading: versionLoading.value,
                          options: versionOptions.value,
                          placeholder: versionLoading.value
                            ? '加载中...'
                            : '请选择对接版本',
                          allowClear: true,
                          disabled: versionLoading.value,
                          showSearch: true,
                          filterOption: (input: string, option: any) => {
                            return option.label
                              .toLowerCase()
                              .includes(input.toLowerCase());
                          },
                        }),
                    ),
                ]),
              ]),
            ]),

            h('div', { class: 'px-6 mb-8' }, [
              h(
                'div',
                {
                  class: 'text-base font-medium mb-5 pb-2.5  border-gray-100',
                },
                'API配置',
              ),
              h(Row, { gutter: 24 }, [
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: 'API账号',
                      name: 'apiAccount',
                      rules: [{ required: true, message: '请输入API账号' }],
                    },
                    () =>
                      h(Input, {
                        value: formState.value.apiAccount,
                        'onUpdate:value': (val: string) =>
                          (formState.value.apiAccount = val),
                        placeholder: '请输入API账号',
                        allowClear: true,
                      }),
                  ),
                ]),
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: 'API密码',
                      name: 'apiPassword',
                      rules: [{ required: true, message: '请输入API密码' }],
                    },
                    () =>
                      h(Input.Password, {
                        value: formState.value.apiPassword,
                        'onUpdate:value': (val: string) =>
                          (formState.value.apiPassword = val),
                        placeholder: '请输入API密码',
                        allowClear: true,
                      }),
                  ),
                ]),
              ]),
              h(Row, { gutter: 24 }, [
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: 'API密钥',
                      name: 'apiKey',
                      rules: [],
                    },
                    () =>
                      h(Input, {
                        value: formState.value.apiKey,
                        'onUpdate:value': (val: string) =>
                          (formState.value.apiKey = val),
                        placeholder: '请输入API密钥',
                        allowClear: true,
                      }),
                  ),
                ]),
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: 'API密钥2',
                      name: 'apiKey2',
                    },
                    () =>
                      h(Input, {
                        value: formState.value.apiKey2,
                        'onUpdate:value': (val: string) =>
                          (formState.value.apiKey2 = val),
                        placeholder: '请输入API密钥2',
                        allowClear: true,
                      }),
                  ),
                ]),
              ]),
            ]),

            h('div', { class: 'px-6 mb-8' }, [
              h(
                'div',
                {
                  class: 'text-base font-medium mb-5 pb-2.5 border-gray-100',
                },
                '接口配置',
              ),
              h(Row, { gutter: 24 }, [
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: 'API地址',
                      name: 'requestAddress',
                      rules: [{ required: true, message: '请输入API请求地址' }],
                    },
                    () =>
                      h(Input, {
                        value: formState.value.requestAddress,
                        'onUpdate:value': (val: string) =>
                          (formState.value.requestAddress = val),
                        placeholder: '请输入API请求地址',
                        allowClear: true,
                      }),
                  ),
                ]),
              ]),
              h(Row, { gutter: 24 }, [
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: 'API类型',
                      name: 'apiType',
                      rules: [{ required: true, message: '请选择API类型' }],
                    },
                    () =>
                      h(Select, {
                        value: formState.value.apiType,
                        'onUpdate:value': (val: SelectValue): void => {
                          formState.value.apiType = String(val ?? '');
                        },
                        options: apiTypeOptions,
                        placeholder: '请选择API类型',
                      }),
                  ),
                ]),
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: '订购后复机',
                      name: 'orderMachine',
                    },
                    () =>
                      h(Switch, {
                        checked: formState.value.orderMachine === 1,
                        onChange: (checked: any) => {
                          formState.value.orderMachine = checked ? 1 : 2;
                        },
                        checkedChildren: '是',
                        unCheckedChildren: '否',
                      }),
                  ),
                ]),
              ]),
              h(Row, { gutter: 24 }, [
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: '信息获取',
                      name: 'apiSources',
                      rules: [
                        { required: true, message: '请选择信息获取方式' },
                      ],
                    },
                    () =>
                      h(Select, {
                        value: formState.value.apiSources,
                        'onUpdate:value': (val: SelectValue): void => {
                          formState.value.apiSources = String(val ?? '');
                        },
                        options: apiSourcesOptions,
                        placeholder: '请选择信息获取方式',
                      }),
                  ),
                ]),
              ]),
            ]),

            h('div', { class: 'px-6 mb-8' }, [
              h(
                'div',
                {
                  class: 'text-base font-medium mb-5 pb-2.5  border-gray-100',
                },
                '轮询设置',
              ),
              h(Row, { gutter: 24 }, [
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: '接口轮询',
                      name: 'apiPolling',
                    },
                    () =>
                      h(Switch, {
                        checked: formState.value.apiPolling === 2,
                        onChange: (checked: any) => {
                          formState.value.apiPolling = checked ? 2 : 1;
                        },
                        checkedChildren: '开启',
                        unCheckedChildren: '关闭',
                      }),
                  ),
                ]),
              ]),
              h(Row, { gutter: 24 }, [
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: '休眠(ms)',
                      name: 'singleCardSleep',
                      rules: [
                        { required: true, message: '请输入单卡休眠时间' },
                      ],
                    },
                    () =>
                      h('div', { class: 'flex items-center' }, [
                        h(InputNumber, {
                          value: formState.value.singleCardSleep,
                          'onUpdate:value': (value: null | number | string) => {
                            formState.value.singleCardSleep =
                              typeof value === 'number' ? value : 1000;
                          },
                          min: 0,
                          style: { width: '120px' },
                          placeholder: '请输入',
                        }),
                        h(
                          Tooltip,
                          {
                            title: '单卡休眠时间',
                          },
                          () =>
                            h(MdiInformationOutline, {
                              class: 'ml-2 text-gray-400 cursor-help',
                              style: 'font-size: 18px',
                            }),
                        ),
                      ]),
                  ),
                ]),
              ]),
            ]),

            h('div', { class: 'px-6' }, [
              h(
                'div',
                {
                  class: 'text-base font-medium mb-5 pb-2.5  border-gray-100',
                },
                '其他设置',
              ),
              h(Row, { gutter: 24 }, [
                h(Col, { span: 12 }, [
                  h(
                    FormItem,
                    {
                      label: '订单递交',
                      name: 'apiOrderSubmission',
                    },
                    () =>
                      h(Switch, {
                        checked: formState.value.apiOrderSubmission === 1,
                        onChange: (checked: any) => {
                          formState.value.apiOrderSubmission = checked ? 1 : 2;
                        },
                        checkedChildren: '是',
                        unCheckedChildren: '否',
                      }),
                  ),
                ]),
              ]),
              h(Row, { gutter: 24 }, [
                h(Col, { span: 24 }, [
                  h(
                    FormItem,
                    {
                      label: '备注',
                      name: 'notes',
                      rules: [],
                      labelCol: { span: 3 },
                      wrapperCol: { span: 20 },
                    },
                    () =>
                      h(Input.TextArea, {
                        value: formState.value.notes,
                        'onUpdate:value': (val: string) =>
                          (formState.value.notes = val),
                        placeholder: '请输入备注',
                        rows: 4,
                        style: { width: '100%', resize: 'none' },
                        allowClear: true,
                      }),
                  ),
                ]),
              ]),
            ]),
          ],
        ),
      ],
    );
  };

  const show = (params: any) => {
    visible.value = true;
    if (params.id) {
      isEdit.value = true;
      currentId.value = params.id;
      loadChannelDetail(params);
    } else {
      isEdit.value = false;
      currentId.value = undefined;
      formState.value = initFormState();
    }
  };

  const renderModal = () => {
    return h(
      Modal,
      {
        visible: visible.value,
        'onUpdate:visible': (val: boolean) => {
          visible.value = val;
        },
        title: h(
          'div',
          { class: 'text-base font-medium' },
          isEdit.value ? '编辑通道' : '添加通道',
        ),
        width: 900,
        confirmLoading: loading.value,
        onOk: handleOk,
        onCancel: handleCancel,
        maskClosable: false,
        destroyOnClose: true,
        centered: true,
        class: 'channel-form-modal',
        wrapClassName: 'channel-form-modal-wrap',
        bodyStyle: { padding: '12px 0' },
      },
      {
        default: () =>
          h(
            Spin,
            {
              spinning: isEdit.value && loading.value,
              tip: '加载中...',
              class: 'w-full',
            },
            {
              default: () => renderForm(),
            },
          ),
      },
    );
  };

  return {
    formRef,
    formState,
    loading,
    show,
    renderModal,
  };
}
