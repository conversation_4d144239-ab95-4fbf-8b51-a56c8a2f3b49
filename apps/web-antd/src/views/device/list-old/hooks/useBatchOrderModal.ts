import type { FormInstance } from 'ant-design-vue';
import type { SelectValue } from 'ant-design-vue/es/select';

import { h, onMounted, ref } from 'vue';

import { Form, Input, message, Modal, Select } from 'ant-design-vue';

import { batchOrderDevice, getDeviceInfo, getPackageListPay } from '#/api';

const FormItem = Form.Item;

export interface UseBatchOrderModalOptions {
  onSuccess?: () => void;
}

interface BatchOrderFormState {
  packageId: null | number; // 充值套餐ID
  takeeffectType: null | number; // 生效类型
  deviceNos: string; // 设备编号列表
  packType: number; // 套餐类型
}

export function useBatchOrderModal(options: UseBatchOrderModalOptions = {}) {
  const visible = ref(false);
  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const packageLoading = ref(false);
  const deviceInfo = ref<any>(null);

  // 套餐选项
  const packageOptions = ref<{ label: string; value: number }[]>([]);

  // 生效类型选项
  const takeeffectTypeOptions = ref<{ label: string; value: number }[]>([
    { label: '立即生效', value: 1 },
    { label: '次月生效', value: 2 },
  ]);

  // 套餐类型选项
  const packTypeOptions = ref<{ label: string; value: number }[]>([
    { label: '基础套餐', value: 1 },
    { label: '加油包', value: 2 },
    { label: '加速宝', value: 3 },
    { label: '体验包', value: 4 },
    { label: '短信包', value: 5 },
    { label: '语音包', value: 6 },
  ]);

  const formState = ref<BatchOrderFormState>({
    packageId: null,
    takeeffectType: null,
    deviceNos: '',
    packType: 1, // 默认为基础套餐
  });

  // 获取设备信息
  const fetchDeviceInfo = async (deviceNo: string) => {
    try {
      const { data } = await getDeviceInfo(deviceNo);
      deviceInfo.value = data;
      return data;
    } catch (error) {
      console.error('获取设备信息失败:', error);
      message.error('获取设备信息失败');
      return null;
    }
  };

  // 获取套餐列表
  const loadPackageOptions = async () => {
    try {
      packageLoading.value = true;

      // 如果有设备信息，则根据设备ID和套餐类型获取套餐列表
      if (deviceInfo.value && deviceInfo.value.id) {
        const res = await getPackageListPay({
          deviceId: Number(deviceInfo.value.id),
          packType: formState.value.packType,
        });

        if (res.code === 1 && res.data) {
          packageOptions.value =
            res.data?.map((item: any) => ({
              label: `${item.name}(成本:${item.packageCost}元,售价:${item.packagePrice}元)`,
              value: item.id,
            })) || [];
        }
      }
    } catch (error) {
      console.error('获取套餐列表失败:', error);
      message.error('获取套餐列表失败');
    } finally {
      packageLoading.value = false;
    }
  };

  onMounted(() => {
    // 初始化时不加载套餐列表，等待设备信息获取后再加载
  });

  // 取消处理函数
  const handleCancel = () => {
    formState.value = {
      packageId: null,
      takeeffectType: null,
      deviceNos: '',
      packType: 1,
    };
    deviceInfo.value = null;
    packageOptions.value = [];
    visible.value = false;
  };

  const handleConfirm = async () => {
    try {
      await formRef.value?.validateFields();
      if (!formState.value.deviceNos.trim()) {
        message.warning('请输入设备编号');
        return;
      }

      loading.value = true;
      const res = await batchOrderDevice({
        packageId: formState.value.packageId,
        takeeffectType: formState.value.takeeffectType,
        deviceNos: formState.value.deviceNos
          .split(',')
          .map((no) => no.trim())
          .filter(Boolean)
          .join(','), // 转回字符串
      });
      if (res.code === 1) {
        message.success(res.msg);
      } else {
        message.error(res.msg);
      }

      handleCancel();
      options.onSuccess?.();
    } catch (error: any) {
      console.error('订购失败:', error);
      if (error.errorFields) {
        return;
      }
    } finally {
      loading.value = false;
    }
  };

  const showModal = async () => {
    visible.value = true;
  };

  const renderBatchOrderModal = () => {
    return h(
      Modal,
      {
        visible: visible.value,
        'onUpdate:visible': (val) => (visible.value = val),
        title: '批量订购',
        width: 600,
        maskClosable: false,
        okText: '确认订购',
        cancelText: '取消',
        confirmLoading: loading.value,
        onOk: handleConfirm,
        onCancel: handleCancel,
        destroyOnClose: true,
      },
      [
        h(
          Form,
          {
            ref: formRef,
            model: formState.value,
            labelCol: { span: 4 },
            wrapperCol: { span: 18 },
          },
          [
            // 设备编号输入
            h(
              FormItem,
              {
                label: '设备编号',
                name: 'deviceNos',
                rules: [{ required: true, message: '请输入设备编号' }],
              },
              [
                h(Input.TextArea, {
                  value: formState.value.deviceNos,
                  'onUpdate:value': (val: string) =>
                    (formState.value.deviceNos = val),
                  onBlur: async () => {
                    // 获取第一个设备编号
                    const firstDeviceNo = formState.value.deviceNos
                      .split(',')[0]
                      ?.trim();
                    if (firstDeviceNo) {
                      const data = await fetchDeviceInfo(firstDeviceNo);
                      if (data) {
                        // 获取到设备信息后，加载套餐列表
                        await loadPackageOptions();
                      }
                    }
                  },
                  placeholder:
                    '请输入设备编号，多个编号之间使用英文逗号(,)分隔',
                  rows: 6,
                }),
              ],
            ),

            // 套餐类型选择
            h(
              FormItem,
              {
                label: '套餐类型',
                name: 'packType',
                rules: [{ required: true, message: '请选择套餐类型' }],
              },
              [
                h(Select, {
                  value: formState.value.packType,
                  'onUpdate:value': (val: SelectValue) => {
                    if (typeof val === 'number') {
                      formState.value.packType = val;
                      // 当套餐类型改变时，重新加载套餐列表
                      if (deviceInfo.value && deviceInfo.value.id) {
                        formState.value.packageId = null; // 清空已选择的套餐
                        loadPackageOptions();
                      }
                    }
                  },
                  options: packTypeOptions.value,
                  placeholder: '请选择套餐类型',
                  style: { width: '100%' },
                }),
              ],
            ),

            // 充值套餐选择
            h(
              FormItem,
              {
                label: '充值套餐',
                name: 'packageId',
                rules: [{ required: true, message: '请选择充值套餐' }],
              },
              [
                h(Select, {
                  value: formState.value.packageId,
                  'onUpdate:value': (val: SelectValue) => {
                    if (typeof val === 'number' || val === null) {
                      formState.value.packageId = val;
                    }
                  },
                  placeholder: packageLoading.value
                    ? '加载中...'
                    : '请选择充值套餐',
                  options: packageOptions.value,
                  loading: packageLoading.value,
                  style: { width: '100%' },
                }),
              ],
            ),

            // 生效类型选择
            h(
              FormItem,
              {
                label: '生效类型',
                name: 'takeeffectType',
                rules: [{ required: true, message: '请选择生效类型' }],
              },
              [
                h(Select, {
                  value: formState.value.takeeffectType,
                  'onUpdate:value': (val: SelectValue) => {
                    if (typeof val === 'number' || val === null) {
                      formState.value.takeeffectType = val;
                    }
                  },
                  placeholder: '请选择生效类型',
                  options: takeeffectTypeOptions.value,
                  style: { width: '100%' },
                }),
              ],
            ),
          ],
        ),
      ],
    );
  };

  return {
    visible,
    showModal,
    renderBatchOrderModal,
  };
}
