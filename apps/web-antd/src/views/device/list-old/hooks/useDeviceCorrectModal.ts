import type { FormInstance } from 'ant-design-vue';

import { h, ref } from 'vue';

import { MdiDownload, MdiUpload } from '@vben/icons';

import { Button, Form, message, Modal, Upload } from 'ant-design-vue';

import {
  correctDeviceCardByImport,
  downloadDeviceCorrectTemplate,
} from '#/api/core/device';

export interface UseDeviceCorrectModalOptions {
  onSuccess?: () => void;
}

export function useDeviceCorrectModal(
  options: UseDeviceCorrectModalOptions = {},
) {
  const visible = ref(false);
  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const selectedFile = ref<File>();

  const handleBeforeUpload = (file: File) => {
    selectedFile.value = file;
    return false;
  };

  const handleDownloadTemplate = async () => {
    try {
      const res = await downloadDeviceCorrectTemplate();
      const contentDisposition = res.headers?.['content-disposition'];
      let filename = '设备卡号矫正模板.xlsx';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.+)/);
        if (filenameMatch) {
          filename = decodeURIComponent(filenameMatch[1].replaceAll('"', ''));
        }
      }

      const blob = new Blob([res.data], { type: res.headers['content-type'] });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.download = filename;

      document.body.append(link);
      link.click();

      setTimeout(() => {
        link.remove();
        window.URL.revokeObjectURL(url);
      }, 100);

      message.success('下载成功');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  const handleConfirm = async () => {
    if (!selectedFile.value) {
      message.warning('请选择要导入的文件');
      return;
    }

    Modal.confirm({
      title: '矫正确认',
      content: '确定要矫正导入文件中的卡号吗？',
      type: 'warning',
      async onOk() {
        try {
          loading.value = true;
          const res = await correctDeviceCardByImport(selectedFile.value!);
          if (res.code === 1) {
            handleCancel();
            options.onSuccess?.();
          } else {
            message.error(res.msg || '矫正失败');
          }
        } catch (error) {
          console.error('矫正失败:', error);
          message.error('矫正失败');
        } finally {
          loading.value = false;
        }
      },
    });
  };

  const handleCancel = () => {
    selectedFile.value = undefined;
    visible.value = false;
  };

  const renderCorrectModal = () => {
    return h(
      Modal,
      {
        visible: visible.value,
        'onUpdate:visible': (val: boolean) => (visible.value = val),
        title: '矫正卡号',
        maskClosable: false,
        okText: '确认矫正',
        cancelText: '取消',
        confirmLoading: loading.value,
        onOk: handleConfirm,
        onCancel: handleCancel,
        destroyOnClose: true,
      },
      {
        default: () =>
          h(
            Form,
            {
              ref: formRef,
              layout: 'vertical',
              style: { width: '100%' },
            },
            {
              default: () => [
                h(
                  'div',
                  { class: 'text-gray-500 text-sm mb-2' },
                  '支持 .xlsx、.xls、.csv 格式文件',
                ),
                h('div', { class: 'flex flex-wrap gap-4 mb-4' }, [
                  h(
                    Upload,
                    {
                      accept: '.xlsx,.xls,.csv',
                      showUploadList: false,
                      beforeUpload: handleBeforeUpload,
                      maxCount: 1,
                    },
                    {
                      default: () =>
                        h(
                          Button,
                          { type: 'primary' },
                          {
                            default: () =>
                              h('div', { class: 'flex items-center' }, [
                                h(MdiUpload, { class: 'mr-1' }),
                                '选择文件',
                              ]),
                          },
                        ),
                    },
                  ),
                  h(
                    Button,
                    {
                      onClick: handleDownloadTemplate,
                    },
                    {
                      default: () =>
                        h('div', { class: 'flex items-center' }, [
                          h(MdiDownload, { class: 'mr-1' }),
                          '下载模板',
                        ]),
                    },
                  ),
                ]),
                h('div', { class: 'p-4 rounded' }, [
                  h('div', { class: 'text-gray-600 mb-2' }, '已选择文件：'),
                  h(
                    'div',
                    { class: 'text-gray-800' },
                    selectedFile.value?.name || '暂无文件',
                  ),
                ]),
              ],
            },
          ),
      },
    );
  };

  return {
    visible,
    showModal: () => (visible.value = true),
    renderCorrectModal,
  };
}
