import type { FormInstance } from 'ant-design-vue';

import { h, ref } from 'vue';

import { MdiDownload, MdiUpload } from '@vben/icons';

import {
  Button,
  Form,
  FormItem,
  message,
  Modal,
  Select,
  Upload,
} from 'ant-design-vue';

import {
  downloadDeviceImportTemplate,
  getDeviceChannelOptions,
  getDeviceRule,
  importDeviceApi,
} from '#/api';

interface ImportDeviceFormState {
  deviceConfigId: null | number;
  devicePackageGroupId: null | number;
}

export interface UseImportDeviceOptions {
  onSuccess?: () => void;
}

export function useImportDevice(options: UseImportDeviceOptions = {}) {
  const visible = ref(false);
  const formRef = ref<FormInstance>();
  const selectedFile = ref<File>();
  const deviceChannelOptions = ref<{ label: string; value: number }[]>([]);
  const devicePackageOptions = ref<{ label: string; value: number }[]>([]);

  const formState = ref<ImportDeviceFormState>({
    deviceConfigId: undefined,
    devicePackageGroupId: undefined,
  });

  const handleConfirm = async () => {
    try {
      await formRef.value?.validateFields([
        'deviceConfigId',
        'devicePackageGroupId',
      ]);
      if (!selectedFile.value) {
        message.warning('请选择要导入的文件');
        return;
      }
      const formData = new FormData();
      formData.append('file', selectedFile.value);
      formData.append(
        'deviceConfigId',
        formState.value.deviceConfigId.toString(),
      );
      if (formState.value.devicePackageGroupId) {
        formData.append(
          'devicePackageGroupId',
          formState.value.devicePackageGroupId.toString(),
        );
      }

      const res = await importDeviceApi(formData);
      if (res.code === 1) {
        message.success(res.msg || '导入任务已提交');
        handleCancel();
        options.onSuccess?.();
      } else {
        message.error(res.msg || '导入失败');
      }
    } catch (error) {
      console.error('导入失败:', error);
      if (error.errorFields) {
        return;
      }
      message.error('导入失败');
    }
  };

  const handleCancel = () => {
    formState.value = {
      deviceConfigId: null,
      devicePackageGroupId: null,
    };
    selectedFile.value = undefined;
    visible.value = false;
  };
  const fetchOptions = async () => {
    try {
      const [channelRes, ruleRes] = await Promise.all([
        getDeviceChannelOptions(),
        getDeviceRule(),
      ]);
      deviceChannelOptions.value = channelRes.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));
      devicePackageOptions.value = ruleRes.data.rows.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    } catch (error) {
      console.error('获取选项失败:', error);
    }
  };

  const handleBeforeUpload = (file: File) => {
    selectedFile.value = file;
    return false;
  };

  const showModal = async () => {
    visible.value = true;
    await fetchOptions();
  };

  const showImportModal = () => {
    return h(
      Modal,
      {
        visible: visible.value,
        'onUpdate:visible': (val) => (visible.value = val),
        title: '设备入库',
        width: 600,
        maskClosable: false,
        okText: '确认入库',
        cancelText: '取消',
        onOk: handleConfirm,
        onCancel: handleCancel,
        destroyOnClose: true,
      },
      [
        h(
          Form,
          {
            ref: formRef,
            model: formState.value,
            class: 'space-y-4',
          },
          [
            // 通道选择
            h(
              FormItem,
              {
                label: '通道名称',
                name: 'deviceConfigId',
                rules: [{ required: true, message: '请选择通道' }],
              },
              [
                h(Select, {
                  value: formState.value.deviceConfigId,
                  'onUpdate:value': (val) =>
                    (formState.value.deviceConfigId = val),
                  placeholder: '请选择通道',
                  options: deviceChannelOptions.value,
                  style: { width: '100%' },
                }),
              ],
            ),

            // 套餐规则选择
            h(
              FormItem,
              {
                label: '套餐规则',
                name: 'devicePackageGroupId',
                rules: [{ required: true, message: '请选择套餐规则' }],
              },
              [
                h(Select, {
                  value: formState.value.devicePackageGroupId,
                  'onUpdate:value': (val) =>
                    (formState.value.devicePackageGroupId = val),
                  placeholder: '请选择套餐规则',
                  options: devicePackageOptions.value,
                  style: { width: '100%' },
                }),
              ],
            ),

            // 文件格式提示
            h(
              'div',
              { class: 'text-gray-500 text-sm mb-2' },
              '支持 .xlsx、.xls、.csv 格式文件',
            ),

            // 上传和下载按钮区域
            h('div', { class: 'flex flex-wrap gap-4 mb-4' }, [
              h(
                Upload,
                {
                  accept: '.xlsx,.xls,.csv',
                  showUploadList: false,
                  beforeUpload: handleBeforeUpload,
                  maxCount: 1,
                },
                [
                  h(Button, { type: 'primary' }, [
                    h('div', { class: 'flex items-center' }, [
                      h(MdiUpload, { class: 'mr-1' }),
                      '选择文件',
                    ]),
                  ]),
                ],
              ),
              h(
                Button,
                {
                  onClick: handleDownloadTemplate,
                },
                [
                  h('div', { class: 'flex items-center' }, [
                    h(MdiDownload, { class: 'mr-1' }),
                    '下载模板',
                  ]),
                ],
              ),
            ]),

            // 已选择文件显示
            h('div', { class: 'p-4 rounded' }, [
              h('div', { class: 'text-gray-600 mb-2' }, '已选择文件：'),
              h(
                'div',
                { class: 'text-gray-800' },
                selectedFile.value?.name || '暂无文件',
              ),
            ]),
          ],
        ),
      ],
    );
  };

  const handleDownloadTemplate = async () => {
    try {
      const res = await downloadDeviceImportTemplate();
      const contentDisposition = res.headers?.['content-disposition'];
      let filename = '分配卡片模板.xlsx';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.+)/);
        if (filenameMatch) {
          filename = decodeURIComponent(filenameMatch[1].replaceAll('"', ''));
        }
      }

      const blob = new Blob([res.data], { type: res.headers['content-type'] });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.download = filename;

      document.body.append(link);
      link.click();

      setTimeout(() => {
        link.remove();
        window.URL.revokeObjectURL(url);
      }, 100);

      message.success('下载成功');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  return {
    showImportModal,
    showModal,
    visible,
  };
}
