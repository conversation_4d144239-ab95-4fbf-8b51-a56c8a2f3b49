import type { FormInstance } from 'ant-design-vue';

import { h, ref } from 'vue';

import { MdiDownload, MdiUpload } from '@vben/icons';

import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Tabs,
  Upload,
} from 'ant-design-vue';

import {
  downloadDeviceAssignTemplate,
  importRecycleDevice,
  linkRecycleDevice,
  separatorRecycleDevice,
} from '#/api';

const FormItem = Form.Item;
const TabPane = Tabs.TabPane;

export interface UseDeviceRecycleOptions {
  onSuccess?: () => void;
}

interface DeviceRecycleFormState {
  startDeviceNo?: string; // 连号-开始设备号
  endDeviceNo?: string; // 连号-结束设备号
  deviceNoList?: string; // 不规则-设备号列表
}

export function useDeviceRecycle(options: UseDeviceRecycleOptions = {}) {
  const visible = ref(false);
  const activeTab = ref('serial'); // serial | irregular | import
  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const selectedFile = ref<File>();

  const formState = ref<DeviceRecycleFormState>({});

  const handleBeforeUpload = (file: File) => {
    selectedFile.value = file;
    return false;
  };

  const handleDownloadTemplate = async () => {
    try {
      const res = await downloadDeviceAssignTemplate();
      const contentDisposition = res.headers?.['content-disposition'];
      let filename = '设备回收模板.xlsx';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.+)/);
        if (filenameMatch) {
          filename = decodeURIComponent(filenameMatch[1].replaceAll('"', ''));
        }
      }
      const blob = new Blob([res.data], { type: res.headers['content-type'] });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', filename);

      document.body.append(link);
      link.click();

      setTimeout(() => {
        link.remove();
        window.URL.revokeObjectURL(url);
      }, 100);

      message.success('下载成功');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  const handleConfirm = async () => {
    try {
      switch (activeTab.value) {
        case 'import': {
          if (!selectedFile.value) {
            message.warning('请选择要导入的文件');
            return;
          }

          Modal.confirm({
            title: '文件回收确认',
            content: '确定要回收导入文件中的设备吗？',
            type: 'warning',
            async onOk() {
              try {
                loading.value = true;
                const formData = new FormData();
                formData.append('file', selectedFile.value);
                await importRecycleDevice(formData);
                handleCancel();
                options.onSuccess?.();
              } catch (error) {
                console.error('回收失败:', error);
                message.error('回收失败');
              } finally {
                loading.value = false;
              }
            },
          });
          break;
        }

        case 'irregular': {
          if (!formState.value.deviceNoList?.trim()) {
            message.warning('请输入要回收的设备号');
            return;
          }

          Modal.confirm({
            title: '不规则回收确认',
            content: '确定要回收这些设备吗？',
            type: 'warning',
            async onOk() {
              try {
                loading.value = true;
                const fromdata = new FormData();
                fromdata.append(
                  'cardNos',
                  formState.value.deviceNoList
                    .split('\n')
                    .map((no) => no.trim())
                    .filter(Boolean),
                );
                await separatorRecycleDevice(fromdata);
                handleCancel();
                options.onSuccess?.();
              } catch (error) {
                console.error('回收失败:', error);
                message.error('回收失败');
              } finally {
                loading.value = false;
              }
            },
          });
          break;
        }

        case 'serial': {
          try {
            await formRef.value?.validateFields([
              'startDeviceNo',
              'endDeviceNo',
            ]);
          } catch {
            return;
          }

          Modal.confirm({
            title: '连号回收确认',
            content: '确定要回收这些设备吗？',
            type: 'warning',
            async onOk() {
              try {
                loading.value = true;
                const fromdata = new FormData();
                fromdata.append('startDeviceNo', formState.value.startDeviceNo);
                fromdata.append('endDeviceNo', formState.value.endDeviceNo);
                await linkRecycleDevice(fromdata);
                handleCancel();
                options.onSuccess?.();
              } catch (error) {
                console.error('回收失败:', error);
                message.error('回收失败');
              } finally {
                loading.value = false;
              }
            },
          });
          break;
        }
      }
    } catch (error) {
      console.error('回收失败:', error);
      if (error.errorFields) {
        return;
      }
      message.error('回收失败');
    }
  };

  const handleCancel = () => {
    formState.value = {};
    selectedFile.value = undefined;
    visible.value = false;
  };

  const renderSerialForm = () => {
    return h('div', { class: 'space-y-4' }, [
      h(
        FormItem,
        {
          label: '开始设备号',
          name: 'startDeviceNo',
          rules: [{ required: true, message: '请输入开始设备号' }],
        },
        () =>
          h(Input, {
            value: formState.value.startDeviceNo,
            'onUpdate:value': (val: string) =>
              (formState.value.startDeviceNo = val),
            placeholder: '请输入开始设备号',
          }),
      ),
      h(
        FormItem,
        {
          label: '结束设备号',
          name: 'endDeviceNo',
          rules: [{ required: true, message: '请输入结束设备号' }],
        },
        () =>
          h(Input, {
            value: formState.value.endDeviceNo,
            'onUpdate:value': (val: string) =>
              (formState.value.endDeviceNo = val),
            placeholder: '请输入结束设备号',
          }),
      ),
    ]);
  };

  const renderIrregularForm = () => {
    return h('div', { class: 'space-y-4' }, [
      h(
        FormItem,
        {
          label: '设备号列表',
          name: 'deviceNoList',
        },
        () =>
          h(Input.TextArea, {
            value: formState.value.deviceNoList,
            'onUpdate:value': (val: string) =>
              (formState.value.deviceNoList = val),
            rows: 10,
            placeholder: '请输入设备号，每行一个',
          }),
      ),
    ]);
  };

  const renderImportForm = () => {
    return h('div', { class: 'space-y-4' }, [
      h(
        'div',
        { class: 'text-gray-500 text-sm mb-2' },
        '支持 .xlsx、.xls、.csv 格式文件',
      ),
      h('div', { class: 'flex flex-wrap gap-4 mb-4' }, [
        h(
          Upload,
          {
            accept: '.xlsx,.xls,.csv',
            showUploadList: false,
            beforeUpload: handleBeforeUpload,
            maxCount: 1,
          },
          {
            default: () =>
              h(
                Button,
                { type: 'primary' },
                {
                  default: () =>
                    h('div', { class: 'flex items-center' }, [
                      h(MdiUpload, { class: 'mr-1' }),
                      '选择文件',
                    ]),
                },
              ),
          },
        ),
        h(
          Button,
          {
            onClick: handleDownloadTemplate,
          },
          {
            default: () =>
              h('div', { class: 'flex items-center' }, [
                h(MdiDownload, { class: 'mr-1' }),
                '下载模板',
              ]),
          },
        ),
      ]),
      h('div', { class: 'p-4 rounded' }, [
        h('div', { class: 'text-gray-600 mb-2' }, '已选择文件：'),
        h(
          'div',
          { class: 'text-gray-800' },
          selectedFile.value?.name || '暂无文件',
        ),
      ]),
    ]);
  };

  const renderDeviceRecycleModal = () => {
    return h(
      Modal,
      {
        visible: visible.value,
        'onUpdate:visible': (val: boolean) => (visible.value = val),
        title: '设备回收',
        width: 600,
        maskClosable: false,
        okText: '确认回收',
        cancelText: '取消',
        confirmLoading: loading.value,
        onOk: handleConfirm,
        onCancel: handleCancel,
        destroyOnClose: true,
      },
      {
        default: () =>
          h(
            Form,
            {
              ref: formRef,
              model: formState.value,
              layout: 'vertical',
              style: { width: '100%' },
            },
            {
              default: () => [
                h(
                  Tabs,
                  {
                    activeKey: activeTab.value,
                    'onUpdate:activeKey': (val: string) =>
                      (activeTab.value = val),
                  },
                  {
                    default: () => [
                      h(
                        TabPane,
                        { key: 'serial', tab: '连号回收' },
                        {
                          default: renderSerialForm,
                        },
                      ),
                      h(
                        TabPane,
                        { key: 'irregular', tab: '不规则回收' },
                        {
                          default: renderIrregularForm,
                        },
                      ),
                      h(
                        TabPane,
                        { key: 'import', tab: '文件回收' },
                        {
                          default: renderImportForm,
                        },
                      ),
                    ],
                  },
                ),
              ],
            },
          ),
      },
    );
  };

  return {
    visible,
    showModal: () => (visible.value = true),
    renderDeviceRecycleModal,
    loading,
  };
}
