<script setup lang="ts">
import type { ChannelItem } from '#/api/core/channel';

import { computed, h, onMounted, ref } from 'vue';

import { MdiFileEdit, MdiPackage, MdiPlus } from '@vben/icons';

import { Card, Form, Input, message, Modal, Radio } from 'ant-design-vue';

import { getDeviceList } from '#/api';
import {
  changeNetwork,
  deleteDevice,
  deviceControl,
  updateDeviceInfo,
  updateDeviceWifi,
  updateMasterCard,
  updateWifiHide,
} from '#/api/core/device';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

// import ViewModal from './components/view-modal.vue';
import {
  advancedSerachItems,
  basicSerachItems,
  createColumns,
  statusOptions,
} from './config';
import { useBatchOrderModal } from './hooks/useBatchOrderModal';
import { useDeviceAssign } from './hooks/useDeviceAssign';
import { useDeviceCorrectModal } from './hooks/useDeviceCorrectModal';
import { useDeviceRecycle } from './hooks/useDeviceRecycle';
import { useImportDevice } from './hooks/useImportDevice';

const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable<ChannelItem, { page: number; pageSize: number }>({
  api: getDeviceList,
  defaultParams: {},
});

// 基础搜索项配置
const basicItems = ref([...basicSerachItems]);

// 高级搜索项配置
const advancedItems = ref([...advancedSerachItems]);

// 处理删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该设备吗？此操作不可恢复！',
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        const res = await deleteDevice(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList(); // 刷新列表
        } else {
          throw new Error(res.msg || '删除失败');
        }
      } catch (error) {
        message.error(error instanceof Error ? error.message : '删除失败');
      }
    },
  });
};
// 使用 computed 来动态更新配置项
const computedBasicItems = computed(() => {
  return basicItems.value;
});

// 组件挂载时加载数据
onMounted(() => {
  // fetchUserOptions();
  getList();
});

// 更新信息
const handleUpdateInfo = async (record: any) => {
  try {
    // 使用useTable提供的loading状态
    loading.value = true;
    const res = await updateDeviceInfo(record.id);
    if (res.code === 1) {
      message.success(res.msg || '更新成功');
      getList(); // 刷新列表
    } else {
      throw new Error(res.msg || '更新失败');
    }
  } catch (error) {
    message.error(error instanceof Error ? error.message : '更新失败');
  } finally {
    // 无论成功失败，都清除loading状态
    loading.value = false;
  }
};

// 修改密码
const handleUpdatePassword = (record: any) => {
  const formRef = ref();
  const formState = ref({
    type: '1',
    wifiName: '',
    wifiPwd: '',
  });

  Modal.confirm({
    title: '修改WIFI密码',
    width: 500,
    content: () =>
      h('div', [
        h(
          Form,
          {
            ref: formRef,
            model: formState.value,
            labelCol: { span: 6 },
            wrapperCol: { span: 16 },
          },
          [
            h(
              Form.Item,
              {
                label: 'WIFI类型',
                name: 'type',
                rules: [{ required: true, message: '请选择WIFI类型' }],
              },
              () =>
                h(Radio.Group, {
                  value: formState.value.type,
                  'onUpdate:value': (val: string) => {
                    formState.value.type = val;
                  },
                  options: [
                    { label: '4G WIFI', value: '1' },
                    { label: '5G WIFI', value: '2' },
                  ],
                }),
            ),
            h(
              Form.Item,
              {
                label: 'WIFI名称',
                name: 'wifiName',
                rules: [{ required: true, message: '请输入WIFI名称' }],
              },
              h(Input, {
                'onUpdate:value': (val: string) =>
                  (formState.value.wifiName = val),
                placeholder: '请输入WIFI名称',
              }),
            ),
            h(
              Form.Item,
              {
                label: 'WIFI密码',
                name: 'wifiPwd',
                rules: [{ required: true, message: '请输入WIFI密码' }],
              },
              h(Input, {
                'onUpdate:value': (val: string) =>
                  (formState.value.wifiPwd = val),
                placeholder: '请输入WIFI密码',
              }),
            ),
          ],
        ),
      ]),
    async onOk() {
      try {
        await formRef.value?.validate();
        const res = await updateDeviceWifi({
          deviceId: record.id,
          ...formState.value,
        });
        if (res.code === 1) {
          message.success('修改成功');
          getList(); // 刷新列表
        } else {
          throw new Error(res.msg || '修改失败');
        }
      } catch (error) {
        message.error(error instanceof Error ? error.message : '修改失败');
        return Promise.reject();
      }
    },
  });
};

// 切换隐藏状态
const handleToggleHide = (record: any) => {
  const formRef = ref();
  const formState = ref({
    type: '1',
    hide: '2', // 默认显示
  });

  Modal.confirm({
    title: '修改WIFI隐藏状态',
    width: 500,
    content: () =>
      h('div', [
        h(
          Form,
          {
            ref: formRef,
            model: formState.value,
            labelCol: { span: 6 },
            wrapperCol: { span: 16 },
          },
          [
            h(
              Form.Item,
              {
                label: 'WIFI类型',
                name: 'type',
                rules: [{ required: true, message: '请选择WIFI类型' }],
              },
              () =>
                h(Radio.Group, {
                  value: formState.value.type,
                  'onUpdate:value': (val: string) => {
                    formState.value.type = val;
                  },
                  options: [
                    { label: '4G WIFI', value: '1' },
                    { label: '5G WIFI', value: '2' },
                  ],
                }),
            ),
            h(
              Form.Item,
              {
                label: '隐藏状态',
                name: 'hide',
                rules: [{ required: true, message: '请选择隐藏状态' }],
              },
              () =>
                h(Radio.Group, {
                  value: formState.value.hide,
                  'onUpdate:value': (val: string) => {
                    formState.value.hide = val;
                  },
                  options: [
                    { label: '隐藏', value: '1' },
                    { label: '显示', value: '2' },
                  ],
                }),
            ),
          ],
        ),
      ]),
    async onOk() {
      try {
        await formRef.value?.validate();
        const res = await updateWifiHide({
          deviceId: record.id,
          ...formState.value,
        });
        if (res.code === 1) {
          message.success('修改成功');
          getList(); // 刷新列表
        } else {
          throw new Error(res.msg || '修改失败');
        }
      } catch (error) {
        message.error(error instanceof Error ? error.message : '修改失败');
        return Promise.reject();
      }
    },
  });
};

// 远程控制
const handleRemoteControl = (record: any) => {
  const formRef = ref();
  const formState = ref({
    type: '2', // 默认重启
  });

  Modal.confirm({
    title: '远程控制',
    width: 500,
    content: () =>
      h('div', [
        h(
          Form,
          {
            ref: formRef,
            model: formState.value,
            labelCol: { span: 6 },
            wrapperCol: { span: 16 },
          },
          [
            h(
              Form.Item,
              {
                label: '控制类型',
                name: 'type',
                rules: [{ required: true, message: '请选择控制类型' }],
              },
              () =>
                h(Radio.Group, {
                  value: formState.value.type,
                  'onUpdate:value': (val: string) => {
                    formState.value.type = val;
                  },
                  options: [
                    { label: '关机', value: '1' },
                    { label: '重启', value: '2' },
                    { label: '出厂设置', value: '3' },
                  ],
                }),
            ),
          ],
        ),
      ]),
    async onOk() {
      try {
        await formRef.value?.validate();
        const res = await deviceControl({
          deviceId: record.id,
          ...formState.value,
        });
        if (res.code === 1) {
          message.success('操作成功');
          getList(); // 刷新列表
        } else {
          throw new Error(res.msg || '操作失败');
        }
      } catch (error) {
        message.error(error instanceof Error ? error.message : '操作失败');
        return Promise.reject();
      }
    },
  });
};

// 切换网络
const handleSwitchNetwork = (record: any) => {
  const formRef = ref();
  const formState = ref({
    switching: '1', // 默认电信
  });

  Modal.confirm({
    title: '切换网络',
    width: 500,
    content: () =>
      h('div', [
        h(
          Form,
          {
            ref: formRef,
            model: formState.value,
            labelCol: { span: 6 },
            wrapperCol: { span: 16 },
          },
          [
            h(
              Form.Item,
              {
                label: '目标网络',
                name: 'switching',
                rules: [{ required: true, message: '请选择目标网络' }],
              },
              () =>
                h(Radio.Group, {
                  value: formState.value.switching,
                  'onUpdate:value': (val: string) => {
                    formState.value.switching = val;
                  },
                  options: [
                    { label: '中国电信', value: '1' },
                    { label: '中国联通', value: '2' },
                    { label: '中国移动', value: '3' },
                    { label: '中国广电', value: '4' },
                  ],
                }),
            ),
          ],
        ),
      ]),
    async onOk() {
      try {
        await formRef.value?.validate();
        const res = await changeNetwork({
          deviceId: record.id,
          ...formState.value,
        });
        if (res.code === 1) {
          message.success('切换成功');
          getList(); // 刷新列表
        } else {
          throw new Error(res.msg || '切换失败');
        }
      } catch (error) {
        message.error(error instanceof Error ? error.message : '切换失败');
        return Promise.reject();
      }
    },
  });
};

const handleSwitchMainCard = (record: any) => {
  const formRef = ref();
  const formState = ref({
    num: '1', // 默认卡槽1
  });

  Modal.confirm({
    title: '切换主卡',
    width: 500,
    content: () =>
      h('div', [
        h(
          Form,
          {
            ref: formRef,
            model: formState.value,
            labelCol: { span: 6 },
            wrapperCol: { span: 16 },
          },
          [
            h(
              Form.Item,
              {
                label: '目标卡槽',
                name: 'num',
                rules: [{ required: true, message: '请选择目标卡槽' }],
              },
              () =>
                h(Radio.Group, {
                  value: formState.value.num,
                  'onUpdate:value': (val: string) => {
                    formState.value.num = val;
                  },
                  options: [
                    { label: '卡槽1', value: '1' },
                    { label: '卡槽2', value: '2' },
                    { label: '卡槽3', value: '3' },
                    { label: '卡槽4', value: '4' },
                  ],
                }),
            ),
          ],
        ),
      ]),
    async onOk() {
      try {
        await formRef.value?.validate();
        const res = await updateMasterCard({
          deviceId: record.id,
          ...formState.value,
        });
        if (res.code === 1) {
          message.success('切换成功');
          getList(); // 刷新列表
        } else {
          throw new Error(res.msg || '切换失败');
        }
      } catch (error) {
        message.error(error instanceof Error ? error.message : '切换失败');
        return Promise.reject();
      }
    },
  });
};

// 创建表格列配置
const columns = computed(() =>
  createColumns({
    onUpdateInfo: handleUpdateInfo,
    onUpdatePassword: handleUpdatePassword,
    onToggleHide: handleToggleHide,
    onRemoteControl: handleRemoteControl,
    onSwitchNetwork: handleSwitchNetwork,
    onSwitchMainCard: handleSwitchMainCard,
    onDelete: handleDelete,
  }),
);

// 在 setup 中
// const { showImportModal } = useImportDevice({
//   channelOptions: deviceChannelOptions,
//   ruleOptions: deviceRuleOptions,
//   onSuccess: () => {
//     getList();
//   },
// });

const { showImportModal, showModal: showImportDeviceModal } = useImportDevice({
  onSuccess: () => {
    getList();
  },
});
const { renderBatchOrderModal, showModal: showBatchOrderModal } =
  useBatchOrderModal({
    onSuccess: () => {
      getList();
    },
  });
const { renderCorrectModal, showModal: showCorrectModal } =
  useDeviceCorrectModal({
    onSuccess: () => {
      getList();
    },
  });
const { renderDeviceAssignModal, showModal: showDeviceAssignModal } =
  useDeviceAssign({
    onSuccess: () => {
      getList();
    },
  });
const { renderDeviceRecycleModal, showModal: showDeviceRecycleModal } =
  useDeviceRecycle({
    onSuccess: () => {
      getList();
    },
  });

// 处理添加
const handleAdd = () => {
  // showImportModal();
  showImportDeviceModal();
};

const customButtons = computed(() => {
  const buttons = [
    {
      key: 'addRule',
      text: '导入设备',
      icon: h(MdiPlus),
      onClick: handleAdd,
      type: 'primary',
    },
    // 批量订购
    {
      key: 'batchOrder',
      text: '批量订购',
      icon: h(MdiPackage),
      onClick: () => {
        showBatchOrderModal();
      },
    },
    // 矫正号码
    {
      key: 'correctNumber',
      text: '矫正号码',
      icon: h(MdiFileEdit),
      onClick: () => {
        showCorrectModal();
      },
    },
    // 分配设备
    {
      key: 'distribution',
      text: '分配设备',
      icon: h(MdiFileEdit),
      onClick: () => {
        showDeviceAssignModal();
      },
    },
    // 设备回收

    {
      key: 'recycling',
      text: '设备回收',
      icon: h(MdiFileEdit),
      onClick: () => {
        showDeviceRecycleModal();
      },
    },
  ];
  return buttons;
});
</script>

<template>
  <div class="channel-list p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="computedBasicItems"
        :advanced-items="advancedItems"
        :loading="loading"
        @search="handleSearch"
        @reset="handleReset"
        :custom-buttons="customButtons"
      />

      <!-- 表格组件 -->
      <BasicTable
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        :status-options="statusOptions"
      />
    </Card>

    <component :is="showImportModal()" />
    <component :is="renderBatchOrderModal()" />
    <component :is="renderCorrectModal()" />
    <component :is="renderDeviceAssignModal()" />
    <component :is="renderDeviceRecycleModal()" />
  </div>
</template>

<style lang="less" scoped>
.channel-list {
  background-color: var(--background-deep);
  min-height: 100%;
}
</style>
