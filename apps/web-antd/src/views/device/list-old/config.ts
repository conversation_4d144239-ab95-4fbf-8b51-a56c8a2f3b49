import { h } from 'vue';

import { Button, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  getDeviceChannelOptions,
  getDeviceRule,
  getUserOptionsApi,
} from '#/api';
import { statusOptions as deviceStatusOptions } from '#/constants/card-status';
import { networkOptions } from '#/constants/device-status';
// import { statusOptions } from '#/constants/card-status';

// 添加类型定义
export interface ActionHandlers {
  onUpdateInfo: (record: any) => void;
  onUpdatePassword: (record: any) => void;
  onToggleHide: (record: any) => void;
  onRemoteControl: (record: any) => void;
  onSwitchNetwork: (record: any) => void;
  onSwitchMainCard: (record: any) => void;
  onDelete: (record: any) => void;
  loading?: boolean; // 添加全局loading状态
}

// 设备状态映射
export const deviceStatusMap: Record<number, { color: string; text: string }> =
  {
    1: { text: '未知', color: '' },
    2: { text: '待激活', color: 'warning' },
    3: { text: '已激活', color: 'success' },
    4: { text: '已停机', color: 'error' },
    5: { text: '预销号', color: 'error' },
    6: { text: '已销号', color: 'error' },
    7: { text: '已拆机', color: 'error' },
    8: { text: '测试期', color: 'processing' },
    9: { text: '沉默期', color: 'warning' },
    10: { text: '库存', color: 'default' },
    11: { text: '已过户', color: 'processing' },
    12: { text: '异常', color: 'error' },
  };

// 实名状态映射
export const nameStatusMap: Record<number, { color: string; text: string }> = {
  1: { text: '未知', color: '' },
  2: { text: '已实名', color: 'success' },
  3: { text: '未实名', color: 'error' },
};

// 运营商映射
export const networkMap: Record<number, string> = {
  0: '未知',
  1: '中国电信',
  2: '中国联通',
  3: '中国移动',
  4: '中国广电',
};

// 获取设备状态文本和颜色
export const getDeviceStatusInfo = (status: number) => {
  return deviceStatusMap[status] || { text: '未知', color: '' };
};

// 获取实名状态文本和颜色
export const getNameStatusInfo = (status: number) => {
  return nameStatusMap[status] || { text: '未知', color: '' };
};

// 获取网络名称
export const getNetworkName = (network: number) => {
  return networkMap[network] || '未知';
};

// 基础搜索配置
export const basicSerachItems = [
  {
    label: '设备号',
    field: 'deviceNo',
    component: 'Input' as const,
    props: {
      placeholder: '请输入设备号',
      allowClear: true,
    },
  },
  {
    label: '设备规则',
    field: 'devicePackageGroupIds',
    component: 'Select' as const,
    remote: {
      api: getDeviceRule,
      transform: (data: any) =>
        data.data.rows.map((item: any) => ({
          label: `${item.name}`, // 组合显示
          value: item.id,
        })),
    },
    props: {
      placeholder: '请选择设备规则',
      allowClear: true,
      // mode: 'multiple', // 支持多选
      optionFilterProp: 'label',
    },
  },
  {
    label: '设备通道',
    field: 'deviceConfigIds',
    component: 'Select' as const,
    remote: {
      api: getDeviceChannelOptions,
      transform: (data: any) =>
        data.data.map((item: any) => ({
          label: `${item.name}`, // 组合显示
          value: item.id,
        })),
    },
    props: {
      placeholder: '请选择设备通道',
      allowClear: true,
      optionFilterProp: 'label',
      // mode: 'multiple', // 支持多选
    },
    // options: [],
  },
  {
    label: '归属代理',
    field: 'userIds',
    component: 'Select' as const,
    remote: {
      api: getUserOptionsApi,
      transform: (data: any) =>
        data.data.map((item: any) => ({
          label: `${item.name}`, // 组合显示
          value: item.id,
        })),
    },
    props: {
      placeholder: '请选择归属代理',
      allowClear: true,
      optionFilterProp: 'label',
    },
  },
  {
    label: '设备状态',
    field: 'status',
    component: 'Select' as const,
    props: {
      placeholder: '请选择设备状态',
      allowClear: true,
    },
    options: deviceStatusOptions,
  },
  {
    label: '实名状态',
    field: 'nameStatus',
    component: 'Select' as const,
    props: {
      placeholder: '请选择实名状态',
      allowClear: true,
    },
    options: [
      { label: '未知', value: 1 },
      { label: '已实名', value: 2 },
      { label: '未实名', value: 3 },
    ],
  },
];

// 高级搜索配置
export const advancedSerachItems = [
  {
    label: '设备状态',
    items: [
      {
        label: '在线状态',
        field: 'presence',
        component: 'Select' as const,
        props: {
          placeholder: '请选择在线状态',
          allowClear: true,
        },
        options: [
          { label: '不支持', value: 1 },
          { label: '在线', value: 2 },
          { label: '离线', value: 3 },
        ],
      },
      {
        label: '当前网络',
        field: 'currentNetwork',
        component: 'Select' as const,
        props: {
          placeholder: '请选择当前网络',
          allowClear: true,
        },
        options: networkOptions,
      },
      {
        label: '开机状态',
        field: 'powerOnStatus',
        component: 'Select' as const,
        props: {
          placeholder: '请选择开机状态',
          allowClear: true,
        },
        options: [
          { label: '不支持', value: 1 },
          { label: '开机', value: 2 },
          { label: '关机', value: 3 },
        ],
      },
    ],
  },
  {
    label: '基本信息',
    items: [
      {
        label: 'IMEI',
        field: 'imeiNo',
        component: 'Input' as const,
        props: {
          placeholder: '请输入IMEI',
          allowClear: true,
        },
      },
      {
        label: '备注',
        field: 'notes',
        component: 'Input' as const,
        props: {
          placeholder: '请输入备注',
          allowClear: true,
        },
      },
      {
        label: '手机号',
        field: 'phone',
        component: 'Input' as const,
        props: {
          placeholder: '请输入手机号',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '重启时间区间',
    items: [
      {
        label: '开始时间',
        field: 'restartTimeBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'restartTimeEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '重置时间区间',
    items: [
      {
        label: '开始时间',
        field: 'reseTimeBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'reseTimeEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '最近心跳区间',
    items: [
      {
        label: '开始时间',
        field: 'recentHeartbeatBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'recentHeartbeatEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '入库时间区间',
    items: [
      {
        label: '开始时间',
        field: 'creationTimeBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'creationTimeEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '更新时间区间',
    items: [
      {
        label: '开始时间',
        field: 'updateTimeBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'updateTimeEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '激活时间区间',
    items: [
      {
        label: '开始时间',
        field: 'activationDatetimeBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'activationDatetimeEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
];

// 创建列配置函数
export const createColumns = (handlers: ActionHandlers) => {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 60,
      align: 'center',
    },
    {
      title: '设备标识',
      dataIndex: 'deviceInfo',
      width: 150,
      align: 'center',
      ellipsis: true,
      customRender: ({ record }: { record: any }) => {
        return h('div', { class: 'text-xs' }, [
          h('div', { class: 'mb-1' }, [h('b', {}, record.deviceNo)]),
          h('div', { class: 'mb-1' }, `IMEI: ${record.imeiNo || '-'}`),
          h('div', { class: 'mb-1' }, `规则: ${record.groupName || '-'}`),
          h('div', {}, `厂家: ${record.apiName || '-'}`),
          h('div', {}, `计费组: ${record.seriesName || '-'}`),
        ]);
      },
    },
    {
      title: 'WIFI信息',
      dataIndex: 'wifi',
      width: 300,
      align: 'center',
      ellipsis: true,
      customRender: ({ record }: { record: any }) => {
        return h('div', { class: 'flex justify-between gap-4 text-xs' }, [
          // 4G WIFI
          h('div', { class: 'flex-1' }, [
            h('div', { class: 'mb-1' }, [
              h('b', {}, '4G: '),
              h(
                Tag,
                { color: record.hideStatus === 2 ? 'success' : 'default' },
                record.hideStatus === 2 ? '开放' : '隐藏',
              ),
            ]),
            h('div', { class: 'mb-1' }, `名称: ${record.wifiName || '-'}`),
            h('div', { class: 'mb-1' }, `密码: ${record.wifiPwd || '-'}`),
            h('div', {}, [
              '连接: ',
              h(
                Tag,
                {
                  color:
                    record.wifiLike && record.wifiLike !== -1
                      ? 'success'
                      : 'default',
                },
                record.wifiLike !== undefined && record.wifiLike >= 0
                  ? record.wifiLike
                  : record.wifiLike === -1
                    ? '不支持'
                    : '0',
              ),
            ]),
          ]),
          // 5G WIFI
          h('div', { class: 'flex-1' }, [
            h('div', { class: 'mb-1' }, [
              h('b', {}, '5G: '),
              h(
                Tag,
                { color: record.hideStatus5g === 2 ? 'success' : 'default' },
                record.hideStatus5g === 2 ? '开放' : '隐藏',
              ),
            ]),
            h('div', { class: 'mb-1' }, `名称: ${record.wifi5gName || '-'}`),
            h('div', { class: 'mb-1' }, `密码: ${record.wifi5gPwd || '-'}`),
            h('div', {}, [
              '连接: ',
              h(
                Tag,
                {
                  color:
                    record.wifi5gLike && record.wifi5gLike !== -1
                      ? 'success'
                      : 'default',
                },
                record.wifi5gLike !== undefined && record.wifi5gLike >= 0
                  ? record.wifi5gLike
                  : record.wifi5gLike === -1
                    ? '不支持'
                    : '0',
              ),
            ]),
          ]),
        ]);
      },
    },
    {
      title: '内置卡信息',
      dataIndex: 'simInfo',
      width: 400,
      align: 'center',
      ellipsis: true,
      customRender: ({ record }: { record: any }) => {
        const renderCardSlot = (index: number) => {
          const slotInfo = {
            id: record[`cardSlot${index}Id`],
            iccid: record[`iccid${index}`],
            network: record[`network${index}`],
            msisdn: record[`msisdn${index}`],
            nameStatus: record[`cardName${index}`],
          };

          if (!slotInfo.id && !slotInfo.iccid) {
            return h('div', { class: 'mb-1' }, [
              h('b', {}, `卡${index}: `),
              h(Tag, { color: 'default' }, '无卡'),
            ]);
          }

          const networkColors: Record<number, string> = {
            0: 'default',
            1: 'cyan',
            2: 'green',
            3: 'blue',
            4: 'purple',
          };

          return h('div', { class: 'text-xs' }, [
            h('div', { class: 'mb-1' }, [
              h('b', {}, `卡${index}: `),
              slotInfo.network
                ? h(
                    Tag,
                    { color: networkColors[slotInfo.network] || 'default' },
                    getNetworkName(slotInfo.network),
                  )
                : null,
            ]),
            slotInfo.iccid
              ? h('div', { class: 'mb-1' }, `ICCID: ${slotInfo.iccid}`)
              : null,
            slotInfo.msisdn
              ? h('div', { class: 'mb-1' }, `号码: ${slotInfo.msisdn}`)
              : null,
            slotInfo.nameStatus
              ? h('div', {}, [
                  h('span', {}, '实名: '),
                  h(
                    Tag,
                    {
                      color:
                        slotInfo.nameStatus === 2
                          ? 'success'
                          : slotInfo.nameStatus === 3
                            ? 'error'
                            : 'default',
                    },
                    getNameStatusInfo(slotInfo.nameStatus).text,
                  ),
                ])
              : null,
          ]);
        };

        // 构造两行，每行两个卡槽
        const rows = [
          [renderCardSlot(1), renderCardSlot(2)],
          [renderCardSlot(3), renderCardSlot(4)],
        ];

        return h(
          'table',
          { style: 'width: 100%' },
          rows.map((row) =>
            h(
              'tr',
              {},
              row.map((cell) =>
                h(
                  'td',
                  { style: 'vertical-align: top; width: 50%; padding: 4px' },
                  [cell],
                ),
              ),
            ),
          ),
        );
      },
    },
    {
      title: '数据用量',
      dataIndex: 'dataInfo',
      width: 150,
      align: 'center',
      ellipsis: true,
      customRender: ({ record }: { record: any }) => {
        return h('div', { class: 'text-xs' }, [
          h('div', { class: 'mb-2' }, [
            h('b', {}, '实际: '),
            h('span', { class: 'ml-1' }, '总:'),
            h(Tag, { color: 'success' }, record.actualTotal || '0'),
            h('span', { class: 'ml-1' }, '今:'),
            h(Tag, { color: 'success' }, record.actualToday || '0'),
          ]),
          h('div', {}, [
            h('b', {}, '用户: '),
            h('span', { class: 'ml-1' }, '总:'),
            h(Tag, { color: 'success' }, record.userTotal || '0'),
            h('span', { class: 'ml-1' }, '今:'),
            h(Tag, { color: 'success' }, record.userToday || '0'),
          ]),
        ]);
      },
    },
    {
      title: '状态信息',
      width: 100,
      align: 'center',
      ellipsis: true,
      customRender: ({ record }: { record: any }) => {
        const statusInfo = getDeviceStatusInfo(record.status);
        const nameInfo = getNameStatusInfo(record.nameStatus);

        return h('div', { class: 'text-xs' }, [
          h('div', { class: 'mb-1' }, [
            h('span', {}, '设备: '),
            h(Tag, { color: statusInfo.color || 'default' }, statusInfo.text),
          ]),
          h('div', { class: 'mb-1' }, [
            h('span', {}, '实名: '),
            h(Tag, { color: nameInfo.color || 'default' }, nameInfo.text),
          ]),
          h('div', {}, [
            h('span', {}, '运营: '),
            h(Tag, {}, getNetworkName(record.currentNetwork)),
          ]),
        ]);
      },
    },
    {
      title: '运行信息',
      width: 300,
      align: 'center',
      ellipsis: true,
      customRender: ({ record }: { record: any }) => {
        const formatTime = (time: any) => {
          return time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-';
        };

        const powerOnText =
          record.powerOnStatus === 1
            ? '不支持'
            : record.powerOnStatus === 2
              ? '开机'
              : record.powerOnStatus === 3
                ? '关机'
                : '-';

        const presenceText =
          record.presence === 1
            ? '不支持'
            : record.presence === 2
              ? '在线'
              : record.presence === 3
                ? '离线'
                : '-';

        const renderTag = (text: string, color: string) =>
          h(Tag, { color }, text);

        const rows = [
          [
            h('div', {}, [
              h('span', {}, '开机: '),
              renderTag(
                powerOnText,
                record.powerOnStatus === 2
                  ? 'success'
                  : (record.powerOnStatus === 3
                    ? 'error'
                    : 'default'),
              ),
            ]),
            h('div', {}, [
              h('span', {}, '在线: '),
              renderTag(
                presenceText,
                record.presence === 2
                  ? 'success'
                  : (record.presence === 3
                    ? 'error'
                    : 'default'),
              ),
            ]),
          ],
          [
            h('div', {}, `心跳: ${formatTime(record.recentHeartbeat)}`),
            h('div', {}, `重置: ${formatTime(record.reseTime)}`),
          ],
          [
            h('div', {}, `重启: ${formatTime(record.restartTime)}`),
            h('div', {}, `入库: ${formatTime(record.creationTime)}`),
          ],
          [
            h('div', {}, `入网: ${formatTime(record.activationTime)}`),
            h('div', {}, `更新: ${formatTime(record.updateTime)}`),
          ],
        ];

        return h(
          'table',
          { style: 'width: 100%' },
          rows.map((row) =>
            h(
              'tr',
              {},
              row.map((cell) =>
                h(
                  'td',
                  { style: 'vertical-align: top; width: 50%; padding: 4px' },
                  [cell],
                ),
              ),
            ),
          ),
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      fixed: 'right',
      align: 'center',
      // 固定
      ellipsis: true,
      customRender: ({ record }: { record: any }) => {
        return h('div', {}, [
          h('div', { style: 'margin-bottom: 4px' }, [
            h(
              Button,
              {
                type: 'link',
                size: 'small',
                onClick: () => handlers.onUpdateInfo(record),
                loading: handlers.loading,
              },
              '更新',
            ),
            h(
              Button,
              {
                type: 'link',
                size: 'small',
                onClick: () => handlers.onUpdatePassword(record),
              },
              '密码',
            ),
            h(
              Button,
              {
                type: 'link',
                size: 'small',
                onClick: () => handlers.onToggleHide(record),
              },
              '隐藏',
            ),
            h(
              Button,
              {
                type: 'link',
                size: 'small',
                onClick: () => handlers.onRemoteControl(record),
              },
              '控制',
            ),
          ]),
          h('div', {}, [
            h(
              Button,
              {
                type: 'link',
                size: 'small',
                onClick: () => handlers.onSwitchNetwork(record),
              },
              '网络',
            ),
            h(
              Button,
              {
                type: 'link',
                size: 'small',
                onClick: () => handlers.onSwitchMainCard(record),
              },
              '主卡',
            ),
            h(
              Button,
              {
                type: 'link',
                size: 'small',
                danger: true,
                onClick: () => handlers.onDelete(record),
              },
              '删除',
            ),
          ]),
        ]);
      },
    },
  ];
};

// 搜索配置
export const searchSchema = [
  {
    field: 'deviceNo',
    label: '设备号',
    component: 'Input',
    props: {
      placeholder: '请输入设备号',
      allowClear: true,
    },
  },
  {
    field: 'imei',
    label: 'IMEI',
    component: 'Input',
    props: {
      placeholder: '请输入IMEI',
      allowClear: true,
    },
  },
];

// 状态选项配置
export const statusOptions = {
  apiPolling: [
    { label: '启用', value: 1, color: 'success' },
    { label: '禁用', value: 0, color: 'error' },
  ],
  apiType: [
    { label: '套餐', value: 1 },
    { label: '前向流量池', value: 2 },
    { label: '后向流量池', value: 3 },
  ],
  apiCardType: [
    { label: 'ICCID', value: 1 },
    { label: 'MIS', value: 2 },
    { label: '虚拟号', value: 3 },
  ],
};
