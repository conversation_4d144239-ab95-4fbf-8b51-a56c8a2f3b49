<script lang="ts" setup>
import { h, watch } from 'vue';

import { Tag } from 'ant-design-vue';

import { getDeviceCardPackageList } from '#/api/core/device';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

const props = defineProps<{
  deviceId?: number | string;
}>();

const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
} = useTable({
  api: getDeviceCardPackageList,
  defaultParams: {},
});

// 表格列配置
const columns = [
  {
    title: '套餐名称',
    dataIndex: 'packageName',
    width: 150,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '套餐类型',
    dataIndex: 'packageType',
    width: 120,
    align: 'center',
    // 套餐类型 1.基础套餐 2.加油包 3.加速宝 4.体验包 5.短信包 6.语音包
    customRender: (record: any) => {
      const tagColor = {
        1: { color: 'success', text: '基础套餐' },
        2: { color: 'green', text: '加油包' },
        3: { color: 'purple', text: '加速宝' },
        4: { color: 'orange', text: '体验包' },
        5: { color: 'red', text: '短信包' },
        6: { color: 'default', text: '语音包' },
      };
      const tag = tagColor[record.text as keyof typeof tagColor];
      return h(Tag, { color: tag.color }, () => tag.text);
    },
  },
  {
    title: '总流量(实)',
    dataIndex: 'totalFlow',
    // width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '使用流量(实)',
    dataIndex: 'usedFlow',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '总流量(虚)',
    dataIndex: 'vTotalFlow',
    // width: 120,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '使用流量(虚）',
    dataIndex: 'vUsedFlow',
    width: 150,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '当前限速值',
    dataIndex: 'speedValue',
    width: 120,
    align: 'center',
  },
  // voidType
  {
    title: '套餐类型',
    dataIndex: 'voidType',
    width: 120,
    align: 'center',
    customRender: (record: any) => {
      return h(
        Tag,
        { color: record.voidType === 1 ? 'success' : 'default' },
        () => (record.voidType === 1 ? '统一虚量' : '阶梯虚量'),
      );
    },
  },
  {
    title: '叠加规则',
    dataIndex: 'stackingRules',
    width: 200,
    align: 'center',
    customRender: (record: any) => {
      // "stackingRules": 1, //叠加规则 1 不叠加 2 有效期内无限叠加 3 按充值日期后延充值
      return h(
        Tag,
        { color: record.stackingRules === 1 ? 'success' : 'default' },
        () =>
          record.stackingRules === 1
            ? '不叠加'
            : record.stackingRules === 2
              ? '有效期内无限叠加'
              : '按充值日期后延充值',
      );
    },
  },

  // "packageState": 2, //套餐包状态 1使用中 2未使用 3已用完 4已过期
  {
    title: '套餐包状态',
    dataIndex: 'packageState',
    width: 120,
    align: 'center',
    customRender: (record: any) => {
      return h(
        Tag,
        {
          color:
            record.text === 1
              ? 'success'
              : record.text === 2
                ? 'default'
                : record.text === 3
                  ? 'warning'
                  : 'error',
        },
        () =>
          record.text === 1
            ? '使用中'
            : record.text === 2
              ? '未使用'
              : record.text === 3
                ? '已用完'
                : '已过期',
      );
    },
  },
  {
    title: '生效时间',
    dataIndex: 'takeeffectTime',
    width: 180,
    align: 'center',
  },
  {
    title: '过期时间',
    dataIndex: 'expirationTime',
    width: 180,
    align: 'center',
  },
];

// 暴露loadData方法给父组件
defineExpose({
  getList,
});

// 监听deviceId变化
watch(
  () => props.deviceId,
  (newVal) => {
    if (newVal) {
      // 更新搜索参数
      searchParams.value = {
        deviceId: newVal,
      };
      getList();
    }
  },
  { immediate: true },
);
</script>

<template>
  <BasicTable
    :loading="loading"
    :columns="columns"
    :data-source="tableData"
    :pagination="pagination"
    @change="handleTableChange"
  />
</template>
