<script lang="ts" setup>
import { Card, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { statusOptions } from '#/constants/card-status';

defineProps<{
  deviceDetail: any;
}>();
</script>

<template>
  <!-- 基本信息 -->
  <Card title="基本信息" class="mb-4 rounded-lg shadow-sm">
    <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3">
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">设备号码</span>
        <span class="font-medium">
          {{ deviceDetail?.deviceNo || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">IMEI号码</span>
        <span class="font-medium">
          {{ deviceDetail?.imeiNo || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">当前使用卡片</span>
        <span class="font-medium">
          {{ deviceDetail?.currentIccId || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">通道名称</span>
        <span class="font-medium">
          {{ deviceDetail?.apiName || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">设备规则</span>
        <span class="font-medium">
          {{ deviceDetail?.groupName || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">所属账号</span>
        <span class="font-medium">
          {{ deviceDetail?.userAccount || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">预存模板</span>
        <span class="font-medium">
          {{ deviceDetail?.prestoreName || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">设备余额</span>
        <span class="font-medium">
          {{ deviceDetail?.balance || '0' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">冻结余额</span>
        <span class="font-medium">
          {{ deviceDetail?.freezeAmount || '0' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">支付密码</span>
        <span class="font-medium">
          {{ deviceDetail?.payPwd || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">绑定手机号</span>
        <span class="font-medium">
          {{ deviceDetail?.phone || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">使用次数</span>
        <span class="font-medium">
          {{ deviceDetail?.returnCycle || '0' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">重启时间</span>
        <span class="font-medium">
          {{
            deviceDetail?.restartTime
              ? dayjs(deviceDetail?.restartTime).format('YYYY-MM-DD HH:mm:ss')
              : '-'
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">重置时间</span>
        <span class="font-medium">
          {{
            deviceDetail?.reseTime
              ? dayjs(deviceDetail?.reseTime).format('YYYY-MM-DD HH:mm:ss')
              : '-'
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">创建时间</span>
        <span class="font-medium">
          {{
            deviceDetail?.creationTime
              ? dayjs(deviceDetail?.creationTime).format('YYYY-MM-DD HH:mm:ss')
              : '-'
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">更新时间</span>
        <span class="font-medium">
          {{
            deviceDetail?.updateTime
              ? dayjs(deviceDetail?.updateTime).format('YYYY-MM-DD HH:mm:ss')
              : '-'
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">激活时间</span>
        <span class="font-medium">
          {{
            deviceDetail?.activationDatetime
              ? dayjs(deviceDetail?.activationDatetime).format(
                  'YYYY-MM-DD HH:mm:ss',
                )
              : '-'
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">设备备注</span>
        <span class="font-medium">
          {{ deviceDetail?.notes || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">总流量(实)</span>
        <span class="font-medium">
          {{ deviceDetail?.totalFlow || '0' }}MB
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">已用流量(实)</span>
        <span class="font-medium"> {{ deviceDetail?.useFlow || '0' }}MB </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">剩余流量(实)</span>
        <span class="font-medium">
          {{ deviceDetail?.residueFlow || '0' }}MB
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">当天流量(实)</span>
        <span class="font-medium"> {{ deviceDetail?.dayFlow || '0' }}MB </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">总流量(显)</span>
        <span class="font-medium">
          {{ deviceDetail?.vTotalFlow || '0' }}MB
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">已用流量(显)</span>
        <span class="font-medium"> {{ deviceDetail?.vUseFlow || '0' }}MB </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">剩余流量(显)</span>
        <span class="font-medium">
          {{ deviceDetail?.vResidueFlow || '0' }}MB
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">当天流量(显)</span>
        <span class="font-medium"> {{ deviceDetail?.vDayFlow || '0' }}MB </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">计费组</span>
        <span class="font-medium"> {{ deviceDetail?.seriesName || '-' }} </span>
      </div>
    </div>
  </Card>

  <!-- 设备状态 -->
  <Card title="设备状态" class="mb-4 rounded-lg shadow-sm">
    <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3">
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">当前电量</span>
        <span class="font-medium">
          {{
            deviceDetail?.currentBatteryLevel === -1
              ? '不支持'
              : deviceDetail?.currentBatteryLevel
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">当前信号</span>
        <span class="font-medium">
          {{
            deviceDetail?.currentSignal === -1
              ? '不支持'
              : deviceDetail?.currentSignal
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">限速阈值</span>
        <span class="font-medium">
          {{
            deviceDetail?.netStatus === '0'
              ? '未限速'
              : `${deviceDetail?.netStatus}kbs`
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">最近心跳</span>
        <span class="font-medium">
          {{
            deviceDetail?.recentHeartbeat
              ? dayjs(deviceDetail?.recentHeartbeat).format(
                  'YYYY-MM-DD HH:mm:ss',
                )
              : '-'
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">开机状态</span>
        <span>
          <Tag
            :color="
              deviceDetail?.powerOnStatus === 2
                ? 'success'
                : deviceDetail?.powerOnStatus === 3
                  ? 'error'
                  : 'default'
            "
          >
            {{
              deviceDetail?.powerOnStatus === 1
                ? '不支持'
                : deviceDetail?.powerOnStatus === 2
                  ? '开机'
                  : '关机'
            }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">在线状态</span>
        <span>
          <Tag
            :color="
              deviceDetail?.presence === 2
                ? 'success'
                : deviceDetail?.presence === 3
                  ? 'error'
                  : 'default'
            "
          >
            {{
              deviceDetail?.presence === 1
                ? '不支持'
                : deviceDetail?.presence === 2
                  ? '在线'
                  : '不在线'
            }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">设备状态</span>
        <span>
          <Tag
            :color="
              deviceDetail?.status === 3
                ? 'success'
                : deviceDetail?.status === 2
                  ? 'processing'
                  : deviceDetail?.status === 4
                    ? 'error'
                    : 'default'
            "
          >
            {{ deviceDetail?.statusMsg || '未知' }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">上游状态</span>
        <span>
          <Tag color="default">
            {{ deviceDetail?.upperStatusMsg || '未知' }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">实名状态</span>
        <span>
          <Tag
            :color="
              deviceDetail?.nameStatus === 2
                ? 'success'
                : deviceDetail?.nameStatus === 3
                  ? 'warning'
                  : 'default'
            "
          >
            {{
              deviceDetail?.nameStatus === 1
                ? '未知'
                : deviceDetail?.nameStatus === 2
                  ? '已实名'
                  : deviceDetail?.nameStatus === 3
                    ? '未实名'
                    : '无需实名'
            }}
          </Tag>
        </span>
      </div>
    </div>
  </Card>

  <!-- 卡槽信息 -->
  <Card title="卡槽信息" class="mb-4 rounded-lg shadow-sm">
    <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3">
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽1 ICCID</span>
        <span class="font-medium">
          {{ deviceDetail?.cardSlot1Iccid || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽1状态</span>
        <span>
          <Tag
            :color="
              statusOptions.find(
                (item) => item.value === deviceDetail?.cardSlot1Status,
              )?.color
            "
          >
            {{
              statusOptions.find(
                (item) => item.value === deviceDetail?.cardSlot1Status,
              )?.label
            }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽1实名状态</span>
        <span>
          <Tag
            :color="
              deviceDetail?.cardSlot1NameStatus === 2 ? 'success' : 'warning'
            "
          >
            {{
              deviceDetail?.cardSlot1NameStatus === 1
                ? '未知'
                : deviceDetail?.cardSlot1NameStatus === 2
                  ? '已实名'
                  : deviceDetail?.cardSlot1NameStatus === 3
                    ? '未实名'
                    : deviceDetail?.cardSlot1NameStatus === 4
                      ? '无需实名'
                      : '其他状态'
            }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽2 ICCID</span>
        <span class="font-medium">
          {{ deviceDetail?.cardSlot2Iccid || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽2状态</span>
        <span>
          <Tag
            :color="
              statusOptions.find(
                (item) => item.value === deviceDetail?.cardSlot2Status,
              )?.color
            "
          >
            {{
              statusOptions.find(
                (item) => item.value === deviceDetail?.cardSlot2Status,
              )?.label
            }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽2实名状态</span>
        <span>
          <Tag
            :color="
              deviceDetail?.cardSlot2NameStatus === 2 ? 'success' : 'warning'
            "
          >
            {{
              deviceDetail?.cardSlot2NameStatus === 1
                ? '未知'
                : deviceDetail?.cardSlot2NameStatus === 2
                  ? '已实名'
                  : deviceDetail?.cardSlot2NameStatus === 3
                    ? '未实名'
                    : deviceDetail?.cardSlot2NameStatus === 4
                      ? '无需实名'
                      : '其他状态'
            }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽3 ICCID</span>
        <span class="font-medium">
          {{ deviceDetail?.cardSlot3Iccid || '不支持' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽3状态</span>
        <span>
          <Tag
            :color="
              statusOptions.find(
                (item) => item.value === deviceDetail?.cardSlot3Status,
              )?.color
            "
          >
            {{
              !deviceDetail?.cardSlot3Status
                ? '不支持'
                : statusOptions.find(
                    (item) => item.value === deviceDetail?.cardSlot3Status,
                  )?.label || '未知'
            }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽3实名状态</span>
        <span>
          <Tag
            :color="
              deviceDetail?.cardSlot3NameStatus === 2 ? 'success' : 'warning'
            "
          >
            {{
              !deviceDetail?.cardSlot3NameStatus
                ? '不支持'
                : deviceDetail?.cardSlot3NameStatus === 1
                  ? '未知'
                  : deviceDetail?.cardSlot3NameStatus === 2
                    ? '已实名'
                    : deviceDetail?.cardSlot3NameStatus === 3
                      ? '未实名'
                      : '无需实名'
            }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽4 ICCID</span>
        <span class="font-medium">
          {{ deviceDetail?.cardSlot4Iccid || '不支持' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽4状态</span>
        <span>
          <Tag
            :color="
              statusOptions.find(
                (item) => item.value === deviceDetail?.cardSlot4Status,
              )?.color
            "
          >
            {{
              !deviceDetail?.cardSlot4Status
                ? '不支持'
                : statusOptions.find(
                    (item) => item.value === deviceDetail?.cardSlot4Status,
                  )?.label || '未知'
            }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">卡槽4实名状态</span>
        <span>
          <Tag
            :color="
              deviceDetail?.cardSlot4NameStatus === 2 ? 'success' : 'warning'
            "
          >
            {{
              !deviceDetail?.cardSlot4NameStatus
                ? '不支持'
                : deviceDetail?.cardSlot4NameStatus === 1
                  ? '未知'
                  : deviceDetail?.cardSlot4NameStatus === 2
                    ? '已实名'
                    : deviceDetail?.cardSlot4NameStatus === 3
                      ? '未实名'
                      : '无需实名'
            }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">WIFI名称</span>
        <span class="font-medium">
          {{ deviceDetail?.wifiName || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">WIFI密码</span>
        <span class="font-medium">
          {{ deviceDetail?.wifiPwd || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">WIFI隐藏状态</span>
        <span>
          <Tag :color="deviceDetail?.hideStatus === 2 ? 'success' : 'warning'">
            {{ deviceDetail?.hideStatus === 1 ? '隐藏' : '不隐藏' }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">WIFI链接数</span>
        <span class="font-medium">
          {{
            deviceDetail?.wifiLike === -1 ? '不支持' : deviceDetail?.wifiLike
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">5G WIFI名称</span>
        <span class="font-medium">
          {{ deviceDetail?.wifi5gName || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">5G WIFI密码</span>
        <span class="font-medium">
          {{ deviceDetail?.wifi5gPwd || '-' }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">5G WIFI隐藏状态</span>
        <span>
          <Tag
            :color="deviceDetail?.hideStatus5g === 2 ? 'success' : 'warning'"
          >
            {{ deviceDetail?.hideStatus5g === 1 ? '隐藏' : '不隐藏' }}
          </Tag>
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">5G WIFI链接数</span>
        <span class="font-medium">
          {{
            deviceDetail?.wifi5gLike === -1
              ? '不支持'
              : deviceDetail?.wifi5gLike
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">运营商</span>
        <span class="font-medium">
          {{
            deviceDetail?.currentNetwork === 1
              ? '中国电信'
              : deviceDetail?.currentNetwork === 2
                ? '中国联通'
                : deviceDetail?.currentNetwork === 3
                  ? '中国移动'
                  : deviceDetail?.currentNetwork === 4
                    ? '中国广电'
                    : '未知'
          }}
        </span>
      </div>
      <div class="flex">
        <span class="w-24 flex-shrink-0 text-gray-500">多卡限制</span>
        <span>
          <Tag
            :color="deviceDetail?.cardRestriction === 2 ? 'success' : 'warning'"
          >
            {{ deviceDetail?.cardRestriction === 1 ? '限制多卡' : '允许多卡' }}
          </Tag>
        </span>
      </div>
    </div>
  </Card>
</template>
