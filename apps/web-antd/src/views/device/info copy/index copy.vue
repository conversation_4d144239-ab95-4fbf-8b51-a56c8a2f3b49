<script lang="ts" setup>
import type { Ref } from 'vue';

import { h, ref } from 'vue';

import { MdiChevronDown, MdiCog, MdiDevices } from '@vben/icons';

import {
  Button,
  Card,
  Col,
  Dropdown,
  Empty,
  Input,
  Menu,
  MenuItem,
  message,
  Modal,
  Select,
  Space,
  Spin,
  TabPane,
  Tabs,
  Tag,
} from 'ant-design-vue';

import {
  batchOrderDevice,
  getDeviceInfo,
  getDevicePackageList,
  limitMultipleCards,
} from '#/api';

import BaseInfo from './components/base-info.vue';
import PackageRecords from './components/package-records.vue';
import {
  basicOperations,
  deviceOperations,
  otherOperations,
  statusMap,
} from './config';
import { useDeviceOperations } from './hooks/useDeviceOperations';

// 搜索表单数据
const searchForm = ref({
  deviceNo: '',
});

// 加载状态
const loading = ref(false);

// 设备详情数据
const deviceDetail = ref(null);

// 当前选中的Tab
const activeTab = ref('packageRecords');

// 状态样式映射
const getStatusClass = (status) => {
  const statusMap = {
    1: 'success',
    2: 'warning',
    3: 'error',
  };
  return statusMap[status] || 'default';
};

// 状态文本映射
const getStatusText = (status) => {
  const statusMap = {
    1: '正常',
    2: '警告',
    3: '故障',
  };
  return statusMap[status] || '未知';
};

// 在线状态样式映射
const getOnlineClass = (status) => {
  return status ? 'success' : 'error';
};

// 在线状态文本映射
const getOnlineText = (status) => {
  return status ? '在线' : '离线';
};

// 套餐记录组件引用
const packageRecordsRef = ref();

// 搜索处理
const handleSearch = async () => {
  if (!searchForm.value.deviceNo) {
    message.warning('请输入设备编号');
    return;
  }
  loading.value = true;
  try {
    const { data } = await getDeviceInfo(searchForm.value.deviceNo);
    if (!data) {
      message.error('未找到设备信息');
      deviceDetail.value = null;
      return;
    }
    deviceDetail.value = data;
    // 如果套餐记录组件存在，触发数据加载
    if (packageRecordsRef.value) {
      packageRecordsRef.value.getList();
    }
  } catch (error) {
    console.error('获取设备详情失败:', error);
    message.error('获取设备详情失败，请稍后重试');
    deviceDetail.value = null;
  } finally {
    loading.value = false;
  }
};

// 刷新处理
const handleRefresh = () => {
  handleSearch();
};

// Tab切换处理
const handleTabChange = (key) => {
  console.log('Tab changed:', key);
};
// 设备操作相关方法
const {
  handleUpdateInfo,
  handleUpdatePassword,
  handleToggleHide,
  handleRemoteControl,
  handleSwitchNetwork,
  handleSwitchMainCard,
  handleDelete,
  handleDeviceRestart,
  handleDeviceStop,
  handleUpdateBalance,
  handleDeviceTransfer,
  handleRechargePackage,
  handleLimitMultiCard,
} = useDeviceOperations({
  onSuccess: handleRefresh,
});

// 基础操作处理函数
const handleBasicOperation = (key: string) => {
  if (!deviceDetail.value) return;
  const deviceId = deviceDetail.value.id;
  switch (key) {
    case 'delete': {
      handleDelete(deviceId);
      break;
    }
    case 'remoteControl': {
      handleRemoteControl(deviceId);
      break;
    }
    case 'switchMainCard': {
      handleSwitchMainCard(deviceId);
      break;
    }
    case 'switchNetwork': {
      handleSwitchNetwork(deviceId, '1');
      break;
    }
    case 'updateInfo': {
      handleUpdateInfo(deviceId);
      break;
    }
    case 'updatePassword': {
      handleUpdatePassword(deviceId);
      break;
    }
  }
};

// 其他操作处理函数
const handleLimitMultiCard1 = () => {
  const limitType: Ref<1 | 2> = ref(1);
  Modal.confirm({
    title: '限制多卡',
    content: () => {
      return h(
        Col,
        {
          span: 24,
        },
        [
          h(Select, {
            value: limitType.value,
            'onUpdate:value': (val: 1 | 2) => {
              limitType.value = val;
            },
            options: [
              { label: '限制多卡', value: 1 },
              { label: '允许多卡', value: 2 },
            ],
            style: { width: '100%' },
            placeholder: '请选择限制类型',
          }),
        ],
      );
    },
    onOk: async () => {
      const res = await limitMultipleCards(
        deviceDetail?.value.id,
        limitType.value,
      );
      message.success(res.msg);
    },
  });
};

const handleRechargePackage1 = async () => {
  // 处理充值套餐
  // console.log('充值套餐');
  const packageId = ref(0);
  const takeeffectType = ref(1);
  const packageList = await getDevicePackageList();
  Modal.confirm({
    title: '充值套餐',
    content: () =>
      h(Col, { span: 24 }, [
        h(Select, {
          options: packageList.data.rows.map((item) => ({
            label: `${item.name}(成本:${item.packageCost}元,售价:${item.packagePrice}元)`,
            value: item.id,
          })),
          'onUpdate:value': (val: number) => {
            packageId.value = val;
          },
          style: { width: '100%' },
          placeholder: '请选择套餐',
        }),
        h(Select, {
          options: [
            { label: '立即生效', value: 1 },
            { label: '次月生效', value: 2 },
          ],
          'onUpdate:value': (val: 1 | 2) => {
            takeeffectType.value = val;
          },
          value: takeeffectType.value,
          style: { width: '100%' },
          class: 'mt-2',
          placeholder: '请选择生效时间',
        }),
      ]),
    onOk: async () => {
      const res = await batchOrderDevice({
        deviceNos: deviceDetail.value.deviceNo,
        packageId: packageId.value,
        takeeffectType: 1,
      });
    },
  });
};

const handleDeviceUpdate = () => {
  // 处理设备更新
  console.log('设备更新');
};

const handleDeviceShutdown = () => {
  // 处理设备停机
  console.log('设备停机');
};

const handleSetAnnouncement = () => {
  // 处理设置公告
  console.log('设置公告');
};

// 处理设备操作
const handleDeviceOperation = (key: string) => {
  if (!deviceDetail.value) return;
  const deviceId = deviceDetail.value.id;

  switch (key) {
    case 'balance': {
      // 从设备详情中获取余额并传递
      const balance = deviceDetail.value.balance;
      handleUpdateBalance(deviceId, balance);
      break;
    }
    case 'restart': {
      handleDeviceRestart(deviceId);
      break;
    }
    case 'stop': {
      handleDeviceStop(deviceId);
      break;
    }
  }
};

// 处理其他操作
const handleOtherOperation = (key: string) => {
  if (!deviceDetail.value) return;

  switch (key) {
    case 'limitMultiCard': {
      handleLimitMultiCard(deviceDetail.value.id);
      break;
    }
    case 'rechargePackage': {
      handleRechargePackage(deviceDetail.value.deviceNo);
      break;
    }
    case 'transfer': {
      handleDeviceTransfer(deviceDetail.value.deviceNo);
      break;
    }
  }
};

// 基础操作配置
const basicOperations = [
  {
    key: 'updatePassword',
    label: '修改密码',
    icon: 'MdiKey',
  },
  {
    key: 'remoteControl',
    label: '远程控制',
    icon: 'MdiRemoteDesktop',
  },
  {
    key: 'updateInfo',
    label: '更新信息',
    icon: 'MdiFileEdit',
  },
  {
    key: 'switchMainCard',
    label: '切换主卡',
    icon: 'MdiSimCard',
  },
  {
    key: 'switchNetwork',
    label: '切换网络',
    icon: 'MdiNetwork',
  },
  {
    key: 'delete',
    label: '删除设备',
    icon: 'MdiDelete',
  },
];

// 其他操作配置
const otherOperations = [
  {
    key: 'transfer',
    label: '设备转移',
    icon: 'MdiTransfer',
  },
  {
    key: 'limitMultiCard',
    label: '限制多卡',
    icon: 'MdiSimCard',
  },
  {
    key: 'rechargePackage',
    label: '充值套餐',
    icon: 'MdiCash',
  },
  // {
  //   key: 'deviceUpdate',
  //   label: '设备更新',
  //   icon: 'MdiUpdate',
  // },
  // {
  //   key: 'deviceShutdown',
  //   label: '设备停机',
  //   icon: 'MdiPower',
  // },
  {
    key: 'setAnnouncement',
    label: '设置公告',
    icon: 'MdiMessage',
  },
];
</script>

<template>
  <div class="device-info">
    <!-- 搜索区域 -->
    <Card :bordered="false" class="mb-4">
      <div class="flex w-full items-center gap-4">
        <div class="flex flex-1 items-center gap-2">
          <span class="whitespace-nowrap">设备编号：</span>
          <Input
            v-model:value="searchForm.deviceNo"
            placeholder="请输入设备编号"
            allow-clear
            class="!w-full"
            @keyup.enter="handleSearch"
          />
        </div>
        <Button type="primary" :loading="loading" @click="handleSearch">
          查询
        </Button>
      </div>
    </Card>

    <!-- 详情内容 -->
    <Spin :spinning="loading">
      <div v-if="deviceDetail" class="detail-content">
        <Card :bordered="false">
          <div class="detail-header">
            <!-- 设备信息概览 -->
            <div class="flex flex-col">
              <div class="mb-2 flex items-center gap-2">
                <h2>设备详情</h2>
                <Tag :color="statusMap.style[deviceDetail?.status]">
                  {{ statusMap.text[deviceDetail?.status] || '未知' }}
                </Tag>
              </div>
            </div>

            <!-- 操作按钮组 -->
            <div class="action-buttons">
              <!-- 基础操作和设备操作 -->
              <div class="operation-group">
                <Dropdown v-permission="1" trigger="click">
                  <Button type="primary" class="operation-button">
                    <div class="flex items-center">
                      <MdiCog class="mr-1" />
                      基础操作
                      <MdiChevronDown class="ml-1" />
                    </div>
                  </Button>
                  <template #overlay>
                    <Menu @click="({ key }) => handleBasicOperation(key)">
                      <MenuItem v-for="item in basicOperations" :key="item.key">
                        <div class="flex items-center">
                          <component :is="item.icon" class="mr-1" />
                          {{ item.label }}
                        </div>
                      </MenuItem>
                    </Menu>
                  </template>
                </Dropdown>
                <Dropdown v-permission="1" trigger="click">
                  <Button class="operation-button">
                    <div class="flex items-center">
                      <MdiDevices class="mr-1" />
                      设备操作
                      <MdiChevronDown class="ml-1" />
                    </div>
                  </Button>
                  <template #overlay>
                    <Menu @click="({ key }) => handleDeviceOperation(key)">
                      <MenuItem
                        v-for="item in deviceOperations"
                        :key="item.key"
                      >
                        <div class="flex items-center">
                          <component :is="item.icon" class="mr-1" />
                          {{ item.label }}
                        </div>
                      </MenuItem>
                    </Menu>
                  </template>
                </Dropdown>
              </div>

              <!-- 其他操作按钮组 -->
              <div class="other-operations">
                <div class="mb-2 text-sm text-gray-500">其他操作</div>
                <Space wrap size="middle">
                  <Button
                    v-for="item in otherOperations"
                    :key="item.key"
                    v-permission="1"
                    class="operation-button"
                    @click="handleOtherOperation(item.key)"
                  >
                    <div class="flex items-center">
                      <component :is="item.icon" class="mr-1" />
                      {{ item.label }}
                    </div>
                  </Button>
                </Space>
              </div>
            </div>
          </div>

          <!-- 基础信息展示 -->
          <BaseInfo :device-detail="deviceDetail" />

          <!-- Tab页签 -->
          <Tabs
            class="mt-4"
            v-model:active-key="activeTab"
            @change="handleTabChange"
          >
            <TabPane key="packageRecords" tab="套餐记录">
              <PackageRecords
                ref="packageRecordsRef"
                :device-id="deviceDetail?.id"
              />
            </TabPane>
          </Tabs>
        </Card>
      </div>
      <Empty v-else description="请输入设备编号查询" />
    </Spin>
  </div>
</template>

<style lang="less" scoped>
.device-info {
  padding: 16px;
  background-color: var(--background-deep);

  .detail-header {
    @apply flex flex-col items-start justify-between gap-4 rounded-lg p-4 sm:flex-row sm:items-center;
  }

  .action-buttons {
    @apply flex flex-col items-end gap-4;

    .operation-group {
      @apply flex gap-2;
    }

    .other-operations {
      @apply flex flex-col items-end;

      .operation-button {
        width: 120px;
        justify-content: flex-start;
      }
    }

    .operation-button {
      width: 120px;
    }
  }
}
</style>
