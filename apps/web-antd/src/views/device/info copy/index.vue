<script lang="ts" setup>
import type { Ref } from 'vue';

import { h, ref } from 'vue';

import { MdiMagnify } from '@vben/icons';

import {
  Button,
  Card,
  Col,
  Empty,
  Form,
  Input,
  message,
  Modal,
  Select,
  Spin,
  Tabs,
  Tag,
} from 'ant-design-vue';

import {
  batchOrderDevice,
  getDeviceInfo,
  getDevicePackageList,
  limitMultipleCards,
} from '#/api';
import { deviceStatusOptions } from '#/constants/device-status';

import BaseInfo from './components/base-info.vue';
import PackageRecords from './components/package-records.vue';
import { basicOperations, deviceOperations, otherOperations } from './config';
import { useDeviceOperations } from './hooks/useDeviceOperations';

// 搜索表单数据
const searchForm = ref({
  deviceNo: '',
});

// 加载状态
const loading = ref(false);

// 设备详情数据
const deviceDetail = ref(null);

// 当前选中的Tab
const activeTab = ref('packageRecords');

// 状态样式映射
const getStatusClass = (status) => {
  const statusMap = {
    1: 'success',
    2: 'warning',
    3: 'error',
  };
  return statusMap[status] || 'default';
};

// 状态文本映射
const getStatusText = (status) => {
  const statusMap = {
    1: '正常',
    2: '警告',
    3: '故障',
  };
  return statusMap[status] || '未知';
};

// 在线状态样式映射
const getOnlineClass = (status) => {
  return status ? 'success' : 'error';
};

// 在线状态文本映射
const getOnlineText = (status) => {
  return status ? '在线' : '离线';
};

// 套餐记录组件引用
const packageRecordsRef = ref();

// 搜索处理
const handleSearch = async () => {
  if (!searchForm.value.deviceNo) {
    message.warning('请输入设备编号');
    return;
  }
  loading.value = true;
  try {
    const { data } = await getDeviceInfo(searchForm.value.deviceNo);
    if (!data) {
      message.error('未找到设备信息');
      deviceDetail.value = null;
      return;
    }
    deviceDetail.value = data;
    // 如果套餐记录组件存在，触发数据加载
    if (packageRecordsRef.value) {
      packageRecordsRef.value.getList();
    }
  } catch (error) {
    console.error('获取设备详情失败:', error);
    message.error('获取设备详情失败，请稍后重试');
    deviceDetail.value = null;
  } finally {
    loading.value = false;
  }
};

// 刷新处理
const handleRefresh = () => {
  handleSearch();
};

// Tab切换处理
const handleTabChange = (key) => {
  console.log('Tab changed:', key);
};
// 设备操作相关方法
const {
  handleUpdateInfo,
  handleUpdatePassword,
  handleToggleHide,
  handleRemoteControl,
  handleSwitchNetwork,
  handleSwitchMainCard,
  handleDelete,
  handleDeviceRestart,
  handleDeviceStop,
  handleUpdateBalance,
  handleDeviceTransfer,
  handleRechargePackage,
  handleLimitMultiCard,
  handleSetAnnouncement,
} = useDeviceOperations({
  onSuccess: handleRefresh,
});

// 基础操作处理函数
const handleBasicOperation = (key: string) => {
  if (!deviceDetail.value) return;
  const deviceId = deviceDetail.value.id;
  switch (key) {
    case 'delete': {
      handleDelete(deviceId);
      break;
    }
    case 'remoteControl': {
      handleRemoteControl(deviceId);
      break;
    }
    case 'switchMainCard': {
      handleSwitchMainCard(deviceId);
      break;
    }
    case 'switchNetwork': {
      handleSwitchNetwork(deviceId, '1');
      break;
    }
    case 'updateInfo': {
      handleUpdateInfo(deviceId);
      break;
    }
    case 'updatePassword': {
      handleUpdatePassword(deviceId);
      break;
    }
  }
};

// 其他操作处理函数
const handleLimitMultiCard1 = () => {
  const limitType: Ref<1 | 2> = ref(1);
  Modal.confirm({
    title: '限制多卡',
    content: () => {
      return h(
        Col,
        {
          span: 24,
        },
        [
          h(Select, {
            value: limitType.value,
            'onUpdate:value': (val: 1 | 2) => {
              limitType.value = val;
            },
            options: [
              { label: '限制多卡', value: 1 },
              { label: '允许多卡', value: 2 },
            ],
            style: { width: '100%' },
            placeholder: '请选择限制类型',
          }),
        ],
      );
    },
    onOk: async () => {
      const res = await limitMultipleCards(
        deviceDetail?.value.id,
        limitType.value,
      );
      message.success(res.msg);
    },
  });
};

const handleRechargePackage1 = async () => {
  // 处理充值套餐
  // console.log('充值套餐');
  const packageId = ref(0);
  const takeeffectType = ref(1);
  const packageList = await getDevicePackageList();
  Modal.confirm({
    title: '充值套餐',
    content: () =>
      h(Col, { span: 24 }, [
        h(Select, {
          options: packageList.data.rows.map((item) => ({
            label: `${item.name}(成本:${item.packageCost}元,售价:${item.packagePrice}元)`,
            value: item.id,
          })),
          'onUpdate:value': (val: number) => {
            packageId.value = val;
          },
          style: { width: '100%' },
          placeholder: '请选择套餐',
        }),
        h(Select, {
          options: [
            { label: '立即生效', value: 1 },
            { label: '次月生效', value: 2 },
          ],
          'onUpdate:value': (val: 1 | 2) => {
            takeeffectType.value = val;
          },
          value: takeeffectType.value,
          style: { width: '100%' },
          class: 'mt-2',
          placeholder: '请选择生效时间',
        }),
      ]),
    onOk: async () => {
      const res = await batchOrderDevice({
        deviceNos: deviceDetail.value.deviceNo,
        packageId: packageId.value,
        takeeffectType: 1,
      });
    },
  });
};

// 处理设备操作
const handleDeviceOperation = (key: string) => {
  if (!deviceDetail.value) return;
  const deviceId = deviceDetail.value.id;

  switch (key) {
    case 'balance': {
      // 从设备详情中获取余额并传递
      const balance = deviceDetail.value.balance;
      handleUpdateBalance(deviceId, balance);
      break;
    }
    case 'restart': {
      handleDeviceRestart(deviceId);
      break;
    }
    case 'stop': {
      handleDeviceStop(deviceId);
      break;
    }
  }
};

// 处理其他操作
const handleOtherOperation = (key: string) => {
  if (!deviceDetail.value) return;

  switch (key) {
    case 'limitMultiCard': {
      handleLimitMultiCard(deviceDetail.value.id);
      break;
    }
    case 'rechargePackage': {
      handleRechargePackage(deviceDetail);
      break;
    }
    case 'setAnnouncement': {
      handleSetAnnouncement(deviceDetail.value.id);
      break;
    }
    case 'transfer': {
      handleDeviceTransfer(deviceDetail.value.deviceNo);
      break;
    }
  }
};

// 基础操作配置
const basicOperations = [
  {
    key: 'updatePassword',
    label: '修改密码',
    icon: 'MdiKey',
  },
  {
    key: 'remoteControl',
    label: '远程控制',
    icon: 'MdiRemoteDesktop',
  },
  {
    key: 'updateInfo',
    label: '更新信息',
    icon: 'MdiFileEdit',
  },
  {
    key: 'switchMainCard',
    label: '切换主卡',
    icon: 'MdiSimCard',
  },
  {
    key: 'switchNetwork',
    label: '切换网络',
    icon: 'MdiNetwork',
  },
  {
    key: 'delete',
    label: '删除设备',
    icon: 'MdiDelete',
  },
];

// 其他操作配置
const otherOperations = [
  {
    key: 'transfer',
    label: '设备转移',
    icon: 'MdiTransfer',
  },
  {
    key: 'limitMultiCard',
    label: '限制多卡',
    icon: 'MdiSimCard',
  },
  {
    key: 'rechargePackage',
    label: '充值套餐',
    icon: 'MdiCash',
  },
  // {
  //   key: 'deviceUpdate',
  //   label: '设备更新',
  //   icon: 'MdiUpdate',
  // },
  // {
  //   key: 'deviceShutdown',
  //   label: '设备停机',
  //   icon: 'MdiPower',
  // },
  {
    key: 'setAnnouncement',
    label: '设置公告',
    icon: 'MdiMessage',
  },
];
</script>

<template>
  <div class="bg-[var(--background-deep)] p-4">
    <!-- 搜索区域 -->
    <Card
      :bordered="false"
      class="mb-4 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md"
    >
      <Form>
        <div
          class="flex flex-col items-stretch gap-4 md:flex-row md:items-center"
        >
          <div class="flex flex-1 items-center gap-4">
            <span class="w-16 flex-shrink-0 font-medium">设备编号</span>
            <Input
              v-model:value="searchForm.deviceNo"
              placeholder="请输入设备编号"
              allow-clear
              class="w-full"
              @press-enter="handleSearch"
            >
              <template #prefix>
                <MdiMagnify class="text-[16px] opacity-60" />
              </template>
            </Input>
          </div>
          <Button
            type="primary"
            class="w-full md:w-auto"
            :loading="loading"
            @click="handleSearch"
          >
            查询
          </Button>
        </div>
      </Form>
    </Card>

    <!-- 详情内容 -->
    <Spin :spinning="loading">
      <div v-if="deviceDetail" class="space-y-5">
        <Card
          class="rounded-lg shadow-sm transition-all duration-300 hover:shadow-md"
        >
          <!-- 头部信息和状态 -->
          <div
            class="mb-5 flex flex-col items-start justify-between gap-4 rounded-lg bg-gradient-to-br from-[var(--primary-color-light)] to-white/90 p-5 shadow-sm sm:flex-row sm:items-center"
          >
            <div>
              <div class="mb-2 flex items-center gap-3">
                <h2 class="m-0 text-lg font-medium">设备详情</h2>
                <Tag
                  :color="
                    deviceStatusOptions.find(
                      (item) => item.value === deviceDetail?.status,
                    )?.color
                  "
                >
                  {{
                    deviceStatusOptions.find(
                      (item) => item.value === deviceDetail?.status,
                    )?.label
                  }}
                </Tag>
              </div>
              <div class="text-gray-500">
                <span>设备编号: {{ deviceDetail?.deviceNo }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <Card class="mb-4 rounded-lg shadow-sm">
            <div class="flex flex-wrap gap-2">
              <!-- 基础操作 -->
              <Button
                v-for="item in basicOperations"
                :key="item.key"
                type="primary"
                @click="() => handleBasicOperation(item.key)"
              >
                <component :is="item.icon" class="mr-1" />
                {{ item.label }}
              </Button>

              <!-- 设备操作 -->
              <Button
                v-for="item in deviceOperations"
                :key="item.key"
                @click="() => handleDeviceOperation(item.key)"
              >
                <component :is="item.icon" class="mr-1" />
                {{ item.label }}
              </Button>

              <!-- 其他操作 -->
              <Button
                v-for="item in otherOperations"
                :key="item.key"
                @click="() => handleOtherOperation(item.key)"
              >
                <component :is="item.icon" class="mr-1" />
                {{ item.label }}
              </Button>
            </div>
          </Card>

          <!-- 基础信息展示 -->
          <BaseInfo :device-detail="deviceDetail" />

          <!-- Tab页签 -->
          <Card>
            <Tabs
              v-model:active-key="activeTab"
              @change="handleTabChange"
              class="mt-5"
            >
              <Tabs.TabPane key="packageRecords" tab="套餐记录">
                <PackageRecords
                  ref="packageRecordsRef"
                  :device-id="deviceDetail?.id"
                />
              </Tabs.TabPane>
            </Tabs>
          </Card>
        </Card>
      </div>
      <Empty v-else description="请输入设备编号查询" />
    </Spin>
  </div>
</template>

<style lang="less" scoped>
.device-info {
  padding: 16px;
  background-color: var(--background-deep);

  .detail-header {
    @apply flex flex-col items-start justify-between gap-4 rounded-lg p-4 sm:flex-row sm:items-center;
  }

  .action-buttons {
    @apply flex flex-col items-end gap-4;

    .operation-group {
      @apply flex gap-2;
    }

    .other-operations {
      @apply flex flex-col items-end;

      .operation-button {
        width: 120px;
        justify-content: flex-start;
      }
    }

    .operation-button {
      width: 120px;
    }
  }
}
</style>
