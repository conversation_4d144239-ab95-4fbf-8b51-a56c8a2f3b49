import { h } from 'vue';

import dayjs from 'dayjs';

import {
  getDeviceChannelOptions,
  getDeviceRule,
  getUserOptionsApi,
} from '#/api';
import { statusOptions as deviceStatusOptions } from '#/constants/card-status';
import { networkOptions } from '#/constants/device-status';
// import { statusOptions } from '#/constants/card-status';

export const deviceStatusMap: Record<number, { color: string; text: string }> =
  {
    1: { text: '未知', color: '' },
    2: { text: '待激活', color: 'warning' },
    3: { text: '已激活', color: 'success' },
    4: { text: '已停机', color: 'error' },
    5: { text: '预销号', color: 'error' },
    6: { text: '已销号', color: 'error' },
    7: { text: '已拆机', color: 'error' },
    8: { text: '测试期', color: 'processing' },
    9: { text: '沉默期', color: 'warning' },
    10: { text: '库存', color: 'default' },
    11: { text: '已过户', color: 'processing' },
    12: { text: '异常', color: 'error' },
  };

// 实名状态映射
export const nameStatusMap: Record<number, { color: string; text: string }> = {
  1: { text: '未知', color: '' },
  2: { text: '已实名', color: 'success' },
  3: { text: '未实名', color: 'error' },
};

// 运营商映射
export const networkMap: Record<number, string> = {
  0: '未知',
  1: '电信',
  2: '联通',
  3: '移动',
  4: '广电',
};

// 获取设备状态文本和颜色
export const getDeviceStatusInfo = (status: number) => {
  return deviceStatusMap[status] || { text: '未知', color: '' };
};

// 获取实名状态文本和颜色
export const getNameStatusInfo = (status: number) => {
  return nameStatusMap[status] || { text: '未知', color: '' };
};

// 获取网络名称
export const getNetworkName = (network: number) => {
  return networkMap[network] || '未知';
};

// 基础搜索配置
export const basicSerachItems = [
  {
    label: '设备号',
    field: 'deviceNo',
    component: 'Input' as const,
    props: {
      placeholder: '请输入设备号',
      allowClear: true,
    },
  },
  {
    label: '设备规则',
    field: 'devicePackageGroupIds',
    component: 'Select' as const,
    remote: {
      api: getDeviceRule,
      transform: (data: any) =>
        data.data.rows.map((item: any) => ({
          label: `${item.name}`, // 组合显示
          value: item.id,
        })),
    },
    props: {
      placeholder: '请选择设备规则',
      allowClear: true,
      // mode: 'multiple', // 支持多选
      optionFilterProp: 'label',
    },
  },
  {
    label: '设备通道',
    field: 'deviceConfigIds',
    component: 'Select' as const,
    remote: {
      api: getDeviceChannelOptions,
      transform: (data: any) =>
        data.data.map((item: any) => ({
          label: `${item.name}`, // 组合显示
          value: item.id,
        })),
    },
    props: {
      placeholder: '请选择设备通道',
      allowClear: true,
      optionFilterProp: 'label',
      // mode: 'multiple', // 支持多选
    },
    // options: [],
  },
  {
    label: '归属代理',
    field: 'userIds',
    component: 'Select' as const,
    remote: {
      api: getUserOptionsApi,
      transform: (data: any) =>
        data.data.map((item: any) => ({
          label: `${item.name}`, // 组合显示
          value: item.id,
        })),
    },
    props: {
      placeholder: '请选择归属代理',
      allowClear: true,
      optionFilterProp: 'label',
    },
  },
  {
    label: '设备状态',
    field: 'status',
    component: 'Select' as const,
    props: {
      placeholder: '请选择设备状态',
      allowClear: true,
    },
    options: deviceStatusOptions,
  },
  {
    label: '实名状态',
    field: 'nameStatus',
    component: 'Select' as const,
    props: {
      placeholder: '请选择实名状态',
      allowClear: true,
    },
    options: [
      { label: '未知', value: 1 },
      { label: '已实名', value: 2 },
      { label: '未实名', value: 3 },
    ],
  },
];

// 高级搜索配置
export const advancedSerachItems = [
  {
    label: '设备状态',
    items: [
      {
        label: '在线状态',
        field: 'presence',
        component: 'Select' as const,
        props: {
          placeholder: '请选择在线状态',
          allowClear: true,
        },
        options: [
          { label: '不支持', value: 1 },
          { label: '在线', value: 2 },
          { label: '离线', value: 3 },
        ],
      },
      {
        label: '当前网络',
        field: 'currentNetwork',
        component: 'Select' as const,
        props: {
          placeholder: '请选择当前网络',
          allowClear: true,
        },
        options: networkOptions,
      },
      {
        label: '开机状态',
        field: 'powerOnStatus',
        component: 'Select' as const,
        props: {
          placeholder: '请选择开机状态',
          allowClear: true,
        },
        options: [
          { label: '不支持', value: 1 },
          { label: '开机', value: 2 },
          { label: '关机', value: 3 },
        ],
      },
    ],
  },
  {
    label: '基本信息',
    items: [
      {
        label: 'IMEI',
        field: 'imeiNo',
        component: 'Input' as const,
        props: {
          placeholder: '请输入IMEI',
          allowClear: true,
        },
      },
      {
        label: '备注',
        field: 'notes',
        component: 'Input' as const,
        props: {
          placeholder: '请输入备注',
          allowClear: true,
        },
      },
      {
        label: '手机号',
        field: 'phone',
        component: 'Input' as const,
        props: {
          placeholder: '请输入手机号',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '重启时间区间',
    items: [
      {
        label: '开始时间',
        field: 'restartTimeBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'restartTimeEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '重置时间区间',
    items: [
      {
        label: '开始时间',
        field: 'reseTimeBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'reseTimeEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '最近心跳区间',
    items: [
      {
        label: '开始时间',
        field: 'recentHeartbeatBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'recentHeartbeatEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '入库时间区间',
    items: [
      {
        label: '开始时间',
        field: 'creationTimeBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'creationTimeEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '更新时间区间',
    items: [
      {
        label: '开始时间',
        field: 'updateTimeBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'updateTimeEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
  {
    label: '激活时间区间',
    items: [
      {
        label: '开始时间',
        field: 'activationDatetimeBegin',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        label: '结束时间',
        field: 'activationDatetimeEnd',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
];

// 创建列配置函数
export const createColumns = () => {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 60,
      align: 'center',
    },
    {
      title: '设备信息',
      dataIndex: 'deviceInfo',
      width: 280,
      align: 'left',
      customRender: ({ record }: { record: any }) => {
        return h('div', { class: 'py-2' }, [
          // 设备号
          h('div', { class: 'mb-2' }, [
            h(
              'span',
              { class: 'text-sm font-semibold' },
              record.deviceNo || '-',
            ),
          ]),

          // IMEI - 完整显示
          h('div', { class: 'mb-1' }, [
            h('span', { class: 'text-xs mr-2' }, 'IMEI:'),
            h('span', { class: 'text-xs font-mono' }, record.imeiNo || '-'),
          ]),

          // 规则组
          h('div', { class: 'mb-1' }, [
            h('span', { class: 'text-xs mr-2' }, '规则:'),
            h('span', { class: 'text-xs' }, record.groupName || '-'),
          ]),

          // 厂家
          h('div', { class: 'mb-1' }, [
            h('span', { class: 'text-xs mr-2' }, '厂家:'),
            h('span', { class: 'text-xs' }, record.apiName || '-'),
          ]),

          // 计费组
          h('div', {}, [
            h('span', { class: 'text-xs mr-2' }, '计费:'),
            h('span', { class: 'text-xs' }, record.seriesName || '-'),
          ]),

          // 归属账户
          h('div', {}, [
            h('span', { class: 'text-xs mr-2' }, '代理:'),
            h('span', { class: 'text-xs' }, record.userAccount || '-'),
          ]),
        ]);
      },
    },
    {
      title: 'WIFI信息',
      dataIndex: 'wifi',
      width: 320,
      align: 'left',
      customRender: ({ record }: { record: any }) => {
        return h('div', { class: 'py-2' }, [
          // 4G WIFI
          h('div', { class: 'mb-3' }, [
            h('div', { class: 'text-xs font-semibold mb-1' }, '4G WIFI'),
            h('div', { class: 'text-xs mb-1' }, [
              h('span', { class: 'mr-2' }, '名称:'),
              h('span', { class: 'font-mono' }, record.wifiName || '-'),
              h('span', { class: 'ml-3 mr-1' }, '状态:'),
              h('span', {}, record.hideStatus === 2 ? '开放' : '隐藏'),
            ]),
            h('div', { class: 'text-xs mb-1' }, [
              h('span', { class: 'mr-2' }, '密码:'),
              h('span', { class: 'font-mono' }, record.wifiPwd || '-'),
              h('span', { class: 'ml-3 mr-1' }, '连接:'),
              h(
                'span',
                {},
                record.wifiLike === -1 ? '不支持' : `${record.wifiLike || 0}台`,
              ),
            ]),
          ]),

          // 5G WIFI
          h('div', {}, [
            h('div', { class: 'text-xs font-semibold mb-1' }, '5G WIFI'),
            h('div', { class: 'text-xs mb-1' }, [
              h('span', { class: 'mr-2' }, '名称:'),
              h('span', { class: 'font-mono' }, record.wifi5gName || '-'),
              h('span', { class: 'ml-3 mr-1' }, '状态:'),
              h('span', {}, record.hideStatus5g === 2 ? '开放' : '隐藏'),
            ]),
            h('div', { class: 'text-xs' }, [
              h('span', { class: 'mr-2' }, '密码:'),
              h('span', { class: 'font-mono' }, record.wifi5gPwd || '-'),
              h('span', { class: 'ml-3 mr-1' }, '连接:'),
              h(
                'span',
                {},
                record.wifi5gLike === -1
                  ? '不支持'
                  : `${record.wifi5gLike || 0}台`,
              ),
            ]),
          ]),
        ]);
      },
    },
    {
      title: 'SIM卡信息',
      dataIndex: 'simInfo',
      width: 450,
      align: 'left',
      customRender: ({ record }: { record: any }) => {
        const getNetworkName = (network: null | number) => {
          if (network === null || network === undefined) return '未知';
          const networkMap: Record<number, string> = {
            0: '未知',
            1: '电信',
            2: '联通',
            3: '移动',
            4: '广电',
          };
          return networkMap[network] || '未知';
        };

        const getNameStatusText = (status: null | number) => {
          if (status === null || status === undefined) return '未知';
          const statusMap: Record<number, string> = {
            1: '未知',
            2: '已实名',
            3: '未实名',
          };
          return statusMap[status] || '未知';
        };

        const getNameStatusColor = (status: null | number) => {
          if (status === 2) return 'text-green-600';
          if (status === 3) return 'text-red-600';
          return 'text-gray-500';
        };

        const renderSlot = (index: number) => {
          const iccid = record[`iccid${index}`];
          const network = record[`network${index}`];
          const msisdn = record[`msisdn${index}`];
          const nameStatus = record[`cardName${index}`];

          // 如果没有ICCID，说明没有卡
          if (!iccid) {
            return h(
              'div',
              {
                class: 'border rounded p-2 text-xs',
              },
              [
                h('div', { class: 'font-medium mb-1' }, `卡槽${index}`),
                h('div', {}, '无卡'),
              ],
            );
          }

          return h('div', { class: 'border rounded p-2 text-xs' }, [
            // 卡槽标题和运营商
            h('div', { class: 'flex items-center justify-between mb-2' }, [
              h('span', { class: 'font-medium' }, `卡槽${index}`),
              h('span', { class: 'font-medium' }, getNetworkName(network)),
            ]),
            // 实名状态
            h('div', { class: 'mb-2' }, [
              h('span', { class: 'mr-1' }, '状态:'),
              h(
                'span',
                { class: getNameStatusColor(nameStatus) },
                getNameStatusText(nameStatus),
              ),
            ]),
            // ICCID - 完整显示
            h('div', { class: 'mb-2' }, [
              h('div', { class: 'mb-1' }, 'ICCID:'),
              h('div', { class: 'font-mono text-xs break-all' }, iccid),
            ]),
            // 手机号
            msisdn
              ? h('div', {}, [
                  h('span', { class: 'mr-1' }, '号码:'),
                  h('span', { class: 'font-mono' }, msisdn),
                ])
              : null,
          ]);
        };

        return h('div', { class: 'py-2' }, [
          h('div', { class: 'grid grid-cols-2 gap-2' }, [
            renderSlot(1),
            renderSlot(2),
          ]),
          h('div', { class: 'grid grid-cols-2 gap-2 mt-2' }, [
            renderSlot(3),
            renderSlot(4),
          ]),
        ]);
      },
    },
    {
      title: '数据用量',
      dataIndex: 'dataInfo',
      // width: 280,
      align: 'center',
      ellipsis: true,
      customRender: ({ record }: { record: any }) => {
        // 格式化流量显示，添加单位
        const formatFlow = (flow: number | string | undefined) => {
          if (flow === undefined || flow === null) return '0 MB';

          // 转换为数字
          const flowNum = Number(flow);

          // 根据大小格式化显示
          return flowNum >= 1024
            ? `${(flowNum / 1024).toFixed(2)} GB`
            : `${flowNum} MB`;
        };

        // 使用实际流量数据
        const actualTotal = formatFlow(record.totalFlow);
        const actualToday = formatFlow(record.dayFlow);
        const actualResidual = formatFlow(record.residueFlow);

        // 使用虚拟流量数据
        const userTotal = formatFlow(record.vTotalFlow);
        const userToday = formatFlow(record.vDayFlow);
        const userResidual = formatFlow(record.vResidueFlow);

        // 创建现代卡片风格的流量展示
        return h('div', { class: 'relative' }, [
          h(
            'div',
            {
              class:
                'border rounded-xl p-3 shadow-sm hover:shadow-md transition-all duration-200',
            },
            [
              // 标题区域
              h('div', { class: 'flex items-center mb-2' }, [
                h('div', {
                  class: 'w-3 h-3 bg-info mr-2 rounded-full',
                }),
                h('div', { class: 'text-sm font-semibold ' }, '数据用量'),
              ]),

              // 实际用量和用户用量并排显示
              h('div', { class: 'grid grid-cols-2 gap-3' }, [
                // 实际用量
                h('div', { class: 'space-y-2' }, [
                  h(
                    'div',
                    { class: 'flex items-center justify-between mb-2' },
                    [
                      h(
                        'span',
                        { class: 'text-xs font-semibold text-success' },
                        '实际用量：',
                      ),
                      h(
                        'div',
                        {
                          class:
                            'px-2 py-1  text-xs font-medium text-success border border-success/30 rounded-xl',
                          title: actualTotal,
                        },
                        actualTotal,
                      ),
                    ],
                  ),
                  h('div', { class: 'group' }, [
                    h(
                      'div',
                      { class: 'text-xs text-gray-500 mb-1 font-medium' },
                      '今日使用',
                    ),
                    h(
                      'div',
                      {
                        class:
                          'text-xs text-success px-2 py-1.5 rounded-md border border-success/30 text-center font-mono',
                        title: actualToday,
                      },
                      actualToday,
                    ),
                  ]),
                  h('div', { class: 'group' }, [
                    h(
                      'div',
                      { class: 'text-xs text-gray-500 mb-1 font-medium' },
                      '剩余流量',
                    ),
                    h(
                      'div',
                      {
                        class:
                          'text-xs text-success px-2 py-1.5 rounded-md border border-success/30 text-center font-mono',
                        title: actualResidual,
                      },
                      actualResidual,
                    ),
                  ]),
                ]),

                // 用户用量
                h('div', { class: 'space-y-2' }, [
                  h(
                    'div',
                    { class: 'flex items-center justify-between mb-2' },
                    [
                      h(
                        'span',
                        { class: 'text-xs font-semibold text-primary' },
                        '用户用量：',
                      ),
                      h(
                        'div',
                        {
                          class:
                            'px-2 py-1  text-xs font-medium text-primary border-primary/30 border rounded-xl',
                          title: userTotal,
                        },
                        userTotal,
                      ),
                    ],
                  ),
                  h('div', { class: 'group' }, [
                    h(
                      'div',
                      { class: 'text-xs text-gray-500 mb-1 font-medium' },
                      '今日使用',
                    ),
                    h(
                      'div',
                      {
                        class:
                          'text-xs text-primary px-2 py-1.5 rounded-md border border-primary/30 text-center font-mono',
                        title: userToday,
                      },
                      userToday,
                    ),
                  ]),
                  h('div', { class: 'group' }, [
                    h(
                      'div',
                      { class: 'text-xs text-gray-500 mb-1 font-medium' },
                      '剩余流量',
                    ),
                    h(
                      'div',
                      {
                        class:
                          'text-xs text-primary px-2 py-1.5 rounded-md border border-primary/30 text-center font-mono',
                        title: userResidual,
                      },
                      userResidual,
                    ),
                  ]),
                ]),
              ]),
            ],
          ),
        ]);
      },
    },
    {
      title: '状态信息',
      width: 200,
      align: 'left',
      ellipsis: false,
      customRender: ({ record }: { record: any }) => {
        const statusInfo = getDeviceStatusInfo(record.status);
        const nameInfo = getNameStatusInfo(record.nameStatus);

        // 电量显示
        const renderBattery = () => {
          if (
            record.currentBatteryLevel === undefined ||
            record.currentBatteryLevel === -1
          ) {
            return null;
          }

          // 根据电量选择颜色样式
          let batteryStyle = {
            bg: '',
            text: 'text-success',
            border: 'border-success/30',
          };
          if (record.currentBatteryLevel < 20) {
            batteryStyle = {
              bg: '',
              text: 'text-error',
              border: 'border-error/30',
            };
          } else if (record.currentBatteryLevel < 50) {
            batteryStyle = {
              bg: '',
              text: 'text-warning',
              border: 'border-warning/30',
            };
          }

          return h('div', { class: 'flex items-center justify-between' }, [
            h('span', { class: 'text-xs text-gray-500 font-medium' }, '电量'),
            h(
              'div',
              {
                class: `px-2 py-1  text-xs font-medium ${batteryStyle.bg} ${batteryStyle.text} ${batteryStyle.border} border`,
                title: `电量: ${record.currentBatteryLevel}%`,
              },
              `${record.currentBatteryLevel}%`,
            ),
          ]);
        };

        // 信号显示
        const renderSignal = () => {
          if (
            record.currentSignal === undefined ||
            record.currentSignal === -1
          ) {
            return null;
          }

          // 根据信号强度选择颜色样式
          let signalStyle = {
            bg: '',
            text: 'text-success',
            border: 'border-success/30',
          };
          if (record.currentSignal < 30) {
            signalStyle = {
              bg: '',
              text: 'text-error',
              border: 'border-error/30',
            };
          } else if (record.currentSignal < 60) {
            signalStyle = {
              bg: '',
              text: 'text-warning',
              border: 'border-warning/30',
            };
          }

          return h('div', { class: 'flex items-center justify-between' }, [
            h('span', { class: 'text-xs text-gray-500 font-medium' }, '信号'),
            h(
              'div',
              {
                class: `px-2 py-1  text-xs font-medium ${signalStyle.bg} ${signalStyle.text} ${signalStyle.border} border`,
                title: `信号强度: ${record.currentSignal}%`,
              },
              `${record.currentSignal}%`,
            ),
          ]);
        };

        // 获取状态样式
        const getStatusStyle = (color: string) => {
          const colorMap: Record<
            string,
            { bg: string; border: string; text: string }
          > = {
            success: {
              bg: '',
              text: 'text-success',
              border: 'border-success/30',
            },
            error: {
              bg: '',
              text: 'text-error',
              border: 'border-error/30',
            },
            warning: {
              bg: '',
              text: 'text-warning',
              border: 'border-warning/30',
            },
            processing: {
              bg: '',
              text: 'text-primary',
              border: 'border-primary/30',
            },
            default: {
              bg: '',
              text: 'text-gray-600',
              border: 'border-gray-200',
            },
          };
          return colorMap[color] || colorMap.default;
        };

        const deviceStatusStyle = getStatusStyle(statusInfo.color || 'default');
        const nameStatusStyle = getStatusStyle(nameInfo.color || 'default');

        return h('div', { class: 'relative' }, [
          h(
            'div',
            {
              class:
                ' border rounded-xl p-3 shadow-sm hover:shadow-md transition-all duration-200',
            },
            [
              // 标题区域
              h('div', { class: 'flex items-center mb-2' }, [
                h('div', {
                  class: 'w-3 h-3 bg-warning mr-2 rounded-full',
                }),
                h('div', { class: 'text-sm font-semibold ' }, '状态信息'),
              ]),

              // 状态信息列表
              h('div', { class: 'space-y-2' }, [
                // 设备状态
                h('div', { class: 'flex items-center justify-between' }, [
                  h(
                    'span',
                    { class: 'text-xs text-gray-500 font-medium' },
                    '设备',
                  ),
                  h(
                    'div',
                    {
                      class: `px-2 py-1  text-xs font-medium ${deviceStatusStyle.bg} ${deviceStatusStyle.text} ${deviceStatusStyle.border} border`,
                      title: `设备状态: ${statusInfo.text}`,
                    },
                    statusInfo.text,
                  ),
                ]),

                // 实名状态
                h('div', { class: 'flex items-center justify-between' }, [
                  h(
                    'span',
                    { class: 'text-xs text-gray-500 font-medium' },
                    '实名',
                  ),
                  h(
                    'div',
                    {
                      class: `px-2 py-1  text-xs font-medium ${nameStatusStyle.bg} ${nameStatusStyle.text} ${nameStatusStyle.border} border`,
                      title: `实名状态: ${nameInfo.text}`,
                    },
                    nameInfo.text,
                  ),
                ]),

                // 运营商
                h('div', { class: 'flex items-center justify-between' }, [
                  h(
                    'span',
                    { class: 'text-xs text-gray-500 font-medium' },
                    '运营商',
                  ),
                  h(
                    'div',
                    {
                      class:
                        'px-2 py-1  text-xs font-medium text-primary border border-primary/30',
                      title: `运营商: ${getNetworkName(record.currentNetwork)}`,
                    },
                    getNetworkName(record.currentNetwork),
                  ),
                ]),

                // 电量（如果有）
                renderBattery(),

                // 信号（如果有）
                renderSignal(),

                // 余额（如果有）
                record.balance
                  ? h('div', { class: 'flex items-center justify-between' }, [
                      h(
                        'span',
                        { class: 'text-xs text-gray-500 font-medium' },
                        '余额',
                      ),
                      h(
                        'div',
                        {
                          class:
                            'px-2 py-1  text-xs font-medium text-info border border-info/30',
                          title: `余额: ¥${record.balance}`,
                        },
                        `¥${record.balance}`,
                      ),
                    ])
                  : null,
              ]),
            ],
          ),
        ]);
      },
    },
    {
      title: '运行信息',
      width: 350,
      align: 'left',
      ellipsis: false,
      customRender: ({ record }: { record: any }) => {
        // 自定义格式化时间的函数，如果时间较新，添加"新"标签
        const formatTimeWithTag = (time: any) => {
          if (!time) return h('span', { class: 'text-xs text-gray-500' }, '-');

          const formattedTime = dayjs(time).format('YYYY-MM-DD HH:mm');
          const isRecent = dayjs().diff(dayjs(time), 'hour') < 24; // 24小时内

          if (isRecent) {
            return h('div', { class: 'flex items-center gap-1' }, [
              h(
                'span',
                {
                  class: 'text-xs text-gray-700 font-mono',
                  title: formattedTime,
                },
                formattedTime,
              ),
              h(
                'div',
                {
                  class:
                    'px-1.5 py-0.5  text-xs font-medium text-success border border-success/30',
                },
                '新',
              ),
            ]);
          }

          return h(
            'span',
            { class: 'text-xs text-gray-700 font-mono', title: formattedTime },
            formattedTime,
          );
        };

        // 获取开机状态文本和颜色样式
        const getPowerStatus = (status: number) => {
          switch (status) {
            case 1: {
              return {
                text: '不支持',
                style: {
                  bg: '',
                  text: 'text-gray-600',
                  border: 'border-gray-200',
                },
              };
            }
            case 2: {
              return {
                text: '开机',
                style: {
                  bg: '',
                  text: 'text-success',
                  border: 'border-success/30',
                },
              };
            }
            case 3: {
              return {
                text: '关机',
                style: {
                  bg: '',
                  text: 'text-error',
                  border: 'border-error/30',
                },
              };
            }
            default: {
              return {
                text: '-',
                style: {
                  bg: '',
                  text: 'text-gray-600',
                  border: 'border-gray-200',
                },
              };
            }
          }
        };

        // 获取在线状态文本和颜色样式
        const getPresenceStatus = (status: number) => {
          switch (status) {
            case 1: {
              return {
                text: '不支持',
                style: {
                  bg: '',
                  text: 'text-gray-600',
                  border: 'border-gray-200',
                },
              };
            }
            case 2: {
              return {
                text: '在线',
                style: {
                  bg: '',
                  text: 'text-success',
                  border: 'border-success/30',
                },
              };
            }
            case 3: {
              return {
                text: '离线',
                style: {
                  bg: '',
                  text: 'text-error',
                  border: 'border-error/30',
                },
              };
            }
            default: {
              return {
                text: '-',
                style: {
                  bg: '',
                  text: 'text-gray-600',
                  border: 'border-gray-200',
                },
              };
            }
          }
        };

        const powerStatus = getPowerStatus(record.powerOnStatus);
        const presenceStatus = getPresenceStatus(record.presence);

        return h('div', { class: 'relative' }, [
          h(
            'div',
            {
              class:
                ' border rounded-xl p-3 shadow-sm hover:shadow-md transition-all duration-200',
            },
            [
              // 标题区域
              h('div', { class: 'flex items-center mb-2' }, [
                h('div', {
                  class: 'w-3 h-3 bg-primary mr-2 rounded-full',
                }),
                h('div', { class: 'text-sm font-semibold ' }, '运行信息'),
              ]),

              // 状态信息
              h('div', { class: 'grid grid-cols-2 gap-2 mb-3' }, [
                // 开机状态
                h('div', { class: 'flex items-center justify-between' }, [
                  h(
                    'span',
                    { class: 'text-xs text-gray-500 font-medium' },
                    '开机',
                  ),
                  h(
                    'div',
                    {
                      class: `px-2 py-1  text-xs font-medium ${powerStatus.style.bg} ${powerStatus.style.text} ${powerStatus.style.border} border`,
                      title: `开机状态: ${powerStatus.text}`,
                    },
                    powerStatus.text,
                  ),
                ]),

                // 在线状态
                h('div', { class: 'flex items-center justify-between' }, [
                  h(
                    'span',
                    { class: 'text-xs text-gray-500 font-medium' },
                    '在线',
                  ),
                  h(
                    'div',
                    {
                      class: `px-2 py-1  text-xs font-medium ${presenceStatus.style.bg} ${presenceStatus.style.text} ${presenceStatus.style.border} border`,
                      title: `在线状态: ${presenceStatus.text}`,
                    },
                    presenceStatus.text,
                  ),
                ]),
              ]),

              // 时间信息
              h('div', { class: 'space-y-2' }, [
                // 心跳和重置
                h('div', { class: 'grid grid-cols-2 gap-2' }, [
                  h('div', { class: 'group' }, [
                    h(
                      'div',
                      { class: 'text-xs text-gray-500 mb-1 font-medium' },
                      '心跳时间',
                    ),
                    h(
                      'div',
                      {
                        class: 'text-xs px-2 py-1.5 rounded-md border',
                      },
                      [formatTimeWithTag(record.recentHeartbeat)],
                    ),
                  ]),
                  h('div', { class: 'group' }, [
                    h(
                      'div',
                      { class: 'text-xs text-gray-500 mb-1 font-medium' },
                      '重置时间',
                    ),
                    h(
                      'div',
                      {
                        class: 'text-xs px-2 py-1.5 rounded-md border',
                      },
                      [formatTimeWithTag(record.reseTime)],
                    ),
                  ]),
                ]),

                // 重启和入库
                h('div', { class: 'grid grid-cols-2 gap-2' }, [
                  h('div', { class: 'group' }, [
                    h(
                      'div',
                      { class: 'text-xs text-gray-500 mb-1 font-medium' },
                      '重启时间',
                    ),
                    h(
                      'div',
                      {
                        class: 'text-xs px-2 py-1.5 rounded-md border',
                      },
                      [formatTimeWithTag(record.restartTime)],
                    ),
                  ]),
                  h('div', { class: 'group' }, [
                    h(
                      'div',
                      { class: 'text-xs text-gray-500 mb-1 font-medium' },
                      '入库时间',
                    ),
                    h(
                      'div',
                      {
                        class: 'text-xs px-2 py-1.5 rounded-md border',
                      },
                      [formatTimeWithTag(record.creationTime)],
                    ),
                  ]),
                ]),

                // 入网和更新
                h('div', { class: 'grid grid-cols-2 gap-2' }, [
                  h('div', { class: 'group' }, [
                    h(
                      'div',
                      { class: 'text-xs text-gray-500 mb-1 font-medium' },
                      '入网时间',
                    ),
                    h(
                      'div',
                      {
                        class: 'text-xs px-2 py-1.5 rounded-md border',
                      },
                      [formatTimeWithTag(record.activationTime)],
                    ),
                  ]),
                  h('div', { class: 'group' }, [
                    h(
                      'div',
                      { class: 'text-xs text-gray-500 mb-1 font-medium' },
                      '更新时间',
                    ),
                    h(
                      'div',
                      {
                        class: 'text-xs px-2 py-1.5 rounded-md border',
                      },
                      [formatTimeWithTag(record.updateTime)],
                    ),
                  ]),
                ]),
              ]),
            ],
          ),
        ]);
      },
    },
    {
      title: '备注信息',
      dataIndex: 'notes',
      // width: 220,
      align: 'center',
      ellipsis: true,
      customRender: ({ record }: { record: any }) => {
        return h('div', { class: 'relative' }, [
          h(
            'div',
            {
              class:
                'border rounded-xl p-3 shadow-sm hover:shadow-md transition-all duration-200',
            },
            [
              // 标题区域
              h('div', { class: 'flex items-center mb-2' }, [
                h('div', {
                  class: 'w-3 h-3 mr-2 rounded-full',
                }),
                h('div', { class: 'text-sm font-semibold' }, '备注信息'),
              ]),

              // 备注内容
              h('div', { class: 'space-y-3' }, [
                // 备注信息
                h('div', { class: 'group' }, [
                  h('div', { class: 'text-xs mb-1.5 font-medium' }, '备注内容'),
                  record.notes
                    ? h(
                        'div',
                        {
                          class:
                            'text-xs px-3 py-2 rounded-md border transition-colors break-all leading-relaxed',
                          title: record.notes,
                        },
                        record.notes,
                      )
                    : h(
                        'div',
                        {
                          class: 'text-xs px-3 py-2 rounded-md border italic',
                        },
                        '暂无备注信息',
                      ),
                ]),

                // 绑定手机号
                h('div', { class: 'group' }, [
                  h('div', { class: 'text-xs mb-1.5 font-medium' }, '绑定手机'),
                  record.phone
                    ? h(
                        'div',
                        {
                          class:
                            'text-xs px-3 py-2 rounded-md border font-mono',
                          title: `绑定手机号: ${record.phone}`,
                        },
                        record.phone,
                      )
                    : h(
                        'div',
                        {
                          class: 'text-xs px-3 py-2 rounded-md border italic',
                        },
                        '未绑定手机号',
                      ),
                ]),
              ]),
            ],
          ),
        ]);
      },
    },
  ];
};

// 搜索配置
export const searchSchema = [
  {
    field: 'deviceNo',
    label: '设备号',
    component: 'Input',
    props: {
      placeholder: '请输入设备号',
      allowClear: true,
    },
  },
  {
    field: 'imei',
    label: 'IMEI',
    component: 'Input',
    props: {
      placeholder: '请输入IMEI',
      allowClear: true,
    },
  },
];

// 状态选项配置
export const statusOptions = {
  apiPolling: [
    { label: '启用', value: 1, color: 'success' },
    { label: '禁用', value: 0, color: 'error' },
  ],
  apiType: [
    { label: '套餐', value: 1 },
    { label: '前向流量池', value: 2 },
    { label: '后向流量池', value: 3 },
  ],
  apiCardType: [
    { label: 'ICCID', value: 1 },
    { label: 'MIS', value: 2 },
    { label: '虚拟号', value: 3 },
  ],
};
