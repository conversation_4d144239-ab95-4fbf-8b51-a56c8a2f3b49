import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed, onMounted, ref } from 'vue';

import { message } from 'ant-design-vue';

import { getUserOptionsApi } from '#/api';
import {
  downloadDeviceAssignTemplate,
  importDeviceAssign,
  linkDeviceAssign,
  separatorDeviceAssign,
} from '#/api/core/device';
import { useForm } from '#/hooks/useAnsheng';
import { handleFileDownload } from '#/utils/export';

// 定义用户选项类型
interface UserOption {
  label: string;
  value: number;
}

/**
 * 设备分配 Hook
 */
export function useDeviceAssign(options: { onSuccess?: () => void } = {}) {
  // 用户选项
  const userOptions = ref<UserOption[]>([]);
  // 已上传文件
  const uploadedFile = ref<File | null>(null);
  // 专门创建一个响应式变量用于RadioGroup
  const currentAssignType = ref<'import' | 'separator' | 'sequential'>(
    'separator',
  );

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      const res = await downloadDeviceAssignTemplate();
      await handleFileDownload(res, '设备分配模板.xlsx');
    } catch (error: any) {
      console.error('下载模板失败:', error);
      message.error(`下载模板失败: ${error.message || '未知错误'}`);
    }
  };

  // 使用表单Hook
  const form = useForm({
    title: '分配设备',
    width: 700,
    simpleLayout: {
      showGroupTitle: false, // 不显示分组标题
      padding: 0, // 更小的内边距
    },
    defaultValues: {
      userId: undefined as number | undefined,
      deviceNoList: '',
      startDeviceNo: '',
      endDeviceNo: '',
      file: null as File | null,
    },
    // 表单提交处理
    create: async (values) => {
      try {
        if (!values.userId) {
          throw new Error('请选择用户');
        }

        const formData = new FormData();
        formData.append('userId', String(values.userId));

        // 根据不同的分配方式调用不同的API
        switch (currentAssignType.value) {
          case 'import': {
            // 文件分配
            const fileToSubmit = values.file || uploadedFile.value;

            if (!fileToSubmit) {
              throw new Error('请选择导入文件');
            }

            formData.append('file', fileToSubmit);

            return await importDeviceAssign(formData);
            // return res;
          }

          case 'separator': {
            // 不规则分配
            if (!values.deviceNoList?.trim()) {
              throw new Error('请输入设备号');
            }

            // 处理多种分隔符：将换行、中文逗号、英文逗号统一转为英文逗号
            const deviceNoList = values.deviceNoList
              // 先替换所有中文逗号为英文逗号
              .replaceAll('，', ',')
              // 将换行符替换为英文逗号
              .replaceAll('\n', ',')
              // 分割并处理每个设备号
              .split(',')
              .map((no) => no.trim())
              .filter(Boolean)
              // 最后合并为单个逗号分隔的字符串
              .join(',');

            // 直接传入逗号分隔的字符串
            formData.append('cardNos', deviceNoList);

            return await separatorDeviceAssign(formData);
          }

          case 'sequential': {
            // 连号分配
            if (!values.startDeviceNo) {
              throw new Error('请输入起始设备号');
            }
            if (!values.endDeviceNo) {
              throw new Error('请输入结束设备号');
            }

            formData.append('startDeviceNo', values.startDeviceNo);
            formData.append('endDeviceNo', values.endDeviceNo);

            return await linkDeviceAssign(formData);
          }

          default: {
            throw new Error('未知的分配方式');
          }
        }
      } catch (error: any) {
        console.error('设备分配失败:', error);
        throw error;
      }
    },
    // 表单规则
    rules: {
      userId: [{ required: true, message: '请选择用户' }],
      deviceNoList: [
        {
          validator: (_, value) => {
            if (currentAssignType.value !== 'separator')
              return Promise.resolve();
            return value?.trim()
              ? Promise.resolve()
              : Promise.reject(new Error('请输入设备号'));
          },
        },
      ],
      startDeviceNo: [
        {
          validator: (_, value) => {
            if (currentAssignType.value !== 'sequential')
              return Promise.resolve();
            return value
              ? Promise.resolve()
              : Promise.reject(new Error('请输入起始设备号'));
          },
        },
      ],
      endDeviceNo: [
        {
          validator: (_, value) => {
            if (currentAssignType.value !== 'sequential')
              return Promise.resolve();
            return value
              ? Promise.resolve()
              : Promise.reject(new Error('请输入结束设备号'));
          },
        },
      ],
      file: [
        {
          validator: (_, value) => {
            if (currentAssignType.value !== 'import') return Promise.resolve();
            return value || uploadedFile.value
              ? Promise.resolve()
              : Promise.reject(new Error('请选择导入文件'));
          },
        },
      ],
    },
    // 成功回调
    onSuccess: () => {
      options.onSuccess?.();
    },
  });

  // 处理文件上传
  const handleFileUpload = (file: File) => {
    uploadedFile.value = file;
    form.formData.file = file;

    // 验证文件字段
    if (form.formRef?.value) {
      form.formRef.value.validateFields(['file']).catch(() => {
        // 忽略验证错误
      });
    }

    return false; // 阻止自动上传
  };

  // 加载用户选项
  const loadUserOptions = async () => {
    try {
      const res = await getUserOptionsApi();
      userOptions.value = res.data.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    } catch (error) {
      console.error('获取用户选项失败:', error);
      message.error('获取用户选项失败');
    }
  };

  // 页面加载时初始化用户选项
  onMounted(() => {
    loadUserOptions();
  });

  // 处理分配方式变更
  const handleAssignTypeChange = (e: any) => {
    // 直接使用事件对象的值
    const value = e.target?.value || e;
    currentAssignType.value = value;

    // 根据选择的分配方式，验证对应的字段
    const fieldsToValidate = ['userId'];
    switch (value) {
      case 'import': {
        fieldsToValidate.push('file');

        break;
      }
      case 'separator': {
        fieldsToValidate.push('deviceNoList');

        break;
      }
      case 'sequential': {
        fieldsToValidate.push('startDeviceNo', 'endDeviceNo');

        break;
      }
      // No default
    }

    // 触发验证
    if (form.formRef?.value) {
      form.formRef.value.validateFields(fieldsToValidate).catch(() => {
        // 忽略验证错误
      });
    }
  };

  // 表单分组
  const formGroups = computed((): FormGroup[] => [
    {
      title: '分配信息',
      fields: [
        {
          name: 'userId',
          label: '分配用户',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择用户',
            showSearch: true,
            allowClear: true,
            // 需要可以搜索 label
            filterOption: (input: string, option: any) =>
              option.label.toLowerCase().includes(input.toLowerCase()),
          },
          remote: {
            api: getUserOptionsApi,
            params: {},
            transform: (res: any) =>
              res.data.map((item: any) => ({
                label: item.name,
                value: item.id,
              })),
          },
        },
        {
          // 使用普通的RadioGroup组件，不绑定到form的值
          component: 'RadioGroup',
          label: '分配方式',
          col: { span: 24 },
          props: {
            options: [
              { label: '不规则分配', value: 'separator' },
              { label: '连号分配', value: 'sequential' },
              { label: '文件分配', value: 'import' },
            ],
            value: currentAssignType.value,
            onChange: handleAssignTypeChange,
          },
        },
        // 不规则分配
        {
          name: 'deviceNoList',
          label: '设备号列表',
          component: 'TextArea',
          col: { span: 24 },
          props: {
            placeholder: '请输入设备号，每行一个',
            rows: 10,
          },
          show: () => currentAssignType.value === 'separator',
        },
        // 连号分配
        {
          name: 'startDeviceNo',
          label: '开始设备号',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入开始设备号',
          },
          show: () => currentAssignType.value === 'sequential',
        },
        {
          name: 'endDeviceNo',
          label: '结束设备号',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入结束设备号',
          },
          show: () => currentAssignType.value === 'sequential',
        },
        // 文件分配
        {
          name: 'file',
          label: '导入文件',
          component: 'UploadBox',
          col: { span: 24 },
          props: {
            accept: '.xlsx,.xls,.csv',
            maxCount: 1,
            onUpload: handleFileUpload,
            onDownload: handleDownloadTemplate,
          },
          show: () => currentAssignType.value === 'import',
        },
      ],
    },
  ]);

  // 显示分配弹窗
  const showDeviceAssignModal = async () => {
    // 重置上传文件
    uploadedFile.value = null;

    // 重置分配方式
    currentAssignType.value = 'separator';

    // 重置表单
    form.resetForm();

    // 显示表单
    form.show();
  };

  // 渲染表单弹窗
  const renderDeviceAssignModal = () => {
    return form.renderFormModal(formGroups.value);
  };

  return {
    form,
    formGroups,
    showDeviceAssignModal,
    renderDeviceAssignModal,
    // 导出当前分配方式，便于调试
    currentAssignType,
  };
}
