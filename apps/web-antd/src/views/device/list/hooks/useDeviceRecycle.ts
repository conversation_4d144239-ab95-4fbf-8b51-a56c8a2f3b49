import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed, ref, watch } from 'vue';

import { message } from 'ant-design-vue';

import {
  downloadDeviceAssignTemplate,
  importRecycleDevice,
  linkRecycleDevice,
  separatorRecycleDevice,
} from '#/api/core/device';
import { useForm } from '#/hooks/useAnsheng';
import { handleFileDownload } from '#/utils/export';

/**
 * 设备回收 Hook
 */
export function useDeviceRecycle(options: { onSuccess?: () => void } = {}) {
  // 当前选择的回收方式
  const recycleType = ref<'import' | 'separator' | 'sequential'>('separator');

  // 使用表单Hook
  const form = useForm({
    title: '设备回收',
    width: 700,
    simpleLayout: {
      showGroupTitle: false, // 不显示分组标题
      padding: 0, // 更小的内边距
    },
    defaultValues: {
      // 不规则回收
      deviceNoList: '',
      // 连号回收
      startDeviceNo: '',
      endDeviceNo: '',
      // 文件回收
      file: null as File | null,
      // 当前选择的回收方式
      recycleType: 'separator',
    },
    // 表单提交处理
    create: async (values) => {
      try {
        // 根据不同的回收方式调用不同的API
        switch (recycleType.value) {
          case 'import': {
            // 文件回收
            if (!values.file) {
              throw new Error('请选择导入文件');
            }

            const formData = new FormData();
            // 确保file不为null
            if (values.file) {
              formData.append('file', values.file);
            }

            return await importRecycleDevice(formData);
          }

          case 'separator': {
            // 不规则回收
            if (!values.deviceNoList?.trim()) {
              throw new Error('请输入设备号');
            }

            const formData = new FormData();
            // 处理多种分隔符：将换行、中文逗号、英文逗号统一转为英文逗号
            const deviceNoList = values.deviceNoList
              // 先替换所有中文逗号为英文逗号
              .replaceAll('，', ',')
              // 将换行符替换为英文逗号
              .replaceAll('\n', ',')
              // 分割并处理每个设备号
              .split(',')
              .map((no) => no.trim())
              .filter(Boolean)
              // 最后合并为单个逗号分隔的字符串
              .join(',');

            // 直接传入逗号分隔的字符串
            formData.append('cardNos', deviceNoList);

            return await separatorRecycleDevice(formData);
          }

          case 'sequential': {
            // 连号回收
            if (!values.startDeviceNo) {
              throw new Error('请输入起始设备号');
            }
            if (!values.endDeviceNo) {
              throw new Error('请输入结束设备号');
            }

            const formData = new FormData();
            formData.append('startDeviceNo', values.startDeviceNo);
            formData.append('endDeviceNo', values.endDeviceNo);

            return await linkRecycleDevice(formData);
          }

          default: {
            throw new Error('未知的回收方式');
          }
        }
      } catch (error: any) {
        console.error('设备回收失败:', error);
        throw error;
      }
    },
    // 表单规则
    rules: {
      deviceNoList: [
        {
          validator: (_, value) => {
            if (recycleType.value !== 'separator') return Promise.resolve();
            return value?.trim()
              ? Promise.resolve()
              : Promise.reject(new Error('请输入设备号'));
          },
        },
      ],
      startDeviceNo: [
        {
          validator: (_, value) => {
            if (recycleType.value !== 'sequential') return Promise.resolve();
            return value
              ? Promise.resolve()
              : Promise.reject(new Error('请输入起始设备号'));
          },
        },
      ],
      endDeviceNo: [
        {
          validator: (_, value) => {
            if (recycleType.value !== 'sequential') return Promise.resolve();
            return value
              ? Promise.resolve()
              : Promise.reject(new Error('请输入结束设备号'));
          },
        },
      ],
      file: [
        {
          validator: (_, value) => {
            if (recycleType.value !== 'import') return Promise.resolve();
            return value
              ? Promise.resolve()
              : Promise.reject(new Error('请选择导入文件'));
          },
        },
      ],
    },
    // 关闭弹窗时重置
    onCancel: () => {
      recycleType.value = 'separator';
    },
    // 成功回调
    onSuccess: () => {
      message.success('设备回收成功');
      options.onSuccess?.();
    },
  });

  // 监听回收方式变化
  watch(recycleType, (newValue) => {
    console.error('回收方式变更为:', newValue);
    // 更新表单的recycleType
    form.formData.recycleType = newValue;
  });

  // 处理回收方式变更
  const handleRecycleTypeChange = (e: any) => {
    // 直接使用事件对象的值
    const value = e.target?.value || e;
    recycleType.value = value as 'import' | 'separator' | 'sequential';
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    const res = await downloadDeviceAssignTemplate();
    await handleFileDownload(res, '设备回收模板.xlsx');
  };

  // 表单分组
  const formGroups = computed((): FormGroup[] => [
    {
      title: '回收信息',
      fields: [
        {
          // 使用普通的RadioGroup组件
          component: 'RadioGroup',
          label: '回收方式',
          col: { span: 24 },
          props: {
            options: [
              { label: '不规则回收', value: 'separator' },
              { label: '连号回收', value: 'sequential' },
              { label: '文件回收', value: 'import' },
            ],
            value: recycleType.value,
            onChange: handleRecycleTypeChange,
          },
        },
        // 不规则回收
        {
          name: 'deviceNoList',
          label: '设备号列表',
          component: 'TextArea',
          col: { span: 24 },
          props: {
            placeholder: '请输入设备号，每行一个',
            rows: 10,
          },
          show: () => recycleType.value === 'separator',
        },
        // 连号回收
        {
          name: 'startDeviceNo',
          label: '开始设备号',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入开始设备号',
          },
          show: () => recycleType.value === 'sequential',
        },
        {
          name: 'endDeviceNo',
          label: '结束设备号',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入结束设备号',
          },
          show: () => recycleType.value === 'sequential',
        },
        // 文件回收
        {
          name: 'file',
          label: '导入文件',
          component: 'UploadBox',
          col: { span: 24 },
          props: {
            accept: '.xlsx,.xls,.csv',
            maxCount: 1,
            onUpload: (file: File) => {
              form.formData.file = file;
              return false; // 阻止自动上传
            },
            onDownload: handleDownloadTemplate,
          },
          show: () => recycleType.value === 'import',
        },
      ],
    },
  ]);

  // 显示回收弹窗
  const showDeviceRecycleModal = () => {
    // 重置回收方式
    recycleType.value = 'separator';
    // 重置表单
    form.resetForm();
    // 显示表单
    form.show();
  };

  // 渲染表单弹窗
  const renderDeviceRecycleModal = () => {
    return form.renderFormModal(formGroups.value);
  };

  return {
    form,
    formGroups,
    showDeviceRecycleModal,
    renderDeviceRecycleModal,
    recycleType,
  };
}
