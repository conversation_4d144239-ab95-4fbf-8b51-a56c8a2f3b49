import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed, ref } from 'vue';

import { message } from 'ant-design-vue';

import {
  downloadDeviceImportTemplate,
  getDeviceChannelOptions,
  getDeviceRule,
  importDeviceApi,
} from '#/api/core/device';
import { useForm } from '#/hooks/useAnsheng';
import { handleFileDownload } from '#/utils/export';

/**
 * 导入设备 Hook
 */
export function useImportDevice(options: { onSuccess?: () => void } = {}) {
  // 存储已上传的文件对象
  const uploadedFile = ref<File | null>(null);

  // 处理下载模板
  const handleDownloadTemplate = async () => {
    try {
      const res = await downloadDeviceImportTemplate();
      await handleFileDownload(res, '分配卡片模板.xlsx');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  // 使用表单Hook
  const form = useForm({
    title: '导入设备',
    width: 600,
    simpleLayout: {
      showGroupTitle: false, // 不显示分组标题
      padding: 0, // 更小的内边距
    },
    defaultValues: {
      file: null as any, // 使用any来绕过类型检查
      deviceConfigId: null as null | number, // 通道ID
      devicePackageGroupId: null as null | number, // 套餐规则ID
    },
    // 表单提交处理
    create: async (values) => {
      try {
        // 如果表单中没有文件但有已上传文件，使用已上传文件
        const fileToUpload = values.file || uploadedFile.value;

        if (!fileToUpload) {
          throw new Error('请选择导入文件');
        }

        if (!values.deviceConfigId) {
          throw new Error('请选择通道');
        }

        message.loading('正在导入设备...');

        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', fileToUpload);

        // 添加通道ID
        formData.append('deviceConfigId', String(values.deviceConfigId));

        // 可选地添加套餐规则ID
        if (values.devicePackageGroupId) {
          formData.append(
            'devicePackageGroupId',
            String(values.devicePackageGroupId),
          );
        }

        // 调用导入API
        const res = await importDeviceApi(formData);
        message.destroy();

        if (res.code === 1) {
          // 重置上传的文件
          uploadedFile.value = null;
          options.onSuccess?.();
          return res;
        } else {
          throw new Error(res.msg || '导入失败');
        }
      } catch (error: any) {
        message.destroy();
        message.error(error.message || '导入失败');
        throw error;
      }
    },
    // 表单规则
    rules: {
      // 修改file验证规则，如果有已上传文件则认为是有效的
      file: [
        {
          required: true,
          message: '请选择导入文件',
        },
      ],
      deviceConfigId: [{ required: true, message: '请选择通道' }],
      devicePackageGroupId: [{ required: true, message: '请选择套餐规则' }],
    },
  });

  // 处理文件上传
  const handleFileUpload = (file: File) => {
    message.success(`文件 ${file.name} 上传成功`);

    // 设置已上传的文件
    uploadedFile.value = file;

    // 同时更新表单的file值
    form.setFormData({ file });

    // 手动触发文件字段的验证
    if (form.formRef && form.formRef.value) {
      form.formRef.value.validateFields(['file']).catch(() => {
        // 忽略验证错误
      });
    }

    return file;
  };

  // 表单分组
  const formGroups = computed((): FormGroup[] => [
    {
      title: '导入信息',
      fields: [
        {
          name: 'deviceConfigId',
          label: '通道名称',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择通道',
            // options: channelOptions.value, // 直接使用本地选项
          },
          remote: {
            api: getDeviceChannelOptions,
            transform: (data: any) =>
              data.data.map((item: any) => ({
                label: item.name,
                value: item.id,
              })),
          },
        },
        {
          name: 'devicePackageGroupId',
          label: '套餐规则',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择套餐规则',
            // options: packageRuleOptions.value, // 直接使用本地选项
          },
          remote: {
            api: getDeviceRule,
            transform: (data: any) =>
              data.data.rows.map((item: any) => ({
                label: item.name,
                value: item.id,
              })),
          },
        },
        {
          name: 'file',
          label: '导入文件',
          component: 'UploadBox',
          col: { span: 24 },
          props: {
            accept: '.xlsx,.xls',
            supportText: '支持 .xlsx, .xls 格式的文件',
            downloadText: '下载模板',
            onDownload: handleDownloadTemplate,
            onUpload: handleFileUpload,
          },
          // 使用helpText显示已上传文件信息
          helpText: uploadedFile.value
            ? `已上传文件: ${uploadedFile.value.name}`
            : '',
        },
      ],
    },
  ]);

  // 显示导入弹窗
  const showImportModal = async () => {
    // 重置已上传的文件
    uploadedFile.value = null;

    // 显示表单
    form.show();
  };

  // 渲染表单弹窗
  const renderImportModal = () => {
    return form.renderFormModal(formGroups.value);
  };

  return {
    form,
    formGroups,
    showImportModal,
    renderImportModal,
  };
}
