import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed, ref } from 'vue';

import { message } from 'ant-design-vue';

import {
  batchOrderDevice,
  getDeviceInfo,
  getPackageListPay,
} from '#/api/core/device';
import { useForm } from '#/hooks/useAnsheng';

export interface UseBatchOrderModalOptions {
  onSuccess?: () => void;
}

export function useBatchOrderModal(options: UseBatchOrderModalOptions = {}) {
  // 设备信息
  const deviceInfo = ref<any>(null);

  // 套餐选项
  const packageOptions = ref<{ label: string; value: number }[]>([]);

  // 套餐类型选项
  const packTypeOptions = [
    { label: '基础套餐', value: 1 },
    { label: '加油包', value: 2 },
    { label: '加速宝', value: 3 },
    { label: '体验包', value: 4 },
    { label: '短信包', value: 5 },
    { label: '语音包', value: 6 },
  ];

  // 生效类型选项
  const takeeffectTypeOptions = [
    { label: '立即生效', value: 1 },
    { label: '次月生效', value: 2 },
  ];

  // 格式化设备编号
  const formatDeviceNos = (input: string): string => {
    if (!input) return '';

    // 1. 将中文逗号替换为英文逗号
    const formatted = input.replaceAll('，', ',');

    // 2. 以逗号、空格、或换行符分割字符串获取设备编号数组
    const deviceArray = formatted.split(/[,\s]+/).filter(Boolean);

    // 3. 返回一行一个设备编号的格式
    return deviceArray.join('\n');
  };

  // 转换为API提交格式（逗号分隔）
  const deviceNosToApiFormat = (input: string): string => {
    if (!input) return '';

    // 使用换行符分割，然后用逗号连接
    return input
      .split(/[\n\r]+/)
      .filter(Boolean)
      .join(',');
  };

  // 使用表单Hook
  const form = useForm({
    title: '批量订购',
    width: 600,
    draggable: true,
    simpleLayout: {
      showGroupTitle: false, // 不显示分组标题
      padding: 0, // 更小的内边距
    },
    defaultValues: {
      deviceNos: '', // 设备编号列表
      packType: 1, // 套餐类型
      packageId: null as null | number, // 充值套餐ID
      takeeffectType: null as null | number, // 生效类型
    },
    // 表单提交处理
    create: async (values) => {
      try {
        const formattedDeviceNos = deviceNosToApiFormat(values.deviceNos);

        if (!formattedDeviceNos) {
          throw new Error('请输入设备编号');
        }

        // 调用批量订购API
        const res = await batchOrderDevice({
          packageId: values.packageId,
          takeeffectType: values.takeeffectType,
          deviceNos: formattedDeviceNos,
        });

        if (res.code === 1) {
          options.onSuccess?.();
          return res;
        } else {
          throw new Error(res.msg || '订购失败');
        }
      } catch (error: any) {
        console.error('订购失败:', error);
        throw error;
      }
    },
    // 表单规则
    rules: {
      deviceNos: [{ required: true, message: '请输入设备编号' }],
      packType: [{ required: true, message: '请选择套餐类型' }],
      packageId: [{ required: true, message: '请选择充值套餐' }],
      takeeffectType: [{ required: true, message: '请选择生效类型' }],
    },
  });

  // 获取套餐列表
  const loadPackageOptions = async (packType: number) => {
    if (!deviceInfo.value || !deviceInfo.value.id) {
      return;
    }

    try {
      // 设置加载状态
      form.loading.value = true;

      const res = await getPackageListPay({
        deviceId: Number(deviceInfo.value.id),
        packType,
      });

      if (res.code === 1 && res.data) {
        packageOptions.value =
          res.data?.map((item: any) => ({
            label: `${item.name}(成本:${item.packageCost}元,售价:${item.packagePrice}元)`,
            value: item.id,
          })) || [];
      }
    } catch (error) {
      console.error('获取套餐列表失败:', error);
      message.error('获取套餐列表失败');
    } finally {
      form.loading.value = false;
    }
  };

  // 获取设备信息
  const fetchDeviceInfo = async (deviceNo: string) => {
    try {
      const { data } = await getDeviceInfo(deviceNo);
      deviceInfo.value = data;

      // 设备信息获取成功后加载套餐选项
      if (data && data.id) {
        await loadPackageOptions(form.formData.packType);
      }

      return data;
    } catch (error) {
      console.error('获取设备信息失败:', error);
      message.error('获取设备信息失败');
      return null;
    }
  };

  // 处理套餐类型变更
  const handlePackTypeChange = async (value: number) => {
    // 清空已选择的套餐
    form.formData.packageId = null;

    // 重新加载套餐列表
    await loadPackageOptions(value);
  };

  // 处理设备编号失焦，执行格式化和获取设备信息
  const handleDeviceNosBlur = () => {
    // 格式化设备编号
    const formattedValue = formatDeviceNos(form.formData.deviceNos);

    // 只有当内容有变化时才更新表单值，避免光标位置重置
    if (formattedValue !== form.formData.deviceNos) {
      form.formData.deviceNos = formattedValue;
    }

    // 获取第一个设备编号
    const firstDeviceNo = formattedValue.split('\n')[0];
    if (firstDeviceNo) {
      fetchDeviceInfo(firstDeviceNo);
    }
  };

  // 显示批量订购弹窗
  const showModal = async (deviceNos = '') => {
    // 重置设备信息
    deviceInfo.value = null;

    // 重置套餐选项
    packageOptions.value = [];

    // 显示表单并设置初始值
    form.show();

    // 如果有设备号码，设置到表单并获取第一个设备的信息
    if (deviceNos) {
      const formattedDeviceNos = formatDeviceNos(deviceNos);
      form.setFormData({ deviceNos: formattedDeviceNos });

      const firstDeviceNo = formattedDeviceNos.split('\n')[0];
      if (firstDeviceNo) {
        await fetchDeviceInfo(firstDeviceNo);
      }
    }
  };

  // 表单分组
  const formGroups = computed((): FormGroup[] => [
    {
      title: '设备信息',
      fields: [
        {
          name: 'deviceNos',
          label: '设备编号',
          component: 'TextArea',
          col: { span: 24 },
          props: {
            placeholder:
              '请输入设备编号，支持多种分隔方式，将自动转为一行一个编号',
            rows: 6,
            onBlur: handleDeviceNosBlur,
          },
        },
      ],
    },
    {
      title: '套餐信息',
      fields: [
        {
          name: 'packType',
          label: '套餐类型',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择套餐类型',
            options: packTypeOptions,
            onChange: handlePackTypeChange,
          },
        },
        {
          name: 'packageId',
          label: '充值套餐',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择充值套餐',
            options: packageOptions.value,
            loading: form.loading.value,
          },
        },
        {
          name: 'takeeffectType',
          label: '生效类型',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择生效类型',
            options: takeeffectTypeOptions,
          },
        },
      ],
    },
  ]);

  // 渲染表单弹窗
  const renderBatchOrderModal = () => {
    return form.renderFormModal(formGroups.value);
  };

  return {
    form,
    showModal,
    renderBatchOrderModal,
  };
}
