import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed, ref } from 'vue';

import { message } from 'ant-design-vue';

import {
  correctDeviceCardByImport,
  downloadDeviceCorrectTemplate,
} from '#/api/core/device';
import { useForm } from '#/hooks/useAnsheng';
import { handleFileDownload } from '#/utils/export';

/**
 * 设备号码矫正 Hook
 */
export function useDeviceCorrectModal(
  options: { onSuccess?: () => void } = {},
) {
  // 选择的文件
  const selectedFile = ref<File | null>(null);

  // 使用表单Hook
  const form = useForm({
    title: '矫正号码',
    width: 600,
    simpleLayout: {
      showGroupTitle: false, // 不显示分组标题
      padding: 0, // 更小的内边距
    },
    defaultValues: {
      file: null as File | null,
    },
    // 表单提交处理
    create: async (values) => {
      // 先检查表单值中是否有文件，再检查selectedFile
      const fileToSubmit = values.file || selectedFile.value;

      if (!fileToSubmit) {
        throw new Error('请上传文件');
      }

      // 直接调用API并返回结果，不捕获错误
      return await correctDeviceCardByImport(fileToSubmit);
    },
    // 表单规则
    rules: {
      file: [{ required: true, message: '请上传文件' }],
    },
    // 成功回调
    onSuccess: () => {
      options.onSuccess?.();
    },
  });

  // 处理文件上传
  const handleFileUpload = (file: File) => {
    message.success(`文件 ${file.name} 上传成功`);

    // 设置已上传的文件
    selectedFile.value = file;

    // 同时更新表单的file值
    form.formData.file = file;

    // 手动触发文件字段的验证
    if (form.formRef && form.formRef.value) {
      form.formRef.value.validateFields(['file']).catch(() => {
        // 忽略验证错误
      });
    }

    return false; // 阻止自动上传
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      const res = await downloadDeviceCorrectTemplate();
      await handleFileDownload(res, '设备卡号矫正模板.xlsx');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  // 删除文件
  const handleRemoveFile = () => {
    selectedFile.value = null;
    form.formData.file = null;

    // 手动触发文件字段的验证
    if (form.formRef && form.formRef.value) {
      form.formRef.value.validateFields(['file']).catch(() => {
        // 忽略验证错误
      });
    }
  };

  // 表单分组
  const formGroups = computed((): FormGroup[] => [
    {
      title: '矫正信息',
      fields: [
        {
          name: 'file',
          label: '导入文件',
          component: 'UploadBox',
          col: { span: 24 },
          props: {
            accept: '.xlsx,.xls,.csv',
            maxCount: 1,
            supportText: '支持 .xlsx, .xls, .csv 格式文件',
            downloadText: '下载模板',
            onDownload: handleDownloadTemplate,
            onUpload: handleFileUpload,
            onRemove: handleRemoveFile,
            value: selectedFile.value,
          },
          // 使用helpText显示已上传文件信息
          helpText: selectedFile.value
            ? `已上传文件: ${selectedFile.value.name}`
            : '',
        },
      ],
    },
  ]);

  // 显示矫正弹窗
  const showCorrectModal = () => {
    selectedFile.value = null;
    form.formData.file = null;
    form.show();
  };

  // 渲染表单弹窗
  const renderCorrectModal = () => {
    return form.renderFormModal(formGroups.value);
  };

  return {
    form,
    formGroups,
    showCorrectModal,
    renderCorrectModal,
    handleDownloadTemplate,
  };
}
