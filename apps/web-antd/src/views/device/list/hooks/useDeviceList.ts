import type { CustomButton } from '#/hooks/useAnsheng/types';

import { computed, h, ref } from 'vue';

import { MdiFileEdit, MdiFileExport, MdiPackage, MdiPlus } from '@vben/icons';

import { exportDevice, getDeviceList } from '#/api/core/device';
import { useAnsheng } from '#/hooks/useAnsheng';
import { useExportNotify } from '#/hooks/useExportNotify';

import {
  advancedSerachItems,
  basicSerachItems,
  createColumns,
  statusOptions,
} from '../config';
import { useBatchOrderModal } from './useBatchOrderModal';
import { useDeviceActions } from './useDeviceActions';
import { useDeviceAssign } from './useDeviceAssign';
import { useDeviceCorrectModal } from './useDeviceCorrectModal';
import { useDeviceRecycle } from './useDeviceRecycle';
import { useImportDevice } from './useImportDevice';

/**
 * 设备列表业务逻辑Hook
 */
export function useDeviceList() {
  // 声明弹窗控制方法，后面再初始化
  let showImportModal: () => void;
  let showBatchOrderModal: () => void;
  let showCorrectModal: () => void;
  let showDeviceAssignModal: () => void;
  let showDeviceRecycleModal: () => void;
  // 刷新
  const refresh = ref(null);
  const loadingAs = ref(null);
  const { showExportModal } = useExportNotify();

  // 使用设备操作Hook
  const deviceActions = useDeviceActions(
    () => (refresh?.value as any)?.getList?.(),
    loadingAs,
  );

  // 使用useAnsheng整合表格、搜索功能
  const {
    loading,
    initialize,
    handleTableChange,
    searchToolbarBind,
    tableBind: origTableBind,
    table,
  } = useAnsheng({
    // 表格配置
    tableOptions: {
      api: getDeviceList,
      columns: createColumns() as any, // 使用类型断言解决类型不匹配问题
      defaultParams: {},
      // 添加操作按钮换行配置
      actionWrap: true,
      // 添加最大可见按钮数量配置
      maxVisibleButtons: 5,
      actionButtons: [
        // 更新
        {
          key: 'update',
          text: '更新设备',
          // type: 'default',
          onClick: (record: any): Promise<void> =>
            deviceActions.handleUpdateInfo(record),
        },
        // 设备复机
        {
          key: 'restart',
          text: '设备复机',
          // type: 'default',
          onClick: (record: any) => {
            console.log(record);
            console.log(record.id);
            return deviceActions.handleDeviceRestart(record);
          },
        },
        // 密码
        {
          key: 'password',
          text: '修改密码',
          // type: 'default',
          onClick: (record: any) => deviceActions.handleUpdatePassword(record),
        },
        // 隐藏
        {
          key: 'hide',
          text: '隐藏设备',
          // type: 'default',
          onClick: (record: any) => deviceActions.handleToggleHide(record),
        },
        // 远程控制
        {
          key: 'remoteControl',
          text: '远程控制',
          // type: 'default',
          onClick: (record: any) => deviceActions.handleRemoteControl(record),
        },
        // 切换网络
        {
          key: 'switchNetwork',
          text: '切换网络',
          // type: 'default',
          onClick: (record: any) => deviceActions.handleSwitchNetwork(record),
        },
        // 切换主卡
        {
          key: 'switchMainCard',
          text: '切换主卡',
          // type: 'default',
          onClick: (record: any) => deviceActions.handleSwitchMainCard(record),
        },
        // 删除
        {
          key: 'delete',
          text: '删除设备',
          danger: true,
          type: 'default',
          permission: 1,
          onClick: (record: any) => deviceActions.handleDelete(record),
        },
      ],
    },
    // 搜索配置
    searchOptions: {
      basicItems: basicSerachItems,
      advancedItems: advancedSerachItems,
      customButtons: [
        // 导出
        {
          text: '导出设备',
          icon: h(MdiFileExport),
          onClick: async () => {
            loading.value = true;
            await exportDevice(table.searchParams);
            loading.value = false;
            showExportModal();
          },
        },
        // 导入
        {
          text: '导入设备',
          icon: h(MdiPlus),
          onClick: () => showImportModal(),
          type: 'default',
        },
        // 批量订购
        {
          text: '批量订购',
          icon: h(MdiPackage),
          onClick: () => showBatchOrderModal(),
        },
        // 矫正号码
        {
          text: '矫正号码',
          icon: h(MdiFileEdit),
          onClick: () => showCorrectModal(),
        },
        // 分配设备
        {
          text: '分配设备',
          icon: h(MdiFileEdit),
          onClick: () => showDeviceAssignModal(),
        },
        // 设备回收
        {
          text: '设备回收',
          icon: h(MdiFileEdit),
          onClick: () => showDeviceRecycleModal(),
        },
      ] as CustomButton[],
    },
    formOptions: {
      title: '设备表单',
      defaultValues: {},
    },
  });

  refresh.value = table as any;
  loadingAs.value = loading;

  // 设置actionButtons

  // 初始化各个模态框
  // 导入设备弹窗
  const { renderImportModal, form: importForm } = useImportDevice({
    onSuccess: () => initialize(),
  });
  showImportModal = () => importForm.show();

  // 批量订购弹窗
  const { renderBatchOrderModal, showModal } = useBatchOrderModal({
    onSuccess: () => initialize(),
  });
  showBatchOrderModal = showModal;

  // 矫正号码弹窗
  const { renderCorrectModal, form: correctForm } = useDeviceCorrectModal({
    onSuccess: () => initialize(),
  });
  showCorrectModal = () => correctForm.show();

  // 设备分配弹窗
  const { renderDeviceAssignModal, form: assignForm } = useDeviceAssign({
    onSuccess: () => initialize(),
  });
  showDeviceAssignModal = () => assignForm.show();

  // 设备回收弹窗
  const { renderDeviceRecycleModal, form: recycleForm } = useDeviceRecycle({
    onSuccess: () => initialize(),
  });
  showDeviceRecycleModal = () => recycleForm.show();
  // 表格绑定对象 - 直接使用固定的actionButtons
  const tableBind = computed(() => ({
    ...origTableBind.value,
    onChange: handleTableChange,
  }));
  return {
    // 从useAnsheng获取的状态和方法
    loading,
    initialize,
    searchToolbarBind,
    tableBind,
    // 状态选项
    statusOptions,
    // 弹窗组件
    renderImportModal,
    renderBatchOrderModal,
    renderCorrectModal,
    renderDeviceAssignModal,
    renderDeviceRecycleModal,
    // 设备操作表单
    renderPasswordModal: deviceActions.renderPasswordModal,
    renderHideModal: deviceActions.renderHideModal,
    renderControlModal: deviceActions.renderControlModal,
    renderNetworkModal: deviceActions.renderNetworkModal,
    renderMainCardModal: deviceActions.renderMainCardModal,
    renderRestartModal: deviceActions.renderRestartModal,
  };
}
