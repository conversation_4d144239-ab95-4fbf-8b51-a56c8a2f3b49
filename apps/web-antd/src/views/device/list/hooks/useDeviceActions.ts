import type { FormGroup } from '#/hooks/useAnsheng/types';

import { computed, ref } from 'vue';

import { message, Modal } from 'ant-design-vue';

import {
  batchOrderDevice,
  changeNetwork,
  deleteDevice,
  deviceControl,
  deviceRestartApi,
  deviceStopApi,
  deviceTransferApi,
  getPackageListPay,
  limitMultipleCards,
  updateDeviceBalanceApi,
  updateDeviceInfo,
  updateDeviceWifi,
  updateMasterCard,
  updateWifiHide,
} from '#/api/core/device';
import {
  getDeviceAnnouncementList,
  getSingleDeviceAnnouncementConfigList,
  setDeviceAnnouncement,
  setSingleDeviceAnnouncement,
} from '#/api/core/deviceAnnouncement';
import { useForm } from '#/hooks/useAnsheng';
import { getFileUrl } from '#/utils/file';

/**
 * 设备操作相关的Hook
 * 包含所有设备操作的表单和处理函数
 */
export function useDeviceActions(refreshList: () => void, loading?: any) {
  // 当前设备ID
  const currentDeviceId = ref<null | number>(null);
  // 当前设备
  // const currentDevice = ref<null | number>(null);
  // 当前设备号
  const currentDeviceNo = ref<null | string>(null);
  // 修改WIFI密码表单
  const passwordForm = useForm({
    title: '修改WIFI密码',
    width: 500,
    draggable: true,
    simpleLayout: {
      showGroupTitle: true, // 不显示分组标题
      padding: 0, // 更小的内边距
    },
    defaultValues: {
      wifiType: '2.4G',
      wifiName: '',
      wifiPassword: '',
    },
    create: async (values) => {
      const res = await updateDeviceWifi({
        deviceId: currentDeviceId.value ?? 1,
        type: values.wifiType === '2.4G' ? '1' : '2',
        wifiName: values.wifiName,
        wifiPwd: values.wifiPassword,
      });
      if (res.code === 1) {
        refreshList();
        return res;
      }
      // throw new Error(res.msg || '修改失败');
    },
    rules: {
      wifiType: [{ required: true, message: '请选择WIFI类型' }],
      wifiName: [{ required: true, message: '请输入WIFI名称' }],
      wifiPassword: [{ required: true, message: '请输入WIFI密码' }],
    },
  });

  // 修改WIFI密码表单分组
  const passwordFormGroups = computed((): FormGroup[] => [
    {
      title: 'WIFI信息',
      fields: [
        {
          name: 'wifiType',
          label: 'WIFI类型',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择WIFI类型',
            options: [
              { label: '2.4G', value: '2.4G' },
              { label: '5G', value: '5G' },
            ],
          },
        },
        {
          name: 'wifiName',
          label: 'WIFI名称',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入WIFI名称',
          },
        },
        {
          name: 'wifiPassword',
          label: 'WIFI密码',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入WIFI密码',
          },
        },
      ],
    },
  ]);

  // 修改WIFI隐藏状态表单
  const hideForm = useForm({
    title: '修改WIFI隐藏状态',
    width: 400,
    simpleLayout: {
      showGroupTitle: false, // 不显示分组标题
      padding: 0, // 更小的内边距
    },
    defaultValues: {
      wifiType: '2.4G',
      isHidden: false,
    },
    create: async (values) => {
      const res = await updateWifiHide({
        deviceId: currentDeviceId.value ?? 1,
        type: values.wifiType === '2.4G' ? '1' : '2',
        hide: values.isHidden ? '1' : '2',
      });
      if (res.code === 1) {
        message.success('WIFI隐藏状态修改成功');
        refreshList();
        return res;
      }
      throw new Error(res.msg || '修改失败');
    },
    rules: {
      wifiType: [{ required: true, message: '请选择WIFI类型' }],
      isHidden: [{ required: true, message: '请选择隐藏状态' }],
    },
  });

  // 修改WIFI隐藏状态表单分组
  const hideFormGroups = computed((): FormGroup[] => [
    {
      title: 'WIFI设置',
      fields: [
        {
          name: 'wifiType',
          label: 'WIFI类型',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择WIFI类型',
            options: [
              { label: '2.4G', value: '2.4G' },
              { label: '5G', value: '5G' },
            ],
          },
        },
        {
          name: 'isHidden',
          label: '隐藏状态',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择隐藏状态',
            options: [
              { label: '显示', value: false },
              { label: '隐藏', value: true },
            ],
          },
        },
      ],
    },
  ]);

  // 远程控制表单
  const controlForm = useForm({
    title: '远程控制',
    width: 400,
    simpleLayout: {
      showGroupTitle: false, // 不显示分组标题
      padding: 0, // 更小的内边距
    },
    defaultValues: {
      action: '1',
    },
    create: async (values) => {
      const res = await deviceControl({
        deviceId: currentDeviceId.value ?? 0,
        type: values.action as '1' | '2' | '3',
      });
      if (res.code === 1) {
        message.success('远程控制操作成功');
        refreshList();
        return res;
      }
      throw new Error(res.msg || '操作失败');
    },
    rules: {
      action: [{ required: true, message: '请选择控制操作' }],
    },
  });

  // 远程控制表单分组
  const controlFormGroups = computed((): FormGroup[] => [
    {
      title: '控制操作',
      fields: [
        {
          name: 'action',
          label: '控制操作',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择控制操作',
            options: [
              { label: '关机设备', value: '1' },
              { label: '重启设备', value: '2' },
              { label: '恢复出厂设置', value: '3' },
            ],
          },
        },
      ],
    },
  ]);

  // 切换网络表单
  const networkForm = useForm({
    title: '切换网络',
    width: 400,
    simpleLayout: {
      showGroupTitle: false, // 不显示分组标题
      padding: 0, // 更小的内边距
    },
    defaultValues: {
      networkType: '1',
    },
    create: async (values) => {
      const res = await changeNetwork({
        deviceId: currentDeviceId.value ?? 0,
        switching: values.networkType as '1' | '2' | '3' | '4',
      });
      if (res.code === 1) {
        message.success('网络切换成功');
        refreshList();
        return res;
      }
      throw new Error(res.msg || '切换失败');
    },
    rules: {
      networkType: [{ required: true, message: '请选择网络类型' }],
    },
  });

  // 切换网络表单分组
  const networkFormGroups = computed((): FormGroup[] => [
    {
      title: '网络设置',
      fields: [
        {
          name: 'networkType',
          label: '网络类型',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择网络类型',
            options: [
              { label: '中国电信', value: '1' },
              { label: '中国联通', value: '2' },
              { label: '中国移动', value: '3' },
              { label: '中国广电', value: '4' },
            ],
          },
        },
      ],
    },
  ]);

  // 切换主卡表单
  const mainCardForm = useForm({
    title: '切换主卡',
    width: 400,
    simpleLayout: {
      showGroupTitle: false, // 不显示分组标题
      padding: 0, // 更小的内边距
    },
    defaultValues: {
      cardSlot: 1,
    },
    create: async (values) => {
      const res = await updateMasterCard({
        deviceId: currentDeviceId.value ?? 0,
        num: String(values.cardSlot) as '1' | '2' | '3' | '4',
      });
      if (res.code === 1) {
        message.success('主卡切换成功');
        refreshList();
        return res;
      }
      throw new Error(res.msg || '切换失败');
    },
    rules: {
      cardSlot: [{ required: true, message: '请选择卡槽' }],
    },
  });

  // 切换主卡表单分组
  const mainCardFormGroups = computed((): FormGroup[] => [
    {
      title: '卡槽设置',
      fields: [
        {
          name: 'cardSlot',
          label: '卡槽选择',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择卡槽',
            options: [
              { label: '卡槽1', value: 1 },
              { label: '卡槽2', value: 2 },
              { label: '卡槽3', value: 3 },
              { label: '卡槽4', value: 4 },
            ],
          },
        },
      ],
    },
  ]);

  // 更新设备信息
  const handleUpdateInfo = async (record: any) => {
    try {
      loading.value = true;
      const res = await updateDeviceInfo(record.id);
      if (res.code === 1) {
        message.success(res.msg);
        refreshList();
      }
    } catch (error) {
      console.error(error);
      // message.error(error instanceof Error ? error.message : '更新失败');
    } finally {
      loading.value = false;
    }
  };

  // 修改密码
  const handleUpdatePassword = (record: any) => {
    currentDeviceId.value = record.id;
    passwordForm.show();
  };

  // 切换隐藏状态
  const handleToggleHide = (record: any) => {
    currentDeviceId.value = record.id;
    hideForm.show();
  };

  // 远程控制
  const handleRemoteControl = (record: any) => {
    currentDeviceId.value = record.id;
    controlForm.show();
  };

  // 切换网络
  const handleSwitchNetwork = (record: any) => {
    currentDeviceId.value = record.id;
    networkForm.show();
  };

  // 切换主卡
  const handleSwitchMainCard = (record: any) => {
    currentDeviceId.value = record.id;
    mainCardForm.show();
  };

  // 渲染表单弹窗
  const renderPasswordModal = () =>
    passwordForm.renderFormModal(passwordFormGroups.value);
  const renderHideModal = () => hideForm.renderFormModal(hideFormGroups.value);
  const renderControlModal = () =>
    controlForm.renderFormModal(controlFormGroups.value);
  const renderNetworkModal = () =>
    networkForm.renderFormModal(networkFormGroups.value);
  const renderMainCardModal = () =>
    mainCardForm.renderFormModal(mainCardFormGroups.value);

  // 删除设备
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除该设备吗？此操作不可恢复！',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const res = await deleteDevice(record.id);
          if (res.code === 1) {
            message.success('删除成功');
            refreshList();
          } else {
            throw new Error(res.msg || '删除失败');
          }
        } catch (error) {
          message.error(error instanceof Error ? error.message : '删除失败');
        }
      },
    });
  };

  // 设备重启表单
  const restartForm = useForm({
    title: '设备复机',
    width: 400,
    simpleLayout: {
      showGroupTitle: false,
      padding: 0,
    },
    defaultValues: {
      type: '1',
    },
    create: async (values) => {
      const res = await deviceRestartApi({
        deviceId: currentDeviceId.value ?? 0,
        type: values.type as '1' | '2',
      });
      if (res.code === 1) {
        message.success('设备复机成功');
        refreshList();
        return res;
      }
      throw new Error(res.msg || '设备复机失败');
    },
    rules: {
      type: [{ required: true, message: '请选择复机类型' }],
    },
  });

  // 设备重启表单分组
  const restartFormGroups = computed((): FormGroup[] => [
    {
      title: '复机设置',
      fields: [
        {
          name: 'type',
          label: '复机类型',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择复机类型',
            options: [
              { label: '复机所有', value: '1' },
              { label: '复机主卡', value: '2' },
            ],
          },
        },
      ],
    },
  ]);

  // 设备停机确认
  const handleDeviceStop = (record: any) => {
    Modal.confirm({
      title: '确认停机',
      content: '确定要对该设备进行停机操作吗？',
      onOk: async () => {
        try {
          const res = await deviceStopApi({ deviceId: record.id });
          if (res.code === 1) {
            message.success('设备停机成功');
            refreshList();
          } else {
            message.error(res.msg || '设备停机失败');
          }
        } catch (error) {
          message.error('操作失败');
          console.error('设备停机失败:', error);
        }
      },
    });
  };

  // 设备重启
  const handleDeviceRestart = (record: any) => {
    currentDeviceId.value = record.id;
    restartForm.show();
  };

  // 修改余额表单
  const balanceForm = useForm({
    title: '修改余额',
    width: 500,
    simpleLayout: {
      showGroupTitle: false,
      padding: 0,
    },
    defaultValues: {
      type: '1',
      balance: '',
      msg: '',
    },
    create: async (values) => {
      const res = await updateDeviceBalanceApi({
        deviceId: currentDeviceId.value ?? 0,
        type: values.type as '1' | '2',
        balance: values.balance,
        msg: values.msg,
      });
      if (res.code === 1) {
        message.success('修改余额成功');
        refreshList();
        return res;
      }
      throw new Error(res.msg || '修改余额失败');
    },
    rules: {
      type: [{ required: true, message: '请选择操作类型' }],
      balance: [
        { required: true, message: '请输入金额' },
        {
          pattern: /^\d+(\.\d{1,2})?$/,
          message: '请输入正确的金额格式',
        },
      ],
    },
  });

  // 修改余额表单分组
  const balanceFormGroups = computed((): FormGroup[] => [
    {
      title: '余额设置',
      fields: [
        {
          name: 'type',
          label: '操作类型',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择操作类型',
            options: [
              { label: '增加余额', value: '1' },
              { label: '扣除余额', value: '2' },
            ],
          },
        },
        {
          name: 'balance',
          label: '金额',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入金额',
          },
        },
        {
          name: 'msg',
          label: '备注',
          component: 'TextArea',
          col: { span: 24 },
          props: {
            placeholder: '请输入备注信息',
            rows: 4,
          },
        },
      ],
    },
  ]);

  // 修改余额
  const handleUpdateBalance = (record: any) => {
    currentDeviceId.value = record.id;
    balanceForm.show();
  };

  // 设备转移表单
  const transferForm = useForm({
    title: '设备转移',
    width: 500,
    simpleLayout: {
      showGroupTitle: false,
      padding: 0,
    },
    defaultValues: {
      newDeviceNo: '',
    },
    create: async (values) => {
      const res = await deviceTransferApi({
        oidDeviceNo: currentDeviceNo.value ?? '',
        newDeviceNo: values.newDeviceNo,
      });
      if (res.code === 1) {
        message.success('设备转移成功');
        refreshList();
        return res;
      }
      throw new Error(res.msg || '设备转移失败');
    },
    rules: {
      newDeviceNo: [{ required: true, message: '请输入新设备号' }],
    },
  });

  // 设备转移表单分组
  const transferFormGroups = computed((): FormGroup[] => [
    {
      title: '转移设置',
      fields: [
        {
          name: 'newDeviceNo',
          label: '新设备号',
          component: 'Input',
          col: { span: 24 },
          props: {
            placeholder: '请输入新设备号',
          },
        },
      ],
    },
  ]);

  // 设备转移
  const handleDeviceTransfer = (record: any) => {
    currentDeviceNo.value = record.deviceNo;
    transferForm.show();
  };

  // 限制多卡表单
  const limitMultiCardForm = useForm({
    title: '限制多卡',
    width: 400,
    simpleLayout: {
      showGroupTitle: false,
      padding: 0,
    },
    defaultValues: {
      limitType: 1,
    },
    create: async (values) => {
      const res = await limitMultipleCards(
        String(currentDeviceId.value ?? 0),
        values.limitType === 1 ? 1 : 2,
      );
      if (res.code === 1) {
        message.success(res.msg);
        refreshList();
        return res;
      }
      throw new Error(res.msg || '设置失败');
    },
    rules: {
      limitType: [{ required: true, message: '请选择限制类型' }],
    },
  });

  // 限制多卡表单分组
  const limitMultiCardFormGroups = computed((): FormGroup[] => [
    {
      title: '多卡设置',
      fields: [
        {
          name: 'limitType',
          label: '限制类型',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择限制类型',
            options: [
              { label: '限制多卡', value: 1 },
              { label: '允许多卡', value: 2 },
            ],
          },
        },
      ],
    },
  ]);

  // 限制多卡
  const handleLimitMultiCard = (record: any) => {
    currentDeviceId.value = record.id;
    limitMultiCardForm.show();
  };

  // 充值套餐表单
  const rechargeForm = useForm({
    title: '充值套餐',
    width: 500,
    simpleLayout: {
      showGroupTitle: false,
      padding: 0,
    },
    defaultValues: {
      packType: 1,
      packageId: undefined,
      takeeffectType: 1,
    },
    create: async (values) => {
      const res = await batchOrderDevice({
        deviceNos: currentDeviceNo.value ?? '',
        packageId: values.packageId ?? 0,
        takeeffectType: values.takeeffectType,
      });
      if (res.code === 1) {
        message.success(res.msg);
        refreshList();
        return res;
      }
      throw new Error(res.msg || '充值失败');
    },
    rules: {
      packType: [{ required: true, message: '请选择套餐类型' }],
      packageId: [{ required: true, message: '请选择套餐' }],
      takeeffectType: [{ required: true, message: '请选择生效时间' }],
    },
  });

  // 套餐列表
  const packageList = ref([]);

  // 充值套餐表单分组
  const rechargeFormGroups = computed((): FormGroup[] => [
    {
      title: '套餐设置',
      fields: [
        {
          name: 'packType',
          label: '套餐类型',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择套餐类型',
            options: [
              { label: '基础套餐', value: 1 },
              { label: '加油包', value: 2 },
              { label: '加速宝', value: 3 },
              { label: '体验包', value: 4 },
              { label: '短信包', value: 5 },
              { label: '语音包', value: 6 },
            ],
            onChange: async (val: number) => {
              if (val && currentDeviceId.value) {
                try {
                  const res = await getPackageListPay({
                    deviceId: currentDeviceId.value,
                    packType: val,
                  });
                  packageList.value = res.data || [];
                  // 清空已选择的套餐ID
                  rechargeForm?.setFieldValue('packageId', undefined);
                } catch (error) {
                  console.error('获取套餐列表失败:', error);
                }
              }
            },
          },
        },
        {
          name: 'packageId',
          label: '套餐选择',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择套餐',
            options: packageList.value.map((item: any) => ({
              label: `${item.name}(成本:${item.packageCost}元,售价:${item.packagePrice}元)`,
              value: item.id,
            })),
          },
        },
        {
          name: 'takeeffectType',
          label: '生效时间',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择生效时间',
            options: [
              { label: '立即生效', value: 1 },
              { label: '次月生效', value: 2 },
            ],
          },
        },
      ],
    },
  ]);

  // 充值套餐
  const handleRechargePackage = async (record: any) => {
    currentDeviceId.value = record.id;
    currentDeviceNo.value = record.deviceNo;

    // 初始加载基础套餐列表
    try {
      const res = await getPackageListPay({
        deviceId: record.id,
        packType: 1,
      });
      packageList.value = res.data || [];
    } catch (error) {
      console.error('获取套餐列表失败:', error);
    }

    rechargeForm.show();
  };

  // 公告图片URL
  const announcementImageUrl = ref('');
  // 公告列表
  const announcementList: any = ref([]);

  // 设置公告表单分组
  const announcementFormGroups = computed((): FormGroup[] => [
    {
      title: '公告设置',
      fields: [
        {
          name: 'selectedAnnouncement',
          label: '选择公告',
          component: 'Select',
          col: { span: 24 },
          props: {
            placeholder: '请选择公告',
            options: announcementList.value.map((item: any) => ({
              label: item.title,
              value: item.id,
            })),
            onChange: (value: any) => {
              if (value) {
                // 找到选中的公告
                const selectedAnnouncement = announcementList.value.find(
                  (item: any) => item.id === value,
                );
                if (selectedAnnouncement) {
                  announcementImageUrl.value = selectedAnnouncement.content;
                  announcementForm.setFormData({
                    selectedAnnouncement: value,
                    view: selectedAnnouncement.content,
                  });
                }
              } else {
                announcementImageUrl.value = '';
                announcementForm.setFormData({
                  selectedAnnouncement: undefined,
                  view: '',
                });
              }
            },
          },
        },
        {
          name: 'view',
          label: '公告内容',
          component: 'Image',
          col: { span: 24 },
          show: (value) => value.view,
          props: {
            src: getFileUrl(announcementImageUrl.value),
            width: 100,
            height: 100,
            alt: '公告图片',
          },
        },
      ],
    },
  ]);
  // 设置公告表单
  const announcementForm = useForm({
    title: '设置设备公告',
    width: 600,
    simpleLayout: {
      showGroupTitle: false,
      padding: 0,
    },
    defaultValues: {
      selectedAnnouncement: undefined,
      view: '',
    },
    create: async (values) => {
      if (!values.selectedAnnouncement) {
        throw new Error('请选择公告');
      }

      // 检查是否已有公告配置
      let hasExistingAnnouncement = false;
      try {
        const currentConfig = await getSingleDeviceAnnouncementConfigList({
          device_id: String(currentDeviceId.value),
        });
        hasExistingAnnouncement =
          currentConfig.data && currentConfig.data.length > 0;
        announcementImageUrl.value = currentConfig.data[0]?.ac__content;
        // selectedAnnouncement
        announcementForm.setFormData({
          selectedAnnouncement: currentConfig.data[0]?.ac_id,
          view: currentConfig.data[0]?.ac__content,
        });
      } catch (error) {
        console.error('检查公告配置失败:', error);
      }

      let res;
      res = await (hasExistingAnnouncement
        ? setSingleDeviceAnnouncement({
            ac_id: values.selectedAnnouncement,
            device_id: String(currentDeviceId.value),
            view: values.view,
          })
        : setDeviceAnnouncement({
            ac_id: values.selectedAnnouncement,
            device_id: currentDeviceId.value ?? 0,
            view: values.view,
          }));

      if (res.code === 1) {
        refreshList();
        return res;
      }
      throw new Error(res.msg || '设置公告失败');
    },
    rules: {
      selectedAnnouncement: [{ required: true, message: '请选择公告' }],
    },
  });

  // 设置公告
  const handleSetAnnouncement = async (record: any) => {
    currentDeviceId.value = record.id;

    // 加载公告列表
    try {
      const res = await getDeviceAnnouncementList({
        page: 1,
        pageSize: 100,
        ac_type: 2,
      });
      announcementList.value = res.data?.rows || [];
    } catch (error) {
      console.error('获取公告列表失败:', error);
    }

    // 获取当前设备的公告配置
    try {
      const deviceAnnouncementConfig =
        await getSingleDeviceAnnouncementConfigList({
          device_id: String(record.id),
        });

      // 如果设备有公告配置，设置选中值
      if (
        deviceAnnouncementConfig.code === 1 &&
        deviceAnnouncementConfig.data?.length > 0
      ) {
        const selectedAnnouncementId = deviceAnnouncementConfig.data[0].ac_id;

        // 直接使用设备配置中的内容
        const deviceContent = deviceAnnouncementConfig.data[0].ac__content;

        // 设置图片URL
        announcementImageUrl.value = deviceContent;

        announcementForm.setAsyncDefaultValues({
          selectedAnnouncement: 7,
          view: deviceContent,
        });
      }
    } catch (error) {
      console.error('获取设备公告配置失败:', error);
    }

    announcementForm.show();
  };

  // 渲染新增表单弹窗
  const renderRestartModal = () =>
    restartForm.renderFormModal(restartFormGroups.value);
  const renderBalanceModal = () =>
    balanceForm.renderFormModal(balanceFormGroups.value);
  const renderTransferModal = () =>
    transferForm.renderFormModal(transferFormGroups.value);
  const renderLimitMultiCardModal = () =>
    limitMultiCardForm.renderFormModal(limitMultiCardFormGroups.value);
  const renderRechargeModal = () =>
    rechargeForm.renderFormModal(rechargeFormGroups.value);
  const renderAnnouncementModal = () =>
    announcementForm.renderFormModal(announcementFormGroups.value);

  return {
    // 表单实例
    passwordForm,
    hideForm,
    controlForm,
    networkForm,
    mainCardForm,
    restartForm,
    balanceForm,
    transferForm,
    limitMultiCardForm,
    rechargeForm,
    announcementForm,
    // 处理函数
    handleUpdateInfo,
    handleUpdatePassword,
    handleToggleHide,
    handleRemoteControl,
    handleSwitchNetwork,
    handleSwitchMainCard,
    handleDelete,
    handleDeviceRestart,
    handleDeviceStop,
    handleUpdateBalance,
    handleDeviceTransfer,
    handleLimitMultiCard,
    handleRechargePackage,
    handleSetAnnouncement,
    // 渲染函数
    renderPasswordModal,
    renderHideModal,
    renderControlModal,
    renderNetworkModal,
    renderMainCardModal,
    renderRestartModal,
    renderBalanceModal,
    renderTransferModal,
    renderLimitMultiCardModal,
    renderRechargeModal,
    renderAnnouncementModal,
  };
}
