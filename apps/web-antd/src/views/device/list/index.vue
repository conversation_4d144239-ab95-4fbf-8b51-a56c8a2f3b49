<script setup lang="ts">
import { onMounted } from 'vue';

import { Card } from 'ant-design-vue';

import { BasicTable, SearchToolbar } from '#/hooks/useAnsheng';

import { useDeviceList } from './hooks/useDeviceList';

// 获取设备列表数据和方法
const {
  initialize,
  searchToolbarBind,
  tableBind,

  // 模态框组件
  renderImportModal,
  renderBatchOrderModal,
  renderCorrectModal,
  renderDeviceAssignModal,
  renderDeviceRecycleModal,
  // 设备操作表单
  renderPasswordModal,
  renderHideModal,
  renderControlModal,
  renderNetworkModal,
  renderMainCardModal,
  renderRestartModal,
} = useDeviceList();

// 页面加载时获取数据
onMounted(() => {
  initialize();
});
</script>

<template>
  <div class="device-list-page p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <div class="my-4">
        <SearchToolbar v-bind="searchToolbarBind" />
      </div>

      <!-- 设备列表表格 -->
      <BasicTable v-bind="tableBind" />
    </Card>

    <!-- 模态框组件 -->
    <component :is="renderImportModal()" />
    <component :is="renderBatchOrderModal()" />
    <component :is="renderCorrectModal()" />
    <component :is="renderDeviceAssignModal()" />
    <component :is="renderDeviceRecycleModal()" />

    <!-- 设备操作表单 -->
    <component :is="renderPasswordModal()" />
    <component :is="renderHideModal()" />
    <component :is="renderControlModal()" />
    <component :is="renderNetworkModal()" />
    <component :is="renderMainCardModal()" />
    <component :is="renderRestartModal()" />
  </div>
</template>

<style lang="less" scoped>
.device-list-page {
  background-color: var(--background-deep);
}
</style>
