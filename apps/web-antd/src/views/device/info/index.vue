<script lang="ts" setup>
import { ref } from 'vue';

import { MdiMagnify } from '@vben/icons';

import {
  Button,
  Card,
  Empty,
  Form,
  Input,
  message,
  Spin,
  Tabs,
  Tag,
} from 'ant-design-vue';

import { getDeviceInfo } from '#/api';
import { deviceStatusOptions } from '#/constants/device-status';
import { useDeviceActions } from '#/views/device/list/hooks/useDeviceActions';

import BaseInfo from './components/base-info.vue';
import PackageRecords from './components/package-records.vue';
import { basicOperations, deviceOperations, otherOperations } from './config';
// 搜索表单数据
const searchForm = ref({
  deviceNo: '',
});

// 加载状态
const loading = ref(false);

// 设备详情数据
const deviceDetail = ref(null);

// 当前选中的Tab
const activeTab = ref('packageRecords');

// 状态样式映射
const getStatusClass = (status) => {
  const statusMap = {
    1: 'success',
    2: 'warning',
    3: 'error',
  };
  return statusMap[status] || 'default';
};

// 状态文本映射
const getStatusText = (status) => {
  const statusMap = {
    1: '正常',
    2: '警告',
    3: '故障',
  };
  return statusMap[status] || '未知';
};

// 在线状态样式映射
const getOnlineClass = (status) => {
  return status ? 'success' : 'error';
};

// 在线状态文本映射
const getOnlineText = (status) => {
  return status ? '在线' : '离线';
};

// 套餐记录组件引用
const packageRecordsRef = ref();

// 搜索处理
const handleSearch = async () => {
  if (!searchForm.value.deviceNo) {
    message.warning('请输入设备编号');
    return;
  }
  loading.value = true;
  try {
    const { data } = await getDeviceInfo(searchForm.value.deviceNo);
    if (!data) {
      message.error('未找到设备信息');
      deviceDetail.value = null;
      return;
    }
    deviceDetail.value = data;
    // 如果套餐记录组件存在，触发数据加载
    if (packageRecordsRef.value) {
      packageRecordsRef.value.getList();
    }
  } catch (error) {
    console.error('获取设备详情失败:', error);
    message.error('获取设备详情失败，请稍后重试');
    deviceDetail.value = null;
  } finally {
    loading.value = false;
  }
};

// 刷新处理
const handleRefresh = () => {
  handleSearch();
};

// Tab切换处理
const handleTabChange = (key) => {
  console.log('Tab changed:', key);
};
// 设备操作相关方法
const {
  handleUpdateInfo,
  handleUpdatePassword,
  handleToggleHide,
  handleRemoteControl,
  handleSwitchNetwork,
  handleSwitchMainCard,
  handleDelete,
  handleDeviceRestart,
  handleDeviceStop,
  handleUpdateBalance,
  handleDeviceTransfer,
  handleRechargePackage,
  handleLimitMultiCard,
  handleSetAnnouncement,
  // 渲染函数
  renderPasswordModal,
  renderHideModal,
  renderControlModal,
  renderNetworkModal,
  renderMainCardModal,
  renderRestartModal,
  renderBalanceModal,
  renderTransferModal,
  renderLimitMultiCardModal,
  renderRechargeModal,
  renderAnnouncementModal,
} = useDeviceActions(handleRefresh, loading);

// 基础操作处理函数
const handleBasicOperation = (key: string) => {
  if (!deviceDetail.value) return;
  const record = deviceDetail.value;
  switch (key) {
    case 'delete': {
      handleDelete(record);
      break;
    }
    case 'remoteControl': {
      handleRemoteControl(record);
      break;
    }
    case 'switchMainCard': {
      handleSwitchMainCard(record);
      break;
    }
    case 'switchNetwork': {
      handleSwitchNetwork(record);
      break;
    }
    case 'updateInfo': {
      handleUpdateInfo(record);
      break;
    }
    case 'updatePassword': {
      handleUpdatePassword(record);
      break;
    }
  }
};

// 处理隐藏状态切换
const handleToggleHideAction = () => {
  if (!deviceDetail.value) return;
  handleToggleHide(deviceDetail.value);
};

// 处理设备操作
const handleDeviceOperation = (key: string) => {
  if (!deviceDetail.value) return;
  const record = deviceDetail.value;

  switch (key) {
    case 'balance': {
      handleUpdateBalance(record);
      break;
    }
    case 'restart': {
      handleDeviceRestart(record);
      break;
    }
    case 'stop': {
      handleDeviceStop(record);
      break;
    }
  }
};

// 处理其他操作
const handleOtherOperation = (key: string) => {
  if (!deviceDetail.value) return;
  const record = deviceDetail.value;

  switch (key) {
    case 'limitMultiCard': {
      handleLimitMultiCard(record);
      break;
    }
    case 'rechargePackage': {
      handleRechargePackage(record);
      break;
    }
    case 'setAnnouncement': {
      handleSetAnnouncement(record);
      break;
    }
    case 'transfer': {
      handleDeviceTransfer(record);
      break;
    }
  }
};

// 基础操作配置
const basicOperations = [
  {
    key: 'updatePassword',
    label: '修改密码',
    icon: 'MdiKey',
  },
  {
    key: 'remoteControl',
    label: '远程控制',
    icon: 'MdiRemoteDesktop',
  },
  {
    key: 'updateInfo',
    label: '更新信息',
    icon: 'MdiFileEdit',
  },
  {
    key: 'switchMainCard',
    label: '切换主卡',
    icon: 'MdiSimCard',
    permission: 1,
  },
  {
    key: 'switchNetwork',
    label: '切换网络',
    icon: 'MdiNetwork',
  },
  {
    key: 'delete',
    label: '删除设备',
    icon: 'MdiDelete',
    permission: 1,
  },
];

// 其他操作配置
const otherOperations = [
  {
    key: 'transfer',
    label: '设备转移',
    icon: 'MdiTransfer',
  },
  {
    key: 'limitMultiCard',
    label: '限制多卡',
    icon: 'MdiSimCard',
    permission: 1,
  },
  {
    key: 'rechargePackage',
    label: '充值套餐',
    icon: 'MdiCash',
  },
  // {
  //   key: 'deviceUpdate',
  //   label: '设备更新',
  //   icon: 'MdiUpdate',
  // },
  // {
  //   key: 'deviceShutdown',
  //   label: '设备停机',
  //   icon: 'MdiPower',
  // },
  {
    key: 'setAnnouncement',
    label: '设置公告',
    icon: 'MdiMessage',
  },
];
</script>

<template>
  <div class="bg-[var(--background-deep)] p-2">
    <!-- 搜索区域 -->
    <Card
      :bordered="false"
      class="mb-4 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md"
    >
      <Form>
        <div
          class="flex flex-col items-stretch gap-4 md:flex-row md:items-center"
        >
          <div class="flex flex-1 items-center gap-4">
            <span class="w-16 flex-shrink-0 font-medium">设备编号</span>
            <Input
              v-model:value="searchForm.deviceNo"
              placeholder="请输入设备编号"
              allow-clear
              class="w-full"
              @press-enter="handleSearch"
            >
              <template #prefix>
                <MdiMagnify class="text-[16px] opacity-60" />
              </template>
            </Input>
          </div>
          <Button
            type="primary"
            class="w-full md:w-auto"
            :loading="loading"
            @click="handleSearch"
          >
            查询
          </Button>
        </div>
      </Form>
    </Card>

    <!-- 详情内容 -->
    <Spin :spinning="loading">
      <div v-if="deviceDetail" class="space-y-5">
        <Card
          class="rounded-lg shadow-sm transition-all duration-300 hover:shadow-md"
        >
          <!-- 头部信息和状态 -->
          <div
            class="mb-5 flex flex-col items-start justify-between gap-4 rounded-lg bg-gradient-to-br from-[var(--primary-color-light)] to-white/90 p-5 shadow-sm sm:flex-row sm:items-center"
          >
            <div>
              <div class="mb-2 flex items-center gap-3">
                <h2 class="m-0 text-lg font-medium">设备详情</h2>
                <Tag
                  :color="
                    deviceStatusOptions.find(
                      (item) => item.value === deviceDetail?.status,
                    )?.color
                  "
                >
                  {{
                    deviceStatusOptions.find(
                      (item) => item.value === deviceDetail?.status,
                    )?.label
                  }}
                </Tag>
              </div>
              <div class="text-gray-500">
                <span>设备编号: {{ deviceDetail?.deviceNo }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <Card class="mb-4 rounded-lg shadow-sm">
            <div class="flex flex-wrap gap-2">
              <!-- 基础操作 -->
              <Button
                v-for="item in basicOperations"
                :key="item.key"
                type="primary"
                v-permission="item.permission"
                @click="() => handleBasicOperation(item.key)"
              >
                <component :is="item.icon" class="mr-1" />
                {{ item.label }}
              </Button>

              <!-- 设备操作 -->
              <Button
                v-for="item in deviceOperations"
                :key="item.key"
                v-permission="item.permission"
                @click="() => handleDeviceOperation(item.key)"
              >
                <component :is="item.icon" class="mr-1" />
                {{ item.label }}
              </Button>

              <!-- 其他操作 -->
              <Button
                v-for="item in otherOperations"
                :key="item.key"
                v-permission="item.permission"
                @click="() => handleOtherOperation(item.key)"
              >
                <component :is="item.icon" class="mr-1" />
                {{ item.label }}
              </Button>
            </div>
          </Card>

          <!-- 基础信息展示 -->
          <BaseInfo :device-detail="deviceDetail" />

          <!-- Tab页签 -->
          <Card>
            <Tabs
              v-model:active-key="activeTab"
              @change="handleTabChange"
              class="mt-5"
            >
              <Tabs.TabPane key="packageRecords" tab="套餐记录">
                <PackageRecords
                  ref="packageRecordsRef"
                  :device-id="deviceDetail?.id"
                />
              </Tabs.TabPane>
            </Tabs>
          </Card>
        </Card>
      </div>
      <Empty v-else description="请输入设备编号查询" />
    </Spin>

    <!-- 表单弹窗 -->
    <component :is="renderPasswordModal" />
    <component :is="renderHideModal" />
    <component :is="renderControlModal" />
    <component :is="renderNetworkModal" />
    <component :is="renderMainCardModal" />
    <component :is="renderRestartModal" />
    <component :is="renderBalanceModal" />
    <component :is="renderTransferModal" />
    <component :is="renderLimitMultiCardModal" />
    <component :is="renderRechargeModal" />
    <component :is="renderAnnouncementModal" />
  </div>
</template>

<style lang="less" scoped>
.device-info {
  padding: 16px;
  background-color: var(--background-deep);

  .detail-header {
    @apply flex flex-col items-start justify-between gap-4 rounded-lg p-4 sm:flex-row sm:items-center;
  }

  .action-buttons {
    @apply flex flex-col items-end gap-4;

    .operation-group {
      @apply flex gap-2;
    }

    .other-operations {
      @apply flex flex-col items-end;

      .operation-button {
        width: 120px;
        justify-content: flex-start;
      }
    }

    .operation-button {
      width: 120px;
    }
  }
}
</style>
