import { h, ref } from 'vue';

import {
  Form,
  Image,
  Input,
  message,
  Modal,
  Radio,
  Select,
} from 'ant-design-vue';

import {
  batchOrderDevice,
  changeNetwork,
  deleteDevice,
  deviceControl,
  deviceRestartApi,
  deviceStopApi,
  deviceTransferApi,
  getPackageListPay,
  limitMultipleCards,
  updateDeviceBalanceApi,
  updateDeviceInfo,
  // updateDeviceMainCard,
  updateDeviceWifi,
  updateMasterCard,
  updateWifiHide,
} from '#/api';
import {
  getDeviceAnnouncementList,
  getSingleDeviceAnnouncementConfigList,
  setDeviceAnnouncement,
  setSingleDeviceAnnouncement,
} from '#/api/core/deviceAnnouncement';
import { getFileUrl } from '#/utils/file';

export interface UseDeviceOperationsOptions {
  onSuccess?: () => void;
}

export function useDeviceOperations(options: UseDeviceOperationsOptions = {}) {
  // 更新设备信息
  const handleUpdateInfo = async (deviceId: number) => {
    try {
      const res = await updateDeviceInfo(deviceId);
      if (res.code === 1) {
        message.success('更新成功');
        options.onSuccess?.();
      } else {
        throw new Error(res.msg || '更新失败');
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '更新失败');
    }
  };

  // 修改WIFI密码
  const handleUpdatePassword = (deviceId: number) => {
    const formRef = ref();
    const formState = ref({
      type: '1',
      wifiName: '',
      wifiPwd: '',
    });

    Modal.confirm({
      title: '修改WIFI密码',
      width: 500,
      content: () =>
        h('div', [
          h(
            Form,
            {
              ref: formRef,
              model: formState.value,
              labelCol: { span: 6 },
              wrapperCol: { span: 16 },
            },
            [
              h(
                Form.Item,
                {
                  label: 'WIFI类型',
                  name: 'type',
                  rules: [{ required: true, message: '请选择WIFI类型' }],
                },
                () =>
                  h(Radio.Group, {
                    value: formState.value.type,
                    'onUpdate:value': (val: string) => {
                      formState.value.type = val;
                    },
                    options: [
                      { label: '4G WIFI', value: '1' },
                      { label: '5G WIFI', value: '2' },
                    ],
                  }),
              ),
              h(
                Form.Item,
                {
                  label: 'WIFI名称',
                  name: 'wifiName',
                  rules: [{ required: true, message: '请输入WIFI名称' }],
                },
                () =>
                  h(Input, {
                    value: formState.value.wifiName,
                    'onUpdate:value': (val: string) => {
                      formState.value.wifiName = val;
                    },
                    placeholder: '请输入WIFI名称',
                  }),
              ),
              h(
                Form.Item,
                {
                  label: 'WIFI密码',
                  name: 'wifiPwd',
                  rules: [{ required: true, message: '请输入WIFI密码' }],
                },
                () =>
                  h(Input, {
                    value: formState.value.wifiPwd,
                    'onUpdate:value': (val: string) => {
                      formState.value.wifiPwd = val;
                    },
                    placeholder: '请输入WIFI密码',
                  }),
              ),
            ],
          ),
        ]),
      async onOk() {
        try {
          await formRef.value?.validate();
          const res = await updateDeviceWifi({
            deviceId,
            ...formState.value,
          });
          if (res.code === 1) {
            message.success('修改成功');
            options.onSuccess?.();
          } else {
            throw new Error(res.msg || '修改失败');
          }
        } catch (error) {
          message.error(error instanceof Error ? error.message : '修改失败');
        }
      },
    });
  };

  // 远程控制
  const handleRemoteControl = (deviceId: number) => {
    console.log(deviceId, 'deviceId');
    const formRef = ref();
    const formState = ref({
      type: '2', // 默认重启
    });

    Modal.confirm({
      title: '远程控制',
      width: 500,
      content: () =>
        h('div', [
          h(
            Form,
            {
              ref: formRef,
              model: formState.value,
              labelCol: { span: 6 },
              wrapperCol: { span: 16 },
            },
            [
              h(
                Form.Item,
                {
                  label: '控制类型',
                  name: 'type',
                  rules: [{ required: true, message: '请选择控制类型' }],
                },
                () =>
                  h(Radio.Group, {
                    value: formState.value.type,
                    'onUpdate:value': (val: string) => {
                      formState.value.type = val;
                    },
                    options: [
                      { label: '关机', value: '1' },
                      { label: '重启', value: '2' },
                      { label: '出厂设置', value: '3' },
                    ],
                  }),
              ),
            ],
          ),
        ]),
      async onOk() {
        try {
          await formRef.value?.validate();
          const res = await deviceControl({
            deviceId,
            type: formState.value.type,
          });
          if (res.code === 1) {
            message.success('操作成功');
            options.onSuccess?.();
          } else {
            throw new Error(res.msg || '操作失败');
          }
        } catch (error) {
          message.error(error instanceof Error ? error.message : '操作失败');
        }
      },
    });
  };

  // 切换WIFI隐藏状态
  const handleToggleHide = async (
    deviceId: number,
    type: string,
    hide: string,
  ) => {
    try {
      const res = await updateWifiHide({
        deviceId,
        type,
        hide,
      });
      if (res.code === 1) {
        message.success('操作成功');
        options.onSuccess?.();
      } else {
        throw new Error(res.msg || '操作失败');
      }
    } catch (error) {
      message.error(error instanceof Error ? error.message : '操作失败');
    }
  };

  // 切换网络
  const handleSwitchNetwork = async (deviceId: number) => {
    const formRef = ref();
    const formState = ref({
      switching: '1', // 默认电信
    });

    Modal.confirm({
      title: '切换网络',
      width: 500,
      content: () =>
        h('div', [
          h(
            Form,
            {
              ref: formRef,
              model: formState.value,
              labelCol: { span: 6 },
              wrapperCol: { span: 16 },
            },
            [
              h(
                Form.Item,
                {
                  label: '目标网络',
                  name: 'switching',
                  rules: [{ required: true, message: '请选择目标网络' }],
                },
                () =>
                  h(Radio.Group, {
                    value: formState.value.switching,
                    'onUpdate:value': (val: string) => {
                      formState.value.switching = val;
                    },
                    options: [
                      { label: '中国电信', value: '1' },
                      { label: '中国联通', value: '2' },
                      { label: '中国移动', value: '3' },
                      { label: '中国广电', value: '4' },
                    ],
                  }),
              ),
            ],
          ),
        ]),
      async onOk() {
        try {
          await formRef.value?.validate();
          const res = await changeNetwork({
            deviceId,
            switching: formState.value.switching,
          });
          if (res.code === 1) {
            message.success('切换成功');
            options.onSuccess?.();
          } else {
            throw new Error(res.msg || '切换失败');
          }
        } catch (error) {
          message.error(error instanceof Error ? error.message : '切换失败');
          return Promise.reject();
        }
      },
    });
  };

  // 切换主卡
  const handleSwitchMainCard = async (deviceId: number) => {
    const formRef = ref();
    const formState = ref({
      num: '1', // 默认卡槽1
    });

    Modal.confirm({
      title: '切换主卡',
      width: 500,
      content: () =>
        h('div', [
          h(
            Form,
            {
              ref: formRef,
              model: formState.value,
              labelCol: { span: 6 },
              wrapperCol: { span: 16 },
            },
            [
              h(
                Form.Item,
                {
                  label: '目标卡槽',
                  name: 'num',
                  rules: [{ required: true, message: '请选择目标卡槽' }],
                },
                () =>
                  h(Radio.Group, {
                    value: formState.value.num,
                    'onUpdate:value': (val: string) => {
                      formState.value.num = val;
                    },
                    options: [
                      { label: '卡槽1', value: '1' },
                      { label: '卡槽2', value: '2' },
                      { label: '卡槽3', value: '3' },
                      { label: '卡槽4', value: '4' },
                    ],
                  }),
              ),
            ],
          ),
        ]),
      async onOk() {
        try {
          await formRef.value?.validate();
          const res = await updateMasterCard({
            deviceId,
            num: formState.value.num,
          });
          if (res.code === 1) {
            message.success('切换成功');
            options.onSuccess?.();
          } else {
            throw new Error(res.msg || '切换失败');
          }
        } catch (error) {
          message.error(error instanceof Error ? error.message : '切换失败');
          return Promise.reject();
        }
      },
    });
  };

  // 删除设备
  const handleDelete = async (deviceId: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除该设备吗？',
      type: 'warning',
      async onOk() {
        try {
          const res = await deleteDevice(deviceId);
          if (res.code === 1) {
            message.success('删除成功');
            options.onSuccess?.();
          } else {
            throw new Error(res.msg || '删除失败');
          }
        } catch (error) {
          message.error(error instanceof Error ? error.message : '删除失败');
        }
      },
    });
  };

  // 处理设备复机
  const handleDeviceRestart = async (deviceId: number | string) => {
    const formState = ref({
      type: '1', // 默认复机所有
    });
    try {
      Modal.confirm({
        title: '确认复机',
        content: () =>
          h(Radio.Group, {
            value: formState.value.type,
            'onUpdate:value': (val: string) => {
              formState.value.type = val;
            },
            options: [
              { label: '复机所有', value: '1' },
              { label: '复机主卡', value: '2' },
            ],
          }),
        async onOk() {
          const res = await deviceRestartApi({
            deviceId,
            type: formState.value.type as '1' | '2',
          });

          // 根据返回code判断是否成功
          if (res.code === 1) {
            message.success('设备复机成功');
            options.onSuccess?.();
          } else {
            // 显示具体的错误信息
            message.error(res.msg || '设备复机失败');
          }
        },
      });
    } catch (error) {
      message.error('操作失败');
      console.error('设备复机失败:', error);
    }
  };

  // 处理设备停机
  const handleDeviceStop = async (deviceId: number | string) => {
    try {
      Modal.confirm({
        title: '确认停机',
        content: '确定要对该设备进行停机操作吗？',
        async onOk() {
          const res = await deviceStopApi({ deviceId });

          if (res.code === 1) {
            message.success('设备停机成功');
            options.onSuccess?.();
          } else {
            message.error(res.msg || '设备停机失败');
          }
        },
      });
    } catch (error) {
      message.error('操作失败');
      console.error('设备停机失败:', error);
    }
  };

  // 处理修改余额
  const handleUpdateBalance = (
    deviceId: number | string,
    currentBalance?: number | string,
  ) => {
    const formRef = ref();
    const formState = ref({
      type: '1', // 默认加余额
      balance: '', // 金额
      msg: '', // 备注
    });

    Modal.confirm({
      title: '修改余额',
      width: 500,
      content: () =>
        h('div', [
          h(
            'div',
            {
              class: 'mb-4 text-center',
            },
            [
              h('span', { class: 'text-gray-600' }, '当前余额：'),
              h(
                'span',
                {
                  class: 'text-lg font-medium text-primary',
                },
                `${currentBalance || 0} 元`,
              ),
            ],
          ),
          h(
            Form,
            {
              ref: formRef,
              model: formState.value,
              labelCol: { span: 6 },
              wrapperCol: { span: 16 },
            },
            [
              h(
                Form.Item,
                {
                  label: '操作类型',
                  name: 'type',
                  rules: [{ required: true, message: '请选择操作类型' }],
                },
                () =>
                  h(Radio.Group, {
                    value: formState.value.type,
                    'onUpdate:value': (val: string) => {
                      formState.value.type = val;
                    },
                    options: [
                      { label: '增加余额', value: '1' },
                      { label: '扣除余额', value: '2' },
                    ],
                  }),
              ),
              h(
                Form.Item,
                {
                  label: '金额',
                  name: 'balance',
                  rules: [
                    { required: true, message: '请输入金额' },
                    {
                      pattern: /^\d+(\.\d{1,2})?$/,
                      message: '请输入正确的金额格式',
                    },
                  ],
                },
                () =>
                  h(Input, {
                    value: formState.value.balance,
                    'onUpdate:value': (val: string) => {
                      formState.value.balance = val;
                    },
                    placeholder: '请输入金额',
                  }),
              ),
              h(
                Form.Item,
                {
                  label: '备注',
                  name: 'msg',
                },
                () =>
                  h(Input.TextArea, {
                    value: formState.value.msg,
                    'onUpdate:value': (val: string) => {
                      formState.value.msg = val;
                    },
                    placeholder: '请输入备注信息',
                    rows: 4,
                  }),
              ),
            ],
          ),
        ]),
      async onOk() {
        try {
          await formRef.value?.validate();
          const res = await updateDeviceBalanceApi({
            deviceId,
            ...formState.value,
          });

          if (res.code === 1) {
            message.success('修改余额成功');
            options.onSuccess?.();
          } else {
            message.error(res.msg || '修改余额失败');
          }
        } catch (error) {
          message.error('操作失败');
          console.error('修改余额失败:', error);
          return Promise.reject(error);
        }
      },
    });
  };

  // 处理设备转移
  const handleDeviceTransfer = (deviceNo: string) => {
    const formRef = ref();
    const formState = ref({
      newDeviceNo: '', // 新设备号
    });

    Modal.confirm({
      title: '设备转移',
      width: 500,
      content: () =>
        h('div', [
          h(
            Form,
            {
              ref: formRef,
              model: formState.value,
              labelCol: { span: 6 },
              wrapperCol: { span: 16 },
            },
            [
              h(
                Form.Item,
                {
                  label: '原设备号',
                  name: 'oidDeviceNo',
                },
                () =>
                  h(Input, {
                    value: deviceNo,
                    disabled: true,
                  }),
              ),
              h(
                Form.Item,
                {
                  label: '新设备号',
                  name: 'newDeviceNo',
                  rules: [
                    { required: true, message: '请输入新设备号' },
                    {
                      pattern: /^\d+$/,
                      message: '请输入正确的设备号格式',
                    },
                  ],
                },
                () =>
                  h(Input, {
                    value: formState.value.newDeviceNo,
                    'onUpdate:value': (val: string) => {
                      formState.value.newDeviceNo = val;
                    },
                    placeholder: '请输入新设备号',
                  }),
              ),
            ],
          ),
        ]),
      async onOk() {
        try {
          await formRef.value?.validate();
          const res = await deviceTransferApi({
            oidDeviceNo: deviceNo,
            newDeviceNo: formState.value.newDeviceNo,
          });

          if (res.code === 1) {
            message.success('设备转移成功');
            options.onSuccess?.();
          } else {
            message.error(res.msg || '设备转移失败');
          }
        } catch (error) {
          message.error('操作失败');
          console.error('设备转移失败:', error);
          return Promise.reject(error);
        }
      },
    });
  };

  // 限制多卡
  const handleLimitMultiCard = (deviceId: number) => {
    const formRef = ref();
    const formState = ref({
      limitType: 1, // 限制类型
    });

    Modal.confirm({
      title: '限制多卡',
      width: 500,
      content: () =>
        h('div', [
          h(
            Form,
            {
              ref: formRef,
              model: formState.value,
              labelCol: { span: 6 },
              wrapperCol: { span: 16 },
            },
            [
              h(
                Form.Item,
                {
                  label: '限制类型',
                  name: 'limitType',
                  rules: [{ required: true, message: '请选择限制类型' }],
                },
                () =>
                  h(Select, {
                    value: formState.value.limitType,
                    'onUpdate:value': (val: 1 | 2) => {
                      formState.value.limitType = val;
                    },
                    options: [
                      { label: '限制多卡', value: 1 },
                      { label: '允许多卡', value: 2 },
                    ],
                    placeholder: '请选择限制类型',
                  }),
              ),
            ],
          ),
        ]),
      async onOk() {
        try {
          await formRef.value?.validate();
          const res = await limitMultipleCards(
            deviceId,
            formState.value.limitType,
          );

          if (res.code === 1) {
            message.success(res.msg);
            options.onSuccess?.();
          } else {
            message.error(res.msg || '设置失败');
          }
        } catch (error) {
          message.error('操作失败');
          console.error('限制多卡失败:', error);
          return Promise.reject(error);
        }
      },
    });
  };

  // 充值套餐
  const handleRechargePackage = async (device: any) => {
    console.log(device, '1111');
    console.log(device.value.id, '1111');
    const formRef = ref();
    const formState = ref({
      packageId: undefined as number | undefined, // 套餐ID
      takeeffectType: 1, // 生效类型
      packType: 1, // 套餐类型
    });
    const packageList = ref([]);

    // 初始加载套餐列表
    packageList.value = await getPackageListPay({
      deviceId: Number(device.value.id),
      packType: formState.value.packType,
    }).then((res) => {
      console.log(res, '3333');
      return res.data;
    });

    console.log(packageList.value, '2222');

    Modal.confirm({
      title: '充值套餐',
      width: 500,
      content: () =>
        h('div', [
          h(
            Form,
            {
              ref: formRef,
              model: formState.value,
              labelCol: { span: 6 },
              wrapperCol: { span: 16 },
            },
            [
              // packType 套餐类型 1.基础套餐 2.加油包 3.加速宝 4.体验包 5.短信包 6.语音包
              h(
                Form.Item,
                {
                  label: '套餐类型',
                  name: 'packType',
                },
                () =>
                  h(Select, {
                    value: formState.value.packType,
                    'onUpdate:value': async (val: number | undefined) => {
                      formState.value.packType = val;
                      // 当套餐类型改变时，重新获取套餐列表
                      if (val) {
                        packageList.value = await getPackageListPay({
                          deviceId: device.value.id,
                          packType: val,
                        }).then((res) => {
                          return res.data;
                        });
                        // 清空已选择的套餐ID
                        formState.value.packageId = undefined;
                        // packageList.value = [];
                      }
                    },
                    options: [
                      { label: '基础套餐', value: 1 },
                      { label: '加油包', value: 2 },
                      { label: '加速宝', value: 3 },
                      { label: '体验包', value: 4 },
                      { label: '短信包', value: 5 },
                      { label: '语音包', value: 6 },
                    ],
                    placeholder: '请选择套餐类型',
                  }),
              ),
              h(
                Form.Item,
                {
                  label: '套餐选择',
                  name: 'packageId',
                  rules: [{ required: true, message: '请选择套餐' }],
                },
                () =>
                  h(Select, {
                    value: formState.value.packageId,
                    'onUpdate:value': (val: number | undefined) => {
                      formState.value.packageId = val;
                    },
                    options:
                      packageList.value.map((item: any) => ({
                        label: `${item.name}(成本:${item.packageCost}元,售价:${item.packagePrice}元)`,
                        value: item.id,
                      })) || [],
                    placeholder: '请选择套餐',
                  }),
              ),
              h(
                Form.Item,
                {
                  label: '生效时间',
                  name: 'takeeffectType',
                  rules: [{ required: true, message: '请选择生效时间' }],
                },
                () =>
                  h(Select, {
                    value: formState.value.takeeffectType,
                    'onUpdate:value': (val: number | undefined) => {
                      if (val === 1 || val === 2) {
                        formState.value.takeeffectType = val;
                      }
                    },
                    options: [
                      { label: '立即生效', value: 1 },
                      { label: '次月生效', value: 2 },
                    ],
                    placeholder: '请选择生效时间',
                  }),
              ),
            ],
          ),
        ]),
      async onOk() {
        try {
          await formRef.value?.validate();

          if (!formState.value.packageId) {
            message.error('请选择套餐');
            return;
          }

          const res = await batchOrderDevice({
            deviceNos: device.value.deviceNo,
            packageId: formState.value.packageId,
            takeeffectType: formState.value.takeeffectType,
          });

          if (res.code === 1) {
            message.success(res.msg);
            options.onSuccess?.();
          } else {
            message.error(res.msg || '充值失败');
          }
        } catch (error) {
          // message.error('操作失败');
          console.error('充值套餐失败:', error);
          throw error;
        }
      },
    });
  };

  // 设置公告
  const handleSetAnnouncement = async (deviceId: number) => {
    const formRef = ref();
    const formState = ref({
      announcementType: 2, // 默认使用类型2
      content: '', // 公告内容
      selectedAnnouncement: undefined as number | undefined, // 选中的公告ID
      view: '1', // 默认可见
      hasExistingAnnouncement: false, // 是否已有公告配置
    });

    // 加载当前设备公告
    let currentDeviceAnnouncements: any[] = [];
    try {
      // 获取设备公告列表
      const announcements = await getDeviceAnnouncementList({
        page: 1,
        pageSize: 100,
        ac_type: 2, // 指定类型为2
      });

      if (announcements.code === 1 && announcements.data.rows.length > 0) {
        currentDeviceAnnouncements = announcements.data.rows;
      }

      // 获取当前设备的公告配置
      const deviceAnnouncementConfig =
        await getSingleDeviceAnnouncementConfigList({
          device_id: String(deviceId),
        });

      // 如果设备有公告配置，设置选中值
      if (
        deviceAnnouncementConfig.code === 1 &&
        deviceAnnouncementConfig.data?.length > 0
      ) {
        formState.value.hasExistingAnnouncement = true;
        formState.value.selectedAnnouncement =
          deviceAnnouncementConfig.data[0].ac_id;

        // 从当前公告列表中找到对应的公告内容
        const selectedAnnouncement = currentDeviceAnnouncements.find(
          (item) => item.id === formState.value.selectedAnnouncement,
        );
        if (selectedAnnouncement) {
          formState.value.content = selectedAnnouncement.content;
          // 设置 view 为公告内容
          formState.value.view = selectedAnnouncement.content;
        }
      }
    } catch (error) {
      console.error('获取设备公告失败:', error);
    }

    // 公告选项
    const announcementOptions = currentDeviceAnnouncements.map((item) => ({
      label: item.title,
      value: item.id,
    }));

    // 检查内容是否是图片链接
    const isImageUrl = (url: string) => {
      if (!url) return false;
      return (
        url.match(/\.(jpeg|jpg|gif|png)$/) !== null || url.startsWith('http')
      );
    };

    Modal.confirm({
      title: '设置设备公告',
      width: 500,
      content: () =>
        h('div', [
          h(
            Form,
            {
              ref: formRef,
              model: formState.value,
              labelCol: { span: 6 },
              wrapperCol: { span: 16 },
            },
            [
              h(
                Form.Item,
                {
                  label: '选择公告',
                  name: 'selectedAnnouncement',
                  rules: [{ required: true, message: '请选择公告' }],
                },
                () =>
                  h(Select, {
                    value: formState.value.selectedAnnouncement,
                    'onUpdate:value': (val: any) => {
                      if (typeof val === 'number') {
                        formState.value.selectedAnnouncement = val;

                        // 更新内容
                        const selectedAnnouncement =
                          currentDeviceAnnouncements.find(
                            (item) => item.id === val,
                          );
                        if (selectedAnnouncement) {
                          formState.value.content =
                            selectedAnnouncement.content;
                          // 设置 view 为公告内容
                          formState.value.view = selectedAnnouncement.content;
                        }
                      }
                    },
                    options: announcementOptions,
                    placeholder: '请选择公告',
                    style: { width: '100%' },
                  }),
              ),
              // 如果内容是图片链接，显示图片
              formState.value.content && isImageUrl(formState.value.content)
                ? h(
                    Form.Item,
                    {
                      label: '公告图片',
                      name: 'contentImage',
                    },
                    () =>
                      h(Image, {
                        src: getFileUrl(formState.value.content),
                        width: 100,
                        height: 100,
                        alt: '公告图片',
                      }),
                  )
                : (formState.value.content
                  ? h(
                      Form.Item,
                      {
                        label: '公告内容',
                        name: 'content',
                      },
                      () =>
                        h(Input.TextArea, {
                          value: formState.value.content,
                          placeholder: '公告内容将根据选择自动填充',
                          rows: 4,
                          disabled: true,
                        }),
                    )
                  : null),
            ].filter(Boolean),
          ),
        ]),
      async onOk() {
        try {
          await formRef.value?.validate();

          if (!formState.value.selectedAnnouncement) {
            message.error('请选择公告');
            return;
          }

          let res;

          // 如果已有公告配置，使用更新API，否则使用创建API
          if (formState.value.hasExistingAnnouncement) {
            // 调用更新公告的API
            res = await setSingleDeviceAnnouncement({
              ac_id: formState.value.selectedAnnouncement,
              device_id: String(deviceId),
              view: formState.value.view, // 使用内容作为view值
            });
          } else {
            // 调用创建公告的API
            res = await setDeviceAnnouncement({
              ac_id: formState.value.selectedAnnouncement,
              device_id: deviceId,
              view: formState.value.view, // 使用内容作为view值
            });
          }

          if (res.code === 1) {
            message.success('设置公告成功');
            options.onSuccess?.();
          } else {
            message.error(res.msg || '设置公告失败');
          }
        } catch (error) {
          message.error('操作失败');
          console.error('设置公告失败:', error);
          throw error;
        }
      },
    });
  };

  return {
    handleUpdateInfo,
    handleUpdatePassword,
    handleToggleHide,
    handleRemoteControl,
    handleSwitchNetwork,
    handleSwitchMainCard,
    handleDelete,
    handleDeviceRestart,
    handleDeviceStop,
    handleUpdateBalance,
    handleDeviceTransfer,
    handleRechargePackage,
    handleLimitMultiCard,
    handleSetAnnouncement,
  };
}
