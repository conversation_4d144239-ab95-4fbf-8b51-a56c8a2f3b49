<script lang="ts" setup>
import type { PictureItem } from '#/api/core/picture';

import { h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import { deletePicture, getPictureList } from '#/api/core/picture';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

// 引入上传模态框组件
import UploadModal from './components/upload-modal.vue';
import { basicSearchItems, createColumns, loadTypeOptions } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getPictureList,
  defaultParams: {},
});

// 使用导入的配置
const columns = createColumns();

// 添加状态
const uploadModalVisible = ref(false);

// 处理上传
const handleUpload = () => {
  uploadModalVisible.value = true;
};

// 处理上传成功
const handleUploadSuccess = () => {
  getList();
};

// 处理删除
const handleDelete = (record: PictureItem) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该素材吗？',
    async onOk() {
      try {
        const res = await deletePicture(record.id.toString());
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch {
        message.error('删除失败');
      }
    },
  });
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'upload',
    icon: h(MdiPlus),
    text: '上传素材',
    type: 'primary' as const,
    onClick: handleUpload,
  },
];

// 组件挂载时加载数据和选项
onMounted(() => {
  loadTypeOptions();
  getList();
});
</script>

<template>
  <div class="material-library p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :custom-buttons="toolbarButtons"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 上传模态框 -->
    <UploadModal
      v-model:visible="uploadModalVisible"
      @ok="handleUploadSuccess"
    />
  </div>
</template>

<style lang="less" scoped>
.material-library {
  background-color: var(--background-deep);
}
</style>
