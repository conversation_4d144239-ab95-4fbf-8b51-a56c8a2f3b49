<script lang="ts" setup>
import { h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  deletePictureCategory,
  getPictureCategories,
} from '#/api/core/picture';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

// 引入编辑模态框组件
import EditModal from './components/edit-modal.vue';
import { basicSearchItems, createColumns } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getPictureCategories,
  defaultParams: {},
});

// 使用导入的配置
const columns = createColumns();

// 添加状态
const editModalVisible = ref(false);
const editingRecord = ref<any>(null);

// 处理新增
const handleAdd = () => {
  editingRecord.value = null;
  editModalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: any) => {
  editingRecord.value = record;
  editModalVisible.value = true;
};

// 处理删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该类型吗？',
    async onOk() {
      try {
        const res = await deletePictureCategory(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch {
        message.error('删除失败');
      }
    },
  });
};

// 处理模态框确认
const handleModalOk = () => {
  getList();
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    icon: h(MdiPlus),
    text: '新增类型',
    type: 'primary' as const,
    onClick: handleAdd,
  },
];

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="material-image-type p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :custom-buttons="toolbarButtons"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 编辑模态框 -->
    <EditModal
      v-model:visible="editModalVisible"
      :record="editingRecord"
      @ok="handleModalOk"
    />
  </div>
</template>

<style lang="less" scoped>
.material-image-type {
  background-color: var(--background-deep);
}
</style>
