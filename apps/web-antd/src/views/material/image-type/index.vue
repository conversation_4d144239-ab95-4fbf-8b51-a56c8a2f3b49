<script lang="ts" setup>
import type { ImageTypeForm } from './config';

import { h, onMounted } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, Form, Input, message, Modal } from 'ant-design-vue';

import {
  createPictureCategory,
  deletePictureCategory,
  getPictureCategories,
  updatePictureCategory,
} from '#/api/core/picture';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
import { useFormModal } from '#/hooks/common/useFormModal';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { basicSearchItems, createColumns } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getPictureCategories,
  defaultParams: {},
});

// 使用导入的配置
const columns = createColumns();

// 表单相关
const formGroups = [
  {
    // title: '基本信息',
    content: (formData: ImageTypeForm) => {
      return h('div', { style: 'width: 100%' }, [
        h(
          Form.Item,
          {
            label: '分类名称',
            name: 'name',
            labelCol: { span: 6 },
            wrapperCol: { span: 14 },
            rules: [{ required: true, message: '请输入分类名称' }],
          },
          () => [
            h(Input, {
              value: formData.name || '',
              placeholder: '请输入分类名称',
              onChange: (e: any) => {
                formData.name = e.target.value || '';
              },
            }),
          ],
        ),
      ]);
    },
  },
];

const formModal = useFormModal<ImageTypeForm>({
  title: (isEdit) => (isEdit ? '编辑分类' : '新增分类'),
  width: 500,
  defaultValues: {
    name: '',
  },
  create: async (params) => {
    await createPictureCategory(params.name);
    return { code: 1, msg: '创建成功' };
  },
  update: async (id, params) => {
    await updatePictureCategory({ id, ...params });
    return { code: 1, msg: '更新成功' };
  },
  onSuccess: () => {
    getList();
  },
});

const handleAdd = () => {
  formModal.show();
};

const handleEdit = (record: ImageTypeForm) => {
  formModal.show(record.id);
  formModal.setFormData({
    id: record.id,
    name: record.name,
  });
};

const handleDelete = async (record: ImageTypeForm) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条记录吗？',
    async onOk() {
      try {
        await deletePictureCategory(record.id);
        message.success('删除成功');
        await getList();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    icon: h(MdiPlus),
    text: '新增类型',
    type: 'primary' as const,
    onClick: handleAdd,
  },
];

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="material-image-type p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :custom-buttons="toolbarButtons"
        :loading="loading"
        :compact="true"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 表单弹窗 -->
    <component :is="formModal.renderFormModal(formGroups)" />
  </div>
</template>

<style lang="less" scoped>
.material-image-type {
  background-color: var(--background-deep);
}
</style>
