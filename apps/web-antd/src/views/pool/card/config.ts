import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type {
  SearchGroup,
  SearchItemConfig,
} from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getPoolGroupList } from '#/api/core/pool';
import { getUserOptionsApi } from '#/api/core/user';
import { realNameOptions, statusOptions } from '#/constants/card-status';

// 基础搜索配置
export const basicSearchItems: SearchItemConfig[] = [
  {
    field: 'iccid_number',
    label: 'ICCID',
    component: 'Input',
    props: {
      placeholder: '请输入ICCID',
      allowClear: true,
    },
  },
  {
    field: 'msisdn_number',
    label: 'MSISDN',
    component: 'Input',
    props: {
      placeholder: '请输入MSISDN',
      allowClear: true,
    },
  },
  {
    field: 'real_name',
    label: '实名状态',
    component: 'Select',
    props: {
      placeholder: '请选择卡片实名状态',
      allowClear: true,
      options: realNameOptions,
    },
  },
  {
    field: 'pool_group_id',
    label: '分池池组',
    component: 'Select',
    remote: {
      api: getPoolGroupList,
      params: { page: 1, pageSize: 999 },
      transform: (res) => {
        return res.data.rows.map((item) => ({
          label: item.name,
          value: item.id,
        }));
      },
    },
    props: {
      placeholder: '请选择分池池组',
      allowClear: true,
      // options: poolGroupOptions,
    },
    adminFlag: true,
  },
];

// 高级搜索配置
export const advancedItems: SearchGroup[] = [
  {
    label: '高级搜索',
    items: [
      {
        field: 'void_number',
        label: 'SIM NO',
        component: 'Input',
        props: {
          placeholder: '请输入SIM NO',
          allowClear: true,
        },
      },
      {
        field: 'status',
        label: '状态',
        component: 'Select',
        props: {
          placeholder: '请选择卡片状态',
          allowClear: true,
          options: statusOptions,
        },
      },
      {
        field: 'pool_group__user_id',
        label: '归属代理',
        component: 'Select',
        remote: {
          api: getUserOptionsApi,
          params: { page: 1, pageSize: 999 },
          transform: (res) => {
            return res.data.map((item) => ({
              label: item.name,
              value: item.id,
            }));
          },
        },
        props: {
          placeholder: '请选择归属代理',
          allowClear: true,
          // options: agentOptions,
        },
      },
    ],
  },
];

// 表格列配置
export const createColumns = () => [
  {
    title: 'ICCID',
    dataIndex: 'iccid_number',
    align: 'center' as AlignType,
    width: 180,
  },
  {
    title: 'MSISDN',
    dataIndex: 'msisdn_number',
    align: 'center' as AlignType,
    width: 150,
  },
  {
    title: 'SIM NO',
    dataIndex: 'void_number',
    align: 'center' as AlignType,
    width: 150,
  },
  {
    title: '分池池组',
    dataIndex: 'pool_group__name',
    align: 'center' as AlignType,
    width: 150,
  },
  {
    title: '归属代理',
    dataIndex: 'pool_group__user__user_name',
    align: 'center' as AlignType,
    width: 120,
  },
  {
    title: '实名状态',
    dataIndex: 'real_name',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => {
      const status = {
        1: { color: 'warning', text: '未实名' },
        2: { color: 'success', text: '已实名' },
        3: { color: 'error', text: '实名失败' },
      };
      const config = status[text] || { color: 'default', text: '未知' };
      return h(Tag, { color: config.color }, config.text);
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => {
      const status: Record<number, { color: string; text: string }> = {
        1: { color: 'default', text: '未知' },
        2: { color: 'warning', text: '待激活' },
        3: { color: 'success', text: '已激活' },
        4: { color: 'error', text: '已停机' },
        5: { color: 'warning', text: '预销号' },
        6: { color: 'error', text: '已销号' },
        7: { color: 'error', text: '已拆机' },
        8: { color: 'processing', text: '测试期' },
        9: { color: 'warning', text: '沉默期' },
        10: { color: 'default', text: '库存' },
        11: { color: 'processing', text: '已过户' },
        12: { color: 'error', text: '异常' },
      };
      const config = status[text] || { color: 'default', text: '未知' };
      return h(Tag, { color: config.color }, config.text);
    },
  },
  {
    title: '创建时间',
    dataIndex: 'creation_time',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
];
