<script lang="ts" setup>
import type {
  PoolCardItem,
  PoolCardListParams,
  PoolGroupItem,
} from '#/api/core/pool';

import { h, onMounted, ref } from 'vue';

import {
  MdiCardAccountDetailsOutline,
  MdiExportVariant,
  MdiTableArrowUp,
} from '@vben/icons';

import { Card, Form, FormItem, message, Modal, Select } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  activationCorrection,
  exportPoolCard,
  getPoolCardList,
  getPoolGroupList,
  updatePoolCard,
} from '#/api/core/pool';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';
import { useUserPermission } from '#/hooks/useUserPermission';
import { handleFileDownload } from '#/utils/export';
// 引入导入卡片 hook
import { useImportCardModal } from '#/views/pool/card/hooks/useImportCardModal';

// 引入配置
import { advancedItems, basicSearchItems, createColumns } from './config';

// 使用 useTable hook
const {
  loading,
  searchParams,
  tableData,
  pagination,
  handleSearch,
  handleReset,
  handleTableChange,
  getList,
} = useTable<PoolCardItem, PoolCardListParams>({
  api: getPoolCardList,
  defaultParams: {},
});

// 使用导入的配置
const columns = createColumns();

// 激活矫正相关
const activationModalVisible = ref(false);
const activationModalLoading = ref(false);
const selectedPoolGroup = ref<number>();
const poolGroups = ref<PoolGroupItem[]>([]);

// 获取用户权限信息
const { isAdmin } = useUserPermission();

// 处理更新
const handleUpdate = async (_record: any, op_type: 1 | 2 | 3) => {
  loading.value = true;
  try {
    const res = await updatePoolCard({
      card_id: _record.id,
      op_type,
    });
    message.success(res.msg);
    getList();
  } catch (error) {
    console.error('操作失败:', error);
    // message.error('操作失败');
  } finally {
    loading.value = false;
  }
};

// 在 setup 中
const importCardModal = useImportCardModal(() => {
  // 成功回调，刷新列表
  getList();
});

// 修改导入处理函数
const handleImport = () => {
  importCardModal.show();
};

// 获取池组列表
const getPoolGroups = async () => {
  try {
    const { data } = await getPoolGroupList({
      page: 1,
      pageSize: 999,
    });
    poolGroups.value = data.rows;
  } catch (error) {
    console.error('获取池组列表失败:', error);
    message.error('获取池组列表失败');
  }
};

// 显示激活矫正弹窗
const handleActivationCorrection = async () => {
  await getPoolGroups();
  activationModalVisible.value = true;
};

// 关闭激活矫正弹窗
const handleActivationModalClose = () => {
  activationModalVisible.value = false;
  selectedPoolGroup.value = undefined;
};

// 提交激活矫正
const handleActivationSubmit = async () => {
  try {
    if (!selectedPoolGroup.value) {
      message.error('请选择池组');
      return;
    }

    activationModalLoading.value = true;
    await activationCorrection({
      pool_group_id: selectedPoolGroup.value,
    });

    message.success('已提交矫正任务请在任务中心查询');
    handleActivationModalClose();
    getList();
  } catch (error) {
    console.error('激活矫正失败:', error);
    message.error('激活矫正失败');
  } finally {
    activationModalLoading.value = false;
  }
};

// 处理导出
const handleExport = async () => {
  try {
    loading.value = true;
    const response = await exportPoolCard({
      ...searchParams.value,
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
    });

    await handleFileDownload(
      response,
      `分池卡片列表_${dayjs().format('YYYY-MM-DD')}.xls`,
    );
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  } finally {
    loading.value = false;
  }
};

// 自定义按钮配置
const customButtons = [
  {
    text: '导入卡片',
    icon: h(MdiTableArrowUp),
    type: 'primary' as const,
    onClick: handleImport,
  },
  {
    text: '激活矫正',
    icon: h(MdiCardAccountDetailsOutline),
    type: 'default' as const,
    onClick: handleActivationCorrection,
  },
  {
    text: '导出',
    icon: h(MdiExportVariant),
    type: 'default' as const,
    onClick: handleExport,
    adminFlag: true,
  },
];

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="pool-card p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedItems"
        :loading="loading"
        :custom-buttons="customButtons"
        :admin-flag="isAdmin"
        :enable-permission-control="true"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          // 更新 复机 删除
          {
            key: 'update',
            text: '更新',
            type: 'link',
            onClick: (record) => handleUpdate(record, 1),
          },
          {
            key: 'reactivate',
            text: '复机',
            type: 'link',
            onClick: (record) => handleUpdate(record, 3),
          },
          {
            key: 'stop',
            text: '停机',
            type: 'link',
            danger: true,
            onClick: (record) => handleUpdate(record, 2),
          },

          // {
          //   key: 'assign',
          //   text: '分配池组',
          //   type: 'link',
          //   onClick: handleAssign,
          // },
          // {
          //   key: 'remove',
          //   text: '移除',
          //   type: 'link',
          //   danger: true,
          //   onClick: handleRemove,
          // },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 导入弹窗 -->
    <component :is="importCardModal.renderModal()" />

    <!-- 激活矫正弹窗 -->
    <Modal
      title="激活矫正"
      :visible="activationModalVisible"
      :confirm-loading="activationModalLoading"
      @cancel="handleActivationModalClose"
      @ok="handleActivationSubmit"
    >
      <Form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <FormItem
          label="分池池组"
          required
          :rules="[{ required: true, message: '请选择池组' }]"
        >
          <Select
            v-model:value="selectedPoolGroup"
            placeholder="请选择池组"
            :options="
              poolGroups.map((item) => ({
                label: item.name,
                value: item.id,
              }))
            "
          />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.pool-card {
  background-color: var(--background-deep);
}
</style>
