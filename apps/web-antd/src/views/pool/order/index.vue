<script lang="ts" setup>
import type { PoolOrderItem, PoolOrderListParams } from '#/api/core/pool';

import { onMounted } from 'vue';

import { Card } from 'ant-design-vue';

import { getPoolOrderList } from '#/api/core/pool';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

// 引入配置
import { advancedItems, basicSearchItems, createColumns } from './config';

// 使用 useTable hook
const {
  loading,
  searchParams,
  tableData,
  pagination,
  handleSearch,
  handleReset,
  handleTableChange,
  getList,
} = useTable<PoolOrderItem, PoolOrderListParams>({
  api: getPoolOrderList,
  defaultParams: {},
});

// 使用导入的配置
const columns = createColumns();

// 处理查看详情
const handleDetail = (_record: any) => {
  // TODO: 实现查看详情逻辑
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="pool-order p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedItems"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="false"
        :action-buttons="[
          {
            key: 'detail',
            text: '查看详情',
            type: 'link',
            onClick: handleDetail,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.pool-order {
  background-color: var(--background-deep);
}
</style>
