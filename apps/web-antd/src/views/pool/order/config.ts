import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { SearchGroup } from '#/components/SearchToolbar/index.vue';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getPoolGroupList } from '#/api/core/pool';
import { getUserOptionsApi } from '#/api/core/user';

// 基础搜索配置
export const basicSearchItems: SearchItemConfig[] = [
  {
    field: 'pool_group_id',
    label: '池组名称',
    component: 'Select',
    remote: {
      api: getPoolGroupList,
      params: { page: 1, pageSize: 999 },
      transform: (res) => {
        return res.data.rows.map((item) => ({
          label: item.name,
          value: item.id,
        }));
      },
    },
    props: {
      placeholder: '请选择分池名称',
      allowClear: true,
      // options: poolGroupOptions,
    },
  },
  {
    field: 'pay_numbers',
    label: '支付单号',
    component: 'Input',
    props: {
      placeholder: '请输入支付单号',
      allowClear: true,
    },
  },
  {
    field: 'pool_group__user_id',
    label: '代理账号',
    component: 'Select',
    remote: {
      api: getUserOptionsApi,
      params: { page: 1, pageSize: 999 },
      transform: (res) => {
        return res.data.map((item) => ({
          label: item.name,
          value: item.id,
        }));
      },
    },
    props: {
      placeholder: '请选择代理账号',
      allowClear: true,
      // options: agentOptions,
    },
  },
];

// 高级搜索配置
export const advancedItems: SearchGroup[] = [
  {
    label: '高级搜索',
    items: [
      {
        field: 'odd_numbers',
        label: '平台单号',
        component: 'Input',
        props: {
          placeholder: '请输入平台单号',
          allowClear: true,
        },
      },
      {
        field: 'remarks',
        label: '充值描述',
        component: 'Input',
        props: {
          placeholder: '请输入充值描述',
          allowClear: true,
        },
      },
    ],
  },
];

// 表格列配置
export const createColumns = () => [
  {
    title: '代理账号',
    dataIndex: 'pool_group__user__user_account',
    align: 'center' as AlignType,
    width: 120,
  },
  {
    title: '池组名称',
    dataIndex: 'pool_group__name',
    align: 'center' as AlignType,
    width: 150,
  },
  {
    title: '充值金额',
    dataIndex: 'recharge_amount',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: string }) => `￥${text}`,
  },
  {
    title: '实付金额',
    dataIndex: 'actual_amount',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: string }) => `￥${text}`,
  },
  {
    title: '充值前余额',
    dataIndex: 'before_amount',
    align: 'center' as AlignType,
    width: 120,
    ellipsis: true,
    customRender: ({ text }: { text: string }) => `￥${text}`,
  },
  {
    title: '充值后余额',
    dataIndex: 'after_amount',
    align: 'center' as AlignType,
    width: 120,
    ellipsis: true,
    customRender: ({ text }: { text: string }) => `￥${text}`,
  },
  {
    title: '支付方式',
    dataIndex: 'pay_method',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => {
      const payMethod: Record<number, string> = {
        1: '支付宝',
        2: '微信',
        99: '后台充值',
      };
      return payMethod[text] || '未知';
    },
  },
  {
    title: '平台单号',
    dataIndex: 'odd_numbers',
    align: 'center' as AlignType,
    width: 180,
  },
  {
    title: '支付单号',
    dataIndex: 'pay_numbers',
    align: 'center' as AlignType,
    width: 180,
  },
  {
    title: '充值描述',
    dataIndex: 'remarks',
    align: 'center' as AlignType,
    width: 200,
  },
  {
    title: '充值状态',
    dataIndex: 'status',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => {
      const status: Record<number, { color: string; text: string }> = {
        1: { color: 'warning', text: '待支付' },
        2: { color: 'success', text: '已完成' },
        3: { color: 'error', text: '已取消' },
      };
      const config = status[text] || { color: 'default', text: '未知' };
      return h(Tag, { color: config.color }, config.text);
    },
  },
  {
    title: '创建时间',
    dataIndex: 'creation_time',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
];
