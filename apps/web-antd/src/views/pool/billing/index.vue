<script lang="ts" setup>
import type { BillDetailItem, BillDetailListParams } from '#/api/core/pool';

import { h, onMounted } from 'vue';

import { MdiTableArrowUp } from '@vben/icons';

import { Card } from 'ant-design-vue';

import { getBillDetailList } from '#/api/core/pool';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';
import { useImportBillModal } from '#/views/pool/billing/hooks/useImportBillModal';

// 引入配置
import { basicSearchItems, createColumns } from './config';

// 使用 useTable hook
const {
  loading,
  searchParams,
  tableData,
  pagination,
  handleSearch,
  handleReset,
  handleTableChange,
  getList,
} = useTable<BillDetailItem, BillDetailListParams>({
  api: getBillDetailList,
  defaultParams: {},
});
// 使用导入的配置
const columns = createColumns();

// 使用导入账单 Modal
const importBillModal = useImportBillModal();

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="billing p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :custom-buttons="[
          {
            text: '导入账单',
            icon: h(MdiTableArrowUp),
            type: 'primary',
            onClick: importBillModal.show,
          },
        ]"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 导入账单 Modal -->
    <component :is="importBillModal.renderModal()" />
  </div>
</template>

<style lang="less" scoped>
.billing {
  background-color: var(--background-deep);
}
</style>
