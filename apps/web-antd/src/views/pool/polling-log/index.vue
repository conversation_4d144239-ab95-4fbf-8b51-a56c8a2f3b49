<script lang="ts" setup>
import type { PoolPollingItem, PoolPollingListParams } from '#/api/core/pool';

import { onMounted } from 'vue';

import { Card } from 'ant-design-vue';

import { getPoolPollingList } from '#/api/core/pool';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

// 引入配置
import { basicSearchItems, createColumns } from './config';

// 使用 useTable hook
const {
  loading,
  searchParams,
  tableData,
  pagination,
  handleSearch,
  handleReset,
  handleTableChange,
  getList,
} = useTable<PoolPollingItem, PoolPollingListParams>({
  api: getPoolPollingList,
  defaultParams: {},
});
// 使用导入的配置
const columns = createColumns();

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="polling-log p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.polling-log {
  background-color: var(--background-deep);
}
</style>
