<script lang="ts" setup>
import type { CardChangeItem, CardChangeListParams } from '#/api/core/pool';

import { onMounted, ref } from 'vue';

import { Card, message } from 'ant-design-vue';

import { getCardChangeList } from '#/api/core/pool';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';

// 引入配置
import { basicSearchItems, createColumns } from './config';

// 状态定义
const loading = ref(false);
const searchParams = ref<Partial<CardChangeListParams>>({});
const tableData = ref<CardChangeItem[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

// 使用导入的配置
const columns = createColumns();

// 加载数据
const getList = async () => {
  try {
    loading.value = true;
    const { data } = await getCardChangeList({
      page: page.value,
      pageSize: pageSize.value,
      ...searchParams.value,
    });
    tableData.value = data.rows;
    total.value = data.total;
  } catch (error) {
    console.error('获取卡变更记录列表失败:', error);
    message.error('获取卡变更记录列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理表格变化
const handleTableChange = (pagination: any) => {
  page.value = pagination.current;
  pageSize.value = pagination.pageSize;
  getList();
};

// 处理搜索
const handleSearch = () => {
  page.value = 1;
  getList();
};

// 处理重置
const handleReset = () => {
  page.value = 1;
  searchParams.value = {};
  getList();
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="card-log p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :pagination="{
          total,
          current: page,
          pageSize,
          showSizeChanger: true,
          showQuickJumper: true,
        }"
        @change="handleTableChange"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.card-log {
  background-color: var(--background-deep);
}
</style>
