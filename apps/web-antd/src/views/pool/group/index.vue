<script lang="ts" setup>
import type { PoolGroupItem, PoolGroupListParams } from '#/api/core/pool';

import { h, onMounted } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import { deletePoolGroup, getPoolGroupList } from '#/api/core/pool';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';
import { useBalanceModal } from '#/views/pool/group/hooks/useBalanceModal';
import { useBillGenerateModal } from '#/views/pool/group/hooks/useBillGenerateModal';
import { usePoolGroupForm } from '#/views/pool/group/hooks/usePoolGroupForm';

// 引入配置和API
import { advancedItems, basicSearchItems, createColumns } from './config';

// 使用 useTable hook
const {
  loading,
  searchParams,
  tableData,
  pagination,
  handleSearch,
  handleReset,
  handleTableChange,
  getList,
} = useTable<PoolGroupItem, PoolGroupListParams>({
  api: getPoolGroupList,
  defaultParams: {},
});

// // 在 setup 中
const { show: showForm, renderFormModal } = usePoolGroupForm(() => {
  // 成功回调，刷新列表
  getList();
});

// 删除处理
const handleDelete = async (record: PoolGroupItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除池组 "${record.name}" 吗？`,
    async onOk() {
      try {
        await deletePoolGroup(record.id);
        message.success('删除成功');
        getList();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 在 setup 中
const balanceModal = useBalanceModal(() => {
  // 成功回调，刷新列表
  getList();
});

// 在 setup 中
const billGenerateModal = useBillGenerateModal(() => {
  // 成功回调，刷新列表
  getList();
});

// 初始化
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="pool-group p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedItems"
        :loading="loading"
        :custom-buttons="[
          {
            key: 'add',
            text: '新增',
            icon: h(MdiPlus),
            type: 'primary',
            onClick: () => showForm(),
          },
          {
            key: 'generate',
            text: '生成账单',
            type: 'default',
            onClick: () => billGenerateModal.show(),
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="createColumns()"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: (record) => showForm(record),
          },
          {
            key: 'balance',
            text: '修改余额',
            type: 'link',
            onClick: (record) => balanceModal.show(record),
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <!-- 添加表单弹窗组件 -->
      <component :is="renderFormModal()" />

      <!-- 余额修改弹窗 -->
      <component :is="balanceModal.renderModal()" />

      <!-- 生成账单弹窗 -->
      <component :is="billGenerateModal.renderModal(tableData)" />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.pool-group {
  background-color: var(--background-deep);
}
</style>
