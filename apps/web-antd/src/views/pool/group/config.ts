import type { AlignType } from 'ant-design-vue/es/vc-trigger/interface';

import type {
  SearchGroup,
  SearchItemConfig,
} from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
// 引入日期格式化工具
import dayjs from 'dayjs';

import { getChannelListApi } from '#/api/core/order';
import { getUserOptionsApi } from '#/api/core/user';

// 基础搜索配置
export const basicSearchItems: SearchItemConfig[] = [
  {
    field: 'name',
    label: '池组名称',
    component: 'Input',
    props: {
      placeholder: '请输入池组名称',
      allowClear: true,
    },
  },
  {
    field: 'operator',
    label: '运营商',
    component: 'Select',
    props: {
      placeholder: '请选择运营商',
      allowClear: true,
      options: [
        { label: '中国移动', value: '1' },
        { label: '中国联通', value: '2' },
        { label: '中国电信', value: '3' },
      ],
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    props: {
      placeholder: '请选择状态',
      allowClear: true,
      options: [
        { label: '正常', value: 1 },
        { label: '停用', value: 2 },
      ],
    },
  },
];

// 高级搜索配置
export const advancedItems: SearchGroup[] = [
  {
    label: '高级搜索',
    items: [
      {
        field: 'channel_id',
        label: '绑定通道',
        component: 'Select',
        remote: {
          api: getChannelListApi,
          params: { page: 1, pageSize: 999 },
          transform: (res) => {
            return res.data.map((item) => ({
              label: item.api_name || item.name,
              value: item.id,
            }));
          },
        },
        props: {
          placeholder: '请选择绑定通道',
          allowClear: true,
          // options: channelOptions,
        },
      },
      {
        field: 'user_id',
        label: '代理账号',
        component: 'Select',
        remote: {
          api: getUserOptionsApi,
          params: { page: 1, pageSize: 999 },
          transform: (res) => {
            return res.data.map((item) => ({
              label: item.name,
              value: item.id,
            }));
          },
        },
        props: {
          placeholder: '请选择代理账号',
          allowClear: true,
          // options: agentOptions,
        },
      },
      {
        field: 'remark',
        label: '备注',
        component: 'Input',
        props: {
          placeholder: '请输入备注信息',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '计费配置',
    items: [
      {
        field: 'billingMode',
        label: '计费模式',
        component: 'Select',
        props: {
          placeholder: '请选择计费模式',
          allowClear: true,
          options: [
            { label: '共享池', value: 1 },
            { label: '单卡计费', value: 2 },
          ],
        },
      },
      {
        field: 'paymentMode',
        label: '缴费模式',
        component: 'Select',
        props: {
          placeholder: '请选择缴费模式',
          allowClear: true,
          options: [
            { label: '预付费', value: 1 },
            { label: '后付费', value: 2 },
          ],
        },
      },
      {
        field: 'billingDate',
        label: '出账时间',
        component: 'Input',
        props: {
          placeholder: '请输入出账日期(1-31)',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '价格配置',
    items: [
      {
        field: 'pricePerGB',
        label: '单G价格',
        component: 'Input',
        props: {
          placeholder: '请输入单G价格',
          allowClear: true,
          suffix: '元',
        },
      },
      {
        field: 'overflowPrice',
        label: '超套价格',
        component: 'Input',
        props: {
          placeholder: '请输入超套价格',
          allowClear: true,
          suffix: '元',
        },
      },
      {
        field: 'minConsumption',
        label: '最低消费',
        component: 'Input',
        props: {
          placeholder: '请输入最低消费',
          allowClear: true,
          suffix: 'G',
        },
      },
    ],
  },
  {
    label: '超套配置',
    items: [
      {
        field: 'overflowMode',
        label: '超套方式',
        component: 'Select',
        props: {
          placeholder: '请选择超套方式',
          allowClear: true,
          options: [
            { label: '超套扣费', value: 1 },
            { label: '超套关停', value: 2 },
          ],
        },
      },
    ],
  },
];

// 表格列配置
export const createColumns = () => [
  {
    title: 'ID',
    dataIndex: 'id',
    align: 'center' as AlignType,
    width: 120,
  },
  {
    title: '池组名称',
    dataIndex: 'name',
    align: 'center' as AlignType,
    width: 120,
  },
  {
    title: '通道名称',
    dataIndex: 'channel__api_name',
    align: 'center',
    width: 160,
    ellipsis: true,
  },
  {
    title: '运营商',
    dataIndex: 'operator',
    align: 'center',
    width: 100,
    customRender: ({ text }: { text: string }) => {
      const operators: Record<string, string> = {
        '1': '移动',
        '2': '联通',
        '3': '电信',
      };
      return operators[text] || '-';
    },
  },
  {
    title: '计费模式',
    dataIndex: 'billing_mode',
    align: 'center',
    width: 100,
    customRender: ({ text }: { text: number }) => {
      return text === 1 ? '共享池' : '单卡计费';
    },
  },
  {
    title: '缴费模式',
    dataIndex: 'payment_mode',
    align: 'center',
    width: 100,
    customRender: ({ text }: { text: number }) => {
      return text === 1 ? '预付费' : '后付费';
    },
  },
  {
    title: '单G价格',
    dataIndex: 'price',
    align: 'center',
    width: 100,
    customRender: ({ text }: { text: string }) => {
      return text ? `¥${Number(text).toFixed(2)}` : '-';
    },
  },
  {
    title: '超套价格',
    dataIndex: 'more_price',
    align: 'center',
    width: 100,
    customRender: ({ text }: { text: null | string }) => {
      return text ? `¥${Number(text).toFixed(2)}` : '-';
    },
  },
  {
    title: '最低消费(G)',
    dataIndex: 'low_dissipation',
    align: 'center',
    width: 120,
  },
  {
    title: '出账日期',
    dataIndex: 'settlement_date',
    align: 'center',
    width: 100,
    customRender: ({ text }: { text: number }) => {
      return text ? `${text}号` : '-';
    },
  },
  {
    title: '总卡数',
    dataIndex: 'total_card',
    align: 'center',
    width: 100,
  },
  {
    title: '已激活',
    dataIndex: 'total_activated',
    align: 'center',
    width: 100,
  },
  {
    title: '总流量(G)',
    dataIndex: 'total_flow',
    align: 'center',
    width: 120,
  },
  {
    title: '账户余额',
    dataIndex: 'balance',
    align: 'center',
    width: 120,
    customRender: ({ text }: { text: string }) => {
      return text ? `¥${Number(text).toFixed(2)}` : '¥0.00';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    align: 'center',
    width: 100,
    customRender: ({ text }: { text: number }) => {
      const status: Record<number, { color: string; text: string }> = {
        1: { color: 'success', text: '正常' },
        2: { color: 'error', text: '停用' },
      };
      const config = status[text] || { color: 'default', text: '未知' };
      return h(Tag, { color: config.color }, config.text);
    },
  },
  {
    title: '创建时间',
    dataIndex: 'creation_time',
    align: 'center',
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
];
