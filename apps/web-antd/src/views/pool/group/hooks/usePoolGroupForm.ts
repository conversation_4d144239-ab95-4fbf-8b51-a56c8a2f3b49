import type { FormGroup } from '../../../../hooks/common/useFormModal';

import { h, ref } from 'vue';

import { Col, Form, Input, InputNumber, Select } from 'ant-design-vue';

import { getChannelListApi } from '#/api/core/order';
import { addPoolGroup, updatePoolGroup } from '#/api/core/pool';
import { getUserOptionsApi } from '#/api/core/user';

import { useFormModal } from '../../../../hooks/common/useFormModal';

const FormItem = Form.Item;

interface PoolGroupFormState {
  name: string; // 池组名称
  billing_mode: number; // 计费模式 1:共享池 2:单卡计费
  low_dissipation: number; // 低消(G)
  channel_id: number; // 绑定通道
  payment_mode: number; // 缴费模式 1:预付费 2:后付费
  user_id: number; // 代理账号
  settlement_date: number; // 运营商出账时间
  price: number; // 单G价格
  more_mode: number; // 超套方式 1:超套扣费 2:超套关停
  remark: string; // 备注
  operator: string; // 运营商 1:移动 2:联通 3:电信 4:广电
  recharge_amount: number; // 初始充值余额
  settlement: number; // 系统自动出账时间
  overflowPrice: number; // 超套单价
}

// 添加一个转换函数，将表单数据转换为API需要的格式
const transformFormData = (data: PoolGroupFormState) => {
  return {
    name: data.name,
    channel_id: data.channel_id,
    operator: data.operator,
    billing_mode: data.billing_mode,
    payment_mode: data.payment_mode,
    price: data.price.toString(),
    more_price:
      data.more_mode === 1 ? data.overflowPrice.toString() : undefined,
    low_dissipation: data.low_dissipation,
    settlement_date: data.settlement_date,
    settlement: data.settlement,
    remarks: data.remark,
    more_mode: data.more_mode,
    balance: data.recharge_amount.toString(),
    user_id: data.user_id,
  };
};

export function usePoolGroupForm(callback?: () => void) {
  // 选项数据
  const channelOptions = ref<{ label: string; value: number }[]>([]);
  const agentOptions = ref<{ label: string; value: number }[]>([]);
  const loading = ref(false);

  // 加载选项数据
  const loadOptions = async () => {
    try {
      loading.value = true;
      const [channelRes, agentRes] = await Promise.all([
        getChannelListApi(),
        getUserOptionsApi(),
      ]);

      if (channelRes.code === 1 && channelRes.data) {
        channelOptions.value = channelRes.data.map((item) => ({
          label: item.api_name || item.name,
          value: item.id,
        }));
      }

      if (agentRes.code === 1 && agentRes.data) {
        agentOptions.value = agentRes.data.map((item) => ({
          label: item.name,
          value: item.id,
        }));
      }
    } catch (error) {
      console.error('加载选项失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 保存原始记录数据
  const originalRecord = ref<any>(null);

  const {
    formRef,
    formData,
    visible,
    loading: modalLoading,
    isEdit,
    currentId,
    show: originalShow,
    close,
    setFormData,
    renderFormModal,
  } = useFormModal<PoolGroupFormState>({
    title: (isEdit) => (isEdit ? '编辑池组' : '新增池组'),
    width: 800,
    create: async (params) => {
      // 转换表单数据
      const transformedData = transformFormData(params);
      return await addPoolGroup(transformedData);
    },
    update: async (id, params) => {
      // 使用原始记录数据，并用修改的数据覆盖
      return await updatePoolGroup({
        ...originalRecord.value,
        ...params,
      });
    },
    onSuccess: callback,
    defaultValues: {
      billing_mode: 1,
      payment_mode: 1,
      more_mode: 2,
      settlement_date: 1,
      settlement: 1,
      recharge_amount: 0,
      low_dissipation: 0,
      price: 0,
      overflowPrice: 0,
    },
  });

  // 表单分组配置
  const formGroups: FormGroup[] = [
    {
      title: '基本信息',
      content: () => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '池组名称',
              name: 'name',
              rules: [{ required: true, message: '请输入池组名称' }],
            },
            () =>
              h(Input, {
                placeholder: '请输入池组名称',
                allowClear: true,
                value: formData.name,
                'onUpdate:value': (val) => (formData.name = val),
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '运营商',
              name: 'operator',
              rules: [{ required: true, message: '请选择运营商' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择运营商',
                options: [
                  { label: '中国移动', value: '1' },
                  { label: '中国联通', value: '2' },
                  { label: '中国电信', value: '3' },
                  { label: '中国广电', value: '4' },
                ],
                allowClear: true,
                value: formData.operator,
                'onUpdate:value': (val) => (formData.operator = val),
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '绑定通道',
              name: 'channel_id',
              rules: [{ required: true, message: '请选择绑定通道' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择绑定通道',
                options: channelOptions.value,
                allowClear: true,
                value: formData.channel_id,
                'onUpdate:value': (val) => (formData.channel_id = val),
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '代理账号',
              name: 'user_id',
              rules: [{ required: true, message: '请选择代理账号' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择代理账号',
                options: agentOptions.value,
                allowClear: true,
                value: formData.user_id,
                'onUpdate:value': (val) => (formData.user_id = val),
              }),
          ),
        ]),
      ],
    },
    {
      title: '计费配置',
      content: () => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '计费模式',
              name: 'billing_mode',
              rules: [{ required: true, message: '请选择计费模式' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择计费模式',
                options: [
                  { label: '共享池', value: 1 },
                  { label: '单卡计费', value: 2 },
                ],
                allowClear: true,
                value: formData.billing_mode,
                'onUpdate:value': (val) => (formData.billing_mode = val),
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '缴费模式',
              name: 'payment_mode',
              rules: [{ required: true, message: '请选择缴费模式' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择缴费模式',
                options: [
                  { label: '预付费', value: 1 },
                  { label: '后付费', value: 2 },
                ],
                allowClear: true,
                value: formData.payment_mode,
                'onUpdate:value': (val) => (formData.payment_mode = val),
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '单G价格',
              name: 'price',
              rules: [{ required: true, message: '请输入单G价格' }],
            },
            () =>
              h(InputNumber, {
                placeholder: '请输入单G价格',
                min: 0,
                precision: 2,
                addonAfter: '元',
                allowClear: true,
                style: { width: '100%' },
                value: formData.price,
                'onUpdate:value': (val) => (formData.price = val),
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '低消(G)',
              name: 'low_dissipation',
              rules: [{ required: true, message: '请输入低消' }],
            },
            () =>
              h(InputNumber, {
                placeholder: '请输入低消',
                min: 0,
                precision: 0,
                addonAfter: 'G',
                allowClear: true,
                style: { width: '100%' },
                value: formData.low_dissipation,
                'onUpdate:value': (val) => (formData.low_dissipation = val),
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '初始充值余额',
              name: 'recharge_amount',
              rules: [{ required: true, message: '请输入初始充值余额' }],
              labelCol: { span: 8 },
            },
            () =>
              h(InputNumber, {
                placeholder: '请输入初始充值余额',
                min: 0,
                precision: 2,
                addonAfter: '元',
                allowClear: true,
                style: { width: '100%' },
                value: formData.recharge_amount,
                'onUpdate:value': (val) => (formData.recharge_amount = val),
              }),
          ),
        ]),
      ],
    },
    {
      title: '出账配置',
      content: () => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '超套方式',
              name: 'more_mode',
              rules: [{ required: true, message: '请选择超套方式' }],
            },
            () =>
              h(Select, {
                placeholder: '请选择超套方式',
                options: [
                  { label: '超套扣费', value: 1 },
                  { label: '超套关停', value: 2 },
                ],
                allowClear: true,
                value: formData.more_mode,
                'onUpdate:value': (val) => {
                  formData.more_mode = val;
                },
              }),
          ),
        ]),
        // 超套价格输入框，仅在选择超套扣费时显示
        formData.more_mode === 1 &&
          h(Col, { span: 12 }, [
            h(
              FormItem,
              {
                label: '超套单价',
                name: 'overflowPrice',
                rules: [{ required: true, message: '请输入超套单价' }],
              },
              () =>
                h(InputNumber, {
                  placeholder: '请输入超套单价',
                  min: 0,
                  precision: 2,
                  addonAfter: '元/G',
                  allowClear: true,
                  style: { width: '100%' },
                  value: formData.overflowPrice,
                  'onUpdate:value': (val) => {
                    formData.overflowPrice = val;
                  },
                }),
            ),
          ]),
        // 调整出账时间布局
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '运营商出账时间',
              name: 'settlement_date',
              rules: [{ required: true, message: '请输入运营商出账时间' }],
              labelCol: { span: 9 },
              wrapperCol: { span: 11 },
            },
            () =>
              h(InputNumber, {
                placeholder: '每月几号出账',
                min: 1,
                max: 31,
                allowClear: true,
                style: { width: '100%' },
                value: formData.settlement_date,
                'onUpdate:value': (val) => (formData.settlement_date = val),
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '系统出账时间',
              name: 'settlement',
              rules: [{ required: true, message: '请输入系统出账时间' }],
              labelCol: { span: 8 },
              wrapperCol: { span: 11 },
            },
            () =>
              h(InputNumber, {
                placeholder: '每月几号出账',
                min: 1,
                max: 31,
                allowClear: true,
                style: { width: '100%' },
                value: formData.settlement,
                'onUpdate:value': (val) => (formData.settlement = val),
              }),
          ),
        ]),
      ],
    },
    {
      title: '其他信息',
      content: () => [
        h(Col, { span: 24 }, [
          h(
            FormItem,
            {
              label: '备注',
              name: 'remark',
              labelCol: { span: 3 },
              wrapperCol: { span: 20 },
            },
            () =>
              h(Input.TextArea, {
                placeholder: '请输入备注信息',
                rows: 4,
                allowClear: true,
                value: formData.remark,
                'onUpdate:value': (val) => (formData.remark = val),
              }),
          ),
        ]),
      ],
    },
  ];

  // 显示表单
  const show = async (record?: any) => {
    await loadOptions();
    originalShow(record?.id);

    if (record) {
      // 保存原始记录数据
      originalRecord.value = record;

      // 设置表单数据
      setFormData({
        name: record.name,
        operator: record.operator,
        channel_id: record.channel_id,
        user_id: record.user_id,
        billing_mode: record.billing_mode,
        payment_mode: record.payment_mode,
        price: Number(record.price),
        low_dissipation: record.low_dissipation,
        settlement_date: record.settlement_date,
        settlement: record.settlement_date,
        more_mode: record.more_mode,
        recharge_amount: Number(record.balance),
        remark: record.remarks,
        overflowPrice: record.more_price ? Number(record.more_price) : 0,
      });
    }
  };

  return {
    formRef,
    formData,
    visible,
    loading: modalLoading,
    isEdit,
    currentId,
    show,
    close,
    renderFormModal: () => renderFormModal(formGroups),
  };
}
