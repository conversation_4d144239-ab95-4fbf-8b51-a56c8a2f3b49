import type { Ref } from 'vue';

import { reactive } from 'vue';

import { cloneDeep } from 'lodash-es';

export interface HistoryState {
  past: Record<string, any[][]>;
  future: Record<string, any[][]>;
}

export function useHistory(currentPageId: Ref<string>) {
  // 历史记录 - 每个页面单独管理历史
  const history = reactive<HistoryState>({
    past: {},
    future: {},
  });

  // 初始化页面历史记录
  function initPageHistory(pageId: string) {
    if (!history.past[pageId]) {
      history.past[pageId] = [];
    }
    if (!history.future[pageId]) {
      history.future[pageId] = [];
    }
  }

  // 保存历史记录
  function saveHistory(components: any[]) {
    const pageId = currentPageId.value;
    if (!history.past[pageId]) {
      history.past[pageId] = [];
    }

    // 只保存最多50条历史记录，避免内存过大
    if (history.past[pageId].length >= 50) {
      history.past[pageId].shift();
    }

    history.past[pageId].push(cloneDeep(components));
    history.future[pageId] = [];
    console.log(
      `页面 ${pageId} 历史记录已保存, 总数:`,
      history.past[pageId].length,
    );
  }

  // 撤销
  function undo(components: Ref<any[]>) {
    const pageId = currentPageId.value;
    if (!history.past[pageId] || history.past[pageId].length === 0)
      return false;

    const current = history.past[pageId].pop();
    if (current) {
      if (!history.future[pageId]) {
        history.future[pageId] = [];
      }
      history.future[pageId].push(cloneDeep(components.value));
      components.value = cloneDeep(current);
      return true;
    }
    return false;
  }

  // 重做
  function redo(components: Ref<any[]>) {
    const pageId = currentPageId.value;
    if (!history.future[pageId] || history.future[pageId].length === 0)
      return false;

    const next = history.future[pageId].pop();
    if (next) {
      if (!history.past[pageId]) {
        history.past[pageId] = [];
      }
      history.past[pageId].push(cloneDeep(components.value));
      components.value = cloneDeep(next);
      return true;
    }
    return false;
  }

  // 清除特定页面的历史记录
  function clearPageHistory(pageId: string) {
    history.past[pageId] = [];
    history.future[pageId] = [];
  }

  // 切换页面时重置当前历史记录指针
  function switchPageHistory(pageId: string) {
    initPageHistory(pageId);
  }

  return {
    history,
    initPageHistory,
    saveHistory,
    undo,
    redo,
    clearPageHistory,
    switchPageHistory,
  };
}
