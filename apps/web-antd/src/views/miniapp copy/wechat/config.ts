import type { ColumnType } from 'ant-design-vue/es/table';
import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

// 状态选项
export const statusOptions = [
  { label: '已开启', value: 1, color: 'green' },
  { label: '已关闭', value: 2, color: 'red' },
];

// 搜索配置
export const searchItems: SearchItemConfig[] = [
  {
    field: 'appid',
    label: 'APPID',
    component: 'Input',
    props: {
      placeholder: '请输入APPID',
      allowClear: true,
    },
  },
  {
    field: 'secret',
    label: 'SECRET',
    component: 'Input',
    props: {
      placeholder: '请输入SECRET',
      allowClear: true,
    },
  },
  {
    field: 'remarks',
    label: '备注',
    component: 'Input',
    props: {
      placeholder: '请输入备注',
      allowClear: true,
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    props: {
      placeholder: '请选择状态',
      allowClear: true,
      options: statusOptions,
    },
  },
];

// 表格列配置
export const columns: ColumnType<any>[] = [
  {
    title: 'APPID',
    dataIndex: 'appid',
    align: 'center' as AlignType,
    width: 200,
  },
  {
    title: 'SECRET',
    dataIndex: 'secret',
    align: 'center' as AlignType,
    width: 300,
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    align: 'center' as AlignType,
    width: 200,
  },
  {
    title: '状态',
    dataIndex: 'status',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }) => {
      const option = statusOptions.find((item) => item.value === text);
      return h(Tag, { color: option?.color }, () => option?.label || '-');
    },
  },
  {
    title: '创建时间',
    dataIndex: 'creation_time',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '更新时间',
    dataIndex: 'update_time',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
];
