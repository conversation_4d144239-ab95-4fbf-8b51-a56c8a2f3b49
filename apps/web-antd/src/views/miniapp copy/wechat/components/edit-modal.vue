<script lang="ts" setup>
import type { WechatMiniapp } from '#/api/core/wechat';

import { ref, watch } from 'vue';

import { Form, Input, Modal, Select } from 'ant-design-vue';

import { statusOptions } from '../config';

const props = defineProps<{
  data?: WechatMiniapp;
  visible: boolean;
}>();

const emit = defineEmits<{
  submit: [values: WechatMiniapp];
  'update:visible': [value: boolean];
}>();

const formRef = ref();
const formData = ref<Partial<WechatMiniapp>>({
  appid: '',
  secret: '',
  remarks: '',
  status: 1,
});

// 监听数据变化
watch(
  [() => props.visible, () => props.data],
  ([visible, data]) => {
    if (visible) {
      // 打开弹窗时设置数据
      formData.value = data
        ? { ...data }
        : {
            appid: '',
            secret: '',
            remarks: '',
            status: 1,
          };
    } else {
      // 关闭弹窗时重置数据
      formRef.value?.resetFields();
      formData.value = {
        appid: '',
        secret: '',
        remarks: '',
        status: 1,
      };
    }
  },
  { immediate: true },
);

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 处理提交
const handleOk = async () => {
  try {
    await formRef.value?.validate();
    emit('submit', formData.value as WechatMiniapp);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};
</script>

<template>
  <Modal
    :visible="visible"
    :title="data?.id ? '编辑小程序' : '新增小程序'"
    @update:visible="$emit('update:visible', $event)"
    @ok="handleOk"
    @cancel="handleCancel"
    :destroy-on-close="true"
  >
    <Form ref="formRef" :model="formData" layout="vertical">
      <Form.Item
        label="APPID"
        name="appid"
        :rules="[{ required: true, message: '请输入APPID' }]"
      >
        <Input v-model:value="formData.appid" placeholder="请输入APPID" />
      </Form.Item>

      <Form.Item
        label="SECRET"
        name="secret"
        :rules="[{ required: true, message: '请输入SECRET' }]"
      >
        <Input v-model:value="formData.secret" placeholder="请输入SECRET" />
      </Form.Item>

      <Form.Item
        label="备注"
        name="remarks"
        :rules="[{ required: true, message: '请输入备注' }]"
      >
        <Input v-model:value="formData.remarks" placeholder="请输入备注" />
      </Form.Item>

      <Form.Item
        label="状态"
        name="status"
        :rules="[{ required: true, message: '请选择状态' }]"
      >
        <Select
          v-model:value="formData.status"
          :options="statusOptions"
          placeholder="请选择状态"
        />
      </Form.Item>
    </Form>
  </Modal>
</template>
