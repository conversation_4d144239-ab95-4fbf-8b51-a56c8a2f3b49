<script lang="ts" setup>
import type { WechatMiniapp } from '#/api/core/wechat';

import { onMounted, ref } from 'vue';

import { Card, message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  addWechatMiniapp,
  deleteWechatMiniapp,
  exportWechatMiniapp,
  getWechatMiniappList,
  updateWechatMiniapp,
} from '#/api/core/wechat';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';
import { handleFileDownload } from '#/utils/export';

import EditModal from './components/edit-modal.vue';
import { columns, searchItems } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getWechatMiniappList,
  defaultParams: {},
});

// 编辑弹窗状态
const editVisible = ref(false);
const editData = ref<Partial<WechatMiniapp>>();

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    text: '新增小程序',
    type: 'primary',
    onClick: () => {
      editData.value = undefined;
      editVisible.value = true;
    },
  },
  {
    key: 'export',
    text: '导出小程序',
    onClick: async () => {
      try {
        const res = await exportWechatMiniapp();
        await handleFileDownload(
          res,
          `小程序列表_${dayjs().format('YYYY-MM-DD')}.xlsx`,
        );
      } catch (error) {
        console.error('导出失败:', error);
        message.error('导出失败');
      }
    },
  },
];

// 处理编辑提交
const handleEditSubmit = async (values: WechatMiniapp) => {
  try {
    let res;

    if (values.id) {
      // 更新 - 使用普通对象
      res = await updateWechatMiniapp({
        id: values.id,
        appid: values.appid,
        secret: values.secret,
        status: values.status,
        remarks: values.remarks,
      });
    } else {
      // 新增 - 使用 FormData
      const formData = new FormData();
      formData.append('appid', values.appid);
      formData.append('secret', values.secret);
      formData.append('status', String(values.status));
      if (values.remarks) {
        formData.append('remarks', values.remarks);
      }
      res = await addWechatMiniapp(formData);
    }

    if (res.code === 1) {
      message.success(res.msg || (values.id ? '修改成功' : '添加成功'));
      editVisible.value = false;
      getList(); // 刷新列表
    } else {
      message.error(res.msg || (values.id ? '修改失败' : '添加失败'));
    }
  } catch (error) {
    console.error(values.id ? '修改失败:' : '添加失败:', error);
    message.error(values.id ? '修改失败' : '添加失败');
  }
};

// 处理编辑
const handleEdit = (row: WechatMiniapp) => {
  editData.value = { ...row };
  editVisible.value = true;
};

// 处理删除
const handleDelete = (row: WechatMiniapp) => {
  Modal.confirm({
    title: '确定删除小程序吗？',
    content: `小程序AppID：${row.appid}`,
    onOk: async () => {
      try {
        const res = await deleteWechatMiniapp(row.id);
        if (res.code === 1) {
          message.success(res.msg || '删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="miniapp-wechat p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        :custom-buttons="toolbarButtons"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-width="120"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 编辑弹窗 -->
    <EditModal
      v-model:visible="editVisible"
      :data="editData"
      @submit="handleEditSubmit"
    />
  </div>
</template>

<style lang="less" scoped>
.miniapp-wechat {
  background-color: var(--background-deep);
}
</style>
