<script lang="ts" setup>
import type { InvoiceConfig } from '#/api/core/invoice';

import { onMounted, ref } from 'vue';

import { MdiExport, MdiPlus } from '@vben/icons';

import {
  Card,
  Col,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
} from 'ant-design-vue';

import {
  addInvoiceConfigApi,
  deleteInvoiceConfigApi,
  exportInvoiceConfigApi,
  getInvoiceConfigListApi,
  updateInvoiceConfigApi,
} from '#/api/core/invoice';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';
import { handleFileDownload } from '#/utils/export';

import { columns, searchItems } from './config';

const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getInvoiceConfigListApi,
  defaultParams: {},
});

// 新增/编辑配置表单
const configForm = ref({
  visible: false,
  loading: false,
  isEdit: false,
  model: {
    id: undefined as number | undefined,
    user_tax: '',
    app_key: '',
    app_secret: '',
    new_url: '',
    get_url: '',
    clerk: '',
    tax_rate: '',
    access_token: '',
    status: 1,
  },
});

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '关闭', value: 2 },
] as const;

// 重置表单
const resetForm = () => {
  configForm.value.model = {
    id: undefined,
    user_tax: '',
    app_key: '',
    app_secret: '',
    new_url: '',
    get_url: '',
    clerk: '',
    tax_rate: '',
    access_token: '',
    status: 1,
  };
  configForm.value.isEdit = false;
};

// 处理新增配置
const handleAdd = () => {
  resetForm();
  configForm.value.visible = true;
};

// 处理导出
const handleExport = async () => {
  console.log('导出');
  const res = await exportInvoiceConfigApi(searchParams.value);
  await handleFileDownload(res, `发票配置导出${Date.now()}.xls`);
};

// 处理编辑
const handleEdit = (record: InvoiceConfig) => {
  configForm.value.model = {
    id: record.id,
    user_tax: record.user_tax,
    app_key: record.app_key,
    app_secret: record.app_secret,
    new_url: record.new_url,
    get_url: record.get_url,
    clerk: record.clerk,
    tax_rate: record.tax_rate,
    access_token: record.access_token,
    status: record.status,
  };
  configForm.value.isEdit = true;
  configForm.value.visible = true;
};

// 处理删除
const handleDelete = (record: InvoiceConfig) => {
  Modal.confirm({
    title: '确定删除该配置吗？',
    onOk: async () => {
      await deleteInvoiceConfigApi(record.id);
      message.success('删除成功');
      getList();
    },
  });
};

// 处理提交
const handleSubmit = async () => {
  // 表单验证
  if (
    !configForm.value.model.user_tax ||
    !configForm.value.model.tax_rate ||
    !configForm.value.model.clerk ||
    !configForm.value.model.app_secret ||
    !configForm.value.model.app_key ||
    !configForm.value.model.new_url ||
    !configForm.value.model.get_url ||
    !configForm.value.model.access_token
  ) {
    message.error('请填写完整信息');
    return;
  }

  configForm.value.loading = true;
  try {
    const api = configForm.value.isEdit
      ? updateInvoiceConfigApi
      : addInvoiceConfigApi;
    const res = await api(configForm.value.model);
    if (res.code === 1) {
      message.success(
        res.msg || (configForm.value.isEdit ? '修改成功' : '创建成功'),
      );
      configForm.value.visible = false;
      resetForm();
      getList();
    } else {
      message.error(res.msg || '操作失败');
    }
  } catch (error) {
    console.error('操作失败:', error);
    message.error('操作失败');
  } finally {
    configForm.value.loading = false;
  }
};

// 处理取消
const handleCancel = () => {
  configForm.value.visible = false;
  resetForm();
};

// 工具栏按钮
const toolbarButtons = [
  {
    key: 'add',
    text: '新增配置',
    type: 'primary',
    icon: MdiPlus,
    onClick: handleAdd,
  },
  // 导出
  {
    key: 'export',
    text: '导出配置',
    icon: MdiExport,
    onClick: handleExport,
  },
];

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="finance-invoice-config p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        :custom-buttons="toolbarButtons"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <!-- 新增/编辑配置弹窗 -->
      <Modal
        :title="configForm.isEdit ? '编辑配置' : '新增配置'"
        :width="800"
        :visible="configForm.visible"
        :confirm-loading="configForm.loading"
        @update:visible="handleCancel"
        @ok="handleSubmit"
      >
        <Form :model="configForm.model" layout="vertical">
          <Row :gutter="16">
            <Col :span="12">
              <Form.Item label="税号" required>
                <Input
                  v-model:value="configForm.model.user_tax"
                  placeholder="请输入税号"
                />
              </Form.Item>
            </Col>
            <Col :span="12">
              <Form.Item label="开票员" required>
                <Input
                  v-model:value="configForm.model.clerk"
                  placeholder="请输入开票员姓名"
                />
              </Form.Item>
            </Col>
            <Col :span="12">
              <Form.Item label="税率" required>
                <Input
                  v-model:value="configForm.model.tax_rate"
                  placeholder="请输入税率"
                />
              </Form.Item>
            </Col>
            <Col :span="12">
              <Form.Item label="app_secret" required>
                <Input
                  v-model:value="configForm.model.app_secret"
                  placeholder="请输入app_secret"
                />
              </Form.Item>
            </Col>
            <Col :span="12">
              <Form.Item label="app_key" required>
                <Input
                  v-model:value="configForm.model.app_key"
                  placeholder="请输入app_key"
                />
              </Form.Item>
            </Col>
            <Col :span="12">
              <Form.Item label="access_token" required>
                <Input
                  v-model:value="configForm.model.access_token"
                  placeholder="请输入access_token"
                />
              </Form.Item>
            </Col>
            <Col :span="12">
              <Form.Item label="开具发票请求地址" required>
                <Input
                  v-model:value="configForm.model.new_url"
                  placeholder="请输入开具发票请求地址"
                />
              </Form.Item>
            </Col>
            <Col :span="12">
              <Form.Item label="获取发票请求地址" required>
                <Input
                  v-model:value="configForm.model.get_url"
                  placeholder="请输入获取发票请求地址"
                />
              </Form.Item>
            </Col>
            <Col :span="12">
              <Form.Item label="状态" required>
                <Select
                  v-model:value="configForm.model.status"
                  :options="statusOptions"
                  placeholder="请选择状态"
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </Card>
  </div>
</template>

<style lang="less" scoped>
.finance-invoice-config {
  background-color: var(--background-deep);
}
</style>
