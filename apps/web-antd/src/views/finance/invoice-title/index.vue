<script lang="ts" setup>
import { onMounted } from 'vue';

import { MdiExport } from '@vben/icons';

import { Card } from 'ant-design-vue';

import {
  exportInvoiceTitleApi,
  getInvoiceTitleListApi,
} from '#/api/core/invoice';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';
import { handleFileDownload } from '#/utils/export';

import { columns, searchItems } from './config';

const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getInvoiceTitleListApi,
  defaultParams: {},
});

// 导出
const handleExport = async () => {
  const res = await exportInvoiceTitleApi(searchParams.value);
  await handleFileDownload(res, `发票抬头导出${Date.now()}.xls`);
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="finance-invoice-title p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        :custom-buttons="[
          {
            text: '导出',
            icon: MdiExport,
            onClick: handleExport,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.finance-invoice-title {
  background-color: var(--background-deep);
}
</style>
