<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { Card } from 'ant-design-vue';

import { getBalanceList } from '#/api/core/balance';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { columns, createSearchItems } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getBalanceList,
  defaultParams: {},
});

// 搜索配置
const searchItems = ref(createSearchItems());

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="finance-balance p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.finance-balance {
  background-color: var(--background-deep);
}
</style>
