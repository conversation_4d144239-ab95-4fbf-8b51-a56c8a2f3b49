import type { ColumnType } from 'ant-design-vue/es/table';

import type { WithdrawRecord } from '#/api/core/withdraw';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

// 搜索配置
export const searchItems: SearchItemConfig[] = [
  {
    field: 'user_account',
    label: '用户账号',
    component: 'Input',
    props: {
      placeholder: '请输入用户账号',
      allowClear: true,
    },
  },
];

// 提现状态配置
export const withdrawStatusConfig = {
  // 提现状态 1通过 2失败 3待处理
  1: { label: '通过', color: 'success' },
  2: { label: '失败', color: 'error' },
  3: { label: '待处理', color: 'warning' },
} as const;

// 提现方式配置
export const withdrawWayConfig = {
  1: '微信',
  2: '支付宝',
  3: '银行卡',
} as const;

// 表格列配置
export const columns: ColumnType<WithdrawRecord>[] = [
  {
    title: '用户账号',
    dataIndex: 'user_account',
    width: 120,
    align: 'center',
  },
  {
    title: '用户名',
    dataIndex: 'user_name',
    width: 120,
    align: 'center',
  },
  {
    title: '手机号',
    dataIndex: 'user_phone',
    width: 120,
    align: 'center',
  },
  {
    title: '提现金额',
    dataIndex: 'withdraw_amount',
    width: 100,
    align: 'center',
    customRender: ({ text }) => `¥${text.toFixed(2)}`,
  },
  {
    title: '手续费',
    dataIndex: 'withdraw_commission',
    width: 100,
    align: 'center',
    customRender: ({ text }) => `¥${text.toFixed(2)}`,
  },
  {
    title: '实际到账',
    dataIndex: 'withdraw_account_amount',
    width: 100,
    align: 'center',
    customRender: ({ text }) => `¥${text.toFixed(2)}`,
  },
  {
    title: '提现方式',
    dataIndex: 'withdraw_way',
    width: 100,
    align: 'center',
    customRender: ({ text }) =>
      withdrawWayConfig[text as keyof typeof withdrawWayConfig] || text,
  },
  {
    title: '收款账号',
    dataIndex: 'receiving_account',
    width: 150,
    align: 'center',
  },
  {
    title: '提现状态',
    dataIndex: 'withdraw_status',
    width: 100,
    align: 'center',
    customRender: ({ text }) => {
      const config = withdrawStatusConfig[text];
      // 提现状态 1通过 2失败 3待处理
      return config ? h(Tag, { color: config.color }, config.label) : text;
    },
  },
  {
    title: '提现前余额',
    dataIndex: 'withdraw_after',
    width: 120,
    align: 'center',
    customRender: ({ text }) => `¥${text.toFixed(2)}`,
  },
  {
    title: '提现后余额',
    dataIndex: 'withdraw_before',
    width: 120,
    align: 'center',
    customRender: ({ text }) => `¥${text.toFixed(2)}`,
  },
  {
    title: '提现时间',
    dataIndex: 'withdraw_time',
    width: 180,
    align: 'center',
    customRender: ({ text }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '处理时间',
    dataIndex: 'withdraw_handle_time',
    width: 180,
    align: 'center',
    customRender: ({ text }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '处理人',
    dataIndex: 'issue_user_account',
    width: 120,
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'withdraw_remark',
    width: 200,
    align: 'center',
  },
  {
    title: '驳回原因',
    dataIndex: 'msg',
    width: 200,
    align: 'center',
  },
];
