<script lang="ts" setup>
import type { WithdrawRecord } from '#/api/core/withdraw';

import { onMounted, ref } from 'vue';

import { Card, Form, Input, message, Modal } from 'ant-design-vue';

import { getWithdrawListApi, handleWithdrawApi } from '#/api/core/withdraw';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { columns, searchItems } from './config';

const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getWithdrawListApi,
  defaultParams: {},
});

// 驳回表单
const rejectForm = ref({
  msg: '',
  visible: false,
  record: null as null | WithdrawRecord,
});

// 处理驳回
const handleReject = (record: WithdrawRecord) => {
  rejectForm.value.msg = '';
  rejectForm.value.record = record;
  rejectForm.value.visible = true;
};

// 处理驳回确认
const handleRejectConfirm = async () => {
  if (!rejectForm.value.msg || !rejectForm.value.record) {
    message.error('请输入驳回原因');
    return;
  }

  try {
    const res = await handleWithdrawApi({
      withdrawId: rejectForm.value.record.id,
      withdrawStatus: 2,
      msg: rejectForm.value.msg,
    });
    if (res.code === 1) {
      message.success(res.msg || '驳回成功');
      rejectForm.value.visible = false;
      getList();
    } else {
      message.error(res.msg || '驳回失败');
    }
  } catch (error) {
    console.error('驳回失败:', error);
    message.error('驳回失败');
  }
};

// 处理通过
const handlePass = (record: WithdrawRecord) => {
  Modal.confirm({
    title: '通过提现',
    content: '确定要通过该提现申请吗？',
    async onOk() {
      try {
        const res = await handleWithdrawApi({
          withdrawId: record.id,
          withdrawStatus: 1,
        });
        if (res.code === 1) {
          message.success(res.msg || '已通过');
          getList();
        } else {
          message.error(res.msg || '处理失败');
        }
      } catch (error) {
        console.error('处理失败:', error);
        message.error('处理失败');
      }
    },
  });
};

// 获取操作按钮配置
const getActionButtons = (record: WithdrawRecord) => {
  // 只有待处理状态(3)才能操作
  const disabled = record.withdraw_status !== 3;

  return [
    {
      key: 'reject',
      text: '驳回',
      type: 'link',
      onClick: handleReject,
      disabled,
    },
    {
      key: 'pass',
      text: '通过',
      type: 'link',
      onClick: handlePass,
      disabled,
    },
  ];
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="finance-withdraw p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :get-action-buttons="getActionButtons"
        :action-buttons="[
          {
            key: 'reject',
            text: '驳回',
            type: 'link',
            onClick: handleReject,
            disabled: (record) => record.withdraw_status !== 3,
          },
          {
            key: 'pass',
            text: '通过',
            type: 'link',
            onClick: handlePass,
            disabled: (record) => record.withdraw_status !== 3,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 驳回弹窗 -->
    <Modal
      title="驳回提现"
      :width="500"
      :visible="rejectForm.visible"
      @update:visible="(val) => (rejectForm.visible = val)"
      @ok="handleRejectConfirm"
    >
      <Form layout="vertical">
        <Form.Item label="驳回原因" required>
          <Input.TextArea
            v-model:value="rejectForm.msg"
            :rows="4"
            placeholder="请输入驳回原因"
            :maxlength="200"
            show-count
          />
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.finance-withdraw {
  background-color: var(--background-deep);
}

:deep(.ant-input) {
  width: 100%;
  resize: none;
}

.mt-2 {
  margin-top: 16px;
}

.mb-1 {
  margin-bottom: 8px;
}

:deep(.ant-btn-link[disabled]) {
  color: rgba(0, 0, 0, 0.25);
}
</style>
