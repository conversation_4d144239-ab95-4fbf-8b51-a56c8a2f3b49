<script lang="ts" setup>
import type { WithdrawConfig } from './config';

import { onMounted, ref } from 'vue';

import { Card, message } from 'ant-design-vue';

import {
  getCommissionListApi,
  getWithdrawConfigApi,
} from '#/api/core/commission';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import WithdrawModal from './components/withdraw-modal.vue';
import {
  columns,
  createSearchItems,
  createToolbarButtons,
  withdrawTypeConfig,
} from './config';

const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getCommissionListApi,
  defaultParams: {},
});

// 搜索配置
const searchItems = ref(createSearchItems([]));

// 提现配置
const withdrawConfig = ref<WithdrawConfig>();
const withdrawForm = ref({
  withdrawAmount: undefined as string | undefined,
  withdrawWay: undefined as string | undefined,
  withdrawRemark: '',
});

// 提现弹窗
const withdrawVisible = ref(false);

// 获取提现配置
const getWithdrawConfig = async () => {
  try {
    const res = await getWithdrawConfigApi();
    console.log(res, 'res');
    // 微信提现 1开启 2关闭
    // 银行卡提现 1开启 2关闭

    if (res.code === 1) {
      withdrawConfig.value = res.data;
      // 根据配置禁用相应的提现方式
      const withdrawTypeOptions = Object.values(withdrawTypeConfig);
      // 微信提现 1开启 2关闭
      // 银行卡提现 1开启 2关闭
      console.log(withdrawTypeOptions, 'withdrawTypeOptions');
      console.log(withdrawTypeConfig, 'withdrawTypeConfig');

      withdrawTypeOptions.forEach((option) => {
        switch (option.value) {
          case 1: {
            option.disabled = res.data.withdrawWechat === 2;

            break;
          }
          case 2: {
            option.disabled = res.data.withdrawAlipay === 2;

            break;
          }
          case 3: {
            option.disabled = res.data.withdrawBankCard === 2;

            break;
          }
          // No default
        }
      });
    } else {
      message.error(res.msg || '获取提现配置失败');
    }
  } catch (error) {
    console.error('获取提现配置失败:', error);
    message.error('获取提现配置失败');
  }
};

// 重置提现表单
const resetWithdrawForm = () => {
  withdrawForm.value = {
    withdrawAmount: undefined,
    withdrawWay: undefined,
    withdrawRemark: '',
  };
};

// 计算实际到账金额
const calculateActualAmount = (amount: number) => {
  if (!withdrawConfig.value) return 0;
  const fee = amount * (withdrawConfig.value.withdrawRate / 100);
  return amount - fee;
};

// 处理提现
const handleWithdraw = () => {
  if (!withdrawConfig.value) {
    message.error('获取提现配置失败');
    return;
  }
  withdrawVisible.value = true;
};

// 处理提现成功
const handleWithdrawSuccess = () => {
  getList();
};

// 工具栏按钮
const toolbarButtons = createToolbarButtons(handleWithdraw);

// 组件挂载时加载数据
onMounted(() => {
  getList();
  getWithdrawConfig();
});
</script>

<template>
  <div class="finance-commission p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :custom-buttons="toolbarButtons"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 提现弹窗 -->
    <WithdrawModal
      v-model:visible="withdrawVisible"
      :config="withdrawConfig"
      @success="handleWithdrawSuccess"
    />
  </div>
</template>

<style lang="less" scoped>
.finance-commission {
  background-color: var(--background-deep);
}

:deep(.withdraw-help) {
  margin-top: 8px;
  font-size: 13px;
  color: rgba(0, 0, 0, 0.45);

  .help-item {
    line-height: 20px;

    &:not(:last-child) {
      margin-bottom: 4px;
    }
  }
}
</style>
