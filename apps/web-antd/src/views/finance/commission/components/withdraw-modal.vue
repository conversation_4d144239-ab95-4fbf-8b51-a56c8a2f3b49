<script lang="ts" setup>
import type { WithdrawConfig } from '../config';

import { computed, ref, watch } from 'vue';

import { Form, Input, message, Modal, Select } from 'ant-design-vue';

import { withdrawApi } from '#/api/core/commission';

import { withdrawTypeConfig } from '../config';

const props = defineProps<{
  config: undefined | WithdrawConfig;
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}>();

// 表单数据
const formData = ref({
  withdrawAmount: undefined as string | undefined,
  withdrawWay: undefined as string | undefined,
  withdrawRemark: '',
});

// 加载状态
const loading = ref(false);

// 计算实际到账金额
const calculateActualAmount = (amount: number) => {
  if (!props.config) return 0;
  const fee = amount * (props.config.withdrawRate / 100);
  return amount - fee;
};

// 手续费和实际到账金额
const amountInfo = computed(() => {
  if (
    !formData.value.withdrawAmount ||
    Number.isNaN(Number(formData.value.withdrawAmount))
  ) {
    return null;
  }

  const amount = Number(formData.value.withdrawAmount);
  const fee = amount * (props.config!.withdrawRate / 100);
  return {
    fee: fee.toFixed(2),
    actual: (amount - fee).toFixed(2),
  };
});

// 处理提交
const handleSubmit = async () => {
  if (!props.config) {
    message.error('获取提现配置失败');
    return;
  }

  // 检查提现金额是否在允许范围内
  if (!formData.value.withdrawAmount) {
    message.error('请输入提现金额');
    return;
  }

  const amount = Number(formData.value.withdrawAmount);
  if (Number.isNaN(amount)) {
    message.error('请输入有效的提现金额');
    return;
  }

  const { withdrawMin, withdrawMax } = props.config;
  if (amount < withdrawMin || amount > withdrawMax) {
    message.error(`提现金额必须在 ${withdrawMin} 到 ${withdrawMax} 之间`);
    return;
  }

  // 检查提现方式
  if (!formData.value.withdrawWay) {
    message.error('请选择提现方式');
    return;
  }

  try {
    loading.value = true;
    const res = await withdrawApi({
      withdrawAmount: formData.value.withdrawAmount,
      withdrawWay: formData.value.withdrawWay,
      withdrawRemark: formData.value.withdrawRemark,
    });
    if (res.code === 1) {
      // message.success('申请提现成功');
      emit('success');
      emit('update:visible', false);
    } else {
      message.error(res.msg || '申请提现失败');
    }
  } catch (error) {
    console.error('申请提现失败:', error);
    // message.error('申请提现失败');
  } finally {
    loading.value = false;
  }
};

// 监听弹窗关闭，重置表单
watch(
  () => props.visible,
  (val) => {
    if (!val) {
      formData.value = {
        withdrawAmount: undefined,
        withdrawWay: undefined,
        withdrawRemark: '',
      };
    }
  },
);
</script>

<template>
  <Modal
    title="申请提现"
    :width="500"
    :visible="visible"
    :mask-closable="false"
    :keyboard="false"
    :confirm-loading="loading"
    @update:visible="(val) => emit('update:visible', val)"
    @ok="handleSubmit"
  >
    <Form layout="vertical">
      <Form.Item label="提现金额" required :tooltip="config?.prompt">
        <div class="withdraw-info">
          <div class="info-row">
            <span>提现范围：{{ config?.withdrawMin }} -
              {{ config?.withdrawMax }}</span>
            <span>手续费率：{{ config?.withdrawRate }}%</span>
          </div>
          <div v-if="amountInfo" class="info-row">
            <span>
              手续费：<span class="fee">{{ amountInfo.fee }}</span>
            </span>
            <span>
              实际到账：<span class="actual">{{ amountInfo.actual }}</span>
            </span>
          </div>
        </div>
        <Input
          v-model:value="formData.withdrawAmount"
          placeholder="请输入提现金额"
          type="number"
          :min="config?.withdrawMin"
          :max="config?.withdrawMax"
        />
      </Form.Item>

      <Form.Item label="提现方式" required>
        <Select
          v-model:value="formData.withdrawWay"
          placeholder="请选择提现方式"
          :options="
            Object.values(withdrawTypeConfig).map((item) => ({
              label: item.label,
              value: String(item.value),
              disabled: item.disabled,
            }))
          "
        />
      </Form.Item>

      <Form.Item label="备注">
        <Input.TextArea
          v-model:value="formData.withdrawRemark"
          placeholder="请输入备注"
          :rows="3"
        />
      </Form.Item>
    </Form>
  </Modal>
</template>

<style lang="less" scoped>
.withdraw-info {
  margin-bottom: 12px;
  padding: 12px 16px;
  font-size: 13px;
  background-color: var(--component-background);
  border: 1px solid var(--border-color);
  border-radius: 2px;

  .info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--text-color-secondary);

    &:not(:last-child) {
      margin-bottom: 8px;
    }

    .fee {
      color: var(--error-color);
      font-weight: 500;
      margin-left: 4px;
    }

    .actual {
      color: var(--success-color);
      font-weight: 500;
      margin-left: 4px;
    }
  }
}
</style>
