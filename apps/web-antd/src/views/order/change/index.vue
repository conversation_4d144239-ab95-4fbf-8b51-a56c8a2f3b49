<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { Card, message, Modal } from 'ant-design-vue';

import { deleteChangeOrder } from '#/api/core/change';
import { exportOperateLogApi, getChangeOrderListApi } from '#/api/core/order';
import { getUserOptionsApi } from '#/api/core/user';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import DeliveryModal from './components/delivery-modal.vue';
import { advancedSearchItems, basicSearchItems, columns } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch: originalHandleSearch,
  handleReset: originalHandleReset,
} = useTable({
  api: getChangeOrderListApi,
  defaultParams: {},
});
const userLoading = ref(false);

// 自定义搜索处理函数，转换userIds数组为逗号分隔的字符串
const handleSearch = () => {
  // 处理userIds数组转为字符串
  const params = searchParams as any;
  if (
    params.userIds &&
    Array.isArray(params.userIds) &&
    params.userIds.length > 0
  ) {
    params.userIds = params.userIds.join(',');
  }
  originalHandleSearch();
};

// 自定义重置函数
const handleReset = () => {
  originalHandleReset();
};

// 导出
const handleExport = async () => {
  try {
    loading.value = true;
    const res = await exportOperateLogApi(searchParams.value);

    message.success(res.msg);

    // handleFileDownload(res, '换卡列表.xlsx');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  } finally {
    loading.value = false;
  }
};

// 发货弹窗相关状态
const deliveryVisible = ref(false);
const currentRecord = ref<any>(null);

// 获取用户选项
const fetchUserOptions = async () => {
  userLoading.value = true;
  try {
    const res = await getUserOptionsApi();
    if (res.code === 1 && res.data) {
      // 准备用户选项数据
      const userOptions = res.data.map(
        (item: { id: number; name: string }) => ({
          label: item.name,
          value: item.id,
        }),
      );

      // 更新基础搜索中的操作账号选项
      const userSearchItem = basicSearchItems.find(
        (item) => item.field === 'userId',
      );
      if (userSearchItem && userSearchItem.props) {
        // 类型断言确保TypeScript不会报错
        (userSearchItem.props as any).loading = userLoading.value;
        (userSearchItem.props as any).options = userOptions;
      }

      // 更新高级搜索中的归属代理选项
      if (
        advancedSearchItems &&
        Array.isArray(advancedSearchItems) &&
        advancedSearchItems.length > 0 &&
        advancedSearchItems[0] &&
        advancedSearchItems[0].items
      ) {
        const agentSearchItem = advancedSearchItems[0].items.find(
          (item) => item.field === 'userIds',
        );
        if (agentSearchItem && agentSearchItem.props) {
          // 类型断言确保TypeScript不会报错
          (agentSearchItem.props as any).loading = userLoading.value;
          (agentSearchItem.props as any).options = userOptions;
        }
      }
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  } finally {
    userLoading.value = false;

    // 更新 loading 状态
    const userSearchItem = basicSearchItems.find(
      (item) => item.field === 'userId',
    );
    if (userSearchItem && userSearchItem.props) {
      (userSearchItem.props as any).loading = userLoading.value;
    }

    if (
      advancedSearchItems &&
      Array.isArray(advancedSearchItems) &&
      advancedSearchItems.length > 0 &&
      advancedSearchItems[0] &&
      advancedSearchItems[0].items
    ) {
      const agentSearchItem = advancedSearchItems[0].items.find(
        (item) => item.field === 'userIds',
      );
      if (agentSearchItem && agentSearchItem.props) {
        (agentSearchItem.props as any).loading = userLoading.value;
      }
    }
  }
};

// 删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该订单吗？',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        const res = await deleteChangeOrder(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList(); // 重新加载数据
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 发货
const handleDelivery = (record: any) => {
  // 检查是否已经填写了新卡号和快递单号
  if (record.trackingNumber) {
    message.info('该订单已发货');
    return;
  }

  currentRecord.value = record;
  deliveryVisible.value = true;
};

// 根据记录状态获取操作按钮
const getActionButtons = (record: any) => {
  const buttons = [];

  // 所有记录都有删除按钮
  buttons.push({
    key: 'delete',
    text: '删除',
    onClick: handleDelete,
  });

  // 只有未发货的记录才显示发货按钮
  if (!record.trackingNumber) {
    buttons.push({
      key: 'delivery',
      text: '发货',
      onClick: handleDelivery,
    });
  }

  return buttons;
};

// 发货成功回调
const handleDeliverySuccess = () => {
  getList(); // 重新加载数据
};

// 组件挂载时加载数据
onMounted(async () => {
  try {
    await fetchUserOptions(); // 先获取用户选项
    await getList(); // 再加载表格数据
  } catch (error) {
    console.error('初始化失败:', error);
    message.error('初始化失败');
  }
});
</script>

<template>
  <div class="order-change p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedSearchItems as any"
        :loading="loading"
        :custom-buttons="[
          {
            text: '导出',
            onClick: handleExport,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :get-action-buttons="getActionButtons"
        :pagination="pagination"
        :action-buttons="[
          {
            key: 'delivery',
            text: '发货',
            onClick: handleDelivery,
          },
        ]"
        @change="handleTableChange"
      />
    </Card>

    <!-- 发货弹窗 -->
    <DeliveryModal
      v-model:visible="deliveryVisible"
      :record="currentRecord"
      @success="handleDeliverySuccess"
    />
  </div>
</template>

<style lang="less" scoped>
.order-change {
  background-color: var(--background-deep);
}
</style>
