<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { Card, Form, Input, InputNumber, message, Modal } from 'ant-design-vue';

import {
  deleteDeviceBalanceOrder,
  getDeviceBalanceOrderList,
  refundDeviceBalanceOrder,
} from '#/api/core/deviceBalanceOrder';
import { getUserOptionsApi } from '#/api/core/user';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { advancedSearchItems, basicSearchItems, columns } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch: originalHandleSearch,
  handleReset: originalHandleReset,
} = useTable({
  api: getDeviceBalanceOrderList,
  defaultParams: {},
  // 添加请求前参数处理函数
  beforeFetch: (params) => {
    // 深拷贝参数避免直接修改引用
    const newParams = { ...params };

    // 处理userIds数组为逗号分隔的字符串
    if (
      newParams.userIds &&
      Array.isArray(newParams.userIds) &&
      newParams.userIds.length > 0
    ) {
      newParams.userIds = newParams.userIds.join(',');
    }

    return newParams;
  },
});

const userLoading = ref(false);

// 自定义搜索处理函数
const handleSearch = () => {
  originalHandleSearch();
};

// 自定义重置函数
const handleReset = () => {
  originalHandleReset();
};

// 获取用户选项
const fetchUserOptions = async () => {
  userLoading.value = true;
  try {
    const res = await getUserOptionsApi();
    if (res.code === 1 && res.data) {
      // 准备用户选项数据
      const userOptions = res.data.map(
        (item: { id: number; name: string }) => ({
          label: item.name,
          value: item.id,
        }),
      );

      // 更新高级搜索中的账号ID选项
      if (
        advancedSearchItems &&
        Array.isArray(advancedSearchItems) &&
        advancedSearchItems.length > 0 &&
        advancedSearchItems[0] &&
        advancedSearchItems[0].items
      ) {
        const userIdsItem = advancedSearchItems[0].items.find(
          (item) => item.field === 'userIds',
        );
        if (userIdsItem && userIdsItem.props) {
          // 类型断言确保TypeScript不会报错
          (userIdsItem.props as any).loading = userLoading.value;
          (userIdsItem.props as any).options = userOptions;
        }
      }
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  } finally {
    userLoading.value = false;

    // 更新 loading 状态
    if (
      advancedSearchItems &&
      Array.isArray(advancedSearchItems) &&
      advancedSearchItems.length > 0 &&
      advancedSearchItems[0] &&
      advancedSearchItems[0].items
    ) {
      const userIdsItem = advancedSearchItems[0].items.find(
        (item) => item.field === 'userIds',
      );
      if (userIdsItem && userIdsItem.props) {
        (userIdsItem.props as any).loading = userLoading.value;
      }
    }
  }
};

// 删除相关状态
const selectedOrderIds = ref<number[]>([]);

// 退款相关状态
const refundModalVisible = ref(false);
const refundFormState = reactive({
  amount: 0,
  remark: '',
});
const refundFormRef = ref();
const refundLoading = ref(false);
const currentOrderId = ref<number>(0);
const currentOrderInfo = ref<{
  orderName?: string;
  orderPrice?: number;
  systemOrdernumber?: string;
}>({});

// 执行删除操作
const handleDelete = async () => {
  Modal.confirm({
    title: '删除确认',
    content: '确定要删除选中的订单吗？此操作不可恢复。',
    onOk: async () => {
      try {
        const res = await deleteDeviceBalanceOrder(selectedOrderIds.value);
        if (res.code === 1) {
          message.success('删除成功');
          getList(); // 刷新列表
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 显示退款弹窗
const showRefundModal = (record: any) => {
  currentOrderId.value = record.id;
  currentOrderInfo.value = {
    orderName: record.orderName,
    systemOrdernumber: record.systemOrdernumber,
    orderPrice: record.prestorePrice,
  };
  // 使用订单的实际金额作为建议退款金额
  refundFormState.amount = record.prestorePrice || 0;
  refundFormState.remark = '';
  refundModalVisible.value = true;
};

// 执行退款操作
const handleRefund = async () => {
  try {
    refundLoading.value = true;
    await refundFormRef.value.validate();

    const res = await refundDeviceBalanceOrder({
      orderId: currentOrderId.value,
      amount: refundFormState.amount,
      msg: refundFormState.remark,
    });

    if (res.code === 1) {
      message.success('退款成功');
      refundModalVisible.value = false;
      getList(); // 刷新列表
    } else {
      message.error(res.msg || '退款失败');
    }
  } catch (error) {
    console.error('退款失败:', error);
    message.error('退款操作失败');
  } finally {
    refundLoading.value = false;
  }
};

// 关闭退款弹窗
const handleCancelRefund = () => {
  refundModalVisible.value = false;
  refundFormState.amount = 0;
  refundFormState.remark = '';
};

// 组件挂载时加载数据
onMounted(async () => {
  try {
    await fetchUserOptions(); // 先获取用户选项
    await getList(); // 再加载表格数据
  } catch (error) {
    console.error('初始化失败:', error);
    message.error('初始化失败');
  }
});
</script>

<template>
  <div class="order-device-balance p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedSearchItems"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :show-action="true"
        :action-buttons="[
          {
            key: 'delete',
            text: '删除',
            onClick: handleDelete,
          },
          {
            key: 'refund',
            text: '退款',
            onClick: showRefundModal,
            disabled: (record) => record.orderState !== 2, // 只有待支付状态可以退款
          },
        ]"
        @change="handleTableChange"
      />
    </Card>

    <!-- 退款弹窗 -->
    <Modal
      v-model:visible="refundModalVisible"
      title="订单退款"
      :confirm-loading="refundLoading"
      @ok="handleRefund"
      @cancel="handleCancelRefund"
      :width="480"
      :mask-closable="false"
      destroy-on-close
    >
      <div
        v-if="currentOrderInfo.orderName || currentOrderInfo.systemOrdernumber"
        class="mb-4 rounded bg-gray-50 p-3"
      >
        <div v-if="currentOrderInfo.orderName" class="mb-1">
          <span class="text-gray-500">商品名称:</span>
          {{ currentOrderInfo.orderName }}
        </div>
        <div v-if="currentOrderInfo.systemOrdernumber" class="mb-1">
          <span class="text-gray-500">系统单号:</span>
          {{ currentOrderInfo.systemOrdernumber }}
        </div>
        <div v-if="currentOrderInfo.orderPrice !== undefined">
          <span class="text-gray-500">订单金额:</span>
          <span class="text-red-500">￥{{ currentOrderInfo.orderPrice }}</span>
        </div>
      </div>

      <Form ref="refundFormRef" :model="refundFormState" layout="vertical">
        <Form.Item
          label="退款金额"
          name="amount"
          :rules="[
            { required: true, message: '请输入退款金额' },
            { type: 'number', min: 0.01, message: '退款金额必须大于0' },
          ]"
        >
          <InputNumber
            v-model:value="refundFormState.amount"
            style="width: 100%"
            placeholder="请输入退款金额"
            :min="0.01"
            :precision="2"
            :step="1"
            addon-after="元"
          />
        </Form.Item>

        <Form.Item label="退款备注" name="remark">
          <Input
            v-model:value="refundFormState.remark"
            placeholder="请输入退款原因或相关说明"
            allow-clear
          />
        </Form.Item>
      </Form>

      <div class="mt-2 text-xs text-gray-500">
        <div>* 退款金额将直接退回至原支付账户</div>
        <div>* 请核对退款信息后再提交</div>
      </div>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.order-device-balance {
  background-color: var(--background-deep);
}
</style>
