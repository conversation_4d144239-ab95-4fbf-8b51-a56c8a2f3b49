import type { ColumnType } from 'ant-design-vue/es/table';
import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getUserOptionsApi } from '#/api/core/user';

// 状态选项配置
export const statusOptions = {
  orderState: [
    { label: '未支付', value: 1, color: 'warning' },
    { label: '已到账', value: 2, color: 'success' },
    { label: '已取消', value: 3, color: 'default' },
  ],
  orderPayment: [
    { label: '微信支付', value: 1, color: 'success' },
    { label: '支付宝', value: 2, color: 'blue' },
    { label: '斗拱聚合', value: 3, color: 'purple' },
    { label: '火脸', value: 4, color: 'red' },
    { label: '斗拱汇付', value: 5, color: 'orange' },
    { label: '小马哥', value: 6, color: 'cyan' },
  ],
  givePosition: [
    { label: '卡片余额', value: 1 },
    { label: '优惠券', value: 2 },
    { label: '不赠送', value: 3 },
    { label: '积分余额', value: 4 },
  ],
};

// 基础搜索配置
export const basicSearchItems: SearchItemConfig[] = [
  {
    field: 'deviceNo',
    label: '设备号',
    component: 'Input',
    props: {
      placeholder: '请输入设备号',
      allowClear: true,
    },
  },
  {
    field: 'orderName',
    label: '订单名称',
    component: 'Input',
    props: {
      placeholder: '请输入订单名称',
      allowClear: true,
    },
  },
  {
    field: 'systemOrdernumber',
    label: '系统单号',
    component: 'Input',
    props: {
      placeholder: '请输入系统单号',
      allowClear: true,
    },
  },
  {
    field: 'paymentOrdernumber',
    label: '支付单号',
    component: 'Input',
    props: {
      placeholder: '请输入支付单号',
      allowClear: true,
    },
  },
];

// 高级搜索配置
export const advancedSearchItems = [
  {
    label: '订单信息',
    items: [
      {
        field: 'orderState',
        label: '订单状态',
        component: 'Select',
        props: {
          placeholder: '请选择订单状态',
          allowClear: true,
          options: statusOptions.orderState,
        },
      },
      {
        field: 'orderPayment',
        label: '支付方式',
        component: 'Select',
        props: {
          placeholder: '请选择支付方式',
          allowClear: true,
          options: statusOptions.orderPayment,
        },
      },
      {
        field: 'userIds',
        label: '所属账号',
        component: 'Select',
        remote: {
          api: getUserOptionsApi,
          transform: (data: any) =>
            data.data.map((item: any) => ({
              label: `${item.name}`, // 组合显示
              value: item.id,
            })),
        },
        props: {
          placeholder: '请选择所属账号',
          allowClear: true,
          optionFilterProp: 'label',
        },
      },
    ],
  },
  {
    label: '其他信息',
    items: [
      {
        field: 'orderIp',
        label: '订单ip',
        component: 'Input',
        props: {
          placeholder: '请输入订单ip',
          allowClear: true,
        },
      },
      {
        field: 'rechargeAddress',
        label: '充值地址',
        component: 'Input',
        props: {
          placeholder: '请输入充值地址',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '时间筛选',
    items: [
      {
        field: 'creationTimeBegin',
        label: '创建开始时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'creationTimeEnd',
        label: '创建结束时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'rechargeTimeBegin',
        label: '支付开始时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'rechargeTimeEnd',
        label: '支付结束时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'refundTimeBegin',
        label: '退款开始时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'refundTimeEnd',
        label: '退款结束时间',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
];

// 表格列配置
export const columns: ColumnType[] = [
  {
    title: '商品名称',
    dataIndex: 'orderName',
    align: 'center' as AlignType,
    width: 200,
  },
  {
    title: '系统单号',
    dataIndex: 'systemOrdernumber',
    align: 'center' as AlignType,
    width: 180,
  },
  {
    title: '支付单号',
    dataIndex: 'paymentOrdernumber',
    align: 'center' as AlignType,
    width: 180,
  },
  {
    title: '订单价格',
    dataIndex: 'prestorePrice',
    align: 'right' as AlignType,
    width: 120,
    customRender: ({ text }: { text: number }) => `¥${text}`,
  },
  {
    title: '实际付款',
    dataIndex: 'paidInAmount',
    align: 'right' as AlignType,
    width: 120,
    customRender: ({ text }: { text: number }) => `¥${text}`,
  },
  {
    title: '赠送金额',
    dataIndex: 'prestoreGive',
    align: 'right' as AlignType,
    width: 120,
    customRender: ({ text }: { text: number }) => `¥${text}`,
  },
  {
    title: '赠送位置',
    dataIndex: 'givePosition',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => {
      const option = statusOptions.givePosition.find(
        (item) => item.value === text,
      );
      return option ? option.label : text;
    },
  },
  {
    title: '订单状态',
    dataIndex: 'orderState',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => {
      const option = statusOptions.orderState.find(
        (item) => item.value === text,
      );
      return option
        ? h(Tag, { color: option.color }, () => option.label)
        : text;
    },
  },
  {
    title: '订购方式',
    dataIndex: 'orderPayment',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }: { text: number }) => {
      const option = statusOptions.orderPayment.find(
        (item) => item.value === text,
      );
      return option ? h(Tag, { color: option.color }, () => option.label) : '-';
    },
  },
  {
    title: '充值IP',
    dataIndex: 'orderIp',
    align: 'center' as AlignType,
    width: 130,
  },
  {
    title: '充值地址',
    dataIndex: 'rechargeAddress',
    align: 'center' as AlignType,
    ellipsis: true,
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }: { text: string }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '支付时间',
    dataIndex: 'rechargeTime',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }: { text: string }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
];
