<script lang="ts" setup>
import type { TableRowSelection } from 'ant-design-vue/es/table/interface';

import { onMounted, ref } from 'vue';

import { Card, message } from 'ant-design-vue';

import { getDevicePackageOrderList } from '#/api/core/devicePackageOrder';
import { getUserOptionsApi } from '#/api/core/user';
import DetailModal from '#/components/DetailModal/index.vue';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import DeleteConfirmModal from './components/delete-confirm-modal.vue';
import RefundModal from './components/refund-modal.vue';
import RepairModal from './components/repair-modal.vue';
import { advancedSearchItems, basicSearchItems, columns } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch: originalHandleSearch,
  handleReset: originalHandleReset,
} = useTable({
  api: getDevicePackageOrderList,
  defaultParams: {},
  // 添加请求前参数处理函数
  beforeFetch: (params) => {
    // 深拷贝参数避免直接修改引用
    const newParams = { ...params };

    // 处理userIds数组为逗号分隔的字符串
    if (
      newParams.userIds &&
      Array.isArray(newParams.userIds) &&
      newParams.userIds.length > 0
    ) {
      newParams.userIds = newParams.userIds.join(',');
    }

    return newParams;
  },
});

const userLoading = ref(false);

// 自定义搜索处理函数
const handleSearch = () => {
  originalHandleSearch();
};

// 自定义重置函数
const handleReset = () => {
  originalHandleReset();
};

// 获取用户选项
const fetchUserOptions = async () => {
  userLoading.value = true;
  try {
    const res = await getUserOptionsApi();
    if (res.code === 1 && res.data) {
      // 准备用户选项数据
      const userOptions = res.data.map(
        (item: { id: number; name: string }) => ({
          label: item.name,
          value: item.id,
        }),
      );

      // 更新高级搜索中的账号ID选项
      if (
        advancedSearchItems &&
        Array.isArray(advancedSearchItems) &&
        advancedSearchItems.length > 0 &&
        advancedSearchItems[0] &&
        advancedSearchItems[0].items
      ) {
        const userIdsItem = advancedSearchItems[0].items.find(
          (item) => item.field === 'userIds',
        );
        if (userIdsItem && userIdsItem.props) {
          // 类型断言确保TypeScript不会报错
          (userIdsItem.props as any).loading = userLoading.value;
          (userIdsItem.props as any).options = userOptions;
        }
      }
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  } finally {
    userLoading.value = false;

    // 更新 loading 状态
    if (
      advancedSearchItems &&
      Array.isArray(advancedSearchItems) &&
      advancedSearchItems.length > 0 &&
      advancedSearchItems[0] &&
      advancedSearchItems[0].items
    ) {
      const userIdsItem = advancedSearchItems[0].items.find(
        (item) => item.field === 'userIds',
      );
      if (userIdsItem && userIdsItem.props) {
        (userIdsItem.props as any).loading = userLoading.value;
      }
    }
  }
};

// 弹窗相关状态
const refundModalVisible = ref(false);
const repairModalVisible = ref(false);
const deleteModalVisible = ref(false);
const detailModalVisible = ref(false);
const currentOrder = ref({});
const selectedRowKeys = ref<number[]>([]);

// 详情模态框数据
const detailSections = ref<any[]>([]);

// 工具函数 - 获取订单状态文本
const getOrderStateText = (state: number) => {
  const stateMap = {
    1: '等待递交',
    2: '成功',
    3: '失败',
    4: '待支付',
    5: '已过期',
    6: '已退款',
  };
  return stateMap[state as keyof typeof stateMap] || `未知状态(${state})`;
};

// 工具函数 - 获取支付方式文本
const getPaymentMethodText = (paymentMethod: number) => {
  if (paymentMethod === null || paymentMethod === undefined) return '/';

  const paymentMap = {
    0: '未支付',
    1: '余额支付',
    2: '批量充值',
    3: '后台单冲',
    4: '接口递交',
    5: '微信支付',
    6: '支付宝支付',
    7: '点卡充值',
    8: '自动订购',
    9: '汇付',
    10: '火脸',
  };
  return (
    paymentMap[paymentMethod as keyof typeof paymentMap] ||
    `未知方式(${paymentMethod})`
  );
};

// 工具函数 - 获取优惠类型文本
const getDiscountTypeText = (type: number) => {
  const typeMap = {
    1: '无优惠',
    2: '冻结余额抵扣',
    3: '优惠券抵扣',
  };
  return typeMap[type as keyof typeof typeMap] || `未知类型(${type})`;
};

// 工具函数 - 获取生效类型文本
const getTakeeffectTypeText = (type: number) => {
  const typeMap = {
    1: '立即生效',
    2: '次月生效',
    3: '客户自选',
  };
  return typeMap[type as keyof typeof typeMap] || `未知类型(${type})`;
};

// 工具函数 - 获取首次状态文本
const getFirstStatusText = (status: number) => {
  const statusMap = {
    0: '未知',
    1: '首充',
    2: '续费',
  };
  return statusMap[status as keyof typeof statusMap] || `未知状态(${status})`;
};

// 工具函数 - 获取会员订单状态文本
const getMemberOrderStateText = (state: number) => {
  if (state === null || state === undefined) return '/';

  const stateMap = {
    1: '待支付',
    2: '已支付',
    3: '已取消',
    4: '已完成',
    5: '已退款',
  };
  return stateMap[state as keyof typeof stateMap] || `未知状态(${state})`;
};

// 处理查看详情
const handleViewDetail = (record: any) => {
  currentOrder.value = record;

  // 准备详情数据
  detailSections.value = [
    {
      title: '订单基本信息',
      items: [
        { label: '订单ID', value: record.id || '/' },
        { label: '订单名称', value: record.orderName || '/' },
        { label: '系统单号', value: record.systemOrdernumber || '/' },
        { label: '上游单号', value: record.upstreamOrdernumber || '/' },
        { label: '支付单号', value: record.paymentOrdernumber || '/' },
        {
          label: '订单状态',
          value:
            record.orderStateDesc ||
            getOrderStateText(record.orderState) ||
            '/',
        },
        {
          label: '订单数量',
          value: record.orderCount || '1',
        },
        {
          label: '订单IP',
          value: record.orderIp || '/',
        },
        {
          label: '是否首次',
          value: getFirstStatusText(record.firstStatus) || '/',
        },
      ],
    },
    {
      title: '财务信息',
      items: [
        { label: '订单成本', value: record.packageCost || '0.00', bold: true },
        { label: '订单价格', value: record.packagePrice || '0.00', bold: true },
        { label: '实际付款', value: record.paidInAmount || '0.00', bold: true },
        { label: '订单盈利', value: record.orderProfit || '0.00', bold: true },
        { label: '优惠金额', value: record.discountAmount || '0.00' },
        {
          label: '优惠类型',
          value: getDiscountTypeText(record.discountType) || '/',
        },
        {
          label: '支付方式',
          value: getPaymentMethodText(record.orderPayment) || '/',
        },
      ],
    },
    {
      title: '时间信息',
      items: [
        { label: '创建时间', value: record.creationTime || '/' },
        { label: '支付时间', value: record.rechargeTime || '/' },
        {
          label: '生效类型',
          value: getTakeeffectTypeText(record.takeeffectType) || '/',
        },
        { label: '生效时间', value: record.takeeffectTime || '/' },
        { label: '到期时间', value: record.expirationTime || '/' },
        { label: '退款时间', value: record.refundTime || '/' },
      ],
    },
    {
      title: '设备信息',
      items: [
        { label: '设备号', value: record.deviceNo || '/' },
        { label: '设备卡号', value: record.iccidNumber || '/' },
        { label: '充值地址', value: record.rechargeAddress || '/' },
      ],
    },
    {
      title: '用户信息',
      items: [
        { label: '所属账号', value: record.userAccount || '/' },
        { label: '订单结果', value: record.errorLog || '/' },
      ],
    },
  ];

  // 如果有会员订单信息，添加会员订单部分
  if (record.membershipOrders) {
    detailSections.value.push({
      title: '会员订单信息',
      items: [
        { label: '会员ID', value: record.membershipOrders.id || '/' },
        {
          label: '系统单号',
          value: record.membershipOrders.systemOrdernumber || '/',
        },
        { label: '会员名称', value: record.membershipOrders.vipName || '/' },
        { label: '会员ID', value: record.membershipOrders.vipId || '/' },
        { label: '会员图片', value: record.membershipOrders.vipImg || '/' },
        { label: '订购结果', value: record.membershipOrders.msg || '/' },
        {
          label: '关联单号',
          value: record.membershipOrders.associationOrdernumber || '/',
        },
        {
          label: '订单类型',
          value:
            record.membershipOrders.type === 1
              ? '卡片套餐'
              : (record.membershipOrders.type === 2
                ? '设备套餐'
                : '/'),
        },
        {
          label: '价格',
          value: record.membershipOrders.price || '0.00',
          bold: true,
        },
        { label: '数量', value: record.membershipOrders.count || '/' },
        {
          label: '创建时间',
          value: record.membershipOrders.creationTime || '/',
        },
        {
          label: '支付时间',
          value: record.membershipOrders.rechargeTime || '/',
        },
        {
          label: '支付方式',
          value:
            getPaymentMethodText(record.membershipOrders.orderPayment) || '/',
        },
        {
          label: '订单状态',
          value:
            getMemberOrderStateText(record.membershipOrders.orderStatus) || '/',
        },
        {
          label: '生效时间',
          value: record.membershipOrders.takeeffectTime || '/',
        },
        {
          label: '到期时间',
          value: record.membershipOrders.expirationTime || '/',
        },
      ],
    });
  }

  detailModalVisible.value = true;
};

// 处理退款
const handleRefund = (record: any) => {
  currentOrder.value = record;
  refundModalVisible.value = true;
};

// 处理补单
const handleRepair = (record: any) => {
  currentOrder.value = record;
  repairModalVisible.value = true;
};

// 处理删除单个订单
const handleDelete = (record: any) => {
  selectedRowKeys.value = [record.id];
  deleteModalVisible.value = true;
};

// 处理批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的订单');
    return;
  }
  deleteModalVisible.value = true;
};

// 表格选择项发生变化
const onSelectChange: TableRowSelection['onChange'] = (keys) => {
  selectedRowKeys.value = keys.map(Number);
};

// 操作成功回调
const handleOperationSuccess = () => {
  // 刷新列表
  getList();
  // 清空选择
  selectedRowKeys.value = [];
};

// 操作失败回调
const handleOperationError = (error: any) => {
  console.error('操作失败:', error);
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'batchDelete',
    text: '批量删除',
    onClick: handleBatchDelete,
    disabled: () => selectedRowKeys.value.length === 0,
  },
];

// 组件挂载时加载数据
onMounted(async () => {
  try {
    await fetchUserOptions(); // 先获取用户选项
    await getList(); // 再加载表格数据
  } catch (error) {
    console.error('初始化失败:', error);
    message.error('初始化失败');
  }
});
</script>

<template>
  <div class="order-device-package p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedSearchItems"
        :loading="loading"
        :custom-buttons="toolbarButtons"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :show-action="true"
        :action-buttons="[
          {
            key: 'view',
            text: '查看详情',
            onClick: handleViewDetail,
          },
          {
            key: 'repair',
            text: '补单',
            onClick: handleRepair,
            // disabled: (record) => record.orderState !== 4, // 只有待支付状态可以补单
          },
          {
            key: 'refund',
            text: '退款',
            onClick: handleRefund,
            // disabled: (record) => record.orderStates !== 2, // 只有成功状态可以退款
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :row-selection="{
          selectedRowKeys,
          onChange: onSelectChange,
        }"
        row-key="id"
        @change="handleTableChange"
      />
    </Card>

    <!-- 详情弹窗 -->
    <DetailModal
      :visible="detailModalVisible"
      :loading="loading"
      :sections="detailSections"
      title="订单详情"
      @update:visible="(val) => (detailModalVisible = val)"
    />

    <!-- 退款弹窗 -->
    <RefundModal
      :visible="refundModalVisible"
      :order="currentOrder"
      :loading="loading"
      @update:visible="(val) => (refundModalVisible = val)"
      @success="handleOperationSuccess"
      @error="handleOperationError"
    />

    <!-- 补单弹窗 -->
    <RepairModal
      :visible="repairModalVisible"
      :order="currentOrder"
      :loading="loading"
      @update:visible="(val) => (repairModalVisible = val)"
      @success="handleOperationSuccess"
      @error="handleOperationError"
    />

    <!-- 删除确认弹窗 -->
    <DeleteConfirmModal
      :visible="deleteModalVisible"
      :order-ids="selectedRowKeys"
      :loading="loading"
      @update:visible="(val) => (deleteModalVisible = val)"
      @success="handleOperationSuccess"
      @error="handleOperationError"
    />
  </div>
</template>

<style lang="less" scoped>
.order-device-package {
  background-color: var(--background-deep);
}
</style>
