<script lang="ts" setup>
import { defineEmits, defineProps, reactive, ref, watch } from 'vue';

import {
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Space,
} from 'ant-design-vue';

import { refundDevicePackageOrder } from '#/api/core/devicePackageOrder';

// 定义属性和事件
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  order: {
    type: Object,
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:visible', 'success', 'error']);

// 表单状态
const formRef = ref();
const formState = reactive({
  refundType: '1', // 默认原路退回
  refundAmount: 0,
  refundRemark: '',
});
const submitting = ref(false);

// 为保持代码兼容性，创建ref引用
const refundType = ref('1');
const refundAmount = ref(0);
const refundRemark = ref('');

// 同步ref和reactive数据
watch(
  () => formState.refundType,
  (val) => {
    refundType.value = val;
  },
);

watch(
  () => formState.refundAmount,
  (val) => {
    refundAmount.value = val;
  },
);

watch(
  () => formState.refundRemark,
  (val) => {
    refundRemark.value = val;
  },
);

watch(
  () => refundType.value,
  (val) => {
    formState.refundType = val;
  },
);

watch(
  () => refundAmount.value,
  (val) => {
    formState.refundAmount = val;
  },
);

watch(
  () => refundRemark.value,
  (val) => {
    formState.refundRemark = val;
  },
);

// 退款类型选项
const RefundTypeOptions = [
  { label: '不退金额（仅修改状态）', value: '0' },
  { label: '原路退回', value: '1' },
  { label: '退回余额', value: '2' },
  { label: '抹除佣金', value: '3' },
];

// 重置表单
const resetForm = () => {
  formState.refundType = '1';
  formState.refundAmount = props.order?.paidInAmount || 0;
  formState.refundRemark = '';
};

// 关闭弹窗
const handleCancel = () => {
  emit('update:visible', false);
  resetForm();
};

// 监听订单数据变化，设置退款金额
watch(
  () => props.order,
  (newVal) => {
    formState.refundAmount =
      newVal && newVal.paidInAmount ? newVal.paidInAmount : 0;
  },
  { immediate: true },
);

// 监听退款类型，如果是不退金额，将退款金额设为0
watch(
  () => formState.refundType,
  (newType) => {
    if (newType === '0') {
      formState.refundAmount = 0;
    } else if (props.order && props.order.paidInAmount) {
      formState.refundAmount = props.order.paidInAmount;
    }
  },
);

// 提交退款
const handleSubmit = async () => {
  try {
    formRef.value?.validate().then(async () => {
      submitting.value = true;
      try {
        // 当选择不退款时不传金额
        const amount =
          formState.refundType === '0'
            ? '0'
            : formState.refundAmount.toString();

        const result = await refundDevicePackageOrder({
          orderId: props.order.id,
          type: formState.refundType,
          amount,
          msg: formState.refundRemark,
        });

        if (result.code === 1) {
          message.success('退款操作成功');
          emit('success');
          handleCancel();
        } else {
          throw new Error(result.msg || '退款操作失败');
        }
      } catch (error) {
        console.error('退款失败:', error);
        const errorMsg =
          error instanceof Error ? error.message : '退款操作失败';
        message.error(errorMsg);
        emit('error', error);
      } finally {
        submitting.value = false;
      }
    });
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};
</script>

<template>
  <Modal
    :visible="visible"
    title="订单退款"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirm-loading="submitting"
    :mask-closable="false"
    width="500px"
    ok-text="确认退款"
    cancel-text="取消"
  >
    <Form ref="formRef" :model="formState" layout="vertical">
      <Form.Item label="订单号">
        <Input :value="order?.systemOrdernumber" disabled />
      </Form.Item>

      <Form.Item label="订单金额">
        <Input
          :value="order?.paidInAmount ? `¥${order.paidInAmount}` : '-'"
          disabled
        />
      </Form.Item>

      <Form.Item label="退款类型" name="refundType" required>
        <Radio.Group
          v-model:value="formState.refundType"
          class="refund-type-group"
        >
          <Space direction="vertical">
            <Radio
              v-for="item in RefundTypeOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </Radio>
          </Space>
        </Radio.Group>
      </Form.Item>

      <Form.Item
        label="退款金额"
        name="refundAmount"
        required
        :rules="[
          { required: formState.refundType !== '0', message: '请输入退款金额' },
        ]"
      >
        <InputNumber
          v-model:value="formState.refundAmount"
          :disabled="formState.refundType === '0'"
          :min="0"
          :max="order?.paidInAmount || 0"
          :precision="2"
          style="width: 100%"
          placeholder="请输入退款金额"
          addon-after="¥"
        />
      </Form.Item>

      <Form.Item label="退款备注" name="refundRemark">
        <Input.TextArea
          v-model:value="formState.refundRemark"
          placeholder="请输入退款备注"
          :rows="4"
          :maxlength="200"
          show-count
        />
      </Form.Item>
    </Form>
  </Modal>
</template>

<style lang="less" scoped>
.refund-type-group {
  width: 100%;
}
</style>
