import type { ColumnType } from 'ant-design-vue/es/table';
import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

// 状态选项配置
export const statusOptions = {
  orderState: [
    { label: '等待递交', value: 1, color: 'warning' },
    { label: '成功', value: 2, color: 'success' },
    { label: '失败', value: 3, color: 'error' },
    { label: '待支付', value: 4, color: 'processing' },
    { label: '已过期', value: 5, color: 'default' },
    { label: '已退款', value: 6, color: 'default' },
  ],
  takeeffectType: [
    { label: '立即生效', value: 1, color: 'success' },
    { label: '次月生效', value: 2, color: 'processing' },
    { label: '客户自选', value: 3, color: 'warning' },
  ],
  orderPayment: [
    { label: '未支付', value: 0, color: 'default' },
    { label: '余额支付', value: 1, color: 'blue' },
    { label: '批量充值', value: 2, color: 'green' },
    { label: '后台单冲', value: 3, color: 'cyan' },
    { label: '接口递交', value: 4, color: 'blue' },
    { label: '微信支付', value: 5, color: 'green' },
    { label: '支付宝支付', value: 6, color: 'blue' },
    { label: '点卡充值', value: 7, color: 'purple' },
    { label: '自动订购', value: 8, color: 'magenta' },
    { label: '汇付', value: 9, color: 'orange' },
    { label: '火脸', value: 10, color: 'red' },
  ],
};

// 基础搜索配置
export const basicSearchItems: SearchItemConfig[] = [
  {
    field: 'deviceNo',
    label: '设备号',
    component: 'Input' as const,
    props: {
      placeholder: '请输入设备号',
      allowClear: true,
    },
  },
  {
    field: 'orderName',
    label: '订单名称',
    component: 'Input' as const,
    props: {
      placeholder: '请输入订单名称',
      allowClear: true,
    },
  },
  {
    field: 'systemOrdernumber',
    label: '系统单号',
    component: 'Input' as const,
    props: {
      placeholder: '请输入系统单号',
      allowClear: true,
    },
  },
  {
    field: 'cardNo',
    label: '设备卡号',
    component: 'Input' as const,
    props: {
      placeholder: '请输入设备卡号',
      allowClear: true,
    },
  },
  {
    field: 'orderState',
    label: '订单状态',
    component: 'Select' as const,
    props: {
      placeholder: '请选择订单状态',
      allowClear: true,
      options: statusOptions.orderState,
    },
  },
  {
    field: 'orderPayment',
    label: '支付方式',
    component: 'Select' as const,
    props: {
      placeholder: '请选择支付方式',
      allowClear: true,
      options: statusOptions.orderPayment,
    },
  },
];

// 高级搜索配置
export const advancedSearchItems = [
  {
    label: '订单信息',
    items: [
      {
        field: 'upstreamOrdernumber',
        label: '上游单号',
        component: 'Input' as const,
        props: {
          placeholder: '请输入上游单号',
          allowClear: true,
        },
      },
      {
        field: 'paymentOrdernumber',
        label: '支付单号',
        component: 'Input' as const,
        props: {
          placeholder: '请输入支付单号',
          allowClear: true,
        },
      },
      {
        field: 'userIds',
        label: '归属代理',
        component: 'Select' as const,
        props: {
          placeholder: '请选择归属代理',
          allowClear: true,
          // mode: 'multiple',
          options: [], // 将在页面中动态设置
        },
      },
      {
        field: 'takeeffectType',
        label: '生效类型',
        component: 'Select' as const,
        props: {
          placeholder: '请选择生效类型',
          allowClear: true,
          options: statusOptions.takeeffectType,
        },
      },
      {
        field: 'orderIp',
        label: '订单IP',
        component: 'Input' as const,
        props: {
          placeholder: '请输入订单IP',
          allowClear: true,
        },
      },
      {
        field: 'rechargeAddress',
        label: '充值地址',
        component: 'Input' as const,
        props: {
          placeholder: '请输入充值地址',
          allowClear: true,
        },
      },
      {
        field: 'errorLog',
        label: '错误日志',
        component: 'Input' as const,
        props: {
          placeholder: '请输入错误日志',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '时间筛选',
    items: [
      {
        field: 'creationTimeBegin',
        label: '创建开始时间',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'creationTimeEnd',
        label: '创建结束时间',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'rechargeTimeBegin',
        label: '支付开始时间',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'rechargeTimeEnd',
        label: '支付结束时间',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'takeeffectTimeBegin',
        label: '生效开始时间',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'takeeffectTimeEnd',
        label: '生效结束时间',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'expirationTimeBegin',
        label: '到期开始时间',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'expirationTimeEnd',
        label: '到期结束时间',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'refundTimeBegin',
        label: '退款开始时间',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
      {
        field: 'refundTimeEnd',
        label: '退款结束时间',
        component: 'DatePicker' as const,
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
        },
      },
    ],
  },
];

// 修复表格列中的类型错误
const discountTypeMap: Record<number, string> = {
  1: '无优惠',
  2: '冻结余额抵扣',
  3: '优惠券抵扣',
};

const takeeffectTypeMap: Record<number, string> = {
  1: '立即生效',
  2: '次月生效',
  3: '客户自选',
};

const orderStateMap: Record<number, { color: string; label: string }> = {
  1: { label: '等待递交', color: 'warning' },
  2: { label: '成功', color: 'success' },
  3: { label: '失败', color: 'error' },
  4: { label: '待支付', color: 'processing' },
  5: { label: '已过期', color: 'default' },
  6: { label: '已退款', color: 'default' },
};

const orderPaymentMap: Record<number, string> = {
  0: '未支付',
  1: '余额支付',
  2: '批量充值',
  3: '后台单冲',
  4: '接口递交',
  5: '微信支付',
  6: '支付宝支付',
  7: '点卡充值',
  8: '自动订购',
  9: '汇付',
  10: '火脸',
};

// 表格列配置
export const columns: ColumnType[] = [
  {
    title: '订单名称',
    dataIndex: 'orderName',
    align: 'center' as AlignType,
    width: 150,
  },
  {
    title: '设备号',
    dataIndex: 'deviceNo',
    align: 'center' as AlignType,
    width: 120,
  },
  {
    title: '设备卡号',
    dataIndex: 'iccidNumber',
    align: 'center' as AlignType,
    width: 120,
  },
  {
    title: '系统单号',
    dataIndex: 'systemOrdernumber',
    align: 'center' as AlignType,
    width: 180,
  },
  {
    title: '上游单号',
    dataIndex: 'upstreamOrdernumber',
    align: 'center' as AlignType,
    width: 180,
  },
  {
    title: '支付单号',
    dataIndex: 'paymentOrdernumber',
    align: 'center' as AlignType,
    width: 180,
  },
  {
    title: '订单成本',
    dataIndex: 'packageCost',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => `¥${text}`,
  },
  {
    title: '订单价格',
    dataIndex: 'packagePrice',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => `¥${text}`,
  },
  {
    title: '实际付款',
    dataIndex: 'paidInAmount',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => `¥${text}`,
  },
  {
    title: '订单盈利',
    dataIndex: 'orderProfit',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => `¥${text}`,
  },
  {
    title: '优惠金额',
    dataIndex: 'discountAmount',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => `¥${text}`,
  },
  {
    title: '优惠类型',
    dataIndex: 'discountType',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }: { text: number }) => discountTypeMap[text] || text,
  },
  {
    title: '生效类型',
    dataIndex: 'takeeffectType',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) =>
      takeeffectTypeMap[text] || text,
  },
  {
    title: '订单状态',
    dataIndex: 'orderState',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }: { text: number }) => {
      const option = orderStateMap[text];
      return option
        ? h(Tag, { color: option.color }, () => option.label)
        : text;
    },
  },
  {
    title: '支付方式',
    dataIndex: 'orderPayment',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }: { text: number }) => orderPaymentMap[text] || text,
  },
  {
    title: '订单个数',
    dataIndex: 'orderCount',
    align: 'center' as AlignType,
    width: 90,
  },
  {
    title: '订单IP',
    dataIndex: 'orderIp',
    align: 'center' as AlignType,
    width: 130,
  },
  {
    title: '充值地址',
    dataIndex: 'rechargeAddress',
    align: 'center' as AlignType,
    ellipsis: true,
    width: 200,
  },
  {
    title: '所属账号',
    dataIndex: 'userAccount',
    align: 'center' as AlignType,
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }: { text: string }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '支付时间',
    dataIndex: 'rechargeTime',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }: { text: string }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '生效时间',
    dataIndex: 'takeeffectTime',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }: { text: string }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '到期时间',
    dataIndex: 'expirationTime',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }: { text: string }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
];
