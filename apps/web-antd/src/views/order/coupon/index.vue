<script lang="ts" setup>
import { onMounted } from 'vue';

import { Card, message, Modal } from 'ant-design-vue';

import { deleteCouponOrder, getCouponOrderList } from '#/api/core/coupon';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { advancedSearchItems, columns, searchItems } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getCouponOrderList,
  defaultParams: {},
});

// 删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该订单吗？',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        const res = await deleteCouponOrder(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList(); // 重新加载数据
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 组件挂载时加载数据
onMounted(async () => {
  try {
    await getList(); // 再加载表格数据
  } catch (error) {
    console.error('初始化失败:', error);
    message.error('初始化失败');
  }
});
</script>

<template>
  <div class="order-coupon p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :advanced-items="advancedSearchItems"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'delete',
            text: '删除',
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.order-coupon {
  background-color: var(--background-deep);
}
</style>
