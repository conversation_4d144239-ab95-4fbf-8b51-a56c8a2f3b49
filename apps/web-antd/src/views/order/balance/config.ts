import type { ColumnType } from 'ant-design-vue/es/table';
import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getUserOptionsApi } from '#/api';

// 订单状态选项
export const OrderStateOptions = [
  { label: '未支付', value: 1, color: 'warning' },
  { label: '成功', value: 2, color: 'success' },
  { label: '已过期', value: 3, color: 'default' },
  { label: '失败', value: 4, color: 'error' },
];

// 支付方式选项
export const PaymentOptions = [
  { label: '未支付', value: 0 },
  { label: '余额支付', value: 1 },
  { label: '批量充值', value: 2 },
  { label: '后台单冲', value: 3 },
  { label: '接口递交', value: 4 },
  { label: '微信支付', value: 5 },
  { label: '支付宝支付', value: 6 },
];

// 搜索配置
export const searchItems: SearchItemConfig[] = [
  {
    field: 'orderName',
    label: '订单名称',
    component: 'Input',
    props: {
      placeholder: '请输入订单名称',
      allowClear: true,
    },
  },
  {
    field: 'systemOrdernumber',
    label: '系统订单号',
    component: 'Input',
    props: {
      placeholder: '请输入系统订单号',
      allowClear: true,
    },
  },
  {
    field: 'paymentOrdernumber',
    label: '支付订单号',
    component: 'Input',
    props: {
      placeholder: '请输入支付订单号',
      allowClear: true,
    },
  },
  {
    field: 'userId',
    label: '充值账号',
    component: 'Select',
    remote: {
      api: getUserOptionsApi,
      transform: (data: any) =>
        data.data.map((item: any) => ({
          label: `${item.name}`, // 组合显示
          value: item.id,
        })),
    },
    props: {
      placeholder: '请选择充值账号',
      allowClear: true,
      showSearch: true,
      // options: [], // 选项将在页面中动态设置
    },
  },
  {
    field: 'orderState',
    label: '订单状态',
    component: 'Select',
    props: {
      placeholder: '请选择订单状态',
      allowClear: true,
      options: OrderStateOptions,
    },
  },
  {
    field: 'orderIp',
    label: '订单IP',
    component: 'Input',
    props: {
      placeholder: '请输入订单IP',
      allowClear: true,
    },
  },
  {
    field: 'rechargeAddress',
    label: '充值地区',
    component: 'Input',
    props: {
      placeholder: '请输入充值地区',
      allowClear: true,
    },
  },
];

// 表格列配置
export const columns: ColumnType[] = [
  {
    title: '订单名称',
    dataIndex: 'orderName',
    align: 'center' as AlignType,
    width: 200,
    ellipsis: true,
  },
  {
    title: '系统订单号',
    dataIndex: 'systemOrdernumber',
    align: 'center' as AlignType,
    width: 200,
    ellipsis: true,
  },
  {
    title: '支付订单号',
    dataIndex: 'paymentOrdernumber',
    align: 'center' as AlignType,
    width: 200,
    ellipsis: true,
  },
  {
    title: '用户账号',
    dataIndex: 'userAccount',
    align: 'center' as AlignType,
    width: 120,
    ellipsis: true,
  },
  {
    title: '实付金额',
    dataIndex: 'paidInAmount',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }) => `¥${text?.toFixed(2) || '0.00'}`,
  },
  {
    title: '订单金额',
    dataIndex: 'orderPrice',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }) => `¥${text?.toFixed(2) || '0.00'}`,
  },
  {
    title: '订单状态',
    dataIndex: 'orderState',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }) => {
      const option = OrderStateOptions.find((item) => item.value === text);
      return option
        ? h(Tag, { color: option.color }, () => option.label)
        : text;
    },
  },
  {
    title: '支付方式',
    dataIndex: 'orderPayment',
    align: 'center' as AlignType,
    width: 120,
    ellipsis: true,
    customRender: ({ text }) => {
      const option = PaymentOptions.find((item) => item.value === text);
      return option ? option.label : text;
    },
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '充值时间',
    dataIndex: 'rechargeTime',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '订单IP',
    dataIndex: 'orderIp',
    align: 'center' as AlignType,
    width: 150,
    ellipsis: true,
  },
  {
    title: '充值地区',
    dataIndex: 'rechargeAddress',
    align: 'center' as AlignType,
    width: 200,
    ellipsis: true,
  },
  {
    title: '错误信息',
    dataIndex: 'errorMsg',
    align: 'center' as AlignType,
    width: 200,
    ellipsis: true,
  },
];
