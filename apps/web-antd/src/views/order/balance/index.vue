<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { Card, message } from 'ant-design-vue';

import { getPrestoreOrderList } from '#/api/core/prestore';
import { getUserOptionsApi } from '#/api/core/user';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { columns, searchItems } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getPrestoreOrderList,
  defaultParams: {},
});

// 获取用户选项
const fetchUserOptions = async () => {
  try {
    const res = await getUserOptionsApi();
    if (res.code === 1 && res.data) {
      // 更新搜索项中的用户选项
      const userSearchItem = searchItems.find(
        (item) => item.field === 'userId',
      );
      if (userSearchItem && userSearchItem.props) {
        userSearchItem.props.options = res.data.map(
          (item: { id: number; name: string }) => ({
            label: item.name,
            value: item.id,
          }),
        );
      }
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  }
};

const tableType = ref<'ant' | 'vxe'>('vxe');
const showForm = ref(false);

// 将搜索配置转换为表单配置
const formSchema = searchItems.map((item) => ({
  fieldName: item.field,
  label: item.label,
  component: item.component,
  componentProps: {
    ...item.props,
    placeholder: item.props?.placeholder || `请输入${item.label}`,
  },
}));

// 组件挂载时加载数据
onMounted(async () => {
  await fetchUserOptions(); // 先获取用户选项
  getList(); // 再加载表格数据
});
</script>

<template>
  <div class="order-prepaid p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :table-type="tableType"
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.order-prepaid {
  background-color: var(--background-deep);
}
</style>
