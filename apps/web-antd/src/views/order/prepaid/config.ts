import type { ColumnType } from 'ant-design-vue/es/table';
import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getUserOptionsApi } from '#/api/core/user';

// 订单状态选项
export const OrderStateOptions = [
  { label: '未支付', value: 1, color: 'warning' },
  { label: '已到账', value: 2, color: 'success' },
  { label: '已取消', value: 3, color: 'error' },
];

// 赠送位置选项
export const GiftPositionOptions = [
  { label: '卡片余额', value: 1 },
  { label: '不赠送', value: 2 },
  { label: '积分余额', value: 3 },
];

// 订购方式选项
export const OrderTypeOptions = [
  { label: '未支付', value: 0 },
  { label: '余额支付', value: 1 },
  { label: '批量充值', value: 2 },
  { label: '后台单冲', value: 3 },
  { label: '接口递交', value: 4 },
  { label: '微信JSAPI', value: 5 },
  { label: '支付宝当面付', value: 6 },
  { label: '点卡充值', value: 7 },
  { label: '自动订购', value: 8 },
  { label: '汇付', value: 9 },
  { label: '火脸', value: 10 },
  { label: '斗拱', value: 11 },
  { label: '小马哥', value: 12 },
];

// 基础搜索配置
export const searchItems: SearchItemConfig[] = [
  {
    field: 'orderName',
    label: '订单名称',
    component: 'Input',
    props: {
      placeholder: '请输入订单名称',
      allowClear: true,
    },
  },
  {
    field: 'systemOrdernumber',
    label: '系统单号',
    component: 'Input',
    props: {
      placeholder: '请输入系统单号',
      allowClear: true,
    },
  },
  {
    field: 'cardNoBegin',
    label: '起始卡号',
    component: 'Input',
    props: {
      placeholder: '请输入起始卡号',
      allowClear: true,
    },
  },
  {
    field: 'cardNoEnd',
    label: '结尾卡号',
    component: 'Input',
    props: {
      placeholder: '请输入结尾卡号',
      allowClear: true,
    },
  },
  {
    field: 'cardUserId',
    label: '归属代理',
    component: 'Select',
    remote: {
      api: getUserOptionsApi,
      transform: (data: any) =>
        data.data.map((item: any) => ({
          label: `${item.name}`, // 组合显示
          value: item.id,
        })),
    },
    props: {
      placeholder: '请选择归属代理',
      allowClear: true,
    },
  },
  {
    field: 'orderState',
    label: '订单状态',
    component: 'Select',
    props: {
      placeholder: '请选择订单状态',
      allowClear: true,
      options: OrderStateOptions,
    },
  },
  {
    field: 'orderPayment',
    label: '订购方式',
    component: 'Select',
    props: {
      placeholder: '请选择订购方式',
      allowClear: true,
      options: OrderTypeOptions,
    },
  },
];

// 高级搜索配置
export const advancedSearchItems = [
  {
    label: '金额信息',
    items: [
      {
        field: 'prestorePriceMin',
        label: '充值金额(最小)',
        component: 'Input',
        props: {
          placeholder: '请输入最小金额',
          allowClear: true,
        },
      },
      {
        field: 'prestorePriceMax',
        label: '充值金额(最大)',
        component: 'Input',
        props: {
          placeholder: '请输入最大金额',
          allowClear: true,
        },
      },
      {
        field: 'prestoreGiveMin',
        label: '赠送余额(最小)',
        component: 'Input',
        props: {
          placeholder: '请输入最小金额',
          allowClear: true,
        },
      },
      {
        field: 'prestoreGiveMax',
        label: '赠送余额(最大)',
        component: 'Input',
        props: {
          placeholder: '请输入最大金额',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '其他配置',
    items: [
      {
        field: 'paymentOrdernumber',
        label: '支付单号',
        component: 'Input',
        props: {
          placeholder: '请输入支付单号',
          allowClear: true,
        },
      },
      {
        field: 'orderIp',
        label: '订单IP',
        component: 'Input',
        props: {
          placeholder: '请输入订单IP',
          allowClear: true,
        },
      },
      {
        field: 'rechargeAddress',
        label: '充值地址',
        component: 'Input',
        props: {
          placeholder: '请输入充值地址',
          allowClear: true,
        },
      },
      {
        field: 'givePosition',
        label: '赠送位置',
        component: 'Select',
        props: {
          placeholder: '请选择赠送位置',
          allowClear: true,
          options: GiftPositionOptions,
        },
      },
    ],
  },
  {
    label: '时间筛选',
    items: [
      {
        label: '创建开始时间',
        field: 'creationTimeBegin',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        label: '创建结束时间',
        field: 'creationTimeEnd',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        label: '充值开始时间',
        field: 'rechargeTimeBegin',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        label: '充值结束时间',
        field: 'rechargeTimeEnd',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        label: '退款开始时间',
        field: 'refundTimeBegin',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
          allowClear: true,
        },
      },
      {
        label: '退款结束时间',
        field: 'refundTimeEnd',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          style: { width: '100%' },
          allowClear: true,
        },
      },
    ],
  },
];

// 表格列配置
export const columns: ColumnType[] = [
  {
    title: 'ICCID',
    dataIndex: 'void_number',
    align: 'center' as AlignType,
    width: 180,
    ellipsis: true,
  },
  {
    title: '订单名称',
    dataIndex: 'order_name',
    align: 'center' as AlignType,
    width: 200,
    ellipsis: true,
  },
  {
    title: '系统单号',
    dataIndex: 'system_ordernumber',
    align: 'center' as AlignType,
    width: 200,
    ellipsis: true,
  },
  {
    title: '充值金额',
    dataIndex: 'prestore_price',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }) => `¥${text?.toFixed(2) || '0.00'}`,
  },
  {
    title: '赠送余额',
    dataIndex: 'prestore_give',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }) => `¥${text?.toFixed(2) || '0.00'}`,
  },
  {
    title: '赠送位置',
    dataIndex: 'give_position',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }) => {
      const option = GiftPositionOptions.find((item) => item.value === text);
      return option ? option.label : text;
    },
  },
  {
    title: '充值地址',
    dataIndex: 'recharge_address',
    align: 'center' as AlignType,
    width: 200,
    ellipsis: true,
  },
  {
    title: 'IP',
    dataIndex: 'order_ip',
    align: 'center' as AlignType,
    width: 150,
    ellipsis: true,
  },
  {
    title: '订单状态',
    dataIndex: 'order_state',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }) => {
      const option = OrderStateOptions.find((item) => item.value === text);
      return option
        ? h(Tag, { color: option.color }, () => option.label)
        : text;
    },
  },
  {
    title: '归属用户',
    dataIndex: 'user_account',
    align: 'center' as AlignType,
    width: 120,
    ellipsis: true,
  },
  {
    title: '付款时间',
    dataIndex: 'recharge_time',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '订购方式',
    dataIndex: 'order_payment',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }) => {
      const option = OrderTypeOptions.find((item) => item.value === text);
      return option ? option.label : text;
    },
  },
  {
    title: '创建时间',
    dataIndex: 'creation_time',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
];
