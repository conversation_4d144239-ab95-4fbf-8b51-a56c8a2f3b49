<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';

import { MdiExport } from '@vben/icons';

import { Card, Form, Input, InputNumber, message, Modal } from 'ant-design-vue';

import {
  deleteBalanceOrder,
  exportBalanceOrder,
  getBalanceOrderList,
  refundBalanceOrder,
} from '#/api/core/balance';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';
import { handleFileDownload } from '#/utils/export';

import { advancedSearchItems, columns, searchItems } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getBalanceOrderList,
  defaultParams: {},
});

// 处理删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该订单吗？',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        const res = await deleteBalanceOrder(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList(); // 重新加载数据
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 导出
const handleExport = async () => {
  try {
    loading.value = true;
    const res = await exportBalanceOrder(searchParams.value);
    await handleFileDownload(res, '余额订单列表.xlsx');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  } finally {
    loading.value = false;
  }
};

// 退款相关状态
const refundModalVisible = ref(false);
const refundLoading = ref(false);
const currentRecord = ref<any>(null);
const refundForm = reactive({
  amount: 0,
  msg: '',
});
const formRef = ref();

// 表单校验规则
const rules = {
  amount: [
    { required: true, message: '请输入退款金额' },
    { type: 'number', min: 0.01, message: '退款金额必须大于0' },
  ],
  msg: [{ max: 200, message: '退款说明不能超过200个字符' }],
};

// 打开退款弹窗
const handleRefund = (record: any) => {
  currentRecord.value = record;
  refundForm.amount = record.prestore_price || 0;
  refundForm.msg = '';
  refundModalVisible.value = true;
};

// 关闭退款弹窗
const closeRefundModal = () => {
  refundModalVisible.value = false;
  refundForm.amount = 0;
  refundForm.msg = '';
};

// 提交退款
const submitRefund = async () => {
  try {
    await formRef.value.validate();
    refundLoading.value = true;

    Modal.confirm({
      title: '确认退款',
      content: '确定要退款吗？',
      okText: '确定',
      cancelText: '取消',
      async onOk() {
        try {
          const res = await refundBalanceOrder({
            orderId: currentRecord.value.id,
            amount: refundForm.amount,
            msg: refundForm.msg,
          });

          if (res.code === 1) {
            message.success('退款成功');
            closeRefundModal();
            getList(); // 刷新列表
          } else {
            message.error(res.msg || '退款失败');
          }
        } catch (error) {
          console.error('退款失败:', error);
          message.error('退款失败');
        } finally {
          refundLoading.value = false;
        }
      },
    });
  } catch (error) {
    console.error('退款失败:', error);
    message.error('退款失败');
  } finally {
    refundLoading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(async () => {
  getList(); // 再加载表格数据
});
</script>

<template>
  <div class="order-balance p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :advanced-items="advancedSearchItems"
        :loading="loading"
        :custom-buttons="[
          {
            icon: MdiExport,
            text: '导出',
            onClick: handleExport,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :show-action="true"
        :action-buttons="[
          {
            key: 'delete',
            text: '删除',
            onClick: handleDelete,
            permission: 0,
          },
          {
            key: 'refund',
            text: '退款',
            onClick: handleRefund,
            disabled: (record) => record.order_state !== 2,
            permission: 0,
          },
        ]"
        @change="handleTableChange"
      />
    </Card>

    <!-- 退款弹窗 -->
    <Modal
      v-model:visible="refundModalVisible"
      title="订单退款"
      :confirm-loading="refundLoading"
      ok-text="确认退款"
      cancel-text="取消"
      @ok="submitRefund"
      @cancel="closeRefundModal"
    >
      <Form
        ref="formRef"
        :model="refundForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <Form.Item label="订单名称" v-if="currentRecord">
          <span>{{ currentRecord.order_name }}</span>
        </Form.Item>
        <Form.Item label="系统单号" v-if="currentRecord">
          <span>{{ currentRecord.system_ordernumber }}</span>
        </Form.Item>
        <Form.Item label="充值金额" v-if="currentRecord">
          <span>¥{{ currentRecord.prestore_price?.toFixed(2) || '0.00' }}</span>
        </Form.Item>
        <Form.Item label="退款金额" name="amount">
          <InputNumber
            v-model:value="refundForm.amount"
            :precision="2"
            :min="0"
            :max="currentRecord?.prestore_price || 0"
            style="width: 100%"
            placeholder="请输入退款金额"
            addon-after="元"
          />
        </Form.Item>
        <Form.Item label="退款说明" name="msg">
          <Input.TextArea
            v-model:value="refundForm.msg"
            :rows="4"
            placeholder="请输入退款说明，最多200字"
            :maxlength="200"
            show-count
          />
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.order-balance {
  background-color: var(--background-deep);
}
</style>
