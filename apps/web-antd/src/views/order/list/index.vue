<script setup lang="ts">
import type { OrderData, SearchItemConfig, TableColumnType } from './imports';

import type { OrderListParams } from '#/api/core/order';

import { exportOrderApi } from '#/api/core/order';

// 导入批量退款弹窗组件
import BatchRefundModal from './components/batch-refund-modal.vue';
// 导入新创建的批量补单弹窗组件
import BatchReplenishModal from './components/batch-replenish-modal.vue';
// 导入退款日志模态框组件
import RefundLogModal from './components/refund-log-modal.vue';
// 统一导入所有需要的内容
import {
  // 本地组件
  BasicTable,
  // UI组件
  Card,
  // 类型
  computed,
  dayjs,
  // API
  deleteOrderApi,
  // 配置
  EffectiveTypeOptions,
  FirstStatusOptions,
  getAdvancedSearchItems,
  getBasicSearchItems,
  getOrderListApi,
  getTableColumns,
  message,
  Modal,
  NameStatusOptions,
  // Vue相关
  onMounted,
  // 子组件
  OrderDetailModal,
  OrderMessageModal,
  OrderStateOptions,
  PaymentOptions,
  ref,
  RefundModal,
  ReplenishModal,
  SearchToolbar,
  Tag,
  useCopyable,
  useTable,
} from './imports';

defineOptions({ name: 'OrderList' });

const { createCopyableCell } = useCopyable();

// 添加时间格式化函数
const formatDateTime = (time: null | string) => {
  if (!time) return '-';
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};

// 表格加载状态
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getOrderListApi,
  defaultParams: {},
  tableType: 'ant',
});

// 基础搜索项
const basicItems = ref<SearchItemConfig[]>(
  getBasicSearchItems() as SearchItemConfig[],
);

// 高级搜索项
interface SearchGroup {
  label?: string;
  items: SearchItemConfig[];
}

const advancedItems = ref<SearchGroup[]>([]);

// 订单详情相关状态
const detailModalVisible = ref(false);
const currentDetailOrder = ref<null | OrderData>(null);

// 报文详情相关状态
const messageModalVisible = ref(false);
const currentMessageOrderId = ref<null | number>(null);

// 退款相关的状态
const refundModalVisible = ref(false);
const currentRefundOrder = ref<null | OrderData>(null);

// 补单相关的状态
const replenishModalVisible = ref(false);
const currentReplenishOrder = ref<null | OrderData>(null);

// 添加退款日志模态框相关状态
const refundLogModalVisible = ref(false);
const currentOrderNumber = ref<string>('');

// 批量补单相关状态
const batchReplenishVisible = ref(false);

// 批量退款相关状态
const batchRefundVisible = ref(false);

// 表格列定义
const columns = ref(
  getTableColumns(createCopyableCell, formatDateTime) as TableColumnType[],
);

// 选择相关状态
const selectionMode = ref(false);
const selectedRowKeys = ref<(number | string)[]>([]);
const selectedRows = ref<OrderData[]>([]);

// 处理选择变化
const handleSelectionChange = (
  keys: (number | string)[],
  rows: OrderData[],
) => {
  selectedRowKeys.value = keys;
  selectedRows.value = rows;
};

// 切换选择模式
const toggleSelectionMode = () => {
  selectionMode.value = !selectionMode.value;
  // 清空选择
  if (!selectionMode.value) {
    selectedRowKeys.value = [];
    selectedRows.value = [];
  }
};

// 查看报文方法
const handleViewMessage = (record: OrderData) => {
  currentMessageOrderId.value = record.id;
  messageModalVisible.value = true;
};

// 退款方法
const handleRefund = (record: OrderData) => {
  currentRefundOrder.value = record;
  refundModalVisible.value = true;
};

// 补单方法
const handleReplenish = (record: OrderData) => {
  currentReplenishOrder.value = record;
  replenishModalVisible.value = true;
};

// 删除处理方法
const handleDelete = (record: OrderData) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该订单吗？',
    async onOk() {
      try {
        await deleteOrderApi(record.id);
        message.success('删除成功');
        await getList();
      } catch {
        message.error('删除失败');
      }
    },
  });
};

// 查看详情处理函数
const handleView = (record: OrderData) => {
  currentDetailOrder.value = record;
  detailModalVisible.value = true;
};

// 退款成功处理
const handleRefundSuccess = () => {
  message.success('退款成功');
  getList(); // 刷新列表
};

// 退款失败处理
const handleRefundError = (error: Error) => {
  message.error(`退款失败: ${error.message}`);
};

// 补单成功处理
const handleReplenishSuccess = () => {
  message.success('补单成功');
  getList(); // 刷新列表
};

// 补单失败处理
const handleReplenishError = (error: Error) => {
  console.error('补单失败', error);
};

// 添加查看退款日志方法
const handleViewRefundLog = (record: OrderData) => {
  if (!record || !record.systemOrdernumber) {
    console.error('无效的订单记录或订单号缺失');
    message.error('无法查看退款日志：订单号缺失');
    return;
  }

  console.error('查看退款日志', record);
  console.error('查看退款日志', record.systemOrdernumber);

  console.error('查看退款日志', record.systemOrdernumber);
  currentOrderNumber.value = record.systemOrdernumber;

  // 确保在下一个事件循环中执行，避免可能的冲突
  setTimeout(() => {
    refundLogModalVisible.value = true;
    console.error('已显示退款日志弹窗');
  }, 0);
};

// 批量退款方法
const handleBatchRefund = () => {
  // 打开批量退款弹窗
  batchRefundVisible.value = true;
};

// 批量补单方法
const handleBatchReplenish = () => {
  if (!selectionMode.value) {
    // 如果不在选择模式，则进入选择模式
    toggleSelectionMode();
    message.info('请选择需要批量补单的订单');
    return;
  }

  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一个订单进行批量补单');
    return;
  }

  // 打开批量补单弹窗
  batchReplenishVisible.value = true;
};

// 导出订单信息方法
const handleExportOrderInfo = async () => {
  console.error('导出订单信息');
  const result = await exportOrderApi(searchParams.value as OrderListParams);
  message.success(result.msg);
};

// 工具栏按钮配置
const toolbarButtons = computed(() => {
  const buttons = [
    // 批量退款
    {
      text: '批量退款',
      onClick: handleBatchRefund,
      type: 'default' as const,
      props: {},
    },
    // 批量补单
    {
      text: selectionMode.value ? '确认批量补单' : '批量补单',
      onClick: handleBatchReplenish,
      type: selectionMode.value ? ('primary' as const) : ('default' as const),
      props: {
        disabled: selectionMode.value && selectedRowKeys.value.length === 0,
      },
    },
    // 导出订单信息
    {
      text: '导出订单信息',
      onClick: handleExportOrderInfo,
      props: {},
    },
  ];

  // 如果显示选择，添加取消选择按钮
  if (selectionMode.value) {
    buttons.push({
      text: '取消选择',
      onClick: toggleSelectionMode,
      type: 'primary' as const,
      props: {
        disabled: false,
        danger: true,
      },
    });
  }

  return buttons;
});

onMounted(async () => {
  try {
    // 获取高级搜索项
    const result = await getAdvancedSearchItems();
    if (result) {
      advancedItems.value = result as SearchGroup[];
    }

    // 获取表格数据
    getList();
  } catch (error) {
    console.error('初始化数据失败:', error);
  }
});
</script>

<template>
  <div class="order-list p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicItems"
        :advanced-items="advancedItems"
        :loading="loading"
        :custom-buttons="toolbarButtons"
        @search="handleSearch"
        @reset="handleReset"
      />
      <!-- 表格 -->
      <BasicTable
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :show-selection="selectionMode"
        :row-selection="{ selectedRowKeys }"
        @reset="handleReset"
        @selection-change="handleSelectionChange"
        show-action
        :status-options="{
          orderState: OrderStateOptions,
          nameStatus: NameStatusOptions,
          firstStatus: FirstStatusOptions,
        }"
        :action-buttons="[
          {
            key: 'view',
            text: '查看',
            onClick: handleView,
            permission: 0,
          },
          {
            key: 'message',
            text: '报文',
            onClick: handleViewMessage,
          },
          {
            key: 'replenish',
            text: '补单',
            onClick: handleReplenish,
            permission: 1,
            // disabled: (record) => record.orderState !== 4,
          },
          {
            key: 'refund',
            text: '退款',
            onClick: handleRefund,
            permission: 1,
            disabled: (record) =>
              record.orderState !== 2 || record.refundStatus === 2,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
            permission: 1,
          },
        ]"
        @change="handleTableChange"
      >
        <!-- 自定义列渲染 -->
        <template #column-orderState="{ text, record }">
          <Tag
            :color="
              OrderStateOptions.find((item) => item.value === text)?.color ||
              'default'
            "
            class="status-tag"
            @click.stop="handleViewRefundLog(record as OrderData)"
          >
            {{
              OrderStateOptions.find((item) => item.value === text)?.label ||
              text
            }}
          </Tag>
        </template>

        <template #column-nameStatus="{ text }">
          <Tag
            :color="
              NameStatusOptions.find((item) => item.value === text)?.color ||
              'default'
            "
            class="name-status-tag"
          >
            {{
              NameStatusOptions.find((item) => item.value === text)?.label ||
              text
            }}
          </Tag>
        </template>

        <template #column-firstStatus="{ text }">
          <Tag
            :color="
              FirstStatusOptions.find((item) => item.value === text)?.color ||
              'default'
            "
            class="first-status-tag"
          >
            {{
              FirstStatusOptions.find((item) => item.value === text)?.label ||
              text
            }}
          </Tag>
        </template>
      </BasicTable>
    </Card>

    <!-- 订单详情弹窗 -->
    <OrderDetailModal
      v-model:visible="detailModalVisible"
      :order-id="currentDetailOrder?.id ?? null"
      :payment-options="PaymentOptions"
      :effective-type-options="EffectiveTypeOptions"
    />

    <!-- 报文详情弹窗 -->
    <OrderMessageModal
      v-model:visible="messageModalVisible"
      :order-id="currentMessageOrderId"
    />

    <!-- 退款弹窗 -->
    <RefundModal
      v-model:visible="refundModalVisible"
      :order="currentRefundOrder"
      :loading="loading"
      @success="handleRefundSuccess"
      @error="handleRefundError"
    />

    <!-- 补单弹窗 -->
    <ReplenishModal
      v-model:visible="replenishModalVisible"
      :order="currentReplenishOrder"
      :loading="loading"
      @success="handleReplenishSuccess"
      @error="handleReplenishError"
    />

    <!-- 批量补单弹窗 -->
    <BatchReplenishModal
      v-model:visible="batchReplenishVisible"
      :orders="selectedRows"
      :loading="loading"
      @success="handleReplenishSuccess"
      @error="handleReplenishError"
    />

    <!-- 批量退款弹窗 -->
    <BatchRefundModal
      v-model:visible="batchRefundVisible"
      :loading="loading"
      @success="handleRefundSuccess"
      @error="handleRefundError"
    />

    <!-- 退款日志弹窗 -->
    <RefundLogModal
      v-model:visible="refundLogModalVisible"
      :system-ordernumber="currentOrderNumber"
    />
  </div>
</template>

<style lang="less" scoped>
.order-list {
  .status-tag {
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
