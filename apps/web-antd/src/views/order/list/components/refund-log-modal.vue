<script setup lang="ts">
import { computed, onMounted, watch } from 'vue';

import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getRefundLogList } from '#/api/core/task';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  systemOrdernumber: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:visible']);

// 退款类型映射
const refundTypeMap = {
  0: '不退金额',
  1: '原路退回',
  2: '退回余额',
  3: '抹除佣金',
} as const;

// 计算属性用于处理v-model:visible，避免直接修改prop
const localVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
});

// 使用useTable hook
const {
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  searchParams,
} = useTable({
  api: getRefundLogList,
  defaultParams: {
    systemOrdernumber: props.systemOrdernumber,
  },
  tableType: 'ant',
});

// 监听对话框可见性变化
watch(
  () => props.visible,
  (newVal) => {
    console.error(
      '弹窗可见性变化:',
      newVal,
      '订单号:',
      props.systemOrdernumber,
    );
    if (newVal && props.systemOrdernumber) {
      console.error('显示弹窗并加载数据，订单号:', props.systemOrdernumber);
      // 更新搜索参数
      searchParams.value = {
        ...searchParams.value,
        systemOrdernumber: props.systemOrdernumber,
      };
      getList();
    }
  },
);

// 监听订单号变化
watch(
  () => props.systemOrdernumber,
  (newVal) => {
    console.error('订单号变化:', newVal);
    if (newVal && props.visible) {
      console.error('订单号变化，加载数据:', newVal);
      // 更新搜索参数
      searchParams.value = {
        ...searchParams.value,
        systemOrdernumber: newVal,
      };
      getList();
    }
  },
);

// 手动刷新数据的方法
const refreshData = () => {
  if (props.systemOrdernumber) {
    console.error('手动刷新数据，订单号:', props.systemOrdernumber);
    // 更新搜索参数
    searchParams.value = {
      ...searchParams.value,
      systemOrdernumber: props.systemOrdernumber,
    };
    getList();
  } else {
    console.error('刷新失败，没有订单号');
    message.error('无法刷新：订单号不存在');
  }
};

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
};

// 组件挂载时，如果对话框可见且有订单号，则加载数据
onMounted(() => {
  console.error(
    '组件挂载，弹窗可见:',
    props.visible,
    '订单号:',
    props.systemOrdernumber,
  );
  if (props.visible && props.systemOrdernumber) {
    // 更新搜索参数
    searchParams.value = {
      ...searchParams.value,
      systemOrdernumber: props.systemOrdernumber,
    };
    getList();
  }
});

// 定义表格列
const columns = [
  {
    title: '订单号',
    dataIndex: 'systemOrdernumber',
    width: 180,
    align: 'center' as const,
  },
  {
    title: '退款单号',
    dataIndex: 'refundOrdernumber',
    width: 180,
    align: 'center' as const,
  },
  {
    title: '退款类型',
    dataIndex: 'refundType',
    width: 120,
    align: 'center' as const,
    customRender: ({ text }: { text: number }) => {
      return refundTypeMap[text as keyof typeof refundTypeMap] || text;
    },
  },
  {
    title: '退款金额',
    dataIndex: 'refundAmount',
    width: 120,
    align: 'center' as const,
    customRender: ({ text }: { text: number }) => `￥${text || 0}`,
  },
  {
    title: '退款时间',
    dataIndex: 'creationTime',
    width: 180,
    align: 'center' as const,
    customRender: ({ text }: { text: string }) => {
      if (!text) return '-';
      return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '备注',
    dataIndex: 'msg',
    align: 'center' as const,
    customRender: ({ text }: { text: null | string }) => text || '-',
  },
];
</script>

<template>
  <Modal
    v-model:visible="localVisible"
    title="退款日志记录"
    width="900px"
    @cancel="handleClose"
    :footer="null"
    destroy-on-close
  >
    <div class="p-4">
      <div class="mb-4 flex items-center justify-between">
        <h3 class="text-lg font-medium">
          订单号: {{ systemOrdernumber || '未指定' }}
        </h3>
        <a @click="refreshData" class="cursor-pointer text-blue-500">
          刷新数据
        </a>
      </div>

      <BasicTable
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        bordered
      />

      <div
        v-if="tableData.length === 0 && !loading"
        class="py-4 text-center text-gray-500"
      >
        暂无退款记录
      </div>
    </div>
  </Modal>
</template>

<style scoped>
.basic-table {
  margin-top: 16px;
}
</style>
