<script setup lang="ts">
import type { OrderData } from '../imports';

import {
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  ref,
  refundOrderApi,
  RefundTypeOptions,
  Space,
  watch,
} from '../imports';

interface RefundModalProps {
  visible: boolean;
  loading?: boolean;
  order: null | OrderData;
}

interface RefundModalEmits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
  (e: 'error', error: Error): void;
}

const props = withDefaults(defineProps<RefundModalProps>(), {
  loading: false,
});

const emit = defineEmits<RefundModalEmits>();

// 退款类型、金额和备注
const refundType = ref('1'); // 默认选择原路退回
const refundAmount = ref<number>(0);
const refundRemark = ref<string>('');
const confirmLoading = ref(false);
const refundAmountType = ref('0'); // 退款金额类型：0退款金额 1退款百分比

// 监听订单变化，重置表单状态
watch(
  () => props.order,
  (newOrder) => {
    if (newOrder) {
      console.log('newOrder', newOrder);
      refundType.value = '1'; // 重置为原路退回
      refundAmount.value = newOrder.paidInAmount || 0; // 设置为订单实付金额
      refundRemark.value = '';
    }
  },
);

watch(
  () => refundType.value,
  (newType) => {
    if (newType === '0') {
      refundAmount.value = 0;
    } else if (props.order && refundAmount.value === 0) {
      // 如果从"不退金额"切换到其他类型，且金额为0，则设置为订单实付金额
      refundAmount.value = props.order.paidInAmount || 0;
    }
  },
);

// 处理关闭弹窗
const handleCancel = () => {
  emit('update:visible', false);
};

// 处理提交退款
const handleOk = async () => {
  if (!props.order) return;

  try {
    confirmLoading.value = true;

    // 校验金额
    if (refundType.value !== '0' && !refundAmount.value) {
      throw new Error('请输入退款金额');
    }

    // 如果是不退金额类型，强制金额为0
    const amount = refundType.value === '0' ? 0 : refundAmount.value;

    await refundOrderApi({
      orderId: props.order.id.toString(),
      type: Number(refundType.value) as 0 | 1 | 2 | 3,
      amount,
      msg: refundRemark.value,
      amountType: refundAmountType.value,
    });

    emit('success');
    emit('update:visible', false);
  } catch (error) {
    emit('error', error as Error);
  } finally {
    confirmLoading.value = false;
  }
};
</script>

<template>
  <Modal
    :visible="visible"
    title="订单退款"
    @cancel="handleCancel"
    @ok="handleOk"
    :confirm-loading="confirmLoading || loading"
    :mask-closable="false"
    width="500px"
  >
    <Form layout="vertical">
      <Form.Item label="订单金额">
        <Input
          :value="order?.paidInAmount ? `￥${order.paidInAmount}` : '-'"
          disabled
        />
      </Form.Item>

      <Form.Item label="退款类型" required>
        <Radio.Group v-model:value="refundType" class="refund-type-group">
          <Space direction="vertical">
            <Radio
              v-for="item in RefundTypeOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </Radio>
          </Space>
        </Radio.Group>
      </Form.Item>

      <Form.Item
        label="退款金额"
        required
        :rules="[{ required: refundType !== '0', message: '请输入退款金额' }]"
      >
        <InputNumber
          v-model:value="refundAmount"
          :disabled="refundType === '0'"
          :min="0"
          :max="order?.paidInAmount || 0"
          :precision="2"
          style="width: 100%"
          placeholder="请输入退款金额"
        />
      </Form.Item>

      <Form.Item label="退款金额类型" required>
        <Radio.Group v-model:value="refundAmountType">
          <Radio value="0">退款金额</Radio>
          <Radio value="1">退款百分比</Radio>
        </Radio.Group>
      </Form.Item>

      <Form.Item label="退款备注">
        <Input.TextArea
          v-model:value="refundRemark"
          placeholder="请输入退款备注"
          :rows="4"
          :maxlength="200"
          show-count
        />
      </Form.Item>
    </Form>
  </Modal>
</template>

<style lang="less" scoped>
.refund-type-group {
  width: 100%;
}
</style>
