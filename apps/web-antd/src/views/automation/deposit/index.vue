<script lang="ts" setup>
import type { DepositTemplate } from '#/api/core/deposit';
import type { CustomButton } from '#/components/SearchToolbar/types';

import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import {
  deleteDepositTemplate,
  getDepositTemplateList,
} from '#/api/core/deposit';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import AssignToDeviceModal from './components/assign-to-device-modal.vue';
import BatchAssignModal from './components/batch-assign-modal.vue';
import EditModal from './components/edit-modal.vue';
import RuleListModal from './components/rule-list-modal.vue';
import { basicSearchItems, createColumns } from './config';

// 使用 useTable hook
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable<DepositTemplate, { page: number; pageSize: number }>({
  api: getDepositTemplateList,
  defaultParams: {},
});

// 编辑模态框状态
const editModalVisible = ref(false);
const editingRecord = ref<DepositTemplate | null>(null);

// 规则列表模态框状态
const ruleListVisible = ref(false);
const currentTemplateId = ref(0);

// 批量分配模态框状态
const batchAssignVisible = ref(false);

// 分配给设备弹窗
const assignToDeviceVisible = ref(false);
const currentRecord = ref<DepositTemplate>();

// 使用导入的配置
const columns = createColumns();

// 处理新增
const handleAdd = () => {
  editingRecord.value = null;
  editModalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: DepositTemplate) => {
  editingRecord.value = record;
  editModalVisible.value = true;
};

// 处理删除
const handleDelete = (record: DepositTemplate) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该模板吗？',
    async onOk() {
      try {
        const res = await deleteDepositTemplate(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理模态框确认
const handleModalOk = () => {
  getList();
};

// 处理批量分配
const handleBatchAssign = () => {
  if (tableData.value.length === 0) {
    message.warning('暂无可分配的预存模板');
    return;
  }
  batchAssignVisible.value = true;
};

// 处理批量分配成功
const handleBatchAssignSuccess = () => {
  message.success('批量分配成功');
  getList();
};

// 处理分配给设备
const handleAssignToDevice = (record: DepositTemplate) => {
  currentRecord.value = record;
  assignToDeviceVisible.value = true;
};

// 处理分配成功
const handleAssignSuccess = () => {
  getList();
};

// 处理查看规则
const handleViewRules = (record: DepositTemplate) => {
  currentTemplateId.value = record.id;
  ruleListVisible.value = true;
};

// 工具栏按钮配置
const toolbarButtons: CustomButton[] = [
  {
    text: '新增模板',
    type: 'primary',
    icon: MdiPlus,
    onClick: handleAdd,
  },
  {
    text: '分配给卡片',
    type: 'default',
    icon: MdiPlus,
    onClick: handleBatchAssign,
  },
  {
    text: '分配给设备',
    type: 'default',
    icon: MdiPlus,
    onClick: handleAssignToDevice,
  },
];

// 表格操作按钮配置
const actionButtons = [
  {
    key: 'view',
    text: '查看规则',
    type: 'link',
    onClick: handleViewRules,
  },
  {
    key: 'edit',
    text: '编辑',
    type: 'link',
    onClick: handleEdit,
  },
  {
    key: 'delete',
    text: '删除',
    type: 'link',
    danger: true,
    onClick: handleDelete,
  },
] satisfies ActionButton[];

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="automation-deposit">
    <!-- <Card> -->
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="basicSearchItems"
      :custom-buttons="toolbarButtons"
      :loading="loading"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :show-action="true"
      :action-buttons="actionButtons"
      :pagination="pagination"
      @change="handleTableChange"
    />
    <!-- </Card> -->

    <!-- 编辑模态框 -->
    <EditModal
      v-model:visible="editModalVisible"
      :record="editingRecord"
      @ok="handleModalOk"
    />

    <!-- 规则列表模态框 -->
    <RuleListModal
      v-model:visible="ruleListVisible"
      :template-id="currentTemplateId"
    />

    <!-- 批量分配模态框 -->
    <BatchAssignModal
      v-model:visible="batchAssignVisible"
      :templates="tableData"
      @ok="handleBatchAssignSuccess"
    />

    <!-- 分配给设备弹窗 -->
    <AssignToDeviceModal
      v-model:visible="assignToDeviceVisible"
      :record="currentRecord"
      @ok="handleAssignSuccess"
    />
  </div>
</template>

<style lang="less" scoped>
.automation-deposit {
  background-color: var(--background-deep);
}
</style>
