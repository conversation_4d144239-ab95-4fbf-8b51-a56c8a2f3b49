<script lang="ts" setup>
import type { ColumnType } from 'ant-design-vue/es/table';

import type { DepositRule } from '#/api/core/deposit';
import type { CustomButton } from '#/components/SearchToolbar/types';

import { ref, watch } from 'vue';

import { MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import { deleteDepositRule, getDepositRuleList } from '#/api/core/deposit';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { ruleSearchItems } from '../config';
import RuleEditModal from './rule-edit-modal.vue';

const props = defineProps<{
  templateId: number;
  visible: boolean;
}>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 使用 useTable hook
const {
  loading,
  tableData: dataSource,
  pagination,
  searchParams,
  page,
  pageSize,
  getList: loadData,
  handleSearch,
  handleReset,
  handleTableChange,
} = useTable({
  api: getDepositRuleList,
  defaultParams: {
    limitId: props.templateId,
  },
  beforeFetch: (params) => {
    // 确保每次请求都带上最新的 limitId
    return {
      ...params,
      limitId: props.templateId,
    };
  },
});

// 表格列配置
const columns: ColumnType[] = [
  {
    title: 'ID',
    dataIndex: 'id',
    align: 'center',
    width: 80,
  },
  {
    title: '价格',
    dataIndex: 'prestorePrice',
    align: 'center',
    width: 100,
    customRender: ({ text }: { text: number }) => {
      return `${text} 元`;
    },
  },
  {
    title: '赠送金额',
    dataIndex: 'prestoreGive',
    align: 'center',
    width: 100,
    customRender: ({ text }: { text: number }) => {
      return `${text} 元`;
    },
  },
  {
    title: '赠送位置',
    dataIndex: 'givePosition',
    align: 'center',
    width: 120,
    customRender: ({ text }: { text: number }) => {
      // 赠送位置 1自身余额 2套餐代金卷 3不赠送 4积分余额
      const options = [
        { label: '自身余额', value: 1 },
        { label: '套餐代金卷', value: 2 },
        { label: '不赠送', value: 3 },
        { label: '积分余额', value: 4 },
      ];
      return options.find((item) => item.value === text)?.label || '-';
    },
  },
  {
    title: '备注',
    dataIndex: 'msg',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }: { text: null | string }) => {
      return text || '-';
    },
  },
];

// 编辑模态框状态
const editModalVisible = ref(false);
const editingRecord = ref<DepositRule | null>(null);

// 监听 visible 和 templateId 变化
watch(
  [() => props.visible, () => props.templateId],
  ([visible, id]) => {
    if (visible && id) {
      // 更新搜索参数中的 limitId
      searchParams.value = {
        ...searchParams.value,
        limitId: id,
      };
      loadData();
    } else {
      dataSource.value = [];
    }
  },
  { immediate: true },
);

// 处理取消
const handleCancel = () => {
  // 清空搜索参数和数据
  page.value = 1;
  pageSize.value = 10;
  searchParams.value = {};
  dataSource.value = [];
  emit('update:visible', false);
};

// 处理编辑
const handleEdit = (record: DepositRule) => {
  editingRecord.value = record;
  editModalVisible.value = true;
};

// 处理删除
const handleDelete = (record: DepositRule) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该规则吗？',
    async onOk() {
      try {
        const res = await deleteDepositRule(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          loadData();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理新增规则
const handleAddRule = () => {
  editingRecord.value = null;
  editModalVisible.value = true;
};

// 处理模态框确认
const handleModalOk = () => {
  loadData();
};

// 处理编辑模态框关闭
const handleEditModalClose = () => {
  editModalVisible.value = false;
  editingRecord.value = null;
};

// 工具栏按钮配置
const toolbarButtons: CustomButton[] = [
  {
    text: '新增规则',
    type: 'primary',
    icon: MdiPlus,
    onClick: handleAddRule,
  },
];

// 定义操作按钮类型
interface ActionButton {
  key: string;
  text: string;
  type: 'dashed' | 'default' | 'link' | 'primary' | 'text';
  danger?: boolean;
  onClick: (record: any) => void;
}

// 表格操作按钮配置
const actionButtons: ActionButton[] = [
  {
    key: 'edit',
    text: '编辑',
    type: 'link',
    onClick: handleEdit,
  },
  {
    key: 'delete',
    text: '删除',
    type: 'link',
    danger: true,
    onClick: handleDelete,
  },
];
</script>

<template>
  <Modal
    title="规则列表"
    :visible="visible"
    :width="1000"
    :footer="null"
    :mask-closable="false"
    @cancel="handleCancel"
  >
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="ruleSearchItems"
      :loading="loading"
      :custom-buttons="toolbarButtons"
      @reset="handleReset"
      @search="handleSearch"
    />

    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="dataSource"
      :show-action="true"
      :action-buttons="actionButtons"
      :pagination="pagination"
      @change="handleTableChange"
    />

    <!-- 规则编辑模态框 -->
    <RuleEditModal
      v-model:visible="editModalVisible"
      :record="editingRecord"
      :template-id="templateId"
      @ok="handleModalOk"
      @cancel="handleEditModalClose"
    />
  </Modal>
</template>

<style lang="less" scoped>
:deep(.ant-modal-body) {
  padding: 24px;
  height: 600px;
  overflow: auto;
}

:deep(.ant-table-wrapper) {
  margin-top: 16px;
  // 确保分页器在底部有足够空间
  :deep(.ant-table) {
    margin-bottom: 48px;
  }
}

:deep(.ant-modal-content) {
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
