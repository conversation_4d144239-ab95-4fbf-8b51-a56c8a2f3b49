<script lang="ts" setup>
import { ref } from 'vue';

import { Card, Tabs } from 'ant-design-vue';

// 导入各个模板组件
import CycleTemplate from './cycle/index.vue';
import DepositTemplate from './deposit/index.vue';
import PackageTemplate from './package/index.vue';
import SpeedLimitTemplate from './speed-limit/index.vue';
import VirtualTemplate from './virtual/index.vue';

const activeKey = ref('deposit');

// Tab 配置
const tabs = [
  {
    key: 'deposit',
    tab: '预存模板',
    component: DepositTemplate,
  },
  {
    key: 'speedLimit',
    tab: '限速模板',
    component: SpeedLimitTemplate,
  },
  {
    key: 'virtual',
    tab: '虚量模板',
    component: VirtualTemplate,
  },
  {
    key: 'cycle',
    tab: '周期模板',
    component: CycleTemplate,
  },
  {
    key: 'package',
    tab: '套餐模板',
    component: PackageTemplate,
  },
];
</script>

<template>
  <div class="automation-manage p-2">
    <Card>
      <Tabs v-model:active-key="activeKey">
        <Tabs.TabPane v-for="item in tabs" :key="item.key" :tab="item.tab">
          <component :is="item.component" />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<style lang="less" scoped>
.automation-manage {
  position: relative;
  min-height: 100vh;
}
</style>
