<script lang="ts" setup>
import type { SpeedLimitRule } from '#/api/core/speedLimit';

import { ref, watch } from 'vue';

import { MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import {
  deleteSpeedLimitRule,
  getSpeedLimitRuleList,
} from '#/api/core/speedLimit';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';

import AddRuleModal from './add-rule-modal.vue';

interface Props {
  visible: boolean;
  templateId?: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 加载状态
const loading = ref(false);
const searchParams = ref({});

// 表格数据
const dataSource = ref<SpeedLimitRule[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    align: 'center' as const,
    width: 80,
  },
  {
    title: '模板ID',
    dataIndex: 'speedId',
    align: 'center' as const,
    width: 100,
  },
  {
    title: '限速ID编码',
    dataIndex: 'speedCode',
    align: 'center' as const,
    width: 200,
  },
  {
    title: '达量值(MB)',
    dataIndex: 'speedValue',
    align: 'center' as const,
    width: 120,
    // customRender: ({ text }: { text: number }) => {
    //   return text || '-';
    // },
  },
  {
    title: '限速类型',
    dataIndex: 'speedType',
    align: 'center' as const,
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return text === 1 ? '日限速' : '达量限速';
    },
  },
];

// 搜索配置
const basicItems = [
  {
    field: 'speedCode',
    label: '限速编码',
    component: 'Input' as const,
    props: {
      placeholder: '请输入限速编码',
      allowClear: true,
    },
  },
  {
    field: 'speedType',
    label: '限速类型',
    component: 'Select' as const,
    props: {
      placeholder: '请选择限速类型',
      allowClear: true,
      options: [
        { label: '日限速', value: 1 },
        { label: '达量限速', value: 2 },
      ],
    },
  },
];

// 加载数据
const loadData = async () => {
  if (!props.templateId) return;

  loading.value = true;
  try {
    const res = await getSpeedLimitRuleList({
      id: props.templateId,
      page: page.value,
      pageSize: pageSize.value,
      ...searchParams.value,
    });

    if (res.code === 1) {
      dataSource.value = res.data.rows;
      total.value = res.data.total;
    } else {
      message.error(res.msg || '获取规则列表失败');
    }
  } catch (error) {
    console.error('获取规则列表失败:', error);
    message.error('获取规则列表失败');
  } finally {
    loading.value = false;
  }
};

// 新增弹窗状态
const addModalVisible = ref(false);

// 编辑弹窗状态
const editingRecord = ref<SpeedLimitRule>();

// 处理新增规则
const handleAddRule = () => {
  addModalVisible.value = true;
};

// 处理新增成功
const handleAddSuccess = () => {
  loadData();
};

// 处理编辑
const handleEdit = (record: SpeedLimitRule) => {
  editingRecord.value = record;
  addModalVisible.value = true;
};

// 处理删除
const handleDelete = (record: SpeedLimitRule) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该规则吗？',
    async onOk() {
      try {
        const res = await deleteSpeedLimitRule(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          loadData();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    text: '新增规则',
    type: 'primary' as const,
    icon: MdiPlus,
    onClick: handleAddRule,
  },
];

// 操作按钮配置
const actionButtons = [
  {
    key: 'edit',
    text: '编辑',
    type: 'link' as const,
    onClick: handleEdit,
  },
  {
    key: 'delete',
    text: '删除',
    type: 'link' as const,
    danger: true,
    onClick: handleDelete,
  },
];

// 监听模板ID变化
watch(
  () => props.templateId,
  (id) => {
    if (id && props.visible) {
      loadData();
    }
  },
);

// 监听弹窗显示
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.templateId) {
      loadData();
    }
  },
);

// 处理表格变化
const handleTableChange = (pagination: any) => {
  page.value = pagination.current;
  pageSize.value = pagination.pageSize;
  loadData();
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
};
</script>

<template>
  <Modal
    title="规则列表"
    :visible="visible"
    :width="1000"
    :footer="null"
    @cancel="handleCancel"
  >
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="basicItems"
      :custom-buttons="toolbarButtons"
      :loading="loading"
      @search="loadData"
      @reset="loadData"
    />

    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="dataSource"
      :row-key="(record) => record.id"
      :show-action="true"
      :action-buttons="actionButtons"
      :pagination="{
        total,
        current: page,
        pageSize,
        showSizeChanger: true,
        showQuickJumper: true,
      }"
      @change="handleTableChange"
    />

    <!-- 新增/编辑规则弹窗 -->
    <AddRuleModal
      v-model:visible="addModalVisible"
      :template-id="templateId"
      :record="editingRecord"
      @ok="handleAddSuccess"
    />
  </Modal>
</template>
