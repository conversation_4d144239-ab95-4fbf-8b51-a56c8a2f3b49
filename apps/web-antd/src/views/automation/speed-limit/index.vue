<script lang="ts" setup>
import type {
  SpeedLimitListParams,
  SpeedLimitTemplate,
} from '#/api/core/speedLimit';

import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import {
  deleteSpeedLimitTemplate,
  getSpeedLimitList,
} from '#/api/core/speedLimit';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import EditModal from './components/edit-modal.vue';
import RuleListModal from './components/rule-list-modal.vue';
import { basicSearchItems, createColumns } from './config';

// 使用 useTable hook
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable<SpeedLimitTemplate, SpeedLimitListParams>({
  api: getSpeedLimitList,
  defaultParams: {},
});

// 编辑模态框状态
const editModalVisible = ref(false);
const editingRecord = ref<null | SpeedLimitTemplate>(null);

// 规则列表模态框状态
const ruleListVisible = ref(false);
const currentTemplateId = ref<number>();

// 使用导入的配置
const columns = createColumns();

// 处理新增
const handleAdd = () => {
  editingRecord.value = null;
  editModalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: any) => {
  editingRecord.value = record;
  editModalVisible.value = true;
};

// 处理删除
const handleDelete = (record: SpeedLimitTemplate) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该模板吗？',
    async onOk() {
      try {
        const res = await deleteSpeedLimitTemplate(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理模态框确认
const handleModalOk = () => {
  getList();
};

// 处理查看规则
const handleViewRules = (record: SpeedLimitTemplate) => {
  currentTemplateId.value = record.id;
  ruleListVisible.value = true;
};

// 操作按钮配置
const actionButtons = [
  {
    key: 'view',
    text: '查看规则',
    type: 'link' as const,
    onClick: handleViewRules,
  },
  {
    key: 'edit',
    text: '编辑',
    type: 'link' as const,
    onClick: handleEdit,
  },
  {
    key: 'delete',
    text: '删除',
    type: 'link' as const,
    danger: true,
    onClick: handleDelete,
  },
];

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    text: '新增模板',
    type: 'primary' as const,
    icon: MdiPlus,
    onClick: handleAdd,
  },
];

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="automation-speed-limit">
    <!-- <Card> -->
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="basicSearchItems"
      :custom-buttons="toolbarButtons"
      :loading="loading"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :show-action="true"
      :action-buttons="actionButtons"
      :pagination="pagination"
      @change="handleTableChange"
    />
    <!-- </Card> -->

    <!-- 编辑模态框 -->
    <EditModal
      v-model:visible="editModalVisible"
      :record="editingRecord"
      @ok="handleModalOk"
    />

    <!-- 规则列表模态框 -->
    <RuleListModal
      v-model:visible="ruleListVisible"
      :template-id="currentTemplateId"
    />
  </div>
</template>

<style lang="less" scoped>
.automation-speed-limit {
  background-color: var(--background-deep);
}
</style>
