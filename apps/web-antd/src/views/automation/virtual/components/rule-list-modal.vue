<script lang="ts" setup>
import type { VirtualRule } from '#/api/core/virtual';

import { ref, watch } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Form, InputNumber, message, Modal } from 'ant-design-vue';

import {
  addVirtualRule,
  deleteVirtualRule,
  editVirtualRule,
  getVirtualRuleList,
} from '#/api/core/virtual';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';

// 定义分页响应接口
interface PaginationResponse<T> {
  rows: T[];
  total: number;
}

// 定义API响应接口
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

interface Props {
  visible: boolean;
  templateId?: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 加载状态
const loading = ref(false);
const searchParams = ref({});

// 表格数据
const dataSource = ref<VirtualRule[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

// 新增/编辑规则弹窗
const ruleFormVisible = ref(false);
const ruleFormLoading = ref(false);
const ruleFormRef = ref();
const ruleFormData = ref({
  id: undefined as number | undefined,
  voidId: props.templateId,
  voidValue: 0,
  voidRatio: 0,
});
const isEditRule = ref(false);

// 加载数据
const loadData = async () => {
  if (!props.templateId) return;

  loading.value = true;
  try {
    const res = (await getVirtualRuleList({
      id: props.templateId,
      page: page.value,
      pageSize: pageSize.value,
    })) as unknown as ApiResponse<PaginationResponse<VirtualRule>>;

    if (res.code === 1) {
      dataSource.value = res.data.rows || [];
      total.value = res.data.total || 0;
    } else {
      message.error(res.msg || '获取规则列表失败');
    }
  } catch (error) {
    console.error('获取规则列表失败:', error);
    message.error('获取规则列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理新增规则
const handleAddRule = () => {
  isEditRule.value = false;
  ruleFormData.value = {
    id: undefined,
    voidId: props.templateId,
    voidValue: 0,
    voidRatio: 0,
  };
  ruleFormVisible.value = true;
};

// 处理编辑规则
const handleEditRule = (record: VirtualRule) => {
  isEditRule.value = true;
  ruleFormData.value = {
    id: record.id,
    voidId: record.voidId,
    voidValue: record.voidValue,
    voidRatio: record.voidRatio,
  };
  ruleFormVisible.value = true;
};

// 处理删除规则
const handleDeleteRule = (record: VirtualRule) => {
  Modal.confirm({
    title: '提示',
    content: '确定删除该规则吗？',
    onOk: async () => {
      const res = await deleteVirtualRule(record.id);
      message.success(res.msg || '删除成功');
      loadData();
    },
  });
};

// 处理保存规则
const handleSaveRule = async () => {
  try {
    await ruleFormRef.value.validate();
    ruleFormLoading.value = true;

    if (!props.templateId) {
      message.error('模板ID不能为空');
      return;
    }

    if (isEditRule.value) {
      // 编辑模式
      if (ruleFormData.value.id === undefined) {
        message.error('规则ID不能为空');
        return;
      }
      const res = await editVirtualRule({
        id: ruleFormData.value.id,
        voidId: props.templateId!,
        voidValue: ruleFormData.value.voidValue,
        voidRatio: ruleFormData.value.voidRatio,
      });

      if (res.code === 1) {
        message.success('编辑成功');
        ruleFormVisible.value = false;
        loadData();
      } else {
        message.error(res.msg || '编辑失败');
      }
    } else {
      // 新增模式
      const res = await addVirtualRule({
        voidId: props.templateId!,
        voidValue: ruleFormData.value.voidValue,
        voidRatio: ruleFormData.value.voidRatio,
      });

      if (res.code === 1) {
        message.success('新增成功');
        ruleFormVisible.value = false;
        loadData();
      } else {
        message.error(res.msg || '新增失败');
      }
    }
  } catch (error) {
    console.error(isEditRule.value ? '编辑规则失败:' : '新增规则失败:', error);
  } finally {
    ruleFormLoading.value = false;
  }
};

// 处理取消规则表单
const handleCancelRuleForm = () => {
  ruleFormVisible.value = false;
};

// 表格列配置
const columns = [
  {
    title: '达量值',
    dataIndex: 'voidValue',
    align: 'center' as const,
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return `${text} MB`;
    },
  },
  {
    title: '虚百分比',
    dataIndex: 'voidRatio',
    align: 'center' as const,
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return `${text}%`;
    },
  },
];

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    text: '新增规则',
    type: 'primary' as const,
    icon: MdiPlus,
    onClick: () => handleAddRule(),
  },
];

// 监听模板ID变化
watch(
  () => props.templateId,
  (id) => {
    if (id && props.visible) {
      loadData();
      // 更新表单数据中的模板ID
      ruleFormData.value.voidId = id;
    }
  },
);

// 监听弹窗显示
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.templateId) {
      loadData();
    }
  },
);

// 处理表格变化
const handleTableChange = (pagination: any) => {
  page.value = pagination.current;
  pageSize.value = pagination.pageSize;
  loadData();
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
};
</script>

<template>
  <Modal
    title="规则列表"
    :visible="visible"
    :width="1000"
    :footer="null"
    @cancel="handleCancel"
  >
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="[]"
      :custom-buttons="toolbarButtons"
      :loading="loading"
      @search="loadData"
      @reset="loadData"
    />

    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="dataSource"
      :row-key="(record) => record.id"
      :show-action="true"
      :action-buttons="[
        // 编辑
        {
          key: 'edit',
          text: '编辑',
          onClick: handleEditRule,
        },
        // 删除
        {
          key: 'delete',
          text: '删除',
          onClick: handleDeleteRule,
        },
      ]"
      :pagination="{
        total,
        current: page,
        pageSize,
        showSizeChanger: true,
        showQuickJumper: true,
      }"
      @change="handleTableChange"
    />

    <!-- 新增/编辑规则弹窗 -->
    <Modal
      :title="isEditRule ? '编辑规则' : '新增规则'"
      :visible="ruleFormVisible"
      :confirm-loading="ruleFormLoading"
      @ok="handleSaveRule"
      @cancel="handleCancelRuleForm"
    >
      <Form
        ref="ruleFormRef"
        :model="ruleFormData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <Form.Item
          label="达量值"
          name="voidValue"
          :rules="[{ required: true, message: '请输入达量值' }]"
        >
          <InputNumber
            v-model:value="ruleFormData.voidValue"
            :min="0"
            style="width: 100%"
            placeholder="请输入达量值(MB)"
          />
        </Form.Item>
        <Form.Item
          label="虚百分比"
          name="voidRatio"
          :rules="[{ required: true, message: '请输入虚百分比' }]"
        >
          <InputNumber
            v-model:value="ruleFormData.voidRatio"
            :min="0"
            :max="100"
            style="width: 100%"
            placeholder="请输入虚百分比(%)"
          />
        </Form.Item>
      </Form>
    </Modal>
  </Modal>
</template>
