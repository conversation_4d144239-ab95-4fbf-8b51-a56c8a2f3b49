<script lang="ts" setup>
import type { CycleRule } from '#/api/core/cycle';

import { ref, watch } from 'vue';

import { MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import { deleteCycleRule, getCycleRuleList } from '#/api/core/cycle';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';

import RuleEditModal from './rule-edit-modal.vue';

interface Props {
  visible: boolean;
  templateId?: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 加载状态
const loading = ref(false);
const searchParams = ref({});

// 表格数据
const dataSource = ref<CycleRule[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    align: 'center' as const,
    width: 80,
  },
  {
    title: '模板ID',
    dataIndex: 'cycleId',
    align: 'center' as const,
    width: 100,
  },
  {
    title: '周期个数',
    dataIndex: 'cycleCount',
    align: 'center' as const,
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return `${text} 个`;
    },
  },
];

// 加载数据
const loadData = async () => {
  if (!props.templateId) return;

  loading.value = true;
  try {
    const res = await getCycleRuleList({
      id: props.templateId,
      page: page.value,
      pageSize: pageSize.value,
    });
    if (res.code === 1) {
      dataSource.value = res.data.rows;
      total.value = res.data.total;
    } else {
      message.error(res.msg || '获取规则列表失败');
    }
  } catch (error) {
    console.error('获取规则列表失败:', error);
    message.error('获取规则列表失败');
  } finally {
    loading.value = false;
  }
};

// 编辑模态框状态
const editModalVisible = ref(false);
const editingRecord = ref<CycleRule | null>(null);

// 处理新增
const handleAdd = () => {
  editingRecord.value = null;
  editModalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: CycleRule) => {
  editingRecord.value = record;
  editModalVisible.value = true;
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    text: '新增规则',
    type: 'primary' as const,
    icon: MdiPlus,
    onClick: handleAdd,
  },
];

// 处理模态框确认
const handleModalOk = () => {
  loadData();
};

// 处理删除
const handleDelete = (record: CycleRule) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该规则吗？',
    async onOk() {
      try {
        const res = await deleteCycleRule(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          loadData();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 操作按钮配置
const actionButtons = [
  {
    key: 'edit',
    text: '编辑',
    type: 'link' as const,
    onClick: handleEdit,
  },
  {
    key: 'delete',
    text: '删除',
    type: 'link' as const,
    danger: true,
    onClick: handleDelete,
  },
] as const;

// 处理取消
const handleCancel = () => {
  // 清空数据
  dataSource.value = [];
  total.value = 0;
  page.value = 1;
  pageSize.value = 10;
  searchParams.value = {};
  editingRecord.value = null;
  editModalVisible.value = false;
  emit('update:visible', false);
};

// 监听模板ID变化
watch(
  () => props.templateId,
  (id) => {
    if (id && props.visible) {
      loadData();
    }
  },
);

// 监听弹窗显示
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.templateId) {
      loadData();
    } else if (!visible) {
      // 关闭时清空数据
      handleCancel();
    }
  },
);

// 处理表格变化
const handleTableChange = (pagination: any) => {
  page.value = pagination.current;
  pageSize.value = pagination.pageSize;
  loadData();
};
</script>

<template>
  <Modal
    title="规则列表"
    :visible="visible"
    :width="1000"
    :footer="null"
    @cancel="handleCancel"
  >
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="[]"
      :custom-buttons="toolbarButtons"
      :loading="loading"
      @search="loadData"
      @reset="loadData"
    />

    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="dataSource"
      :row-key="(record) => record.id"
      :show-action="true"
      :action-buttons="actionButtons"
      :pagination="{
        total,
        current: page,
        pageSize,
        showSizeChanger: true,
        showQuickJumper: true,
      }"
      @change="handleTableChange"
    />

    <!-- 编辑模态框 -->
    <RuleEditModal
      v-model:visible="editModalVisible"
      :template-id="templateId"
      :record="editingRecord"
      @ok="handleModalOk"
    />
  </Modal>
</template>
