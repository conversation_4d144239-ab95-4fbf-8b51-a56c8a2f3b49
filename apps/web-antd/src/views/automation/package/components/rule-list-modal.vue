<script lang="ts" setup>
import type { PackageRule } from '#/api/core/package';
import type { ActionButton } from '#/components/BasicTable/types';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { onMounted, ref, watch } from 'vue';

import { MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getPackageRuleListApi } from '#/api/core/order';
import { deletePackagePrice, getPackageRuleList } from '#/api/core/package';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';

import AddRuleModal from './add-rule-modal.vue';

interface Props {
  visible: boolean;
  templateId?: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 加载状态
const loading = ref(false);

// 表格数据
const dataSource = ref<PackageRule[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    align: 'center' as const,
    width: 80,
  },
  {
    title: '套餐名称',
    dataIndex: 'packageName',
    align: 'center' as const,
    width: 200,
  },
  {
    title: '套餐成本',
    dataIndex: 'packageCost',
    align: 'center' as const,
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return `${text} 元`;
    },
  },
  {
    title: '套餐价格',
    dataIndex: 'packagePrice',
    align: 'center' as const,
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return `${text} 元`;
    },
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
    align: 'center' as const,
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    align: 'center' as const,
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
];

// 搜索参数
const searchParams = ref({
  packageName: '',
  groupId: undefined,
});

// 规则选项
const ruleOptions = ref<Array<{ label: string; value: number }>>([]);

// 加载规则选项
const loadRuleOptions = async () => {
  try {
    const res = await getPackageRuleListApi();
    if (res.code === 1) {
      ruleOptions.value = res.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error('获取规则选项失败:', error);
  }
};

// 搜索配置
const searchItems: SearchItemConfig[] = [
  {
    field: 'packageName',
    label: '套餐名称',
    component: 'Input',
    props: {
      placeholder: '请输入套餐名称',
      allowClear: true,
    },
  },
  {
    field: 'groupId',
    label: '套餐规则',
    component: 'Select',
    props: {
      placeholder: '请选择套餐规则',
      allowClear: true,
      options: ruleOptions,
    },
  },
];

// 处理搜索
const handleSearch = () => {
  page.value = 1;
  loadData();
};

// 处理重置
const handleReset = () => {
  page.value = 1;
  searchParams.value = {
    packageName: '',
    groupId: undefined,
  };
  loadData();
};

// 加载数据
const loadData = async () => {
  if (!props.templateId) return;

  loading.value = true;
  try {
    const res = await getPackageRuleList({
      templateId: props.templateId,
      page: page.value,
      pageSize: pageSize.value,
      ...searchParams.value,
    });
    if (res.code === 1) {
      dataSource.value = res.data.rows;
      total.value = res.data.total;
    } else {
      message.error(res.msg || '获取规则列表失败');
    }
  } catch (error) {
    console.error('获取规则列表失败:', error);
    message.error('获取规则列表失败');
  } finally {
    loading.value = false;
  }
};

// 监听模板ID变化
watch(
  () => props.templateId,
  (id) => {
    if (id && props.visible) {
      loadData();
    }
  },
);

// 监听弹窗显示
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.templateId) {
      loadData();
    }
  },
);

// 处理表格变化
const handleTableChange = (pagination: any) => {
  page.value = pagination.current;
  pageSize.value = pagination.pageSize;
  loadData();
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 组件挂载时加载规则选项
onMounted(() => {
  loadRuleOptions();
});

// 新增模态框状态
const addModalVisible = ref(false);

// 处理新增
const handleAdd = () => {
  addModalVisible.value = true;
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    text: '新增套餐',
    type: 'primary',
    icon: MdiPlus,
    onClick: handleAdd,
  },
];

// 处理新增成功
const handleAddSuccess = () => {
  loadData();
};

// 编辑模态框状态
const editModalVisible = ref(false);
const editingRecord = ref<null | PackageRule>(null);

// 处理编辑
const handleEdit = (record: PackageRule) => {
  editingRecord.value = record;
  editModalVisible.value = true;
};

// 处理编辑成功
const handleEditSuccess = () => {
  loadData();
};

// 处理删除
const handleDelete = (record: PackageRule) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该套餐吗？',
    async onOk() {
      try {
        const res = await deletePackagePrice(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          loadData();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 操作按钮配置
const actionButtons: ActionButton[] = [
  {
    key: 'edit',
    text: '编辑',
    type: 'link',
    onClick: handleEdit,
  },
  {
    key: 'delete',
    text: '删除',
    type: 'link',
    danger: true,
    onClick: handleDelete,
  },
];
</script>

<template>
  <Modal
    title="套餐列表"
    :visible="visible"
    :width="1000"
    :footer="null"
    @cancel="handleCancel"
  >
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="searchItems"
      :custom-buttons="toolbarButtons"
      :loading="loading"
      @reset="handleReset"
      @search="handleSearch"
    />

    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="dataSource"
      :row-key="(record) => record.id"
      :show-action="true"
      :action-buttons="actionButtons"
      :pagination="{
        total,
        current: page,
        pageSize,
        showSizeChanger: true,
        showQuickJumper: true,
      }"
      @change="handleTableChange"
    />

    <!-- 新增套餐模态框 -->
    <AddRuleModal
      v-model:visible="addModalVisible"
      :template-id="templateId"
      @ok="handleAddSuccess"
    />

    <!-- 编辑套餐模态框 -->
    <AddRuleModal
      v-model:visible="editModalVisible"
      :template-id="templateId"
      :record="editingRecord"
      @ok="handleEditSuccess"
    />
  </Modal>
</template>
