<script setup lang="ts">
import { computed, ref } from 'vue';

import {
  MdiCalendarClock,
  MdiChevronRight,
  MdiCrown,
  MdiGift,
  MdiHistory,
  MdiStar,
  MdiTrendingUp,
  MdiWallet,
} from '@vben/icons';

import { Avatar, Button, Modal, Progress } from 'ant-design-vue';

import MembershipBenefits from './components/membership-benefits.vue';
import MembershipHistory from './components/membership-history.vue';

// 会员等级数据
const memberLevels = {
  bronze: {
    name: '青铜会员',
    level: 'bronze',
    color: '#CD7F32',
    icon: MdiStar,
    points: 0,
  },
  silver: {
    name: '白银会员',
    level: 'silver',
    color: '#C0C0C0',
    icon: MdiStar,
    points: 1000,
  },
  gold: {
    name: '黄金会员',
    level: 'gold',
    color: '#FFD700',
    icon: MdiCrown,
    points: 5000,
  },
};

// 模拟用户数据
const userData = ref({
  username: '张三',
  avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
  memberLevel: 'bronze',
  memberPoints: 650,
  memberExpireDate: '2024-12-31',
  totalSpent: 3200,
  monthlySpent: 850,
  memberBenefits: [
    { id: 1, name: '免费配送', active: true },
    { id: 2, name: '生日礼券', active: false },
    { id: 3, name: '专属客服', active: true },
  ],
  recentTransactions: [
    {
      id: 1,
      title: '会员充值',
      amount: 500,
      type: 'recharge',
      date: '2023-10-15',
    },
    {
      id: 2,
      title: '购买商品',
      amount: 299,
      type: 'purchase',
      date: '2023-10-12',
    },
    {
      id: 3,
      title: '服务预订',
      amount: 199,
      type: 'purchase',
      date: '2023-10-08',
    },
  ],
});

// 计算当前会员等级
const currentLevel = computed(
  () => memberLevels[userData.value.memberLevel] || memberLevels.bronze,
);

// 计算下一个会员等级
const nextLevel = computed(() => {
  const levels = Object.values(memberLevels).sort(
    (a, b) => a.points - b.points,
  );
  const currentIndex = levels.findIndex(
    (level) => level.level === userData.value.memberLevel,
  );
  return currentIndex < levels.length - 1 ? levels[currentIndex + 1] : null;
});

// 计算升级进度
const pointsToNextLevel = computed(() => {
  if (!nextLevel.value) return 0;
  return nextLevel.value.points - userData.value.memberPoints;
});

const upgradeProgress = computed(() => {
  if (!nextLevel.value) return 100;
  const currentLevelPoints = currentLevel.value.points;
  const nextLevelPoints = nextLevel.value.points;
  const currentPoints = userData.value.memberPoints;

  return Math.min(
    100,
    Math.max(
      0,
      Math.floor(
        ((currentPoints - currentLevelPoints) /
          (nextLevelPoints - currentLevelPoints)) *
          100,
      ),
    ),
  );
});

// 弹窗控制
const benefitsModalVisible = ref(false);
const historyModalVisible = ref(false);

// 打开会员权益弹窗
const openBenefitsModal = () => {
  benefitsModalVisible.value = true;
};

// 打开消费记录弹窗
const openHistoryModal = () => {
  historyModalVisible.value = true;
};
</script>

<template>
  <div class="p-4 md:p-6">
    <!-- 会员信息卡片 -->
    <div
      class="mb-6 overflow-hidden rounded-xl bg-white shadow-lg dark:bg-gray-800"
    >
      <!-- 顶部信息区域 -->
      <div class="border-b border-gray-100 p-6 dark:border-gray-700">
        <div
          class="flex flex-col items-center justify-between gap-6 md:flex-row md:items-start"
        >
          <!-- 用户档案 -->
          <div
            class="flex flex-col items-center gap-4 md:flex-row md:items-start"
          >
            <!-- 头像区域 -->
            <div class="relative">
              <Avatar
                :src="userData.avatar"
                :size="80"
                class="border-4 border-white shadow-md dark:border-gray-700"
              />
              <div
                class="absolute -bottom-1 -right-1 flex h-8 w-8 items-center justify-center rounded-full text-white"
                :style="{ backgroundColor: currentLevel.color }"
              >
                <component :is="currentLevel.icon" class="h-4 w-4" />
              </div>
            </div>

            <!-- 用户信息 -->
            <div class="text-center md:text-left">
              <div
                class="mb-2 flex flex-col items-center gap-2 md:flex-row md:items-start"
              >
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                  {{ userData.username }}
                </h1>
                <div
                  class="rounded-full px-3 py-1 text-sm font-medium"
                  :style="{
                    backgroundColor: `${currentLevel.color}20`,
                    color: currentLevel.color,
                  }"
                >
                  {{ currentLevel.name }}
                </div>
              </div>

              <div
                class="flex flex-col gap-4 text-sm text-gray-500 md:flex-row md:gap-6 dark:text-gray-400"
              >
                <div class="flex items-center gap-1">
                  <MdiCalendarClock class="h-4 w-4" />
                  <span>有效期至: {{ userData.memberExpireDate }}</span>
                </div>

                <div
                  class="hidden h-5 w-px self-center bg-gray-200 md:block dark:bg-gray-600"
                ></div>

                <div class="flex items-center gap-1">
                  <component :is="currentLevel.icon" class="h-4 w-4" />
                  <span>距{{ nextLevel?.name || '满级' }}还需:
                    {{ pointsToNextLevel || '0' }}积分</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 积分信息 -->
          <div class="relative">
            <div class="flex h-28 w-28 flex-col items-center justify-center">
              <div class="text-2xl font-bold text-gray-900 dark:text-white">
                {{ userData.memberPoints }}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                我的积分
              </div>
              <Button
                type="primary"
                size="small"
                class="mt-2 flex items-center"
              >
                积分明细
                <MdiChevronRight class="h-4 w-4" />
              </Button>
            </div>
            <div class="absolute left-0 top-0 h-full w-full">
              <Progress
                type="circle"
                :percent="upgradeProgress"
                :stroke-color="currentLevel.color"
                :width="112"
                :stroke-width="6"
                :format="() => ''"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 等级进度条 -->
      <div class="px-6 py-4">
        <div class="mb-2 flex justify-between">
          <div
            v-for="(level, key) in memberLevels"
            :key="key"
            class="flex flex-col items-center"
            :class="{
              'opacity-50':
                memberLevels[userData.memberLevel].points < level.points,
            }"
          >
            <div
              class="mb-1 flex h-8 w-8 items-center justify-center rounded-full"
              :style="{
                backgroundColor:
                  userData.memberLevel === key ||
                  memberLevels[userData.memberLevel].points >= level.points
                    ? level.color
                    : '#e5e7eb',
                boxShadow:
                  userData.memberLevel === key
                    ? `0 0 0 2px ${level.color}`
                    : 'none',
              }"
            >
              <component :is="level.icon" class="h-4 w-4 text-white" />
            </div>
            <span
              class="text-xs font-medium text-gray-700 dark:text-gray-300"
              >{{ level.name }}</span>
          </div>
        </div>

        <Progress
          :percent="upgradeProgress"
          :stroke-color="currentLevel.color"
          :show-info="false"
          class="mt-2"
        />
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <!-- 左侧 - 等级进度和会员功能 -->
      <div class="space-y-6 lg:col-span-1">
        <!-- 会员等级卡片 -->
        <div class="rounded-xl bg-white p-6 shadow-md dark:bg-gray-800">
          <div class="mb-6 flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              会员等级进度
            </h2>
            <span class="text-sm text-gray-500 dark:text-gray-400">有效期至: {{ userData.memberExpireDate }}</span>
          </div>

          <!-- 等级节点 -->
          <div
            class="relative space-y-6 pl-6 before:absolute before:bottom-2 before:left-[11px] before:top-2 before:w-0.5 before:bg-gray-100 before:content-[''] dark:before:bg-gray-700"
          >
            <div
              v-for="(level, key) in memberLevels"
              :key="key"
              class="flex items-center gap-4"
              :class="{
                'opacity-60':
                  memberLevels[userData.memberLevel].points < level.points,
              }"
            >
              <div
                class="z-10 flex h-6 w-6 shrink-0 items-center justify-center rounded-full"
                :style="{ backgroundColor: level.color }"
              >
                <component :is="level.icon" class="h-3 w-3 text-white" />
              </div>
              <div>
                <div class="font-medium text-gray-900 dark:text-white">
                  {{ level.name }}
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  {{ level.points }}积分
                </div>
              </div>
            </div>
          </div>

          <!-- 升级进度 -->
          <div v-if="nextLevel" class="mb-4 mt-6">
            <div class="mb-1 flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">升级进度</span>
              <span class="text-gray-900 dark:text-white">还需
                <span class="text-primary-500 font-semibold">{{
                  pointsToNextLevel
                }}</span>
                积分</span>
            </div>
            <Progress
              :percent="upgradeProgress"
              :stroke-color="nextLevel.color"
            />
          </div>

          <Button type="primary" block class="mt-4">立即升级</Button>
        </div>

        <!-- 会员功能卡片 -->
        <div class="rounded-xl bg-white p-6 shadow-md dark:bg-gray-800">
          <h2 class="mb-6 text-lg font-semibold text-gray-900 dark:text-white">
            会员功能
          </h2>

          <div class="grid grid-cols-2 gap-4">
            <div
              class="flex cursor-pointer flex-col items-center rounded-xl border border-gray-100 bg-white p-4 text-center shadow-sm transition-shadow hover:shadow-md dark:border-gray-600 dark:bg-gray-700"
              @click="openBenefitsModal"
            >
              <div
                class="mb-3 flex h-12 w-12 items-center justify-center rounded-xl bg-emerald-50 text-emerald-500 dark:bg-emerald-900/30 dark:text-emerald-400"
              >
                <MdiGift class="h-6 w-6" />
              </div>
              <div class="mb-1 font-medium text-gray-900 dark:text-white">
                会员权益
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                查看专属权益
              </div>
            </div>

            <div
              class="flex cursor-pointer flex-col items-center rounded-xl border border-gray-100 bg-white p-4 text-center shadow-sm transition-shadow hover:shadow-md dark:border-gray-600 dark:bg-gray-700"
              @click="openHistoryModal"
            >
              <div
                class="mb-3 flex h-12 w-12 items-center justify-center rounded-xl bg-orange-50 text-orange-500 dark:bg-orange-900/30 dark:text-orange-400"
              >
                <MdiHistory class="h-6 w-6" />
              </div>
              <div class="mb-1 font-medium text-gray-900 dark:text-white">
                消费记录
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                查看历史消费
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧 - 数据与交易记录 -->
      <div class="space-y-6 lg:col-span-2">
        <!-- 数据统计 -->
        <div class="rounded-xl bg-white p-6 shadow-md dark:bg-gray-800">
          <h2 class="mb-6 text-lg font-semibold text-gray-900 dark:text-white">
            我的数据
          </h2>

          <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div class="flex items-center gap-4">
              <div
                class="flex h-12 w-12 shrink-0 items-center justify-center rounded-xl bg-indigo-50 text-indigo-500 dark:bg-indigo-900/30 dark:text-indigo-400"
              >
                <MdiWallet class="h-6 w-6" />
              </div>
              <div>
                <div
                  class="text-xl font-semibold text-gray-900 dark:text-white"
                >
                  ¥{{ userData.totalSpent.toFixed(2) }}
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  累计消费
                </div>
              </div>
            </div>

            <div class="flex items-center gap-4">
              <div
                class="flex h-12 w-12 shrink-0 items-center justify-center rounded-xl bg-emerald-50 text-emerald-500 dark:bg-emerald-900/30 dark:text-emerald-400"
              >
                <MdiTrendingUp class="h-6 w-6" />
              </div>
              <div>
                <div
                  class="text-xl font-semibold text-gray-900 dark:text-white"
                >
                  ¥{{ userData.monthlySpent.toFixed(2) }}
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  本月消费
                </div>
              </div>
            </div>

            <div class="flex items-center gap-4">
              <div
                class="flex h-12 w-12 shrink-0 items-center justify-center rounded-xl bg-orange-50 text-orange-500 dark:bg-orange-900/30 dark:text-orange-400"
              >
                <MdiGift class="h-6 w-6" />
              </div>
              <div>
                <div
                  class="text-xl font-semibold text-gray-900 dark:text-white"
                >
                  {{ userData.memberBenefits.filter((b) => b.active).length }}
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  激活权益
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近交易记录 -->
        <div class="rounded-xl bg-white p-6 shadow-md dark:bg-gray-800">
          <div class="mb-6 flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              最近交易
            </h2>
            <Button
              type="link"
              @click="openHistoryModal"
              class="flex items-center"
            >
              查看全部 <MdiChevronRight class="h-4 w-4" />
            </Button>
          </div>

          <div class="divide-y divide-gray-100 dark:divide-gray-700">
            <div
              v-for="tx in userData.recentTransactions"
              :key="tx.id"
              class="flex items-center justify-between py-4 first:pt-0 last:pb-0"
            >
              <div>
                <div class="font-medium text-gray-900 dark:text-white">
                  {{ tx.title }}
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  {{ tx.date }}
                </div>
              </div>
              <div
                :class="{
                  'text-emerald-500': tx.type === 'recharge',
                  'text-indigo-500': tx.type === 'purchase',
                }"
                class="text-lg font-semibold"
              >
                {{ tx.type === 'recharge' ? '+' : '-' }}¥{{
                  tx.amount.toFixed(2)
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 会员权益弹窗 -->
    <Modal
      v-model:visible="benefitsModalVisible"
      title="会员权益"
      width="800px"
      :footer="null"
      :destroy-on-close="true"
    >
      <MembershipBenefits />
    </Modal>

    <!-- 消费记录弹窗 -->
    <Modal
      v-model:visible="historyModalVisible"
      title="消费记录"
      width="900px"
      :footer="null"
      :destroy-on-close="true"
    >
      <MembershipHistory />
    </Modal>
  </div>
</template>
