<script setup lang="ts">
import type { TableColumnsType } from 'ant-design-vue';

import { h, ref } from 'vue';

import { Table, Tag } from 'ant-design-vue';

interface HistoryRecord {
  id: number;
  date: string;
  type: 'purchase' | 'recharge' | 'refund';
  title: string;
  amount: number;
  points: number;
  status: 'failed' | 'pending' | 'success';
}

const records = ref<HistoryRecord[]>([
  {
    id: 1,
    date: '2023-11-15 14:32:10',
    type: 'purchase',
    title: '购买商品 - 智能插座',
    amount: 299,
    points: 299,
    status: 'success',
  },
  {
    id: 2,
    date: '2023-11-10 09:15:23',
    type: 'recharge',
    title: '会员充值',
    amount: 500,
    points: 550,
    status: 'success',
  },
  {
    id: 3,
    date: '2023-10-25 16:48:35',
    type: 'purchase',
    title: '购买商品 - 网关设备',
    amount: 1299,
    points: 1299,
    status: 'success',
  },
  {
    id: 4,
    date: '2023-10-15 11:30:42',
    type: 'refund',
    title: '退款 - 温湿度传感器',
    amount: -149,
    points: -149,
    status: 'success',
  },
  {
    id: 5,
    date: '2023-09-28 18:12:05',
    type: 'purchase',
    title: '购买商品 - 路由器',
    amount: 499,
    points: 499,
    status: 'success',
  },
  {
    id: 6,
    date: '2023-09-20 13:25:18',
    type: 'recharge',
    title: '会员充值',
    amount: 1000,
    points: 1200,
    status: 'success',
  },
  {
    id: 7,
    date: '2023-08-15 10:05:36',
    type: 'purchase',
    title: '购买商品 - 智能灯泡套装',
    amount: 399,
    points: 399,
    status: 'success',
  },
]);

const columns: TableColumnsType = [
  {
    title: '日期',
    dataIndex: 'date',
    key: 'date',
    width: 180,
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
    customRender: ({ text }) => {
      const typeConfig = {
        purchase: { color: 'blue', text: '购买' },
        recharge: { color: 'green', text: '充值' },
        refund: { color: 'orange', text: '退款' },
      };
      return h(Tag, { color: typeConfig[text].color }, typeConfig[text].text);
    },
  },
  {
    title: '内容',
    dataIndex: 'title',
    key: 'title',
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount',
    align: 'right',
    customRender: ({ text, record }) => {
      const color = record.type === 'refund' ? '#f5222d' : '#52c41a';
      return h(
        'span',
        { style: { color, fontWeight: 500 } },
        `${record.type === 'refund' ? '-' : '+'}¥${Math.abs(text).toFixed(2)}`,
      );
    },
  },
  {
    title: '积分',
    dataIndex: 'points',
    key: 'points',
    align: 'right',
    customRender: ({ text }) => {
      return h(
        'span',
        {
          style: {
            color: text < 0 ? '#f5222d' : '#1890ff',
            fontWeight: 500,
          },
        },
        `${text > 0 ? '+' : ''}${text}`,
      );
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    customRender: ({ text }) => {
      const statusConfig = {
        success: { color: 'success', text: '成功' },
        pending: { color: 'processing', text: '处理中' },
        failed: { color: 'error', text: '失败' },
      };
      return h(
        Tag,
        { color: statusConfig[text].color },
        statusConfig[text].text,
      );
    },
  },
];

const pagination = {
  pageSize: 10,
  total: records.value.length,
  showTotal: (total) => `共 ${total} 条记录`,
  showQuickJumper: true,
  showSizeChanger: true,
};
</script>

<template>
  <div class="membership-history">
    <div class="summary-section">
      <div class="summary-item">
        <div class="summary-label">总消费金额</div>
        <div class="summary-value">
          ¥{{
            records
              .filter((r) => r.type === 'purchase')
              .reduce((sum, record) => sum + record.amount, 0)
              .toFixed(2)
          }}
        </div>
      </div>
      <div class="summary-item">
        <div class="summary-label">累计充值金额</div>
        <div class="summary-value">
          ¥{{
            records
              .filter((r) => r.type === 'recharge')
              .reduce((sum, record) => sum + record.amount, 0)
              .toFixed(2)
          }}
        </div>
      </div>
      <div class="summary-item">
        <div class="summary-label">累计获得积分</div>
        <div class="summary-value highlight">
          {{
            records.reduce(
              (sum, record) => (record.points > 0 ? sum + record.points : sum),
              0,
            )
          }}
        </div>
      </div>
    </div>

    <Table
      :columns="columns"
      :data-source="records"
      :pagination="pagination"
      :row-key="(record) => record.id"
      class="history-table"
    />
  </div>
</template>

<style lang="less" scoped>
.membership-history {
  .summary-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    gap: 16px;

    @media (max-width: 768px) {
      flex-direction: column;
    }

    .summary-item {
      flex: 1;
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 16px;
      text-align: center;

      .summary-label {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 8px;
      }

      .summary-value {
        font-size: 24px;
        font-weight: 600;

        &.highlight {
          color: #1890ff;
        }
      }
    }
  }

  .history-table {
    :deep(.ant-table-thead > tr > th) {
      background-color: #fafafa;
      font-weight: 500;
    }

    :deep(.ant-table-row:hover) {
      td {
        background-color: #f0f7ff !important;
      }
    }
  }
}
</style>
