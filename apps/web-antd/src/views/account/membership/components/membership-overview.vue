<script setup>
import { computed, ref } from 'vue';
import { Button, Progress, Card, Avatar, Badge } from 'ant-design-vue';
import { MdiStar, MdiCrown, MdiGift, MdiTrendingUp, MdiWallet, MdiChevronRight } from '@vben/icons';

// 定义会员等级数据
const memberLevels = {
  bronze: {
    name: '青铜会员',
    level: 'bronze',
    color: '#CD7F32',
    icon: MdiStar,
    points: 0,
  },
  silver: {
    name: '白银会员',
    level: 'silver',
    color: '#C0C0C0',
    icon: MdiStar,
    points: 1000,
  },
  gold: {
    name: '黄金会员',
    level: 'gold',
    color: '#FFD700',
    icon: MdiCrown,
    points: 5000,
  },
};

// 模拟用户数据
const userData = ref({
  username: '张三',
  avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
  memberLevel: 'bronze',
  memberPoints: 650,
  memberExpireDate: '2024-12-31',
  totalSpent: 3200,
  monthlySpent: 850,
  memberBenefits: [
    { id: 1, name: '免费配送', active: true },
    { id: 2, name: '生日礼券', active: false },
    { id: 3, name: '专属客服', active: true },
  ],
  recentTransactions: [
    {
      id: 1,
      title: '会员充值',
      amount: 500,
      type: 'recharge',
      date: '2023-10-15',
    },
    {
      id: 2,
      title: '购买商品',
      amount: 299,
      type: 'purchase',
      date: '2023-10-12',
    },
    {
      id: 3,
      title: '服务预订',
      amount: 199,
      type: 'purchase',
      date: '2023-10-08',
    },
  ],
});

// 计算当前会员等级
const currentLevel = computed(() => memberLevels[userData.value.memberLevel] || memberLevels.bronze);

// 计算下一个会员等级
const nextLevel = computed(() => {
  const levels = Object.values(memberLevels).sort((a, b) => a.points - b.points);
  const currentIndex = levels.findIndex((level) => level.level === userData.value.memberLevel);
  return currentIndex < levels.length - 1 ? levels[currentIndex + 1] : null;
});

// 计算升级进度
const pointsToNextLevel = computed(() => {
  if (!nextLevel.value) return 0;
  return nextLevel.value.points - userData.value.memberPoints;
});

const upgradeProgress = computed(() => {
  if (!nextLevel.value) return 100;
  const currentLevelPoints = currentLevel.value.points;
  const nextLevelPoints = nextLevel.value.points;
  const currentPoints = userData.value.memberPoints;

  return Math.min(
    100,
    Math.max(
      0,
      Math.floor(((currentPoints - currentLevelPoints) / (nextLevelPoints - currentLevelPoints)) * 100)
    )
  );
});
</script>

<template>
  <div class="membership-overview">
    <div class="overview-layout">
      <!-- 会员信息卡片 (左侧) -->
      <div class="member-info-section">
        <Card :bordered="false" class="member-card">
          <div class="member-profile">
            <Avatar :size="80" :src="userData.avatar" class="user-avatar" />
            <Badge :count="currentLevel.name" :color="currentLevel.color" class="level-badge">
              <template #count>
                <div class="level-badge-content">
                  <component :is="currentLevel.icon" />
                  <span>{{ currentLevel.name }}</span>
                </div>
              </template>
            </Badge>
            <div class="profile-details">
              <h2>{{ userData.username }}</h2>
              <div class="expiry-date">有效期至: {{ userData.memberExpireDate }}</div>
            </div>
          </div>

          <div class="points-section">
            <div class="points-value">{{ userData.memberPoints }}</div>
            <div class="points-label">我的积分</div>
            <Button type="primary" size="small" ghost>积分明细</Button>
          </div>
        </Card>

        <!-- 数据统计卡片 -->
        <div class="stats-section">
          <Card :bordered="false" class="stats-card">
            <div class="stats-content">
              <div class="stats-item">
                <div class="stats-icon">
                  <MdiWallet />
                </div>
                <div>
                  <div class="stats-value">¥{{ userData.totalSpent.toFixed(2) }}</div>
                  <div class="stats-label">累计消费</div>
                </div>
              </div>
              <div class="stats-item">
                <div class="stats-icon trend-icon">
                  <MdiTrendingUp />
                </div>
                <div>
                  <div class="stats-value">¥{{ userData.monthlySpent.toFixed(2) }}</div>
                  <div class="stats-label">本月消费</div>
                </div>
              </div>
              <div class="stats-item">
                <div class="stats-icon gift-icon">
                  <MdiGift />
                </div>
                <div>
                  <div class="stats-value">{{ userData.memberBenefits.filter((b) => b.active).length }}</div>
                  <div class="stats-label">激活权益</div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      <!-- 升级进度与交易历史 (右侧) -->
      <div class="member-status-section">
        <!-- 升级进度卡片 -->
        <Card v-if="nextLevel" :bordered="false" class="upgrade-card">
          <div class="card-title">
            <h3>会员升级进度</h3>
          </div>

          <div class="upgrade-content">
            <div class="level-comparison">
              <div class="current-level">
                <div
                  class="level-icon"
                  :style="{ backgroundColor: currentLevel.color + '20', color: currentLevel.color }"
                >
                  <component :is="currentLevel.icon" />
                </div>
                <div class="level-name">{{ currentLevel.name }}</div>
              </div>

              <div class="progress-container">
                <Progress
                  :percent="upgradeProgress"
                  :stroke-color="nextLevel.color"
                  :show-info="false"
                  size="small"
                />
                <div class="progress-text">{{ upgradeProgress }}%</div>
              </div>

              <div class="next-level">
                <div
                  class="level-icon"
                  :style="{ backgroundColor: nextLevel.color + '20', color: nextLevel.color }"
                >
                  <component :is="nextLevel.icon" />
                </div>
                <div class="level-name">{{ nextLevel.name }}</div>
              </div>
            </div>

            <div class="upgrade-info">
              <div class="points-needed">
                距离{{ nextLevel.name }}还需 <span class="highlight">{{ pointsToNextLevel }}</span> 积分
              </div>
              <Button type="primary">立即升级</Button>
            </div>
          </div>
        </Card>

        <!-- 最近交易记录卡片 -->
        <Card :bordered="false" class="transactions-card">
          <div class="card-title">
            <h3>最近交易</h3>
            <Button type="link" size="small">
              查看全部
              <MdiChevronRight />
            </Button>
          </div>

          <div class="transactions-list">
            <div
              v-for="tx in userData.recentTransactions"
              :key="tx.id"
              class="transaction-item"
            >
              <div class="tx-info">
                <div class="tx-title">{{ tx.title }}</div>
                <div class="tx-date">{{ tx.date }}</div>
              </div>
              <div
                class="tx-amount"
                :class="tx.type"
              >
                {{ tx.type === 'recharge' ? '+' : '-' }}¥{{ tx.amount.toFixed(2) }}
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.membership-overview {
  .overview-layout {
    display: grid;
    grid-template-columns: minmax(300px, 1fr) 2fr;
    gap: 24px;

    @media (max-width: 992px) {
      grid-template-columns: 1fr;
    }
  }

  .member-info-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .member-card {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

    :deep(.ant-card-body) {
      padding: 24px;
    }

    .member-profile {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-bottom: 24px;
      margin-bottom: 24px;
      border-bottom: 1px solid #f0f0f0;

      .user-avatar {
        margin-bottom: 16px;
        border: 3px solid #fff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .level-badge {
        position: absolute;
        top: 0;
        right: 0;

        :deep(.ant-badge-count) {
          padding: 0 8px;
          height: 22px;
          border-radius: 11px;

          .level-badge-content {
            display: flex;
            align-items: center;
            height: 100%;

            svg {
              width: 14px;
              height: 14px;
              margin-right: 4px;
            }

            span {
              font-size: 12px;
              white-space: nowrap;
            }
          }
        }
      }

      .profile-details {
        text-align: center;

        h2 {
          font-size: 20px;
          margin-bottom: 4px;
        }

        .expiry-date {
          font-size: 13px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }

    .points-section {
      text-align: center;

      .points-value {
        font-size: 32px;
        font-weight: 600;
        color: var(--primary-color);
      }

      .points-label {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        margin-bottom: 16px;
      }
    }
  }

  .stats-section {
    .stats-card {
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

      :deep(.ant-card-body) {
        padding: 20px;
      }

      .stats-content {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .stats-item {
        display: flex;
        align-items: center;
        gap: 16px;

        .stats-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          background-color: rgba(var(--primary-color-rgb), 0.1);
          color: var(--primary-color);
          display: flex;
          align-items: center;
          justify-content: center;

          svg {
            width: 20px;
            height: 20px;
          }

          &.trend-icon {
            background-color: rgba(16, 185, 129, 0.1);
            color: #10B981;
          }

          &.gift-icon {
            background-color: rgba(245, 158, 11, 0.1);
            color: #F59E0B;
          }
        }

        .stats-value {
          font-size: 18px;
          font-weight: 600;
          line-height: 1.2;
        }

        .stats-label {
          font-size: 13px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
  }

  .member-status-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      font-size: 16px;
      margin: 0;
      font-weight: 600;
    }
  }

  .upgrade-card,
  .transactions-card {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

    :deep(.ant-card-body) {
      padding: 24px;
    }
  }

  .upgrade-content {
    .level-comparison {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24px;

      .current-level,
      .next-level {
        text-align: center;

        .level-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 8px;

          svg {
            width: 24px;
            height: 24px;
          }
        }

        .level-name {
          font-size: 14px;
          font-weight: 600;
        }
      }

      .progress-container {
        flex: 1;
        padding: 0 20px;
        text-align: center;

        .progress-text {
          margin-top: 6px;
          font-size: 13px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }

    .upgrade-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;

      .points-needed {
        font-size: 14px;

        .highlight {
          color: var(--primary-color);
          font-weight: 600;
        }
      }
    }
  }

  .transactions-list {
    .transaction-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
        padding-bottom: 0;
      }

      &:first-child {
        padding-top: 0;
      }

      .tx-info {
        .tx-title {
          font-size: 14px;
          margin-bottom: 4px;
        }

        .tx-date {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);
        }
      }

      .tx-amount {
        font-size: 16px;
        font-weight: 600;

        &.recharge {
          color: #10B981;
        }

        &.purchase {
          color: rgba(0, 0, 0, 0.85);
        }
      }
    }
  }

  @media (max-width: 576px) {
    .level-comparison {
      .progress-container {
        padding: 0 10px !important;
      }

      .current-level,
      .next-level {
        .level-icon {
          width: 40px !important;
          height: 40px !important;

          svg {
            width: 20px !important;
            height: 20px !important;
          }
        }
      }
    }

    .upgrade-info {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start !important;
    }
  }
}
</style>
