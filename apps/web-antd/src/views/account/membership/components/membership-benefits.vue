<script setup lang="ts">
import { ref } from 'vue';

import { Badge, Card, Tabs } from 'ant-design-vue';

const activeTab = ref('bronze');

const membershipBenefits = {
  bronze: {
    color: '#CD7F32',
    benefits: [
      { name: '免费配送', description: '所有订单免费配送', available: true },
      {
        name: '生日礼券',
        description: '生日当月获得专属优惠券',
        available: false,
      },
      { name: '会员折扣', description: '享受商品9.5折优惠', available: true },
      { name: '专属客服', description: '优先客服服务', available: false },
      { name: '积分加速', description: '消费积分1.0倍加速', available: true },
    ],
  },
  silver: {
    color: '#C0C0C0',
    benefits: [
      { name: '免费配送', description: '所有订单免费配送', available: true },
      {
        name: '生日礼券',
        description: '生日当月获得专属优惠券',
        available: true,
      },
      { name: '会员折扣', description: '享受商品9折优惠', available: true },
      { name: '专属客服', description: '优先客服服务', available: true },
      { name: '积分加速', description: '消费积分1.2倍加速', available: true },
    ],
  },
  gold: {
    color: '#FFD700',
    benefits: [
      { name: '免费配送', description: '所有订单免费配送', available: true },
      {
        name: '生日礼券',
        description: '生日当月获得专属优惠券',
        available: true,
      },
      { name: '会员折扣', description: '享受商品8.5折优惠', available: true },
      { name: '专属客服', description: '7×24小时专属客服', available: true },
      { name: '积分加速', description: '消费积分1.5倍加速', available: true },
      {
        name: '专属活动',
        description: '会员专享活动优先参与',
        available: true,
      },
      {
        name: '赠品特权',
        description: '每月免费获赠精美赠品',
        available: true,
      },
    ],
  },
};
</script>

<template>
  <div class="membership-benefits">
    <Tabs v-model:active-key="activeTab">
      <Tabs.TabPane key="bronze" tab="青铜会员">
        <div class="benefits-container">
          <Card
            v-for="(benefit, index) in membershipBenefits.bronze.benefits"
            :key="index"
            :bordered="false"
            class="benefit-card"
            :class="{ unavailable: !benefit.available }"
          >
            <div class="benefit-content">
              <div class="benefit-status">
                <Badge
                  :color="
                    benefit.available
                      ? membershipBenefits.bronze.color
                      : '#d9d9d9'
                  "
                />
              </div>
              <div class="benefit-info">
                <div class="benefit-title">{{ benefit.name }}</div>
                <div class="benefit-desc">{{ benefit.description }}</div>
              </div>
            </div>
          </Card>
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane key="silver" tab="白银会员">
        <div class="benefits-container">
          <Card
            v-for="(benefit, index) in membershipBenefits.silver.benefits"
            :key="index"
            :bordered="false"
            class="benefit-card"
            :class="{ unavailable: !benefit.available }"
          >
            <div class="benefit-content">
              <div class="benefit-status">
                <Badge
                  :color="
                    benefit.available
                      ? membershipBenefits.silver.color
                      : '#d9d9d9'
                  "
                />
              </div>
              <div class="benefit-info">
                <div class="benefit-title">{{ benefit.name }}</div>
                <div class="benefit-desc">{{ benefit.description }}</div>
              </div>
            </div>
          </Card>
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane key="gold" tab="黄金会员">
        <div class="benefits-container">
          <Card
            v-for="(benefit, index) in membershipBenefits.gold.benefits"
            :key="index"
            :bordered="false"
            class="benefit-card"
            :class="{ unavailable: !benefit.available }"
          >
            <div class="benefit-content">
              <div class="benefit-status">
                <Badge
                  :color="
                    benefit.available
                      ? membershipBenefits.gold.color
                      : '#d9d9d9'
                  "
                />
              </div>
              <div class="benefit-info">
                <div class="benefit-title">{{ benefit.name }}</div>
                <div class="benefit-desc">{{ benefit.description }}</div>
              </div>
            </div>
          </Card>
        </div>
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style lang="less" scoped>
.membership-benefits {
  .benefits-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
    margin-top: 16px;
  }

  .benefit-card {
    border-radius: 8px;
    background-color: #f9f9f9;
    transition: all 0.3s;

    &.unavailable {
      opacity: 0.6;
    }

    .benefit-content {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .benefit-status {
      :deep(.ant-badge-status-dot) {
        width: 12px;
        height: 12px;
      }
    }

    .benefit-info {
      .benefit-title {
        font-weight: 500;
        margin-bottom: 4px;
      }

      .benefit-desc {
        font-size: 13px;
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }

  :deep(.ant-tabs-tab) {
    padding: 12px 24px;
    font-size: 16px;
  }
}
</style>
