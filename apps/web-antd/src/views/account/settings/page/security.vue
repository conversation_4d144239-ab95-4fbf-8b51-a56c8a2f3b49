<script setup lang="ts">
import { ref } from 'vue';

import { Mdi<PERSON>ey } from '@vben/icons';

import { Button, message } from 'ant-design-vue';

import ChangePassword from '#/views/account/settings/components/change-password.vue';

// 控制修改密码弹窗的显示
const modalVisible = ref(false);

// 显示修改密码弹窗
const showModal = () => {
  modalVisible.value = true;
};

// 密码修改成功的处理函数
const handlePasswordSuccess = () => {
  message.success('密码修改成功');
};

// 密码修改失败的处理函数
const handlePasswordError = (errorMsg: string) => {
  console.error('密码修改失败:', errorMsg);
};

// 取消修改密码的处理函数
const handlePasswordCancel = () => {
  console.log('取消修改密码');
};
</script>

<template>
  <div class="security">
    <div class="mx-auto max-w-3xl">
      <!-- 登录密码 -->
      <div class="flex items-center justify-between rounded p-4">
        <div class="flex items-center space-x-3">
          <MdiKey class="text-xl" />
          <div>
            <div class="font-medium">登录密码</div>
            <div class="mt-1 text-sm text-gray-600">用于保护账号安全</div>
          </div>
        </div>
        <Button type="link" @click="showModal">修改</Button>
      </div>
    </div>

    <!-- 使用改造后的ChangePassword组件 -->
    <ChangePassword
      v-model="modalVisible"
      title="修改密码"
      @success="handlePasswordSuccess"
      @error="handlePasswordError"
      @cancel="handlePasswordCancel"
    />
  </div>
</template>

<style scoped>
.security {
  padding: 16px;
}
</style>
