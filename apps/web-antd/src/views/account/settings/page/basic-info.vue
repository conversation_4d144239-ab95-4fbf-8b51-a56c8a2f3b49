<script setup lang="ts">
import { ref } from 'vue';

import {
  AntAlipay,
  MdiAccount,
  MdiBank,
  MdiCellphone,
  MdiWechat,
} from '@vben/icons';
import { useUserStore } from '@vben/stores';

import { Button, Input, message, Modal, QRCode } from 'ant-design-vue';

import {
  bindPhoneNumber,
  checkNeedVerifyCode,
  checkNeedVerifyOldPhone,
  getUserInfoApi,
  sendVerifyCode,
  updateUserInfo,
  verifyCode,
  verifyOldPhone,
} from '#/api/core/user';
import { getDomainName } from '#/utils/domain';

const userStore = useUserStore();
const { userInfo } = userStore;
console.log(userInfo);

const formData = ref({
  username: userInfo?.userName || '',
  phone: userInfo?.userPhone || '',
  wechat: userInfo?.userOpenid || '',
  alipay: userInfo?.userAlipay || '',
  bankCard: userInfo?.userBankCard || '',
});

// 弹窗相关
const modalVisible = ref(false);
const modalType = ref<'alipay' | 'bankCard' | 'phone' | 'username' | 'wechat'>(
  'username',
);
const modalValue = ref('');

// 验证码相关
const verifyModalVisible = ref(false);
const verifyCodeValue = ref('');
const countdown = ref(0);

// 手机号绑定相关
const isChangingPhone = ref(false); // 是否是换绑流程
const newPhoneNumber = ref(''); // 新手机号
const newPhoneVerifyCode = ref(''); // 新手机号验证码
const newPhoneCountdown = ref(0); // 新手机号倒计时

// 微信二维码相关
const qrCodeVisible = ref(false);
const qrCodeUrl = ref('');

// 清除弹窗数据
const clearModalData = () => {
  modalValue.value = '';
  verifyCodeValue.value = '';
  countdown.value = 0;
  newPhoneNumber.value = '';
  newPhoneVerifyCode.value = '';
  newPhoneCountdown.value = 0;
  isChangingPhone.value = false;
  qrCodeUrl.value = '';
};

// 监听弹窗关闭
const handleModalClose = () => {
  clearModalData();
};

// 监听验证码弹窗关闭
const handleVerifyModalClose = () => {
  clearModalData();
};

// 显示编辑弹窗
const showEditModal = () => {
  modalVisible.value = true;
  modalValue.value = formData.value[modalType.value];
};

// 发送验证码
const handleSendCode = async () => {
  if (countdown.value > 0) return;
  if (!userInfo?.userPhone) {
    message.error('请先绑定手机号');
    return;
  }

  try {
    const res = await sendVerifyCode({
      phone: userInfo.userPhone,
    });
    if (res.data?.code === 1) {
      message.success('验证码发送成功');
      countdown.value = 60;
      const timer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    } else {
      message.error(res.data?.msg || '验证码发送失败');
    }
  } catch {
    message.error('验证码发送失败');
  }
};

// 显示新手机号绑定弹窗
const showNewPhoneModal = () => {
  modalVisible.value = true;
  modalValue.value = '';
};

// 发送新手机号验证码
const handleSendNewPhoneCode = async () => {
  if (newPhoneCountdown.value > 0) return;
  if (!newPhoneNumber.value) {
    message.error('请输入手机号');
    return;
  }

  try {
    const res = await sendVerifyCode({
      phone: newPhoneNumber.value,
    });
    if (res.data?.code === 1) {
      message.success('验证码发送成功');
      newPhoneCountdown.value = 60;
      const timer = setInterval(() => {
        newPhoneCountdown.value--;
        if (newPhoneCountdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    } else {
      message.error(res.data?.msg || '验证码发送失败');
    }
  } catch {
    message.error('验证码发送失败');
  }
};

// 生成微信绑定二维码URL
const generateWechatQrCodeUrl = () => {
  const domain = getDomainName();
  return `${domain}/java/pay/editUserOpenId?state=1`;
};

// 修改显示弹窗逻辑
const showModal = async (
  type: 'alipay' | 'bankCard' | 'phone' | 'username' | 'wechat',
) => {
  modalType.value = type;

  // 用户名、支付宝、银行卡和微信需要验证
  if (['alipay', 'bankCard', 'username', 'wechat'].includes(type)) {
    try {
      const res = await checkNeedVerifyCode();
      console.log(res, 'mmm');
      if (res.code === 1) {
        if (type === 'wechat') {
          // 微信绑定显示二维码
          qrCodeUrl.value = generateWechatQrCodeUrl();
          qrCodeVisible.value = true;
        } else {
          // 其他字段显示编辑弹窗
          showEditModal();
        }
      } else {
        // 需要验证，显示验证码弹窗并发送验证码
        if (!userInfo?.userPhone) {
          message.error('请先绑定手机号');
          return;
        }
        verifyModalVisible.value = true;
        // 自动发送第一次验证码
        // handleSendCode();
      }
    } catch {
      message.error('验证检查失败');
    }
  } else if (type === 'phone') {
    try {
      if (formData.value.phone) {
        // 换绑流程
        isChangingPhone.value = true;
        const res = await checkNeedVerifyOldPhone();
        if (res.code === 1) {
          // 已验证过旧手机号，直接进入新手机号绑定
          showNewPhoneModal();
        } else {
          // 需要验证旧手机号
          verifyModalVisible.value = true;
          // handleSendCode();
        }
      } else {
        // 首次绑定流程
        isChangingPhone.value = false;
        showNewPhoneModal();
      }
    } catch {
      message.error('验证检查失败');
    }
  }
};

// 验证码确认逻辑修改
const handleVerifySubmit = async () => {
  if (!userInfo?.userPhone) {
    message.error('请先绑定手机号');
    return;
  }

  try {
    if (modalType.value === 'phone' && isChangingPhone.value) {
      // 换绑手机号的旧手机号验证
      const res = await verifyOldPhone({
        oldPhone: userInfo.userPhone,
        code: verifyCodeValue.value,
      });
      if (res.code === 1) {
        message.success('验证成功');
        verifyModalVisible.value = false;
        clearModalData();
        showNewPhoneModal();
      } else {
        message.error(res.msg || '验证失败');
      }
    } else {
      const res = await verifyCode({
        oldPhone: userInfo.userPhone,
        code: verifyCodeValue.value,
      });
      if (res.data?.code === 1) {
        message.success('验证成功');
        verifyModalVisible.value = false;
        clearModalData();
        if (modalType.value === 'wechat') {
          // 微信绑定显示二维码
          qrCodeUrl.value = generateWechatQrCodeUrl();
          qrCodeVisible.value = true;
        } else {
          // 其他字段显示编辑弹窗
          showEditModal();
        }
      } else {
        message.error(res.data?.msg || '验证失败');
      }
    }
  } catch {
    message.error('验证失败');
  }
};

// 提交修改
const handleSubmit = async () => {
  try {
    if (modalType.value === 'phone') {
      // 手机号绑定/换绑
      await bindPhoneNumber({
        type: isChangingPhone.value ? '2' : '1',
        phone: modalValue.value,
        code: newPhoneVerifyCode.value,
      });
      formData.value.phone = modalValue.value;
      clearPhoneData();
    } else {
      // 其他字段修改逻辑
      const params: Record<string, string> = {};
      switch (modalType.value) {
        case 'alipay': {
          params.userAlipay = modalValue.value;
          break;
        }
        case 'bankCard': {
          params.userBankCard = modalValue.value;
          break;
        }
        case 'username': {
          params.userName = modalValue.value;
          break;
        }
      }
      await updateUserInfo(params);
      formData.value[modalType.value] = modalValue.value;
    }

    message.success('保存成功');
    modalVisible.value = false;
    clearModalData();
    const userInfo = await getUserInfoApi();
    userStore.setUserInfo(userInfo);
  } catch {
    message.error('保存失败');
  }
};
</script>

<template>
  <div class="basic-info">
    <div class="mx-auto max-w-3xl space-y-4">
      <!-- 用户姓名 -->
      <div class="flex items-center justify-between rounded p-4">
        <div class="flex items-center space-x-3">
          <MdiAccount class="text-xl" />
          <div>
            <div class="font-medium">用户姓名</div>
            <div class="mt-1 text-sm text-gray-600">
              {{ formData.username || '未设置' }}
            </div>
          </div>
        </div>
        <Button type="link" @click="showModal('username')">
          {{ formData.username ? '修改' : '设置' }}
        </Button>
      </div>

      <!-- 手机号码 -->
      <div class="flex items-center justify-between rounded p-4">
        <div class="flex items-center space-x-3">
          <MdiCellphone class="text-xl" />
          <div>
            <div class="font-medium">手机号码</div>
            <div class="mt-1 text-sm text-gray-600">
              {{ formData.phone ? `已绑定：${formData.phone}` : '未绑定' }}
            </div>
          </div>
        </div>
        <Button type="link" @click="() => showModal('phone')">
          {{ formData.phone ? '修改' : '绑定' }}
        </Button>
      </div>

      <!-- 微信账号 -->
      <div class="flex items-center justify-between rounded p-4">
        <div class="flex items-center space-x-3">
          <MdiWechat class="text-xl" />
          <div>
            <div class="font-medium">微信账号</div>
            <div class="mt-1 text-sm text-gray-600">
              {{ formData.wechat ? `已绑定：${formData.wechat}` : '未绑定' }}
            </div>
          </div>
        </div>
        <Button type="link" @click="() => showModal('wechat')">
          {{ formData.wechat ? '修改' : '绑定' }}
        </Button>
      </div>

      <!-- 支付宝账号 -->
      <div class="flex items-center justify-between rounded p-4">
        <div class="flex items-center space-x-3">
          <AntAlipay class="text-xl" />
          <div>
            <div class="font-medium">支付宝账号</div>
            <div class="mt-1 text-sm text-gray-600">
              {{ formData.alipay ? `已绑定：${formData.alipay}` : '未绑定' }}
            </div>
          </div>
        </div>
        <Button type="link" @click="() => showModal('alipay')">
          {{ formData.alipay ? '修改' : '绑定' }}
        </Button>
      </div>

      <!-- 银行卡号 -->
      <div class="flex items-center justify-between rounded p-4">
        <div class="flex items-center space-x-3">
          <MdiBank class="text-xl" />
          <div>
            <div class="font-medium">银行卡号</div>
            <div class="mt-1 text-sm text-gray-600">
              {{
                formData.bankCard ? `已绑定：${formData.bankCard}` : '未绑定'
              }}
            </div>
          </div>
        </div>
        <Button type="link" @click="() => showModal('bankCard')">
          {{ formData.bankCard ? '修改' : '绑定' }}
        </Button>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <Modal
      v-model:visible="modalVisible"
      :title="
        modalType === 'username'
          ? '设置用户姓名'
          : modalType === 'phone'
            ? isChangingPhone
              ? '换绑手机号'
              : '绑定手机号'
            : modalType === 'wechat'
              ? '绑定微信'
              : modalType === 'alipay'
                ? '绑定支付宝'
                : '绑定银行卡'
      "
      @ok="handleSubmit"
      @cancel="handleModalClose"
      :mask-closable="false"
    >
      <template v-if="modalType === 'phone'">
        <div class="space-y-4">
          <div>
            <div class="mb-1.5 text-sm text-gray-600">手机号</div>
            <Input v-model:value="modalValue" placeholder="请输入手机号" />
          </div>
          <div>
            <div class="mb-1.5 text-sm text-gray-600">验证码</div>
            <div class="flex items-center space-x-2">
              <Input
                v-model:value="newPhoneVerifyCode"
                placeholder="请输入验证码"
                class="flex-1"
              />
              <Button
                type="primary"
                :disabled="newPhoneCountdown > 0"
                @click="handleSendNewPhoneCode"
                class="min-w-[100px]"
              >
                {{
                  newPhoneCountdown > 0 ? `${newPhoneCountdown}s` : '获取验证码'
                }}
              </Button>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div>
          <div class="mb-1.5 text-sm text-gray-600">
            {{
              modalType === 'username'
                ? '用户姓名'
                : modalType === 'wechat'
                  ? '微信账号'
                  : modalType === 'alipay'
                    ? '支付宝账号'
                    : '银行卡号'
            }}
          </div>
          <Input
            v-model:value="modalValue"
            :placeholder="
              modalType === 'username'
                ? '请输入用户姓名'
                : modalType === 'wechat'
                  ? '请输入微信账号'
                  : modalType === 'alipay'
                    ? '请输入支付宝账号'
                    : '请输入银行卡号'
            "
          />
        </div>
      </template>
    </Modal>

    <!-- 验证码弹窗 -->
    <Modal
      v-model:visible="verifyModalVisible"
      title="安全验证"
      @ok="handleVerifySubmit"
      @cancel="handleVerifyModalClose"
      :mask-closable="false"
      :ok-button-props="{ disabled: !verifyCodeValue }"
    >
      <div class="space-y-4">
        <div class="text-sm text-gray-600">
          为了保证您的账户安全，请完成安全验证
        </div>
        <div>
          <div class="mb-1.5 text-sm text-gray-600">验证码</div>
          <div class="flex items-center space-x-2">
            <Input
              v-model:value="verifyCodeValue"
              placeholder="请输入验证码"
              class="flex-1"
            />
            <Button
              type="primary"
              :disabled="countdown > 0"
              @click="handleSendCode"
              class="min-w-[100px]"
            >
              {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
            </Button>
          </div>
          <div class="mt-1.5 text-xs text-gray-400">
            验证码将发送至绑定手机 {{ userInfo?.userPhone }}
          </div>
        </div>
      </div>
    </Modal>

    <!-- 微信二维码弹窗 -->
    <Modal
      v-model:visible="qrCodeVisible"
      title="微信绑定"
      :footer="null"
      @cancel="clearModalData"
      :mask-closable="false"
    >
      <div class="flex flex-col items-center space-y-4">
        <div class="text-sm text-gray-600">请使用微信扫描二维码完成绑定</div>
        <QRCode :value="qrCodeUrl" :size="200" />
      </div>
    </Modal>
  </div>
</template>

<style scoped>
.basic-info {
  padding: 16px;
}
</style>
