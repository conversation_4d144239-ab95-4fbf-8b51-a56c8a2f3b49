<script setup lang="ts">
import { computed, ref } from 'vue';

import { Form, Input, message, Modal } from 'ant-design-vue';

import { changePassword } from '#/api/core/user';

// 定义组件的props
const props = defineProps({
  // 支持v-model绑定
  modelValue: {
    type: Boolean,
    default: false,
  },
  // 弹窗标题
  title: {
    type: String,
    default: '修改密码',
  },
  // 确认按钮文本
  okText: {
    type: String,
    default: '确认',
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: '取消',
  },
  // 是否可以点击蒙层关闭
  maskClosable: {
    type: Boolean,
    default: false,
  },
});

// 定义组件事件
const emit = defineEmits(['update:modelValue', 'success', 'error', 'cancel']);

// 计算属性，用于v-model绑定
const modalVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

// 表单数据
const formData = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
});

// 验证确认密码
const validateConfirmPassword = async (_rule: any, value: string) => {
  if (value !== formData.value.newPassword) {
    throw '两次输入的密码不一致';
  }
};

// 清除表单数据
const clearFormData = () => {
  formData.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  };
};

// 处理弹窗关闭
const handleModalClose = () => {
  clearFormData();
  emit('cancel');
};

// 提交修改密码
const handleSubmit = async () => {
  try {
    // 验证新密码和确认密码是否一致
    if (formData.value.newPassword !== formData.value.confirmPassword) {
      message.error('两次输入的密码不一致');
      return;
    }

    const res = await changePassword({
      original: formData.value.oldPassword,
      new: formData.value.newPassword,
    });

    console.log('res', res);

    if (res?.code === 1) {
      modalVisible.value = false;
      clearFormData();
      emit('success');
    } else {
      message.error(res?.msg || '密码修改失败');
      emit('error', res?.msg || '密码修改失败');
    }
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '密码修改失败';
    message.error(errorMsg);
    emit('error', errorMsg);
  }
};
</script>

<template>
  <Modal
    v-model:visible="modalVisible"
    :title="title"
    :ok-text="okText"
    :cancel-text="cancelText"
    :mask-closable="maskClosable"
    @ok="handleSubmit"
    @cancel="handleModalClose"
  >
    <Form :model="formData" layout="vertical">
      <Form.Item
        label="当前密码"
        name="oldPassword"
        :rules="[{ required: true, message: '请输入当前密码' }]"
      >
        <Input.Password
          v-model:value="formData.oldPassword"
          placeholder="请输入当前密码"
        />
      </Form.Item>

      <Form.Item
        label="新密码"
        name="newPassword"
        :rules="[{ required: true, message: '请输入新密码' }]"
      >
        <Input.Password
          v-model:value="formData.newPassword"
          placeholder="请输入新密码"
        />
      </Form.Item>

      <Form.Item
        label="确认新密码"
        name="confirmPassword"
        :rules="[
          { required: true, message: '请确认新密码' },
          { validator: validateConfirmPassword },
        ]"
      >
        <Input.Password
          v-model:value="formData.confirmPassword"
          placeholder="请再次输入新密码"
        />
      </Form.Item>
    </Form>
  </Modal>
</template>
