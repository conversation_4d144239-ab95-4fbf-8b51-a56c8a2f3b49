<script setup lang="ts">
import { ref } from 'vue';

import { Card, Tabs } from 'ant-design-vue';

import BasicInfo from './page/basic-info.vue';
import Security from './page/security.vue';

const activeKey = ref('basic');
</script>

<template>
  <div class="account-center p-2">
    <Card>
      <Tabs v-model:active-key="activeKey">
        <Tabs.TabPane key="basic" tab="基本信息">
          <BasicInfo />
        </Tabs.TabPane>
        <Tabs.TabPane key="security" tab="账户安全">
          <Security />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<style lang="less" scoped>
.account-center {
  background-color: var(--background-deep);
  min-height: 100%;
}
</style>
