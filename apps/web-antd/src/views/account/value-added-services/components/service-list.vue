<script setup lang="ts">
import { h, onMounted, ref } from 'vue';

import {
  MdiChartBar,
  MdiCryptocurrencyPay,
  MdiHeadset,
  MdiMessageText,
  MdiRefresh,
  MdiRemote,
  MdiShieldCheck,
  MdiWallet,
} from '@vben/icons';

import {
  Button,
  Card,
  Empty,
  message,
  Spin,
  Tag,
  Tooltip,
  Typography,
} from 'ant-design-vue';

import {
  getServiceCategoryListApi,
  getTerminalSystemuserApi,
} from '#/api/core/system';
import {
  BalanceBuyModal,
  CollectionBalanceBuyModal,
  SmsBuyModal,
} from '#/components';

const { Title } = Typography;

interface Service {
  id: string;
  name: string;
  description: string;
  price: string;
  unit: string;
  status: string;
  type: string;
  icon: string;
  popular?: boolean;
  features?: string[];
  bgColor?: string;
}

const smsModalVisible = ref(false);
const balanceModalVisible = ref(false);
const collectionBalanceModalVisible = ref(false);
const handleBuySuccess = () => {
  message.success('购买成功');
};

// 服务数据直接定义为数组
const services = ref<Service[]>([
  {
    id: '1',
    name: '短信服务',
    description: '提供短信通知、验证码发送等短信发送服务',
    price: '0.099',
    unit: '条',
    status: 'available',
    type: 'sms',
    icon: 'message-text',
    popular: false,
    // bgColor: '#36cfc9',
    features: ['短信通知', '验证码发送', '营销推广'],
  },
  {
    id: '2',
    name: '余额充值',
    description: '提供余额充值服务',
    price: '100',
    unit: '元',
    status: 'available',
    type: 'balance',
    icon: 'balance',
    popular: false,
    // bgColor: '#597ef7',
    features: ['短信购买'],
  },
  {
    id: '3',
    name: '收款额度',
    description: '提供收款额度购买服务',
    price: '1',
    unit: '元',
    status: 'available',
    type: 'cryptocurrency',
    icon: 'cryptocurrency',
    popular: true,
    // bgColor: '#597ef7',
    features: ['收款额度购买'],
  },
  // {
  //   id: '3',
  //   name: '高级数据分析',
  //   description: '提供详细的设备数据分析和报表功能',
  //   price: '199',
  //   unit: '月',
  //   status: 'available',
  //   type: 'service',
  //   icon: 'chart-bar',
  //   bgColor: '#2f54eb',
  //   features: ['设备使用分析', '趋势预测', '自定义报表', '数据导出'],
  // },
  // {
  //   id: '4',
  //   name: '设备远程控制',
  //   description: '允许远程控制和管理所有联网设备',
  //   price: '299',
  //   unit: '月',
  //   status: 'available',
  //   type: 'service',
  //   icon: 'remote',
  //   bgColor: '#13c2c2',
  //   features: ['远程开关机', '参数调整', '批量控制', '定时任务'],
  // },
  // {
  //   id: '5',
  //   name: '安全监控服务',
  //   description: '24小时安全监控和异常警报',
  //   price: '399',
  //   unit: '月',
  //   status: 'available',
  //   type: 'service',
  //   icon: 'shield-check',
  //   popular: true,
  //   bgColor: '#722ed1',
  //   features: ['24小时监控', '异常警报', '安全审计', '入侵检测'],
  // },
]);

const loading = ref(false);
const balance = ref(0);
const collectionBalance = ref(0);

// 获取其他服务列表
const fetchOtherServices = async () => {
  try {
    loading.value = true;
    const { data } = await getServiceCategoryListApi();
    if (data && data.length > 0) {
      // 获取已有服务的ID列表，避免重复添加
      const existingIds = services.value.map((s) => s.name.toLowerCase());

      // 将API返回的数据映射到Service接口格式并添加到services中
      const apiServices = data
        .map((item) => {
          const title = item.title.toLowerCase();

          // 跳过已有的默认服务
          if (
            title.includes('短信') ||
            title.includes('余额充值') ||
            title.includes('收款额度')
          ) {
            return null;
          }

          // 根据服务标题或描述决定服务类型和图标
          let type = 'service';
          let icon = 'service';
          let unit = '次';
          let popular = false;

          const desc = item.desc.toLowerCase();

          if (title.includes('分析') || desc.includes('分析')) {
            type = 'analysis';
            icon = 'chart-bar';
            unit = '月';
          } else if (title.includes('控制') || desc.includes('控制')) {
            type = 'control';
            icon = 'remote';
            unit = '月';
          } else if (title.includes('监控') || desc.includes('监控')) {
            type = 'monitor';
            icon = 'shield-check';
            unit = '月';
            popular = true;
          } else if (title.includes('技术') || desc.includes('技术支持')) {
            popular = true;
          }

          // 从描述中提取功能列表
          const features = item.desc
            .split(/[、,，]/)
            .filter(Boolean)
            .map((f) => f.trim());

          return {
            id: String(item.id),
            name: item.title,
            description: item.desc,
            price: String(item.price),
            unit,
            status: 'available',
            type,
            icon,
            popular,
            features: features.length > 0 ? features : ['增值服务'],
          };
        })
        .filter(Boolean); // 过滤掉null值

      // 将有效的API服务添加到列表中
      services.value = [...services.value, ...apiServices];
    }
  } catch (error) {
    console.error('获取其他服务列表失败:', error);
    message.error('获取其他服务列表失败');
  } finally {
    loading.value = false;
  }
};

const fetchBalanceInfo = async () => {
  try {
    loading.value = true;
    const { data } = await getTerminalSystemuserApi();
    balance.value = data.balance;
    collectionBalance.value = data.collectionBalance;
  } catch {
    message.error('获取余额信息失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchOtherServices();
  fetchBalanceInfo();
});

// 获取图标组件
const getIconComponent = (iconName: string) => {
  const iconMap: Record<string, any> = {
    'message-text': MdiMessageText,
    balance: MdiWallet,
    'chart-bar': MdiChartBar,
    remote: MdiRemote,
    'shield-check': MdiShieldCheck,
    headset: MdiHeadset,
    cryptocurrency: MdiCryptocurrencyPay,
    service: MdiHeadset, // 技术支持图标
  };

  const IconComponent = iconMap[iconName];
  if (!IconComponent) return null;

  return h(IconComponent, {
    style: {
      // color: 'white',
      fontSize: '28px',
    },
  });
};

// 显示订购确认弹窗
const showOrderModal = (service: Service) => {
  switch (service.type) {
    case 'balance': {
      balanceModalVisible.value = true;
      break;
    }
    case 'cryptocurrency': {
      collectionBalanceModalVisible.value = true;
      break;
    }
    case 'sms': {
      smsModalVisible.value = true;
      break;
    }
    default: {
      break;
    }
  }
};
</script>

<template>
  <div class="service-list">
    <Spin :spinning="loading" tip="加载中...">
      <div v-if="services.length > 0">
        <!-- 特色服务区域 -->
        <div class="featured-services">
          <Title :level="5" class="section-title">特色服务</Title>

          <div class="service-grid">
            <Card
              :bordered="true"
              v-for="service in services.filter(
                (s) =>
                  s.type === 'sms' ||
                  s.type === 'balance' ||
                  s.type === 'cryptocurrency',
              )"
              :key="service.id"
              class="service-card"
              :class="{ 'popular-service': service.popular }"
              hoverable
            >
              <div v-if="service.popular" class="popular-tag">热门</div>
              <div class="service-card-inner">
                <div class="service-card-header">
                  <div
                    class="service-icon"
                    :style="{ background: service.bgColor }"
                  >
                    <component :is="getIconComponent(service.icon)" />
                  </div>
                  <h3 class="service-title">{{ service.name }}</h3>
                </div>

                <p class="service-description">{{ service.description }}</p>

                <div class="service-features">
                  <Tag
                    v-for="(feature, index) in service.features"
                    :key="index"
                  >
                    {{ feature }}
                  </Tag>
                </div>

                <div class="service-footer">
                  <div class="service-price-info">
                    <div class="service-price-tag">
                      <span class="price-value">¥{{ service.price }}</span>
                      <span class="price-unit">/{{ service.unit }}</span>
                    </div>
                    <div
                      v-if="
                        service.type === 'balance' ||
                        service.type === 'cryptocurrency'
                      "
                      class="balance-wrapper"
                    >
                      <Tooltip
                        :title="
                          service.type === 'balance'
                            ? `当前余额：${balance}元`
                            : `当前收款额度：${collectionBalance}元`
                        "
                      >
                        <div class="balance-info">
                          当前：{{
                            service.type === 'balance'
                              ? balance
                              : collectionBalance
                          }}元
                        </div>
                      </Tooltip>
                      <Button
                        type="link"
                        class="refresh-button"
                        @click.stop="fetchBalanceInfo"
                      >
                        <MdiRefresh />
                      </Button>
                    </div>
                  </div>

                  <div class="service-action">
                    <Button type="primary" @click="showOrderModal(service)">
                      {{ service.type === 'sms' ? '购买套餐' : '充值' }}
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        <!-- 其他服务区域 -->
        <div class="other-services-section">
          <Title :level="5" class="section-title">其他增值服务</Title>

          <div class="service-grid">
            <Card
              v-for="service in services.filter((s) => s.type === 'service')"
              :key="service.id"
              class="service-card"
              :class="{ 'popular-service': service.popular }"
              bordered
              hoverable
            >
              <div v-if="service.popular" class="popular-tag">热门</div>
              <div class="service-card-inner">
                <div class="service-card-header">
                  <div
                    class="service-icon"
                    :style="{ background: service.bgColor }"
                  >
                    <component :is="getIconComponent(service.icon)" />
                  </div>
                  <h3 class="service-title">{{ service.name }}</h3>
                </div>

                <p class="service-description">{{ service.description }}</p>

                <div class="service-features">
                  <Tag
                    v-for="(feature, index) in service.features"
                    :key="index"
                  >
                    {{ feature }}
                  </Tag>
                </div>

                <div class="service-footer">
                  <div class="service-price-tag">
                    <span class="price-value">¥{{ service.price }}</span>
                    <span class="price-unit">/{{ service.unit }}</span>
                  </div>

                  <div class="service-action">
                    <Button type="primary" @click="showOrderModal(service)">
                      订购服务
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>

      <Empty
        v-else-if="!loading"
        description="暂无可用服务"
        class="empty-container"
      />
    </Spin>

    <SmsBuyModal
      v-model:visible="smsModalVisible"
      @success="handleBuySuccess"
    />
    <BalanceBuyModal
      v-model:visible="balanceModalVisible"
      @success="handleBuySuccess"
    />

    <CollectionBalanceBuyModal
      v-model:visible="collectionBalanceModalVisible"
      @success="handleBuySuccess"
    />
  </div>
</template>

<style lang="less" scoped>
.service-list {
  // padding: 8px 0;
}

.section-title {
  position: relative;
  margin: 10px 0 24px;
  padding-left: 12px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background: hsl(var(--primary));
    border-radius: 2px;
  }
}

.featured-services {
  margin-bottom: 16px;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.service-card {
  height: 100%;
  position: relative;
  transition: all 0.3s;
  // border-radius: 8px;
  overflow: hidden;
  // background-color: var(--background-deep);

  &.popular-service {
    //border: 1px solid hsl(var(--primary));
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  }

  .popular-tag {
    position: absolute;
    top: 0;
    right: 24px;
    background-color: #f5222d;
    color: white;
    padding: 2px 10px;
    font-size: 12px;
    border-radius: 0 0 8px 8px;
    z-index: 1;
  }

  &-inner {
    display: flex;
    flex-direction: column;
    height: 100%;
    // padding: 24px 16px;
  }

  .service-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    position: relative;

    .service-icon {
      width: 48px;
      height: 48px;
      margin-right: 16px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px;
    }
  }

  .service-title {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
    flex: 1;
  }

  .service-description {
    // color: rgba(0, 0, 0, 0.65);
    margin-bottom: 16px;
    flex: 1;
  }

  .service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 24px;
  }

  .service-footer {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: auto;

    .service-price-info {
      .service-price-tag {
        .price-value {
          font-size: 22px;
          font-weight: 500;
          color: #f5222d;
        }

        .price-unit {
          font-size: 14px;
        }
      }

      .balance-wrapper {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-top: 4px;
      }

      .balance-info {
        //font-size: 13px;
        // color: rgba(0, 0, 0, 0.45);
        color: #f5222d;
      }

      .refresh-button {
        padding: 0;
        height: auto;
        color: rgba(0, 0, 0, 0.45);

        &:hover {
          color: hsl(var(--primary));
        }
      }
    }
  }
}

.empty-container {
  padding: 40px 0;
}
</style>
