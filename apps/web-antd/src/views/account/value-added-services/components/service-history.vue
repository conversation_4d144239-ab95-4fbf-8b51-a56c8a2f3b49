<script setup lang="ts">
import type { TableColumnsType, TablePaginationConfig } from 'ant-design-vue';

import { h, onMounted, ref } from 'vue';

import { Empty, Table, Tag } from 'ant-design-vue';

interface ServiceRecord {
  id: string;
  serviceName: string;
  orderTime: string;
  expireTime: null | string;
  price: string;
  status: 'active' | 'completed' | 'expired';
}

// 模拟API调用，实际项目中应替换为真实API
const getServiceHistory = async (): Promise<ServiceRecord[]> => {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  return [
    {
      id: '1001',
      serviceName: '高级数据分析',
      orderTime: '2023-10-15 14:30:25',
      expireTime: '2023-11-15 14:30:25',
      price: '199',
      status: 'active',
    },
    {
      id: '1002',
      serviceName: '设备远程控制',
      orderTime: '2023-09-22 09:15:10',
      expireTime: '2023-10-22 09:15:10',
      price: '299',
      status: 'expired',
    },
    {
      id: '1003',
      serviceName: '专家技术支持',
      orderTime: '2023-10-05 16:45:30',
      expireTime: null,
      price: '599',
      status: 'completed',
    },
  ];
};

interface StatusMap {
  active: { color: string; text: string };
  expired: { color: string; text: string };
  completed: { color: string; text: string };
  [key: string]: { color: string; text: string };
}

const columns: TableColumnsType = [
  {
    title: '服务名称',
    dataIndex: 'serviceName',
    key: 'serviceName',
    width: '20%',
  },
  {
    title: '订购时间',
    dataIndex: 'orderTime',
    key: 'orderTime',
    width: '20%',
  },
  {
    title: '到期时间',
    dataIndex: 'expireTime',
    key: 'expireTime',
    width: '20%',
    customRender: ({ text }: { text: null | string }) => {
      return text || '一次性服务';
    },
  },
  {
    title: '价格',
    dataIndex: 'price',
    key: 'price',
    width: '15%',
    customRender: ({ text }: { text: string }) => {
      return `${text}元`;
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '15%',
    align: 'center' as const,
    customRender: ({ text }: { text: string }) => {
      const statusMap: StatusMap = {
        active: { color: 'success', text: '使用中' },
        expired: { color: 'error', text: '已过期' },
        completed: { color: 'processing', text: '已完成' },
      };

      const status = statusMap[text] || { color: 'default', text: '未知状态' };

      return h(Tag, { color: status.color }, () => status.text);
    },
  },
];

const historyData = ref<ServiceRecord[]>([]);
const loading = ref(false);
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 加载服务历史记录
const loadHistory = async () => {
  loading.value = true;
  try {
    const data = await getServiceHistory();
    historyData.value = data;
    pagination.value.total = data.length;
  } catch (error) {
    console.error('Failed to load service history:', error);
  } finally {
    loading.value = false;
  }
};

// 处理分页变化
const handleTableChange = (pag: TablePaginationConfig) => {
  if (pag.current) {
    pagination.value.current = pag.current;
  }
  if (pag.pageSize) {
    pagination.value.pageSize = pag.pageSize;
  }
  loadHistory();
};

onMounted(() => {
  loadHistory();
});

// 创建空状态内容
const customizeRenderEmpty = () => {
  return h(Empty, { description: '暂无订购记录' });
};
</script>

<template>
  <div class="service-history">
    <Table
      :columns="columns"
      :data-source="historyData"
      :loading="loading"
      :pagination="pagination"
      row-key="id"
      @change="handleTableChange"
      :bordered="false"
      size="middle"
      :locale="{ emptyText: customizeRenderEmpty }"
    />
  </div>
</template>

<style lang="less" scoped>
.service-history {
  padding: 8px 0;

  :deep(.ant-table) {
    background-color: white;
    border-radius: 8px;

    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 500;
    }

    .ant-table-row:hover > td {
      background-color: #f5f5f5;
    }

    .ant-pagination {
      margin: 16px 0 8px;
    }
  }
}
</style>
