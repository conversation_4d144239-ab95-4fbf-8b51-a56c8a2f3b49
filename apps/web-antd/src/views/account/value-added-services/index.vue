<script setup lang="ts">
import { ref } from 'vue';

import { AnalysisChartsTabs } from '@vben/common-ui';

import ServiceList from './components/service-list.vue';

const activeKey = ref('list');

const chartTabs: [] = [
  {
    label: '增值服务列表',
    value: 'list',
  },
  // {
  //   label: '服务订购记录',
  //   value: 'history',
  // },
];
</script>

<template>
  <div class="value-added-services p-2">
    <!-- <Card> -->
    <AnalysisChartsTabs :tabs="chartTabs">
      <template #list>
        <ServiceList />
      </template>
      <!-- <template #history>
        <ServiceHistory />
      </template> -->
    </AnalysisChartsTabs>
    <!-- </Card> -->
  </div>
</template>

<style lang="less" scoped>
.value-added-services {
  background-color: var(--background-deep);
  min-height: 100%;
  // padding: 24px;

  .content-card {
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

    .service-tabs {
      .ant-tabs-nav {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
