import type { WechatMiniapp } from '#/api/core/wechat';

// hooks/useWechatMiniapp.ts
import { computed } from 'vue';

import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  addWechatMiniapp,
  deleteWechatMiniapp,
  exportWechatMiniapp,
  getWechatMiniappList,
  updateWechatMiniapp,
} from '#/api/core/wechat';
import { useAnsheng, useCustomDetail } from '#/hooks/useAnsheng';
import { objectToFormData } from '#/utils/common';
import { handleFileDownload } from '#/utils/export';

import { columns, searchItems, statusOptions } from '../config';

export function useWechatMiniapp() {
  // 使用useAnsheng管理表格、搜索和表单，不使用detail功能
  const {
    tableData,
    loading,
    pagination,
    searchToolbarBind: origSearchToolbarBind,
    tableBind: origTableBind,
    handleTableChange,
    handleSearch,
    handleReset,
    table,
    form,
  } = useAnsheng({
    formOptions: {
      title: (isEdit) => (isEdit ? '编辑小程序' : '新增小程序'),
      width: 600,
      simpleLayout: {
        showGroupTitle: false,
        padding: 0,
      },
      defaultValues: {
        appid: '',
        secret: '',
        remarks: '',
        status: 1,
      },
      create: async (values) => {
        const formData = objectToFormData(values, {
          customTransform: {
            remarks: (value) => (value === '' ? undefined : value),
          },
        });
        return await addWechatMiniapp(formData);
      },
      update: async (id, values) => {
        return await updateWechatMiniapp({
          id,
          appid: values.appid,
          secret: values.secret,
          status: values.status,
          remarks: values.remarks,
        });
      },
      onSuccess: () => {
        table.getList();
      },
    },

    tableOptions: {
      api: getWechatMiniappList,
      columns,
      actionButtons: [
        {
          key: 'view',
          text: '查看',
          type: 'link',
          onClick: (record) => customDetail.open(record, '小程序详情'),
        },
        {
          key: 'edit',
          text: '编辑',
          type: 'link',
          onClick: (record) => form.show(record),
        },
        {
          key: 'delete',
          text: '删除',
          type: 'link',
          danger: true,
          onClick: handleDelete,
        },
      ],
    },
    searchOptions: {
      basicItems: searchItems,
      defaultValues: {
        appid: '',
        secret: '',
        remarks: '',
        status: undefined,
      },
      customButtons: [
        {
          key: 'add',
          text: '新增小程序',
          type: 'primary',
          onClick: () => form.show(),
        },
        {
          key: 'export',
          text: '导出小程序',
          onClick: handleExport,
        },
      ],
    },
    deleteApi: deleteWechatMiniapp,
  });

  // 使用自定义detail hook
  const customDetail = useCustomDetail();

  // 创建自定义详情组件
  const detailComponent = customDetail.renderDetailModal([
    {
      title: '基本信息',
      items: [
        { label: 'APPID', field: 'appid' },
        { label: 'SECRET', field: 'secret' },
        { label: '备注', field: 'remarks' },
        {
          label: '状态',
          field: 'status',
          formatter: (value) => {
            const option = statusOptions.find((item) => item.value === value);
            return option ? option.label : '-';
          },
        },
      ],
    },
    {
      title: '时间信息',
      items: [
        {
          label: '创建时间',
          field: 'creation_time',
          formatter: (value) =>
            value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
        {
          label: '更新时间',
          field: 'update_time',
          formatter: (value) =>
            value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
      ],
    },
  ]);

  // 处理导出
  async function handleExport() {
    try {
      const res = await exportWechatMiniapp();
      await handleFileDownload(
        res,
        `小程序列表_${dayjs().format('YYYY-MM-DD')}.xlsx`,
      );
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    }
  }

  // 处理删除
  function handleDelete(row: WechatMiniapp) {
    Modal.confirm({
      title: '确定删除小程序吗？',
      content: `小程序AppID：${row.appid}`,
      onOk: async () => {
        try {
          const res = await deleteWechatMiniapp(row.id);
          if (res.code === 1) {
            message.success(res.msg || '删除成功');
            table.getList();
          } else {
            message.error(res.msg || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  }

  // 使用声明式表单替代之前的渲染函数方式
  const formComponent = () =>
    form.renderFormModal([
      {
        title: '小程序信息',
        fields: [
          {
            name: 'appid',
            label: 'APPID',
            component: 'Input',
            rules: [{ required: true, message: '请输入APPID' }],
            props: {
              placeholder: '请输入APPID',
            },
          },
          {
            name: 'secret',
            label: 'SECRET',
            component: 'Input',
            rules: [{ required: true, message: '请输入SECRET' }],
            props: {
              placeholder: '请输入SECRET',
            },
          },
          {
            name: 'remarks',
            label: '备注',
            component: 'Input',
            // rules: [{ required: true, message: '请输入备注' }],
            props: {
              placeholder: '请输入备注',
            },
          },
          {
            name: 'status',
            label: '状态',
            component: 'Select',
            rules: [{ required: true, message: '请选择状态' }],
            props: {
              placeholder: '请选择状态',
            },
            options: statusOptions,
          },
        ],
      },
    ]);

  // 以下计算属性直接使用useAnsheng提供的
  const searchToolbarBind = computed(() => origSearchToolbarBind.value);
  const tableBind = computed(() => ({
    ...origTableBind.value,
    onChange: handleTableChange,
  }));

  // 初始加载
  table.getList();

  return {
    // 数据
    tableData,
    loading,
    pagination,

    // 计算属性
    searchToolbarBind,
    tableBind,
    formComponent,

    // 自定义详情组件
    detailComponent,

    // 方法
    handleTableChange,
    handleSearch,
    handleReset,
    handleDelete,
    handleExport,

    // 导出customDetail以便直接操作
    customDetail,
  };
}
