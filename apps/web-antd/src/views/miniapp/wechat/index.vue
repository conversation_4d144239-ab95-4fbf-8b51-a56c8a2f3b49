<script setup lang="ts">
import { Card } from 'ant-design-vue';

import { BasicTable, SearchToolbar } from '#/hooks/useAnsheng';

import { useWechatMiniapp } from './hooks/useWechatMiniapp';

// 使用自定义hook获取所有数据和方法
const {
  searchToolbarBind,
  tableBind,
  formComponent, // 使用useAnsheng生成的表单组件
  detailComponent,
} = useWechatMiniapp();
</script>

<template>
  <div class="miniapp-wechat p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar v-bind="searchToolbarBind" />

      <!-- 表格 -->
      <BasicTable v-bind="tableBind" />
    </Card>

    <!-- 使用useAnsheng生成的表单组件 -->
    <component :is="formComponent" />

    <!-- 添加详情模态框 -->
    <!-- 自定义详情组件 -->
    <component :is="detailComponent" />
    <!-- <DetailModal
      v-if="detail"
      v-model:visible="detail.visible"
      :title="detail.title"
      :sections="detail.sections"
      :loading="loading"
    /> -->
  </div>
</template>

<style lang="less" scoped>
.miniapp-wechat {
  background-color: var(--background-deep);
}
</style>
