<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { VbenCountToAnimator } from '@vben/common-ui';
import {
  MdiAccountGroup,
  MdiCurrencyUsd,
  MdiFileDocument,
  MdiTrendingUp,
} from '@vben/icons';

import {
  getDailyTransactionApi,
  getMonthlyTransactionApi,
  getTotalAmountApi,
} from '#/api/core/transactionStatistics';

// 加载状态
const loading = ref(true);

// 数据状态
const totalAmount = ref('0');
const dailyData = ref({
  todayAmount: '0',
  todayGrowthRate: '0',
  yesterdayAmount: '0',
  yesterdayGrowthRate: '0',
});
const monthlyData = ref({
  thisMonthAmount: '0',
  thisMonthGrowthRate: '0',
  lastMonthAmount: '0',
  lastMonthGrowthRate: '0',
});

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const [totalAmountRes, dailyDataRes, monthlyDataRes] = await Promise.all([
      getTotalAmountApi(),
      getDailyTransactionApi(),
      getMonthlyTransactionApi(),
    ]);

    totalAmount.value = totalAmountRes.data.totalAmount;
    dailyData.value = dailyDataRes.data;
    monthlyData.value = monthlyDataRes.data;
  } catch (error) {
    console.error('加载交易数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 计算指标卡片数据
const metricsData = computed(() => [
  {
    icon: MdiFileDocument,
    label: '今日交易额',
    value: dailyData.value.todayAmount,
    trend: dailyData.value.todayGrowthRate,
    lastValue: '0', // 用于计算增长比较
    color: '#1890FF',
    sparkline: [30, 25, 35, 45, 40, 50, 45, 55, 50, 60, 55, 65],
  },
  {
    icon: MdiAccountGroup,
    label: '昨日交易额',
    value: dailyData.value.yesterdayAmount,
    trend: dailyData.value.yesterdayGrowthRate,
    lastValue: '0',
    color: '#36CFC9',
    sparkline: [20, 30, 25, 35, 30, 40, 35, 45, 40, 50, 45, 55],
  },
  {
    icon: MdiTrendingUp,
    label: '本月交易额',
    value: monthlyData.value.thisMonthAmount,
    trend: monthlyData.value.thisMonthGrowthRate,
    lastValue: '0',
    color: '#722ED1',
    sparkline: [35, 45, 40, 50, 45, 55, 45, 50, 45, 50, 55, 45],
  },
  {
    icon: MdiCurrencyUsd,
    label: '上月交易额',
    value: monthlyData.value.lastMonthAmount,
    trend: monthlyData.value.lastMonthGrowthRate,
    lastValue: '0',
    color: '#FA8C16',
    sparkline: [45, 50, 40, 45, 55, 45, 50, 60, 55, 50, 55, 60],
  },
]);

// 获取增长率的颜色
const getTrendColor = (trend: string) => {
  const value = Number.parseFloat(trend);
  if (value > 0) return '#52c41a';
  if (value < 0) return '#ff4d4f';
  return '#666666';
};

// 注释掉未使用的变量以修复linter错误
/*
// 计算进度百分比（假设目标为1亿元）- 未使用但保留以供未来功能使用
const _progress = computed(() => {
  const target = 100_000_000; // 1亿元
  const current = Number(totalAmount.value);
  return Math.min((current / target) * 100, 100);
});

// 圆环长度 - 未使用，但保留以便将来使用
const _circleLength = computed(() => Math.PI * 2 * 90);
*/

// 添加随机位置计算，避免重新渲染时光斑位置跳动
const lightSpots = computed(() =>
  Array.from({ length: 3 }, (_, i) => ({
    left: `${30 + Math.random() * 40}%`,
    top: `${20 + Math.random() * 60}%`,
    delay: `${i * 2}s`,
  })),
);

// 添加波浪动画路径
const wavePathD = computed(() => {
  const width = 1000;
  const height = 50;
  const points = [];
  for (let i = 0; i <= width; i += 20) {
    const x = i;
    const y = (Math.sin(i / 50) * height) / 2;
    points.push(`${x},${y + height}`);
  }
  return `M0,${height} L${points.join(' L')} L${width},${height * 2} L0,${height * 2} Z`;
});

// 修改背景图标配置，调整位置更分散
const bgIcons = [
  {
    icon: MdiFileDocument,
    size: 24,
    color: '#1890FF',
    position: { left: '5%', top: '10%' },
    delay: '0s',
  },
  {
    icon: MdiAccountGroup,
    size: 28,
    color: '#36CFC9',
    position: { right: '8%', top: '15%' },
    delay: '2s',
  },
  {
    icon: MdiTrendingUp,
    size: 20,
    color: '#722ED1',
    position: { left: '8%', bottom: '20%' },
    delay: '4s',
  },
  {
    icon: MdiCurrencyUsd,
    size: 26,
    color: '#FA8C16',
    position: { right: '6%', bottom: '18%' },
    delay: '6s',
  },
];

// 在组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="transaction-container relative flex min-h-[320px] flex-col">
    <!-- 主要数据展示 -->
    <div
      class="transaction-card relative flex min-h-[180px] items-center justify-center"
    >
      <!-- 背景装饰 -->
      <div class="absolute inset-0">
        <!-- 漂浮图标 -->
        <div class="absolute inset-0 -z-10">
          <div
            v-for="(icon, index) in bgIcons"
            :key="index"
            class="animate-float-icon absolute opacity-[0.03]"
            :style="{
              ...icon.position,
              animationDelay: icon.delay,
            }"
          >
            <component
              :is="icon.icon"
              :style="{
                fontSize: `${icon.size}px`,
                color: icon.color,
              }"
            />
            <div
              class="absolute -inset-2 -z-10 rounded-full opacity-15 blur-lg"
              :style="{
                backgroundColor: icon.color,
              }"
            ></div>
          </div>
        </div>

        <!-- 波浪动画 -->
        <div class="absolute inset-0 opacity-[0.02]">
          <svg
            class="h-full w-full"
            preserveAspectRatio="none"
            viewBox="0 0 1000 100"
          >
            <path
              :d="wavePathD"
              class="animate-wave text-primary fill-current"
              fill-opacity="0.3"
            />
            <path
              :d="wavePathD"
              class="animate-wave-delay text-primary fill-current"
              fill-opacity="0.2"
              transform="translate(-30, 0)"
            />
          </svg>
        </div>
        <div class="animate-pulse-slow absolute left-0 top-0 h-40 w-40">
          <div
            class="absolute h-full w-full rounded-full bg-[#867AE5] opacity-[0.03] blur-3xl"
          ></div>
        </div>
        <div class="animate-pulse-slow-delay absolute right-0 top-0 h-40 w-40">
          <div
            class="absolute h-full w-full rounded-full bg-[#91CDFF] opacity-[0.03] blur-3xl"
          ></div>
        </div>
        <!-- 装饰线条 -->
        <div
          class="absolute left-0 right-0 top-1/2 h-px bg-gradient-to-r from-transparent via-[#867AE5]/10 to-transparent"
        ></div>
        <!-- 动态光斑 -->
        <div class="absolute inset-0">
          <div
            v-for="(spot, i) in lightSpots"
            :key="i"
            class="animate-float absolute h-24 w-24"
            :style="{
              left: spot.left,
              top: spot.top,
              animationDelay: spot.delay,
            }"
          >
            <div
              class="bg-gradient-radial absolute h-full w-full rounded-full from-white/5 to-transparent opacity-20 blur-2xl"
            ></div>
          </div>
        </div>
      </div>

      <!-- 中心内容 -->
      <div class="relative z-20 flex flex-col items-center">
        <!-- 标题和金额 -->
        <div class="relative z-10 flex flex-col items-center">
          <div class="mb-4 flex items-center gap-2 backdrop-blur-sm">
            <div
              class="h-6 w-[3px] rounded-full bg-gradient-to-b from-[#867AE5] to-[#91CDFF]"
            ></div>
            <div
              class="whitespace-nowrap text-sm font-medium text-[#666] sm:text-base dark:text-[#999]"
            >
              累计成交金额 (元)
            </div>
          </div>
          <div class="group relative backdrop-blur-sm">
            <div class="flex items-baseline">
              <span
                class="text-xl font-medium text-[#666] sm:text-2xl dark:text-[#999]"
                >¥</span>
              <span
                class="relative ml-2 text-[32px] font-bold tracking-wider text-[#333] sm:text-[48px] dark:text-white"
              >
                <VbenCountToAnimator
                  :duration="3000"
                  :end-val="Number(totalAmount)"
                  :start-val="1"
                  separator=","
                />
                <div
                  class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 blur-sm transition-all duration-1000 group-hover:translate-x-full group-hover:opacity-100"
                ></div>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="absolute inset-0 z-30 flex items-center justify-center bg-white/30 backdrop-blur-sm dark:bg-black/30"
    >
      <div class="flex flex-col items-center">
        <div
          class="border-primary h-12 w-12 animate-spin rounded-full border-4 border-t-transparent"
        ></div>
        <span class="text-primary mt-2">加载数据中...</span>
      </div>
    </div>

    <!-- 指标卡片 -->
    <div
      class="relative z-20 grid w-full max-w-full grid-cols-1 gap-1.5 px-1 sm:grid-cols-2 sm:gap-4 sm:px-4 lg:grid-cols-4"
    >
      <template v-for="(item, index) in metricsData" :key="index">
        <div
          class="group relative w-full overflow-hidden rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent p-1 transition-all duration-500 hover:from-white/[0.08] sm:p-4 dark:from-white/[0.02] dark:hover:from-white/[0.04]"
          style="min-height: 100px"
        >
          <!-- 背景装饰 -->
          <div
            class="absolute inset-0 opacity-0 transition-opacity duration-500 group-hover:opacity-100"
          >
            <div
              class="absolute inset-0"
              :style="{
                background: `radial-gradient(circle at 50% -20%, ${item.color}10, transparent 70%)`,
              }"
            ></div>
            <div
              class="absolute inset-0 bg-gradient-to-b from-transparent to-black/5 dark:to-white/5"
            ></div>
          </div>

          <!-- 迷你折线图 -->
          <div
            class="absolute bottom-0 left-0 right-0 h-8 opacity-[0.08] transition-opacity duration-500 group-hover:opacity-[0.15] sm:h-16"
          >
            <svg
              class="h-full w-full"
              viewBox="0 0 120 40"
              preserveAspectRatio="none"
            >
              <defs>
                <linearGradient
                  :id="`line-gradient-${index}`"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="0%" :stop-color="item.color" stop-opacity="1" />
                  <stop
                    offset="100%"
                    :stop-color="item.color"
                    stop-opacity="0"
                  />
                </linearGradient>
              </defs>
              <path
                :d="`M0 ${40 - (item.sparkline?.[0] || 0)} ${item.sparkline?.map((value, i) => `L${(i + 1) * 10} ${40 - value}`).join(' ') || ''}`"
                :stroke="item.color"
                stroke-width="2"
                fill="none"
              />
              <path
                :d="`M0 ${40 - (item.sparkline?.[0] || 0)} ${item.sparkline?.map((value, i) => `L${(i + 1) * 10} ${40 - value}`).join(' ') || ''} L120 40 L0 40 Z`"
                :fill="`url(#line-gradient-${index})`"
                opacity="0.1"
              />
            </svg>
          </div>

          <!-- 图标和趋势 -->
          <div class="mb-0.5 flex items-center justify-between sm:mb-3">
            <div class="relative">
              <div
                class="relative flex h-5 w-5 items-center justify-center rounded-lg transition-all duration-500 group-hover:scale-110 sm:h-10 sm:w-10"
                :style="{ backgroundColor: `${item.color}15` }"
              >
                <component
                  :is="item.icon"
                  class="text-sm transition-colors sm:text-xl"
                  :style="{ color: item.color }"
                />
                <!-- 悬浮光圈 -->
                <div
                  class="absolute -inset-1 rounded-lg opacity-0 transition-all duration-500 group-hover:opacity-30"
                  :style="{
                    background: `radial-gradient(circle, ${item.color} 0%, transparent 70%)`,
                    filter: 'blur(4px)',
                  }"
                ></div>
              </div>
            </div>
            <div
              class="flex items-center gap-0.5 rounded-full px-1 py-0.5 text-[9px] font-medium backdrop-blur-sm transition-colors sm:gap-1 sm:px-2.5 sm:py-1 sm:text-xs"
              :style="{
                backgroundColor: `${item.color}10`,
                color: getTrendColor(item.trend),
              }"
            >
              <span
                class="transform transition-transform group-hover:translate-y-[-1px]"
              >
                {{ parseFloat(item.trend) >= 0 ? '↗' : '↘' }}
              </span>
              <span>{{ item.trend }}%</span>
            </div>
          </div>

          <!-- 数据 -->
          <div class="relative space-y-0 sm:space-y-2">
            <div
              class="text-md font-semibold tracking-wide text-[#333] transition-colors sm:text-2xl dark:text-white"
            >
              <template v-if="item.value.endsWith('%')">
                {{ item.value }}
              </template>
              <template v-else>
                <VbenCountToAnimator
                  :duration="3000"
                  :end-val="Number(item.value)"
                  :start-val="1"
                  separator=","
                />
              </template>
            </div>
            <div
              class="text-[10px] text-[#666] transition-colors sm:text-sm dark:text-[#999]"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-20px);
  }
}

@keyframes wave {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(1000px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.1;
  }
}

/* 响应式布局样式 */
@media (max-width: 640px) {
  /* 移动端样式 */
  .transaction-container {
    min-height: auto;
  }

  .transaction-card {
    min-height: 140px;
  }

  /* 指标卡片样式 */
  .group.relative.w-full {
    min-height: 80px !important;
  }
}

/* 平板样式 */
@media (min-width: 641px) and (max-width: 1024px) {
  .transaction-container {
    min-height: 360px;
  }

  .transaction-card {
    min-height: 160px;
  }
}

/* 桌面样式 */
@media (min-width: 1025px) {
  .transaction-container {
    min-height: 380px;
  }

  .transaction-card {
    min-height: 200px;
  }
}

.bg-card {
  background-color: var(--vben-card-bg);
}

.animate-float-icon {
  animation: float 6s ease-in-out infinite;
}

.animate-wave {
  animation: wave 8s linear infinite;
}

.animate-wave-delay {
  animation: wave 8s linear infinite;
  animation-delay: -4s;
}

.animate-pulse-slow {
  animation: pulse 4s ease-in-out infinite;
}

.animate-pulse-slow-delay {
  animation: pulse 4s ease-in-out infinite;
  animation-delay: -2s;
}
</style>
