<script lang="ts" setup>
import type { EChartsOption } from 'echarts';
import type { EchartsUIType } from '@vben/plugins/echarts';
import { computed, onMounted, ref } from 'vue';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';
import { Spin } from 'ant-design-vue';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark } = usePreferences();
const loading = ref(false);

// 模拟数据
const conversionData = [
  { value: 100, name: '访问人数' },
  { value: 80, name: '有效咨询' },
  { value: 60, name: '付费意向' },
  { value: 40, name: '成交人数' },
];

const sourceData = [
  { value: 30, name: '自然搜索' },
  { value: 25, name: '直接访问' },
  { value: 20, name: '外部链接' },
  { value: 25, name: '其他' },
];

const themeColors = computed(() => ({
  primary: isDark.value ? '#177ddc' : '#1890ff',
  colors: isDark.value
    ? ['#177ddc', '#49b1f5', '#13c2c2', '#52c41a']
    : ['#1890ff', '#40a9ff', '#36cfc9', '#73d13d'],
  background: isDark.value ? '#000' : '#fff',
  text: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
}));

const getChartOption = computed<EChartsOption>(() => ({
  backgroundColor: 'transparent',
  tooltip: {
    trigger: 'item',
  },
  legend: {
    orient: 'horizontal',
    bottom: '5%',
    textStyle: {
      color: themeColors.value.text,
    },
  },
  series: [
    {
      name: '访问来源',
      type: 'pie',
      radius: ['50%', '70%'],
      center: ['25%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2,
      },
      label: {
        show: false,
        position: 'center',
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold',
        },
      },
      labelLine: {
        show: false,
      },
      data: sourceData,
    },
    {
      name: '转化漏斗',
      type: 'funnel',
      left: '55%',
      top: '10%',
      bottom: '10%',
      width: '40%',
      min: 0,
      max: 100,
      minSize: '0%',
      maxSize: '100%',
      sort: 'descending',
      gap: 2,
      label: {
        show: true,
        position: 'right',
      },
      labelLine: {
        length: 10,
        lineStyle: {
          width: 1,
          type: 'solid',
        },
      },
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 1,
      },
      emphasis: {
        label: {
          fontSize: 14,
        },
      },
      data: conversionData,
    },
  ],
}));

const updateChart = () => {
  renderEcharts(getChartOption.value);
};

onMounted(() => {
  updateChart();
});
</script>

<template>
  <Spin :spinning="loading">
    <EchartsUI ref="chartRef" class="!h-[400px]" />
  </Spin>
</template>
