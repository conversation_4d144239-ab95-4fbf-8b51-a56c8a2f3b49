<script lang="ts" setup>
import type { EChartsOption } from 'echarts';

import type { EchartsUIType } from '@vben/plugins/echarts';

import type { YearOrderMoneyTrendData } from '#/api/core/transactionStatistics';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';

import { Spin } from 'ant-design-vue';

import { getYearOrderMoneyTrendApi } from '#/api/core/transactionStatistics';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark } = usePreferences();
const loading = ref(true);

// 图表数据
const chartData = ref<YearOrderMoneyTrendData>({
  xAxisData: [],
  yAxisData: [],
});

// 图表配置
const getChartOptions = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'axis',
    backgroundColor: isDark.value ? '#1f1f1f' : '#fff',
    borderColor: isDark.value ? '#303030' : '#e5e5e5',
    formatter: (params: any) => {
      const param = Array.isArray(params) ? params[0] : params;
      const date = param.name;
      const value = param.value;
      return `<div style="font-weight: 500; margin-bottom: 4px;">${date}</div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background: #91CDFF;"></span>
                <span>金额: ¥${value.toLocaleString()}</span>
              </div>`;
    },
  },
  grid: {
    top: '8%',
    left: '3%',
    right: '4%',
    bottom: '6%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: chartData.value.xAxisData,
    axisLine: {
      lineStyle: {
        color: isDark.value ? 'rgba(255, 255, 255, 0.1)' : '#eee',
      },
    },
    axisLabel: {
      fontSize: 11,
      color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
    },
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false,
    },
    splitLine: {
      lineStyle: {
        color: isDark.value ? 'rgba(255, 255, 255, 0.1)' : '#eee',
      },
    },
    axisLabel: {
      fontSize: 11,
      color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
      formatter: (value) => `¥${value.toLocaleString()}`,
    },
  },
  series: [
    {
      name: '订单金额',
      type: 'line',
      data: chartData.value.yAxisData,
      smooth: true,
      symbolSize: 6,
      itemStyle: {
        color: '#91CDFF',
      },
      lineStyle: {
        width: 2,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: '#91CDFF',
            },
            {
              offset: 1,
              color: '#91CDFFCC',
            },
          ],
        },
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: '#91CDFF33',
            },
            {
              offset: 1,
              color: '#91CDFF00',
            },
          ],
        },
      },
    },
  ],
}));

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const res = await getYearOrderMoneyTrendApi();

    if (res.code === 1 && res.data) {
      chartData.value = res.data;
      renderEcharts(getChartOptions.value);
    }
  } catch (error) {
    console.error('获取年度订单金额趋势数据失败:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadData();
});

watch(
  () => isDark.value,
  () => {
    if (chartData.value.xAxisData.length > 0) {
      renderEcharts(getChartOptions.value);
    }
  },
);
</script>

<template>
  <div class="h-[180px] w-full">
    <Spin :spinning="loading">
      <EchartsUI ref="chartRef" class="!h-[180px]" />
    </Spin>
  </div>
</template>
