<script lang="ts" setup>
import { h, onMounted, onUnmounted, ref } from 'vue';

import { usePreferences } from '@vben/preferences';

import { Table } from 'ant-design-vue';

const { isDark } = usePreferences();

interface ProductItem {
  id: number;
  name: string;
  price: number;
  totalSales: number;
  orderCount: number;
  totalAmount: number;
  stock: number;
}

const columns = [
  {
    title: '商品名称',
    dataIndex: 'name',
    width: '25%',
    customRender: ({ text }: { text: string }) => {
      return h('span', { class: 'text-sm text-[#333] dark:text-white' }, text);
    },
  },
  {
    title: '单价',
    dataIndex: 'price',
    width: '15%',
    customRender: ({ text }: { text: number }) => {
      return h('span', { class: 'text-sm' }, `¥${text.toLocaleString()}`);
    },
  },
  {
    title: '销量',
    dataIndex: 'totalSales',
    width: '15%',
    customRender: ({ text }: { text: number }) => {
      return h('span', { class: 'text-sm' }, text.toLocaleString());
    },
  },
  {
    title: '订单数',
    dataIndex: 'orderCount',
    width: '15%',
    customRender: ({ text }: { text: number }) => {
      return h('span', { class: 'text-sm' }, text.toLocaleString());
    },
  },
  {
    title: '总金额',
    dataIndex: 'totalAmount',
    width: '15%',
    customRender: ({ text }: { text: number }) => {
      return h('span', { class: 'text-sm' }, `¥${text.toLocaleString()}`);
    },
  },
  {
    title: '库存',
    dataIndex: 'stock',
    width: '15%',
    customRender: ({ text }: { text: number }) => {
      return h('span', { class: 'text-sm' }, text.toLocaleString());
    },
  },
];

// 扩充数据源
const allData = [
  {
    id: 1,
    name: '5G全网通手机',
    price: 4688,
    totalSales: 5414,
    orderCount: 4448,
    totalAmount: 20_081_664,
    stock: 623,
  },
  {
    id: 2,
    name: '4G全网通手机',
    price: 3110,
    totalSales: 5543,
    orderCount: 2227,
    totalAmount: 6_925_970,
    stock: 512,
  },
  {
    id: 3,
    name: '物联网通信模块',
    price: 230,
    totalSales: 5510,
    orderCount: 2113,
    totalAmount: 485_990,
    stock: 662,
  },
  {
    id: 4,
    name: '智能手表',
    price: 1299,
    totalSales: 3245,
    orderCount: 2876,
    totalAmount: 3_735_924,
    stock: 432,
  },
  {
    id: 5,
    name: '无线耳机',
    price: 899,
    totalSales: 4567,
    orderCount: 3998,
    totalAmount: 3_594_102,
    stock: 345,
  },
  {
    id: 6,
    name: 'WiFi路由器',
    price: 459,
    totalSales: 2789,
    orderCount: 2345,
    totalAmount: 1_076_355,
    stock: 234,
  },
];

const dataSource = ref<ProductItem[]>([]);
const currentIndex = ref(0);
const pageSize = 3; // 每页显示的数量

// 更新显示的数据
const updateDisplayData = () => {
  const start = currentIndex.value;
  const end = start + pageSize;
  dataSource.value = allData.slice(start, end);

  // 更新索引
  currentIndex.value = end >= allData.length ? 0 : currentIndex.value + 1;
};

let timer: NodeJS.Timer | null = null;

// 开始自动轮播
const startAutoScroll = () => {
  // 初始化显示数据
  updateDisplayData();

  // 设置定时器，每5秒更新一次
  timer = setInterval(() => {
    updateDisplayData();
  }, 5000);
};

// 停止自动轮播
const stopAutoScroll = () => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
};

// 鼠标悬停时暂停轮播
const handleMouseEnter = () => {
  stopAutoScroll();
};

// 鼠标离开时恢复轮播
const handleMouseLeave = () => {
  startAutoScroll();
};

onMounted(() => {
  startAutoScroll();
});

onUnmounted(() => {
  stopAutoScroll();
});
</script>

<template>
  <div class="product-table h-full overflow-auto">
    <Table
      :columns="columns"
      :data-source="dataSource"
      :pagination="false"
      size="small"
      :scroll="{ y: 240 }"
    >
      <template #headerCell="{ column }">
        <span class="text-xs text-[#666] dark:text-[#999]">{{
          column.title
        }}</span>
      </template>
    </Table>
  </div>
</template>

<style scoped>
:deep(.ant-table) {
  background: transparent !important;
}

:deep(.ant-table-thead > tr > th) {
  padding: 8px !important;
  background: transparent !important;
  border-bottom: 1px solid
    v-bind('isDark ? "rgba(255, 255, 255, 0.08)" : "rgba(0, 0, 0, 0.06)"') !important;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px !important;
  border-bottom: 1px solid
    v-bind('isDark ? "rgba(255, 255, 255, 0.04)" : "rgba(0, 0, 0, 0.02)"') !important;
  transition: all 0.3s ease-in-out; /* 添加过渡效果 */
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: v-bind(
    'isDark ? "rgba(255, 255, 255, 0.04)" : "rgba(0, 0, 0, 0.02)"'
  ) !important;
}

:deep(.ant-table-tbody > tr > td.ant-table-cell) {
  color: v-bind('isDark ? "rgba(255, 255, 255, 0.85)" : "#666"');
}
</style>
