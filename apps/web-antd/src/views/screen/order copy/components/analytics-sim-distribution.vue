<script lang="ts" setup>
import type { EChartsOption } from 'echarts';
import type { EchartsUIType } from '@vben/plugins/echarts';
import { onMounted, ref, computed, watch } from 'vue';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';
import { getSimDistributionDataApi, type SimDistributionData } from '#/api/core/dashboard';
import { Spin } from 'ant-design-vue';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark, theme } = usePreferences();
const loading = ref(true);

const simData = ref<SimDistributionData>();

// 获取数据
const loadData = async () => {
  try {
    loading.value = true;
    const res = await getSimDistributionDataApi();
    if (res.code === 1) {
      simData.value = res.data;
    }
  } catch (error) {
    console.error('获取SIM卡分布数据失败:', error);
  } finally {
    loading.value = false;
  }
};

const themeColors = computed(() => ({
  primary: isDark.value ? '#177ddc' : '#1890ff',
  colors: isDark.value 
    ? ['#177ddc', '#49b1f5', '#13c2c2', '#52c41a']
    : ['#1890ff', '#40a9ff', '#36cfc9', '#73d13d'],
  background: isDark.value ? '#000' : '#fff',
  text: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
}));

const chartData = computed(() => {
  if (!simData.value) return [];
  return [
    { value: simData.value.move, name: '中国移动' },
    { value: simData.value.telecommunication, name: '中国电信' },
    { value: simData.value.unicom, name: '中国联通' },
    { value: simData.value.broadcast, name: '中国广电' }
  ];
});

const getChartOptions = computed<EChartsOption>(() => ({
  backgroundColor: 'transparent',
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'horizontal',
    bottom: '1%',
    left: 'center',
    itemGap: 20,
    itemWidth: 12,
    itemHeight: 12,
    padding: [0, 0, 0, 0],
    textStyle: {
      color: themeColors.value.text,
      fontSize: 12
    }
  },
  series: [
    {
      name: 'SIM卡分布',
      type: 'pie',
      radius: ['45%', '75%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: themeColors.value.background,
        borderWidth: 2
      },
      label: {
        show: false
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: chartData.value,
      color: themeColors.value.colors
    }
  ]
}));

onMounted(() => {
  loadData();
});

watch(
  [() => isDark.value, () => theme.value, () => simData.value],
  () => {
    renderEcharts(getChartOptions.value);
  }
);
</script>

<template>
  <div class="bg-card rounded-lg w-full">
    <div class="px-4">
      <Spin :spinning="loading">
        <EchartsUI ref="chartRef" class="h-[400px]" />
      </Spin>
    </div>
  </div>
</template> 
