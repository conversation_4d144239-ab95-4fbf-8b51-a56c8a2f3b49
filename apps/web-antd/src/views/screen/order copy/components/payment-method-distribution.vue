<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';

import { getPaymentDistributionApi } from '#/api/core/transactionStatistics';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark } = usePreferences();

// 模拟数据
const defaultData = [
  { name: '微信支付', value: 42, color: '#40A9FF' }, // 更柔和的蓝色
  { name: '支付宝', value: 28, color: '#5B8FF9' }, // 深蓝色
  { name: '余额支付', value: 20, color: '#5AD8A6' }, // 清新绿色
  { name: '其他方式', value: 10, color: '#FF9C6E' }, // 温暖橙色
];

const chartData = ref(defaultData);
const loading = ref(true);

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const res = await getPaymentDistributionApi();

    console.log('支付方式分布数据:', res);

    if (res.code === 1 && res.data) {
      // 为API返回的数据添加颜色属性并转换value为数字类型
      const colorMap = {
        微信支付: '#40A9FF',
        余额支付: '#5AD8A6',
        其他支付: '#FF9C6E',
      };

      chartData.value = res.data.map((item) => ({
        name: item.name,
        value: Number.parseFloat(item.value),
        color: colorMap[item.name] || '#5B8FF9', // 如果找不到对应颜色，使用默认蓝色
      }));
      renderEcharts(getChartOptions.value);
    } else {
      chartData.value = defaultData;
    }
  } catch (error) {
    console.error('获取支付方式分布数据失败:', error);
    chartData.value = defaultData;
  } finally {
    loading.value = false;
  }
};

const getChartOptions = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c}%',
    // backgroundColor: isDark.value ? '#1f1f1f' : '#fff',
    // borderColor: isDark.value ? '#303030' : '#e5e5e5',
  },
  legend: {
    orient: 'horizontal',
    bottom: '0',
    left: 'center',
    itemWidth: 8,
    itemHeight: 8,
    icon: 'circle',
    itemGap: 24,
    textStyle: {
      fontSize: 11,
      color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['45%', '65%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
      },
      itemStyle: {
        borderRadius: 6,
        borderColor: isDark.value ? '#1f1f1f' : '#ffffff',
        borderWidth: 2,
      },
      emphasis: {
        scale: true,
        scaleSize: 5,
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.2)',
        },
      },
      data: chartData.value.map((item) => ({
        ...item,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: item.color,
              },
              {
                offset: 1,
                color: `${item.color}CC`,
              },
            ],
          },
        },
      })),
    },
  ],
}));

onMounted(() => {
  renderEcharts(getChartOptions.value);
  loadData();
});

watch(
  () => isDark.value,
  () => {
    renderEcharts(getChartOptions.value);
  },
);
</script>

<template>
  <Spin :spinning="loading">
    <div class="h-[180px] w-full">
      <EchartsUI ref="chartRef" class="!h-[180px]" />
    </div>
  </Spin>
</template>
