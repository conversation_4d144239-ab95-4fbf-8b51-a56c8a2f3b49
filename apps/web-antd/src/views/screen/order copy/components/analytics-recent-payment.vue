<script lang="ts" setup>
import type { EChartsOption } from 'echarts';
import type { EchartsUIType } from '@vben/plugins/echarts';
import { onMounted, ref, computed, watch } from 'vue';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';
import { getRecentPaymentDataApi, type RecentPaymentData } from '#/api/core/dashboard';
import { Spin } from 'ant-design-vue';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark, theme } = usePreferences();

const paymentData = ref<RecentPaymentData[]>([]);
const loading = ref(true);

// 获取数据
const loadData = async () => {
  try {
    loading.value = true;
    const res = await getRecentPaymentDataApi();
    if (res.code === 1) {
      paymentData.value = res.data;
    }
  } catch (error) {
    console.error('获取收款数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 根据主题计算颜色
const themeColors = computed(() => ({
  primary: isDark.value ? '#177ddc' : '#1890ff',
  splitLine: isDark.value ? 'rgba(253, 253, 253, 0.12)' : '#f0f0f0',
  text: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
  subText: isDark.value ? 'rgba(255, 255, 255, 0.45)' : '#999',
  background: isDark.value ? '#000' : '#fff',
}));

const getChartOptions = computed<EChartsOption>(() => ({
  backgroundColor: 'transparent',
  grid: {
    bottom: '5%',
    containLabel: true,
    left: '1%',
    right: '1%',
    top: '15%',
  },
  series: [
    {
      name: '收款金额',
      type: 'line',
      data: paymentData.value.map(item => item.amount),
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      lineStyle: {
        width: 3,
        color: themeColors.value.primary,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: isDark.value ? 'rgba(24,144,255,0.25)' : 'rgba(24,144,255,0.35)'
            },
            {
              offset: 1,
              color: isDark.value ? 'rgba(24,144,255,0.05)' : 'rgba(24,144,255,0.05)'
            }
          ]
        }
      },
      itemStyle: {
        color: themeColors.value.primary,
        borderWidth: 2
      },
      emphasis: {
        itemStyle: {
          color: themeColors.value.primary,
          borderColor: themeColors.value.background,
          borderWidth: 2,
          shadowColor: isDark.value ? 'rgba(24,144,255,0.5)' : 'rgba(24,144,255,0.5)',
          shadowBlur: 10
        }
      }
    }
  ],
  tooltip: {
    trigger: 'axis',
    backgroundColor: isDark.value ? '#1f1f1f' : '#fff',
    borderColor: themeColors.value.splitLine,
    borderWidth: 1,
    padding: [8, 12],
    textStyle: {
      color: themeColors.value.text
    },
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: themeColors.value.primary,
        width: 1,
        type: 'dashed'
      }
    },
    formatter: (params: any) => {
      const data = params[0];
      return `${data.name}<br/><span style="color: ${themeColors.value.primary}">◆</span> 收款金额：￥${data.value.toFixed(2)}`;
    }
  },
  xAxis: {
    type: 'category' as const,
    data: paymentData.value.map(item => item.click_date.slice(5)),
    boundaryGap: false,
    axisLine: {
      lineStyle: {
        color: themeColors.value.splitLine
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: themeColors.value.subText,
      fontSize: 12,
      formatter: (value: string) => value.slice(5) // 只显示月-日
    }
  },
  yAxis: {
    type: 'value' as const,
    name: '金额(元)',
    nameTextStyle: {
      color: themeColors.value.subText,
      fontSize: 12,
      padding: [0, 8, 10, 0],
      align: 'right'
    },
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: themeColors.value.subText,
      fontSize: 12,
      margin: 15,
      formatter: (value: number) => `￥${value.toFixed(2)}`
    },
    splitLine: {
      lineStyle: {
        color: [themeColors.value.splitLine],
        type: 'dashed'
      }
    }
  }
}));

onMounted(() => {
  loadData();
});

watch(
  [() => isDark.value, () => theme.value, () => paymentData.value],
  () => {
    renderEcharts(getChartOptions.value);
  }
);
</script>

<template>
  <div class="bg-card rounded-lg w-full">
    <div class="px-4">
      <Spin :spinning="loading">
        <EchartsUI ref="chartRef" class="h-[500px]" />
      </Spin>
    </div>
  </div>
</template> 
