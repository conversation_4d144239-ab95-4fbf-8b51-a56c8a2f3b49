<script lang="ts" setup>
import { onMounted, onUnmounted, ref } from 'vue';

import { formatDate } from '@vben/utils';

import { Tag } from 'ant-design-vue';

import { getBackgroundTaskList } from '#/api/core/task';

interface TaskItem {
  id: number;
  tasks_name: string;
  tasks_status: number;
  error_msg: string;
  on_time: string;
  end_time: string;
  user_account: string;
}

interface TaskResponse {
  code: number;
  msg: string;
  data: {
    rows: TaskItem[];
    total: number;
  };
}

const taskList = ref<TaskItem[]>([]);
const loading = ref(true);
let timer: null | ReturnType<typeof setInterval> = null;

// 获取任务列表数据
const fetchTaskList = async () => {
  try {
    const res = (await getBackgroundTaskList({
      page: 1,
      pageSize: 6, // 减少数量以适应显示
    })) as unknown as TaskResponse;

    if (res.code === 1 && res.data?.rows) {
      taskList.value = res.data.rows;
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 定时刷新数据
const startPolling = () => {
  timer = setInterval(() => {
    fetchTaskList();
  }, 30_000); // 每30秒刷新一次
};

onMounted(() => {
  fetchTaskList();
  startPolling();
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});

const getStatusTag = (status: number) => {
  const statusMap = {
    1: { color: 'processing', text: '运行中' },
    2: { color: 'success', text: '已完成' },
    3: { color: 'error', text: '异常中止' },
  };
  return (
    statusMap[status as keyof typeof statusMap] || {
      color: 'default',
      text: '未知',
    }
  );
};

const formatDateTime = (date: string) => {
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss');
};
</script>

<template>
  <div class="task-list-container h-[450px] py-4">
    <div class="task-list">
      <div class="animate-scroll">
        <template v-for="task in taskList" :key="`original-${task.id}`">
          <div
            class="task-item hover:border-primary/30 hover:bg-primary/5 group h-[76px] cursor-pointer rounded-lg border border-transparent bg-white/50 p-3 transition-all dark:bg-black/20"
          >
            <div class="mb-1.5 flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Tag :color="getStatusTag(task.tasks_status).color">
                  {{ getStatusTag(task.tasks_status).text }}
                </Tag>
                <span
                  class="text-sm font-medium text-[#333] transition-colors dark:text-white"
                >
                  {{ task.tasks_name }}
                </span>
              </div>
              <span class="text-xs text-[#999]">{{ task.user_account }}</span>
            </div>
            <div class="space-y-1">
              <div class="flex items-center justify-between text-xs">
                <span class="text-[#666] dark:text-[#999]">
                  开始时间：{{ formatDateTime(task.on_time) }}
                </span>
                <span v-if="task.end_time" class="text-[#666] dark:text-[#999]">
                  结束时间：{{ formatDateTime(task.end_time) }}
                </span>
              </div>
              <div class="text-xs text-[#666] dark:text-[#999]">
                {{ task.error_msg }}
              </div>
            </div>
          </div>
        </template>
        <!-- 复制一份列表用于无缝滚动 -->
        <template v-for="task in taskList" :key="`duplicate-${task.id}`">
          <div
            class="task-item hover:border-primary/30 hover:bg-primary/5 group h-[76px] cursor-pointer rounded-lg border border-transparent bg-white/50 p-3 transition-all dark:bg-black/20"
          >
            <div class="mb-1.5 flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Tag :color="getStatusTag(task.tasks_status).color">
                  {{ getStatusTag(task.tasks_status).text }}
                </Tag>
                <span
                  class="text-sm font-medium text-[#333] transition-colors dark:text-white"
                >
                  {{ task.tasks_name }}
                </span>
              </div>
              <span class="text-xs text-[#999]">{{ task.user_account }}</span>
            </div>
            <div class="space-y-1">
              <div class="flex items-center justify-between text-xs">
                <span class="text-[#666] dark:text-[#999]">
                  开始时间：{{ formatDateTime(task.on_time) }}
                </span>
                <span v-if="task.end_time" class="text-[#666] dark:text-[#999]">
                  结束时间：{{ formatDateTime(task.end_time) }}
                </span>
              </div>
              <div class="text-xs text-[#666] dark:text-[#999]">
                {{ task.error_msg }}
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
.task-list-container {
  position: relative;
}

.task-list {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.animate-scroll {
  animation: scroll-y 30s linear infinite;
  will-change: transform;
}

.animate-scroll:hover {
  animation-play-state: paused;
}

@keyframes scroll-y {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-50%);
  }
}

.task-item {
  position: relative;
  margin-bottom: 12px;
}

.task-item:not(:last-child)::after {
  position: absolute;
  right: 0;
  bottom: -6px;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, rgb(0 0 0 / 5%), transparent);
}
</style>
