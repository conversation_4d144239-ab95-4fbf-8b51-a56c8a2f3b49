<script lang="ts" setup>
import type { EChartsOption } from 'echarts';

import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark } = usePreferences();
const data = [
  { name: '早间(6-12时)', value: 35, color: '#867AE5' },
  { name: '午间(12-18时)', value: 42, color: '#91CDFF' },
  { name: '晚间(18-24时)', value: 20, color: '#E58AFF' },
  { name: '凌晨(0-6时)', value: 3, color: '#FFE4B0' },
];

const getChartOptions = (): EChartsOption => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c}%',
    backgroundColor: isDark.value ? '#1f1f1f' : '#fff',
    borderColor: isDark.value ? '#303030' : '#e5e5e5',
  },
  legend: {
    orient: 'vertical',
    right: '2%',
    top: 'middle',
    itemWidth: 8,
    itemHeight: 8,
    icon: 'circle',
    formatter: (name: string) => {
      const item = data.find((i) => i.name === name);
      return `${name}  ${item?.value}%`;
    },
    textStyle: {
      fontSize: 11,
      color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
    },
    itemGap: 12,
    padding: [0, 0, 0, 0],
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '60%'],
      center: ['32%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
      },
      emphasis: {
        disabled: true,
        scale: false,
      },
      data: data.map((item) => ({
        ...item,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: item.color,
              },
              {
                offset: 1,
                color: `${item.color}CC`,
              },
            ],
          },
        },
      })),
    },
  ],
});

onMounted(() => {
  if (chartRef.value) {
    renderEcharts(getChartOptions());
  }
});

watch(
  () => isDark.value,
  () => {
    renderEcharts(getChartOptions());
  },
);
</script>

<template>
  <div class="h-[140px] w-full">
    <EchartsUI ref="chartRef" class="!h-[140px]" />
  </div>
</template>

<style scoped>
:deep(.echarts) {
  height: 150px !important;
}
</style>
