<script lang="ts" setup>
import type { EChartsOption } from 'echarts';

import type { EchartsUIType } from '@vben/plugins/echarts';

import type { PackageAnalysisData } from '#/api/core/transactionStatistics';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';

import { Spin } from 'ant-design-vue';

import { getPackageAnalysisApi } from '#/api/core/transactionStatistics';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark } = usePreferences();
const loading = ref(true);

// 图表数据
const chartData = ref<PackageAnalysisData>({
  salesData: [],
  growthRateData: [],
  xAxisData: [],
});

// 格式化增长率，保留两位小数
const formatGrowthRate = (value: number): number => {
  return Number(value.toFixed(2));
};

// 获取图表配置
const getChartOptions = computed(() => {
  // 格式化增长率数据
  const formattedGrowthRateData = chartData.value.growthRateData.map((value) =>
    formatGrowthRate(value),
  );

  const options = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: ['销售数量', '增长率'],
      bottom: '0',
      left: 'center',
      textStyle: {
        color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: chartData.value.xAxisData,
        axisLine: {
          lineStyle: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.3)' : '#ccc',
          },
        },
        axisLabel: {
          color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
          interval: 0,
          rotate: 30,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '销售数量',
        axisLine: {
          lineStyle: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.3)' : '#ccc',
          },
        },
        axisLabel: {
          color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
          formatter: '{value}',
        },
        splitLine: {
          lineStyle: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.1)' : '#eee',
          },
        },
      },
      {
        type: 'value',
        name: '增长率',
        axisLine: {
          lineStyle: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.3)' : '#ccc',
          },
        },
        axisLabel: {
          color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
          formatter: '{value}%',
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '销售数量',
        type: 'bar',
        data: chartData.value.salesData,
        itemStyle: {
          color: '#91CDFF',
        },
        emphasis: {
          itemStyle: {
            color: '#91CDFF',
            opacity: 0.8,
          },
        },
        barWidth: '20%',
      },
      {
        name: '增长率',
        type: 'line',
        yAxisIndex: 1,
        data: formattedGrowthRateData,
        symbol: 'circle',
        symbolSize: 8,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#867AE5',
        },
        itemStyle: {
          color: '#867AE5',
          borderWidth: 2,
          borderColor: '#fff',
        },
        emphasis: {
          scale: true,
        },
      },
    ],
  };

  return options as EChartsOption;
});

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const res = await getPackageAnalysisApi();

    if (res.code === 1 && res.data) {
      chartData.value = res.data;
      renderEcharts(getChartOptions.value);
    }
  } catch (error) {
    console.error('获取套餐分析数据失败:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadData();
});

watch(
  () => isDark.value,
  () => {
    if (chartData.value.xAxisData.length > 0) {
      renderEcharts(getChartOptions.value);
    }
  },
);
</script>

<template>
  <div class="h-[180px] w-full">
    <Spin :spinning="loading">
      <EchartsUI ref="chartRef" class="!h-[180px]" />
    </Spin>
  </div>
</template>
