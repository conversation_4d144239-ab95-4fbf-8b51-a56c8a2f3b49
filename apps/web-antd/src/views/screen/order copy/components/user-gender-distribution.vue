<script lang="ts" setup>
import type { EChartsOption } from 'echarts';

import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark } = usePreferences();

const data = [
  { name: '男性', value: 42, color: '#91CDFF' },
  { name: '女性', value: 58, color: '#E58AFF' },
];

const getChartOptions = (): EChartsOption => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c}%',
    backgroundColor: isDark.value ? '#1f1f1f' : '#fff',
    borderColor: isDark.value ? '#303030' : '#e5e5e5',
  },
  legend: {
    orient: 'horizontal',
    bottom: '0',
    left: 'center',
    itemWidth: 8,
    itemHeight: 8,
    icon: 'circle',
    itemGap: 24,
    formatter: (name: string) => {
      const item = data.find((i) => i.name === name);
      return `${name}  ${item?.value}%`;
    },
    textStyle: {
      fontSize: 11,
      color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
    },
    padding: [0, 0, 0, 0],
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '60%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
      },
      emphasis: {
        disabled: true,
        scale: false,
      },
      data: data.map((item) => ({
        ...item,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: item.color,
              },
              {
                offset: 1,
                color: `${item.color}CC`,
              },
            ],
          },
        },
      })),
    },
  ],
});

onMounted(() => {
  renderEcharts(getChartOptions());
});

watch(
  () => isDark.value,
  () => {
    renderEcharts(getChartOptions());
  },
);
</script>

<template>
  <div class="h-[140px] w-full">
    <EchartsUI ref="chartRef" class="!h-[140px]" />
  </div>
</template>

<style scoped>
.gender-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin-bottom: 8px;
  font-size: 20px;
  font-weight: bold;
  border-radius: 50%;
}

.male {
  color: #6495ed;
  background-color: rgb(100 149 237 / 20%);
}

.female {
  color: #ffb6c1;
  background-color: rgb(255 182 193 / 20%);
}

.gender-label {
  color: #666;
  text-align: center;
}
</style>
