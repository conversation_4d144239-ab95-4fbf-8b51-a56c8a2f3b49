<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';
import { onMounted, ref, computed, watch } from 'vue';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';
import { getOperatorUsageDataApi, type OperatorUsageData } from '#/api/core/dashboard';
import { Spin } from 'ant-design-vue';

const props = defineProps<{
  operator: 'mobile' | 'telecom' | 'unicom' | 'radio'
}>();

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark, theme } = usePreferences();

const usageData = ref<OperatorUsageData>();
const loading = ref(true);

// 获取数据
const loadData = async () => {
  try {
    loading.value = true;
    const res = await getOperatorUsageDataApi();
    if (res.code === 1) {
      usageData.value = res.data;
    }
  } catch (error) {
    console.error('获取运营商数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 根据运营商类型获取对应数据
const currentOperatorData = computed(() => {
  if (!usageData.value) return [];
  
  switch (props.operator) {
    case 'mobile':
      return usageData.value.move.map((item: { this_day_dosage: number; click_date: string; }) => ({
        value: item.this_day_dosage,
        date: item.click_date
      }));
    case 'telecom':
      return usageData.value.telecommunication.map(item => ({
        value: item.this_day_dosage,
        date: item.click_date
      }));
    case 'unicom':
      return usageData.value.unicom.map(item => ({
        value: item.this_day_dosage,
        date: item.click_date
      }));
    case 'radio':
      return usageData.value.broadcast.map(item => ({
        value: item.this_day_dosage,
        date: item.click_date
      }));
    default:
      return [];
  }
});

// 根据主题计算颜色
const themeColors = computed(() => ({
  // 使用主题色作为主要颜色
  primary: isDark.value ? '#177ddc' : '#1890ff',
  // 背景和文字颜色
  splitLine: isDark.value ? 'rgba(253, 253, 253, 0.12)' : '#f0f0f0',
  text: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
  subText: isDark.value ? 'rgba(255, 255, 255, 0.45)' : '#999',
  // 渐变色
  areaStart: isDark.value ? 'rgba(23, 125, 220, 0.25)' : 'rgba(24, 144, 255, 0.3)',
  areaEnd: isDark.value ? 'rgba(23, 125, 220, 0.05)' : 'rgba(24, 144, 255, 0.05)',
  // 背景色
  background: isDark.value ? '#000' : '#fff',
}));

const getChartOptions = computed(() => ({
  backgroundColor: 'transparent',
  grid: {
    bottom: '8%',
    containLabel: true,
    left: '0%',
    right: '1%',
    top: '15%',
  },
  series: [
    {
      name: '流量使用',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      data: currentOperatorData.value.map(item => item.value),
      itemStyle: {
        color: themeColors.value.primary,
        borderWidth: 2
      },
      lineStyle: {
        width: 3,
        color: themeColors.value.primary,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: themeColors.value.areaStart
            },
            {
              offset: 1,
              color: themeColors.value.areaEnd
            }
          ]
        }
      },
      emphasis: {
        itemStyle: {
          color: themeColors.value.primary,
          borderColor: themeColors.value.background,
          borderWidth: 2,
          shadowColor: isDark.value ? 'rgba(23, 125, 220, 0.5)' : 'rgba(24, 144, 255, 0.5)',
          shadowBlur: 10
        }
      }
    }
  ],
  tooltip: {
    trigger: 'axis',
    backgroundColor: isDark.value ? '#1f1f1f' : '#fff',
    borderColor: themeColors.value.splitLine,
    borderWidth: 1,
    padding: [8, 5],
    textStyle: {
      color: themeColors.value.text
    },
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: themeColors.value.primary,
        width: 1,
        type: 'dashed'
      }
    },
    formatter: (params: any) => {
      const data = params[0];
      return `${data.name}<br/><span style="color: ${themeColors.value.primary}">◆</span> 流量使用：${data.value} GB`;
    }
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: currentOperatorData.value.map(item => item.date),
    axisLine: {
      lineStyle: {
        color: themeColors.value.splitLine
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: themeColors.value.subText,
      fontSize: 12
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: [themeColors.value.splitLine],
        type: 'dashed'
      }
    }
  },
  yAxis: {
    type: 'value',
    name: '流量(GB)',
    nameTextStyle: {
      color: themeColors.value.subText,
      fontSize: 12,
      padding: [0, 8, 10, 0],
      align: 'right'
    },
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: themeColors.value.subText,
      fontSize: 12,
      margin: 15,
      formatter: '{value} GB'
    },
    splitLine: {
      lineStyle: {
        color: [themeColors.value.splitLine],
        type: 'dashed'
      }
    }
  }
}));

// 初始化
onMounted(() => {
  loadData();
});

// 监听数据变化和主题变化重新渲染图表
watch(
  [() => isDark.value, () => theme.value, () => currentOperatorData.value],
  () => {
    renderEcharts(getChartOptions.value);
  }
);
</script>

<template>
  <div class="bg-card rounded-lg w-full">
    <div class="px-4">
      <Spin :spinning="loading">
        <EchartsUI ref="chartRef" class="h-[500px]" />
      </Spin>
    </div>
  </div>
</template> 
