<script lang="ts" setup>
import type { EChartsOption } from 'echarts';

import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import * as echarts from 'echarts';

// 导入地图数据
import chinaJSON from './china.json';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 修正数据格式，确保与地图数据的名称匹配
const data = [
  { name: '北京市', value: 2341 },
  { name: '上海市', value: 2018 },
  { name: '广东省', value: 1829 },
  { name: '江苏省', value: 1456 },
  { name: '浙江省', value: 1289 },
  { name: '四川省', value: 1087 },
  { name: '湖北省', value: 945 },
  { name: '福建省', value: 867 },
  { name: '山东省', value: 823 },
  { name: '河南省', value: 765 },
  { name: '陕西省', value: 689 },
  { name: '云南省', value: 578 },
  { name: '河北省', value: 645 },
  { name: '安徽省', value: 590 },
  { name: '湖南省', value: 512 },
  { name: '江西省', value: 498 },
  { name: '贵州省', value: 456 },
  { name: '重庆市', value: 445 },
  { name: '天津市', value: 389 },
  { name: '广西壮族自治区', value: 367 },
  { name: '内蒙古自治区', value: 298 },
  { name: '山西省', value: 276 },
  { name: '黑龙江省', value: 265 },
  { name: '辽宁省', value: 245 },
  { name: '吉林省', value: 234 },
  { name: '新疆维吾尔自治区', value: 189 },
  { name: '甘肃省', value: 167 },
  { name: '海南省', value: 156 },
  { name: '宁夏回族自治区', value: 145 },
  { name: '青海省', value: 134 },
  { name: '西藏自治区', value: 98 },
];

const getChartOptions = (): EChartsOption => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} 单',
    confine: true, // 确保提示框不会超出容器
  },
  visualMap: {
    min: 0,
    max: 2500,
    left: '5%',
    bottom: '10%',
    text: ['高', '低'],
    calculable: true,
    inRange: {
      color: ['#E8F4FF', '#91CDFF', '#867AE5'],
    },
    textStyle: {
      color: '#666',
    },
    itemWidth: 15,
    itemHeight: 70,
  },
  geo: {
    map: 'china',
    roam: false,
    scaleLimit: {
      min: 1,
      max: 2,
    },
    zoom: 1,
    top: '10%',
    bottom: '15%',
    layoutCenter: ['50%', '50%'],
    layoutSize: '100%',
    label: {
      show: false,
    },
    itemStyle: {
      areaColor: '#E8F4FF',
      borderColor: '#fff',
      borderWidth: 1,
    },
    emphasis: {
      itemStyle: {
        areaColor: '#867AE5',
      },
      label: {
        show: true,
        color: '#fff',
      },
    },
  },
  series: [
    {
      name: '订单分布',
      type: 'map',
      geoIndex: 0,
      data,
    },
  ],
});

onMounted(() => {
  // 注册地图
  echarts.registerMap('china', chinaJSON as any);
  if (chartRef.value) {
    renderEcharts(getChartOptions());
  }
});
</script>

<template>
  <div class="h-[180px] w-full">
    <EchartsUI ref="chartRef" class="!h-[180px]" />
  </div>
</template>

<style scoped>
:deep(.echarts) {
  height: 180px !important;
}
</style>
