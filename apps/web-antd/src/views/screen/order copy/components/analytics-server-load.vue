<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from 'vue';

import { MdiCpu, MdiHarddisk, MdiMemory, MdiSpeedometer } from '@vben/icons';
import { usePreferences } from '@vben/preferences';

import { Descriptions, Progress } from 'ant-design-vue';

import { getServerStatusApi } from '#/api/core/monitor';

const { isDark } = usePreferences();
const loading = ref(true);
const worker = ref<Worker>();

interface ServerData {
  cpu: number;
  memory: number;
  disk: number;
  load: number; // 新增：系统负载
}

const serverData = ref<ServerData>();

const loadData = async () => {
  try {
    const res = await getServerStatusApi();

    if (res.code === 1 && res.data) {
      const { disk_total, disk_free, memory_percent, cpu_total } = res.data;

      serverData.value = {
        cpu: Number((cpu_total / 2).toFixed(1)), // CPU使用率 = 总负载/核心数
        memory: Number(memory_percent.toFixed(1)),
        disk: Number(
          (((disk_total - disk_free) / disk_total) * 100).toFixed(1),
        ),
        load: Number(cpu_total.toFixed(1)), // 原始负载值
      };
    }
  } catch (error) {
    console.error('Failed to fetch server status:', error);
  }
};

// 初始化 Web Worker
const initWorker = () => {
  if (typeof Worker !== 'undefined') {
    worker.value = new Worker(
      new URL('#/workers/serverMonitor.worker.ts', import.meta.url),
      { type: 'module' },
    );

    worker.value.onmessage = async (event) => {
      if (event.data.type === 'fetch') {
        await loadData();
      }
    };

    // 开始轮询
    worker.value.postMessage({ type: 'start' });
    loading.value = false;
  }
};

// 清理 Worker
const cleanupWorker = () => {
  if (worker.value) {
    worker.value.postMessage({ type: 'stop' });
    worker.value.terminate();
    worker.value = undefined;
  }
};

const themeColors = computed(() => ({
  success: isDark.value ? '#49aa19' : '#52c41a',
  warning: isDark.value ? '#d89614' : '#faad14',
  error: isDark.value ? '#a61d24' : '#ff4d4f',
}));

const statusItems = computed(() => [
  {
    icon: MdiCpu,
    label: 'CPU使用率',
    value: serverData.value?.cpu || 0,
    color: getStatusColor(serverData.value?.cpu || 0),
    suffix: '%',
  },
  {
    icon: MdiMemory,
    label: '内存使用率',
    value: serverData.value?.memory || 0,
    color: getStatusColor(serverData.value?.memory || 0),
    suffix: '%',
  },
  {
    icon: MdiHarddisk,
    label: '磁盘使用率',
    value: serverData.value?.disk || 0,
    color: getStatusColor(serverData.value?.disk || 0),
    suffix: '%',
  },
  {
    icon: MdiSpeedometer,
    label: '系统负载',
    value: serverData.value?.load || 0,
    color: getStatusColor((serverData.value?.load || 0) * 10),
    suffix: '',
  },
]);

function getStatusColor(value: number) {
  if (value >= 80) return '#ff4d4f';
  if (value >= 60) return '#faad14';
  return '#52c41a';
}

onMounted(() => {
  initWorker();
});

onUnmounted(() => {
  cleanupWorker();
});
</script>

<template>
  <div class="server-monitor" :class="{ 'is-dark': isDark }">
    <Descriptions
      :column="2"
      bordered
      size="middle"
      :label-style="{
        color: isDark ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.85)',
        fontWeight: 500,
      }"
      :content-style="{
        color: isDark ? 'rgba(255, 255, 255, 0.65)' : 'rgba(0, 0, 0, 0.65)',
      }"
    >
      <Descriptions.Item
        v-for="item in statusItems"
        :key="item.label"
        :label="item.label"
        :span="1"
      >
        <div class="status-content">
          <div class="status-value">
            <component
              :is="item.icon"
              class="status-icon"
              :style="{ color: item.color }"
            />
            <span>{{ item.value }}{{ item.suffix }}</span>
          </div>
          <Progress
            :percent="item.label === '系统负载' ? item.value * 10 : item.value"
            :stroke-color="item.color"
            :track-color="
              isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)'
            "
            :show-info="false"
            :size="[300, 20]"
            :stroke-width="6"
          />
        </div>
      </Descriptions.Item>
    </Descriptions>
  </div>
</template>

<style lang="scss" scoped>
.server-monitor {
  padding: 16px;
  // background: #f0f2f5;
  border-radius: 8px;

  &.is-dark {
    // background: #1e1e1e;
  }
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-value {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 15px;
}

.status-icon {
  font-size: 18px;
}

:deep(.ant-descriptions) {
  background: transparent;
}

:deep(.ant-descriptions-item-label) {
  background: v-bind(
    "isDark ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'"
  );
}

:deep(.ant-descriptions-item-content) {
  background: v-bind(
    "isDark ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'"
  );
}

:deep(.ant-progress-outer) {
  padding-right: 0 !important;
  margin-right: 0 !important;
}

:deep(.ant-progress-inner) {
  background: v-bind(
    "isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)'"
  );
  border-radius: 4px;
}

:deep(.ant-progress-bg) {
  border-radius: 4px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
