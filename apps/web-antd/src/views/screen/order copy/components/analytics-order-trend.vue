<script lang="ts" setup>
import type { EChartsOption } from 'echarts';
import type { EchartsUIType } from '@vben/plugins/echarts';
import { onMounted, ref, computed, watch } from 'vue';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';
import { Spin } from 'ant-design-vue';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark, theme } = usePreferences();
const loading = ref(true);

// 模拟数据
const dates = ['03-20', '03-21', '03-22', '03-23', '03-24', '03-25', '03-26'];
const orderData = ref({
  orderCount: [150, 230, 224, 218, 135, 147, 260],
  completeRate: [0.93, 0.88, 0.91, 0.94, 0.89, 0.92, 0.95]
});

const themeColors = computed(() => ({
  primary: isDark.value ? '#177ddc' : '#1890ff',
  secondary: isDark.value ? '#52c41a' : '#73d13d',
  splitLine: isDark.value ? 'rgba(253, 253, 253, 0.12)' : '#f0f0f0',
  text: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
}));

const getChartOptions = computed<EChartsOption>(() => ({
  backgroundColor: 'transparent',
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: themeColors.value.splitLine
      }
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  legend: {
    data: ['订单数量', '完成率'],
    textStyle: {
      color: themeColors.value.text
    }
  },
  xAxis: [
    {
      type: 'category',
      data: dates,
      axisPointer: {
        type: 'shadow'
      },
      axisLine: {
        lineStyle: {
          color: themeColors.value.splitLine
        }
      },
      axisTick: {
        show: false
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '订单数量',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: themeColors.value.splitLine
        }
      }
    },
    {
      type: 'value',
      name: '完成率',
      min: 0,
      max: 1,
      interval: 0.2,
      axisLabel: {
        formatter: '{value * 100}%'
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    }
  ],
  series: [
    {
      name: '订单数量',
      type: 'bar',
      data: orderData.value.orderCount,
      itemStyle: {
        color: themeColors.value.primary,
        borderRadius: [4, 4, 0, 0]
      }
    },
    {
      name: '完成率',
      type: 'line',
      yAxisIndex: 1,
      data: orderData.value.completeRate,
      smooth: true,
      symbolSize: 8,
      itemStyle: {
        color: themeColors.value.secondary
      },
      lineStyle: {
        width: 3
      }
    }
  ]
}));

onMounted(async () => {
  await new Promise(resolve => setTimeout(resolve, 500));
  loading.value = false;
  renderEcharts(getChartOptions.value);
});

watch(
  [() => isDark.value, () => theme.value],
  () => {
    renderEcharts(getChartOptions.value);
  }
);
</script>

<template>
  <div class="bg-card rounded-lg w-full">
    <div class="px-4">
      <Spin :spinning="loading">
        <EchartsUI ref="chartRef" class="h-[400px]" />
      </Spin>
    </div>
  </div>
</template> 
