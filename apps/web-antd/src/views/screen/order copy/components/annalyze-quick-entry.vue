<script lang="ts" setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';

import {
  MdiAccountMultiplePlus,
  MdiCreditCardPlus,
  MdiFileDocumentPlus,
  MdiPlus,
} from '@vben/icons';

const router = useRouter();

const shortcutItems = computed(() => [
  {
    icon: MdiAccountMultiplePlus,
    label: '新增客户',
    path: '/customer/list/add',
  },
  {
    icon: MdiPlus,
    label: '新增套餐',
    path: '/package/list/add',
  },
  {
    icon: MdiCreditCardPlus,
    label: '新增卡片',
    path: '/sim/list/add',
  },
  {
    icon: MdiFileDocumentPlus,
    label: '新增订单',
    path: '/order/list/add',
  },
]);

const handleClick = (path: string) => {
  router.push(path);
};
</script>

<template>
  <div class="shortcut-list">
    <div
      v-for="item in shortcutItems"
      :key="item.label"
      class="shortcut-item"
      @click="handleClick(item.path)"
    >
      <div class="icon-wrapper">
        <component :is="item.icon" />
      </div>
      <span class="label">{{ item.label }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// .shortcut-list {
//   @apply grid grid-cols-2 gap-4;
// }

// .shortcut-item {
//   @apply flex cursor-pointer flex-col items-center justify-center rounded-lg p-4 transition-all duration-300;
//   @apply hover:bg-gray-50 dark:hover:bg-gray-800;

//   .icon-wrapper {
//     @apply mb-2 text-2xl text-gray-600 dark:text-gray-300;
//   }

//   .label {
//     @apply text-sm font-medium text-gray-700 dark:text-gray-300;
//   }
// }
</style>
