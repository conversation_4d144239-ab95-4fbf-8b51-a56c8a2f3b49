<script lang="ts" setup>
import type { EChartsOption } from 'echarts';

import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark } = usePreferences();

// 模拟数据
const weekData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
const completedData = [320, 302, 301, 334, 390, 330, 320];
const processingData = [120, 132, 101, 134, 90, 230, 210];
const pendingData = [220, 182, 191, 234, 290, 330, 310];
const cancelledData = [150, 212, 201, 154, 190, 330, 410];

const getChartOptions = (): EChartsOption => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
    },
  },
  legend: {
    data: ['已完成', '处理中', '待处理', '已取消'],
    bottom: '0',
    left: 'center',
    textStyle: {
      color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
      fontSize: 12,
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    top: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: true,
    data: weekData,
    axisLine: {
      lineStyle: {
        color: '#E5E6EB',
      },
    },
    axisLabel: {
      color: '#666',
    },
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#666',
    },
    splitLine: {
      lineStyle: {
        color: '#E5E6EB',
        type: 'dashed',
      },
    },
  },
  series: [
    {
      name: '已完成',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#52C41A',
      },
      lineStyle: {
        width: 2,
        color: '#52C41A',
      },
      data: completedData,
    },
    {
      name: '处理中',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#1890FF',
      },
      lineStyle: {
        width: 2,
        color: '#1890FF',
      },
      data: processingData,
    },
    {
      name: '待处理',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#722ED1',
      },
      lineStyle: {
        width: 2,
        color: '#722ED1',
      },
      data: pendingData,
    },
    {
      name: '已取消',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#F5222D',
      },
      lineStyle: {
        width: 2,
        color: '#F5222D',
      },
      data: cancelledData,
    },
  ],
});

onMounted(() => {
  renderEcharts(getChartOptions());
});

watch(
  () => isDark.value,
  () => {
    renderEcharts(getChartOptions());
  },
);
</script>

<template>
  <div class="h-[180px] w-full">
    <EchartsUI ref="chartRef" class="!h-[180px]" />
  </div>
</template>
