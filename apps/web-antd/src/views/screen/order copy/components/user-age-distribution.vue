<script lang="ts" setup>
import type { EChartsOption } from 'echarts';

import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark } = usePreferences();

const data = [
  { name: '18-24岁', value: 25, color: '#867AE5' },
  { name: '25-34岁', value: 38, color: '#91CDFF' },
  { name: '35-44岁', value: 22, color: '#E58AFF' },
  { name: '45岁以上', value: 15, color: '#FFE4B0' },
];

const getChartOptions = (): EChartsOption => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c}%',
    backgroundColor: isDark.value ? '#1f1f1f' : '#fff',
    borderColor: isDark.value ? '#303030' : '#e5e5e5',
  },
  legend: {
    orient: 'vertical',
    right: '2%',
    top: 'middle',
    itemWidth: 8,
    itemHeight: 8,
    icon: 'circle',
    formatter: (name: string) => {
      const item = data.find((i) => i.name === name);
      return `${name}  ${item?.value}%`;
    },
    textStyle: {
      fontSize: 11,
      color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
    },
    itemGap: 12,
    padding: [0, 0, 0, 0],
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '60%'],
      center: ['32%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
      },
      emphasis: {
        disabled: true,
        scale: false,
      },
      data: data.map((item) => ({
        ...item,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: item.color,
              },
              {
                offset: 1,
                color: `${item.color}CC`,
              },
            ],
          },
        },
      })),
    },
  ],
});

onMounted(() => {
  renderEcharts(getChartOptions());
});

watch(
  () => isDark.value,
  () => {
    renderEcharts(getChartOptions());
  },
);
</script>

<template>
  <div class="h-[140px] w-full">
    <EchartsUI ref="chartRef" class="!h-[140px]" />
  </div>
</template>

<style scoped>
:deep(.echarts) {
  height: 150px !important;
}
</style>
