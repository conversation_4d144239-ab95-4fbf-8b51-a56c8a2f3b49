<script lang="ts" setup>
import type { AnalysisOverviewItem } from '@vben/common-ui';
import type { TabOption } from '@vben/types';

import type { OverviewData } from '#/api/core/dashboard';

import { computed, onMounted, ref } from 'vue';

import {
  AnalysisChartCard,
  AnalysisChartsTabs,
  AnalysisOverview,
} from '@vben/common-ui';
import {
  MdiAccountGroup,
  MdiCurrencyUsd,
  MdiFileDocument,
  MdiSimOutline,
} from '@vben/icons';

import { getOverviewDataApi } from '#/api/core/dashboard';

import AnalyticsOperatorStatus from './analytics-operator-status.vue';
import AnalyticsOperatorUsage from './analytics-operator-usage.vue';
import AnalyticsRecentPayment from './analytics-recent-payment.vue';
import AnalyticsServerLoad from './analytics-server-load.vue';
import AnalyticsMemoryUsage from './analytics-memory-usage.vue';
import AnalyticsVisitsSource from './analytics-visits-source.vue';
import AnalyticsYearlyOrders from './analytics-yearly-orders.vue';

const overviewData = ref<OverviewData>();

// 获取数据
const loadData = async () => {
  try {
    const res = await getOverviewDataApi();
    if (res.code === 1) {
      overviewData.value = res.data;
    }
  } catch (error) {
    console.error(error);
  }
};

// 计算展示数据
const overviewItems = computed<AnalysisOverviewItem[]>(() => {
  if (!overviewData.value) return [];

  return [
    {
      icon: MdiCurrencyUsd,
      title: '财务统计',
      value: overviewData.value.todaySIncome,
      valueTitle: '今日收入',
      totalValue: overviewData.value.totalIncome,
      totalTitle: '总收入',
      totalTitle2: '总发放',
      totalValue2: overviewData.value.totalDistribution,
      prefix: '￥',
      totalPrefix: '￥',
      total2Prefix: '￥',
    },
    {
      icon: MdiSimOutline,
      title: 'SIM卡统计',
      value: overviewData.value.cardPreStorage,
      valueTitle: '今日激活',
      totalValue: overviewData.value.totalNumberOfCards,
      totalTitle: '总数量',
      totalTitle2: '已激活',
      totalValue2: overviewData.value.numberOfNetworks,
    },
    {
      icon: MdiAccountGroup,
      title: '客户统计',
      value: overviewData.value.ordinaryCustomers,
      valueTitle: '今日新增',
      totalValue: overviewData.value.totalNumberOfCustomers,
      totalTitle: '总客户',
      totalTitle2: '活跃客户',
      totalValue2: overviewData.value.capableCustomers,
    },
    {
      icon: MdiFileDocument,
      title: '订单统计',
      value: overviewData.value.outbound,
      valueTitle: '今日订单',
      totalValue: overviewData.value.totalNumberOfPackageOrders,
      totalTitle: '总订单',
      totalTitle2: '已完成',
      totalValue2: overviewData.value.totalNumberOfProductOrders,
    },
  ];
});

onMounted(() => {
  loadData();
});

const chartTabs: TabOption[] = [
  {
    label: '中国移动',
    value: 'mobile',
  },
  {
    label: '中国电信',
    value: 'telecom',
  },
  {
    label: '中国联通',
    value: 'unicom',
  },
  {
    label: '中国广电',
    value: 'radio',
  },
];
</script>

<template>
  <div class="p-5">
    <AnalysisOverview :items="overviewItems" />
    <AnalysisChartsTabs :tabs="chartTabs" class="mt-5">
      <template #mobile>
        <AnalyticsOperatorUsage operator="mobile" />
      </template>
      <template #telecom>
        <AnalyticsOperatorUsage operator="telecom" />
      </template>
      <template #unicom>
        <AnalyticsOperatorUsage operator="unicom" />
      </template>
      <template #radio>
        <AnalyticsOperatorUsage operator="radio" />
      </template>
    </AnalysisChartsTabs>

    <div class="mt-5 w-full md:flex">
      <AnalysisChartCard
        class="mt-5 md:mr-4 md:mt-0 md:w-2/3"
        title="近七日收款"
      >
        <AnalyticsRecentPayment />
      </AnalysisChartCard>
      <AnalysisChartCard class="mt-5 md:mt-0 md:w-1/3" title="SIM卡分布">
        <AnalyticsVisitsSource />
      </AnalysisChartCard>
    </div>
    <div class="mt-5 w-full md:flex">
      <AnalysisChartCard
        class="mt-5 md:mr-4 md:mt-0 md:w-1/3"
        title="服务器负载"
      >
        <AnalyticsServerLoad />
      </AnalysisChartCard>
      <AnalysisChartCard
        class="mt-5 md:mr-4 md:mt-0 md:w-1/3"
        title="内存使用率"
      >
        <AnalyticsMemoryUsage />
      </AnalysisChartCard>
      <AnalysisChartCard class="mt-5 md:mt-0 md:w-2/3" title="年度订单分析">
        <AnalyticsYearlyOrders />
      </AnalysisChartCard>
    </div>
    <div class="mt-5">
      <AnalyticsOperatorStatus />
    </div>
  </div>
</template>
