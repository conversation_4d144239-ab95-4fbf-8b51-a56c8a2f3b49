<script lang="ts" setup>
import type { MemoryUsageInfo } from '#/api/core/monitor';

import { computed, onMounted, onUnmounted, ref } from 'vue';

import { MdiMemory } from '@vben/icons';
import { usePreferences } from '@vben/preferences';

import { Card, Descriptions, Progress } from 'ant-design-vue';
import { LineChart } from 'echarts/charts';
import {
  GridComponent,
  LegendComponent,
  TooltipComponent,
} from 'echarts/components';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';

import { getServerMemoryUsage } from '#/api/core/monitor';

use([
  CanvasRenderer,
  LineChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
]);

const { isDark } = usePreferences();
const loading = ref(true);
const worker = ref<Worker>();
const memoryData = ref<MemoryUsageInfo>();

const loadData = async () => {
  try {
    loading.value = true;
    const res = await getServerMemoryUsage();
    console.log(res, 'getServerMemoryUsage');
    if (res.code === 1 && res.data) {
      memoryData.value = res.data;
    }
  } catch (error) {
    console.error('Failed to fetch memory usage:', error);
  } finally {
    loading.value = false;
  }
};

// 初始化 Web Worker
const initWorker = () => {
  if (typeof Worker !== 'undefined') {
    worker.value = new Worker(
      new URL('#/workers/serverMonitor.worker.ts', import.meta.url),
      { type: 'module' },
    );

    worker.value.onmessage = async (event) => {
      if (event.data.type === 'fetch') {
        await loadData();
      }
    };

    worker.value.postMessage({ type: 'start' });
    loading.value = false;
  }
};

// 清理 Worker
const cleanupWorker = () => {
  if (worker.value) {
    worker.value.postMessage({ type: 'stop' });
    worker.value.terminate();
    worker.value = undefined;
  }
};

const memoryItems = computed(() => [
  {
    label: '当前内存使用率',
    value: memoryData.value?.current_memory_usage_percent || 0,
    color: getStatusColor(memoryData.value?.current_memory_usage_percent || 0),
  },
  {
    label: '缓存内存',
    value: memoryData.value?.cached_memory_percent || 0,
    color: '#1890ff',
  },
  {
    label: '缓冲区',
    value: memoryData.value?.buffer_memory_percent || 0,
    color: '#13c2c2',
  },
  {
    label: '已用内存',
    value: memoryData.value?.used_memory_percent || 0,
    color: '#722ed1',
  },
  {
    label: '可用内存',
    value: memoryData.value?.available_memory_percent || 0,
    color: '#52c41a',
  },
]);

function getStatusColor(value: number) {
  if (value >= 80) return '#ff4d4f';
  if (value >= 60) return '#faad14';
  return '#52c41a';
}

onMounted(async () => {
  await loadData();
  initWorker();
});

onUnmounted(() => {
  cleanupWorker();
});
</script>

<template>
  <div class="memory-monitor" :class="{ 'is-dark': isDark }">
    <Card :bordered="false" class="memory-card">
      <Descriptions
        :column="3"
        bordered
        size="middle"
        :label-style="{
          color: isDark ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.85)',
          fontWeight: 500,
        }"
        :content-style="{
          color: isDark ? 'rgba(255, 255, 255, 0.65)' : 'rgba(0, 0, 0, 0.65)',
        }"
      >
        <Descriptions.Item
          v-for="item in memoryItems"
          :key="item.label"
          :label="item.label"
          :span="1"
        >
          <div class="status-content">
            <div class="status-value">
              <MdiMemory class="status-icon" :style="{ color: item.color }" />
              <span>{{ item.value }}%</span>
            </div>
            <Progress
              :percent="item.value"
              :stroke-color="item.color"
              :track-color="
                isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)'
              "
              :show-info="false"
              :size="[200, 20]"
              :stroke-width="6"
            />
          </div>
        </Descriptions.Item>
      </Descriptions>
    </Card>
  </div>
</template>

<style lang="scss" scoped>
.memory-monitor {
  padding: 16px;
  border-radius: 8px;

  &.is-dark {
    // background: #1e1e1e;
  }
}

.memory-card {
  :deep(.ant-card-body) {
    padding: 24px;
  }
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-value {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 15px;
}

.status-icon {
  font-size: 18px;
}

.chart-container {
  height: 300px;
  margin-top: 24px;
}

.chart {
  height: 100%;
}

:deep(.ant-descriptions) {
  background: transparent;
}

:deep(.ant-descriptions-row:last-child) {
  .ant-descriptions-item {
    border-bottom: none;
  }
}
</style>
