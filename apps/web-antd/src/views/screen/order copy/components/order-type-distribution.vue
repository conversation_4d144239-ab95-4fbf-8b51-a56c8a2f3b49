<script setup lang="ts">
import type { EChartsOption } from 'echarts';

import type { EchartsUIType } from '@vben/plugins/echarts';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';

import { Spin } from 'ant-design-vue';

import { getOrderTypeDistributionApi } from '#/api/core/transactionStatistics';

// 数据类型
interface OrderTypeItem {
  name: string;
  value: number;
}

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark } = usePreferences();
const loading = ref(true);

// 定义数据颜色映射
const typeColors: Record<string, string> = {
  卡片套餐订单: '#867AE5',
  设备订单: '#91CDFF',
  话费充值订单: '#E58AFF',
  其他订单: '#FFE4B0',
};

// 备用颜色
const fallbackColors = [
  '#867AE5',
  '#91CDFF',
  '#E58AFF',
  '#FFE4B0',
  '#6DD400',
  '#FFC233',
  '#FB7293',
];

// 订单类型数据
const orderTypeData = ref<OrderTypeItem[]>([]);

// 计算总数
const total = computed(() => {
  return orderTypeData.value.reduce((acc, cur) => acc + cur.value, 0);
});

// 获取图表配置
const getChartOptions = computed(() => {
  const chartOption = {
    grid: {
      left: '3%',
      right: '15%',
      top: '10%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: {
      show: false,
      type: 'value',
      max: total.value,
    },
    yAxis: {
      type: 'category',
      data: orderTypeData.value.map((item) => item.name),
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
        fontSize: 12,
        margin: 20,
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: 12,
        data: orderTypeData.value.map((item, index) => {
          const color =
            typeColors[item.name] ||
            fallbackColors[index % fallbackColors.length];
          return {
            value: item.value,
            itemStyle: {
              color,
              borderRadius: 6,
            },
          };
        }),
        label: {
          show: true,
          position: 'right',
          color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
          formatter: (params: any) => {
            const percentage = ((params.value / total.value) * 100).toFixed(1);
            return `${params.value} (${percentage}%)`;
          },
          fontSize: 12,
        },
        showBackground: true,
        backgroundStyle: {
          color: isDark.value ? 'rgba(255, 255, 255, 0.05)' : '#f5f5f5',
          borderRadius: 6,
        },
        emphasis: {
          itemStyle: {
            opacity: 0.9,
            shadowBlur: 10,
            shadowColor: 'rgba(0,0,0,0.2)',
          },
        },
      },
    ],
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const { name, value } = params[0];
        const percentage = ((value / total.value) * 100).toFixed(1);
        return `${name}<br/>数量：${value}<br/>占比：${percentage}%`;
      },
      backgroundColor: isDark.value ? '#1f1f1f' : '#fff',
      borderColor: isDark.value ? '#303030' : '#e5e5e5',
      textStyle: {
        color: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
      },
    },
  };

  return chartOption as EChartsOption;
});

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const res = await getOrderTypeDistributionApi();

    if (res.code === 1 && res.data?.orderTypeData) {
      // 转换API数据，将字符串值转为数字
      orderTypeData.value = res.data.orderTypeData.map((item) => ({
        name: item.name,
        value: Number.parseFloat(item.value),
      }));
      renderEcharts(getChartOptions.value);
    }
  } catch (error) {
    console.error('获取订单类型分布数据失败:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadData();
});

watch(
  () => isDark.value,
  () => {
    if (orderTypeData.value.length > 0) {
      renderEcharts(getChartOptions.value);
    }
  },
);
</script>

<template>
  <div class="h-[180px] w-full">
    <Spin :spinning="loading">
      <EchartsUI ref="chartRef" class="!h-[180px]" />
    </Spin>
  </div>
</template>

<style scoped>
:deep(.echarts) {
  height: 150px !important;
}
</style>
