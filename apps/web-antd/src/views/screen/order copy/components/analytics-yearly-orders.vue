<script lang="ts" setup>
import type { EChartsOption } from 'echarts';

import type { EchartsUIType } from '@vben/plugins/echarts';

// 不再需要OrderData和ApiResponse，使用transactionStatistics中定义的接口
import type { YearOrderAnalysisData } from '#/api/core/transactionStatistics';

import { computed, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { usePreferences } from '@vben/preferences';

import { Spin } from 'ant-design-vue';

import { getYearOrderAnalysisApi } from '#/api/core/transactionStatistics';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);
const { isDark } = usePreferences();
const loading = ref(true);
const chartData = ref<YearOrderAnalysisData>({
  series: [],
  xAxisData: [],
});

const themeColors = computed(() => ({
  device: '#867AE5',
  sim: '#91CDFF',
  package: '#E58AFF',
  recharge: '#FFE4B0',
  text: isDark.value ? 'rgba(255, 255, 255, 0.85)' : '#666',
  splitLine: isDark.value ? 'rgba(253, 253, 253, 0.12)' : '#f0f0f0',
}));

// 获取系列颜色
const getSeriesColor = (seriesName: string): string => {
  if (seriesName === '设备订单') return themeColors.value.device;
  if (seriesName === 'SIM卡订单') return themeColors.value.sim;
  if (seriesName === '套餐订单') return themeColors.value.package;
  return themeColors.value.recharge; // 话费订单
};

// 解决类型问题，使用正确的TooltipFormatterCallback类型
const getChartOptions = computed<EChartsOption>(() => {
  return {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: isDark.value ? '#1f1f1f' : '#fff',
      borderColor: isDark.value ? '#303030' : '#e5e5e5',
      formatter: (params) => {
        const paramArray = Array.isArray(params) ? params : [params];
        const date = paramArray[0]?.name || '';
        const html = [
          `<div style="font-weight: 500; margin-bottom: 8px;">${date}</div>`,
        ];

        paramArray.forEach((param) => {
          const color = getSeriesColor(param.seriesName as string);
          html.push(`
            <div style="margin-top: 4px;">
              <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background: ${color}; margin-right: 8px;"></span>
              <span>${param.seriesName}：${param.value || 0}</span>
            </div>
          `);
        });

        return html.join('');
      },
    },
    legend: {
      data: chartData.value.series.map((item) => item.name),
      textStyle: {
        color: themeColors.value.text,
        fontSize: 12,
      },
      icon: 'circle',
      itemWidth: 8,
      itemHeight: 8,
      itemGap: 24,
      bottom: '0%',
      left: 'center',
    },
    grid: {
      top: '8%',
      left: '3%',
      right: '3%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: chartData.value.xAxisData,
      axisLine: {
        lineStyle: {
          color: themeColors.value.splitLine,
        },
      },
      axisTick: { show: false },
      axisLabel: {
        color: themeColors.value.text,
        fontSize: 12,
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: themeColors.value.splitLine,
          type: 'dashed',
        },
      },
      axisLabel: {
        color: themeColors.value.text,
        fontSize: 12,
        formatter: (value) => {
          return value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value;
        },
      },
    },
    series: chartData.value.series.map((series) => ({
      name: series.name,
      type: 'bar',
      data: series.data,
      itemStyle: {
        color: getSeriesColor(series.name),
        borderRadius: [4, 4, 0, 0],
      },
      barWidth: 12,
      emphasis: {
        itemStyle: {
          opacity: 0.8,
        },
      },
    })),
  };
});

// 获取数据
const loadData = async () => {
  try {
    loading.value = true;
    const res = await getYearOrderAnalysisApi();

    if (res.code === 1 && res.data) {
      chartData.value = res.data;
      renderEcharts(getChartOptions.value);
    }
  } catch (error) {
    console.error('获取年度订单数据失败:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadData();
});

watch(
  () => isDark.value,
  () => {
    if (chartData.value.series.length > 0) {
      renderEcharts(getChartOptions.value);
    }
  },
);
</script>

<template>
  <div class="w-full rounded-lg">
    <div class="h-[360px]">
      <Spin :spinning="loading">
        <EchartsUI ref="chartRef" class="!h-[360px]" />
      </Spin>
    </div>
  </div>
</template>
