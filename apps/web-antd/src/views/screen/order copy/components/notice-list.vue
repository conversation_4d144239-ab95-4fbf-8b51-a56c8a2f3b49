<script lang="ts" setup>
import { ref } from 'vue';

import { Badge } from 'ant-design-vue';

// 模拟公告数据
const notices = ref([
  {
    id: 1,
    title: '系统维护通知',
    type: 'warning',
    date: '2024-03-20',
    content: '系统将于本周六凌晨2点进行例行维护',
  },
  {
    id: 2,
    title: '新功能上线公告',
    type: 'success',
    date: '2024-03-19',
    content: '订单管理系统新增批量处理功能',
  },
  {
    id: 3,
    title: '价格调整通知',
    type: 'info',
    date: '2024-03-18',
    content: '部分商品价格将于下月起调整',
  },
  {
    id: 4,
    title: '紧急通知',
    type: 'error',
    date: '2024-03-17',
    content: '请及时处理待审核订单',
  },
  {
    id: 5,
    title: '系统升级完成',
    type: 'success',
    date: '2024-03-16',
    content: '系统性能优化升级已完成',
  },
]);

// 获取对应的徽标状态颜色
const getBadgeStatus = (type: string) => {
  const statusMap: Record<
    string,
    'default' | 'error' | 'processing' | 'success' | 'warning'
  > = {
    success: 'success',
    warning: 'warning',
    error: 'error',
    info: 'processing',
  };
  return statusMap[type] || 'default';
};
</script>

<template>
  <div class="notice-list-container h-full overflow-hidden">
    <div class="notice-list" :style="{ '--notice-count': notices.length }">
      <div
        v-for="notice in [...notices, ...notices]"
        :key="notice.id"
        class="notice-item hover:bg-primary/5 group cursor-pointer rounded-lg p-3 transition-all"
      >
        <div class="mb-1 flex items-center justify-between">
          <div class="flex items-center gap-2">
            <Badge :status="getBadgeStatus(notice.type)" />
            <span
              class="text-sm font-medium text-[#333] transition-colors dark:text-white"
            >
              {{ notice.title }}
            </span>
          </div>
          <span class="text-xs text-[#999]">{{ notice.date }}</span>
        </div>
        <div class="text-xs text-[#666] transition-colors dark:text-[#999]">
          {{ notice.content }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.notice-list-container {
  position: relative;
  padding: 0 4px;
}

.notice-list {
  animation: scroll-y 20s linear infinite;
  will-change: transform;
}

.notice-list:hover {
  animation-play-state: paused;
}

.notice-item {
  border: 1px solid transparent;
}

.notice-item:hover {
  border-color: var(--ant-primary-color);
}

@keyframes scroll-y {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(calc(-100% / 2));
  }
}
</style>
