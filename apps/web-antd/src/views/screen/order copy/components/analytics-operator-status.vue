<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { Card, Progress } from 'ant-design-vue';
import { usePreferences } from '@vben/preferences';
import { getCardStatusApi } from '#/api/core/dashboard';

interface OperatorStatus {
  activated: number;
  inactivated: number;
  suspended: number;
  total: number;
}

interface OperatorData {
  mobile: OperatorStatus;
  telecom: OperatorStatus;
  unicom: OperatorStatus;
  broadcast: OperatorStatus;
}

const { isDark } = usePreferences();
const loading = ref(true);
const statusData = ref<OperatorData>({
  mobile: { activated: 0, inactivated: 0, suspended: 0, total: 0 },
  telecom: { activated: 0, inactivated: 0, suspended: 0, total: 0 },
  unicom: { activated: 0, inactivated: 0, suspended: 0, total: 0 },
  broadcast: { activated: 0, inactivated: 0, suspended: 0, total: 0 }
});

const operatorConfig = {
  mobile: {
    name: '中国移动',
    logo: '/static/yidong.png',
    color: '#3366CC',
    type: 3
  },
  telecom: {
    name: '中国电信',
    logo: '/static/dianxin.png',
    color: '#2F9DF4',
    type: 1
  },
  unicom: {
    name: '中国联通',
    logo: '/static/liantong.png',
    color: '#FF6B6B',
    type: 2
  },
  broadcast: {
    name: '中国广电',
    logo: '/static/guangdian.png',
    color: '#4CAF50',
    type: 4
  }
};

const loadOperatorData = async () => {
  try {
    loading.value = true;
    const promises = Object.entries(operatorConfig).map(async ([key, config]) => {
      const res = await getCardStatusApi(config.type);
      if (res.code === 1 && res.data) {
        statusData.value[key] = {
          activated: res.data.already_activate_count,
          inactivated: res.data.not_activate_count,
          suspended: res.data.shutdown_count,
          total: res.data.card_count
        };
      }
    });

    await Promise.all(promises);
  } catch (error) {
    console.error('Failed to fetch operator status:', error);
  } finally {
    loading.value = false;
  }
};

const getActivationRate = (data: OperatorStatus) => {
  return data.total > 0 ? Math.round((data.activated / data.total) * 100) : 0;
};

onMounted(() => {
  loadOperatorData();
});
</script>

<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <div
      v-for="(data, operator) in statusData"
      :key="operator"
      class="operator-card"
      :class="{ 'is-dark': isDark }"
    >
      <!-- 头部区域 -->
      <div class="card-header">
        <div class="operator-info">
          <img 
            :src="operatorConfig[operator].logo" 
            :alt="operatorConfig[operator].name"
            class="operator-logo"
          />
          <div class="operator-title">
            <div class="operator-name">{{ operatorConfig[operator].name }}</div>
            <div class="activation-info">
              <div class="activation-bar">
                <div 
                  class="activation-progress"
                  :style="{
                    width: `${getActivationRate(data)}%`,
                    backgroundColor: operatorConfig[operator].color
                  }"
                />
              </div>
              <span class="activation-text">{{ getActivationRate(data) }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 状态数据 -->
      <div class="status-list">
        <div class="status-item">
          <div class="status-label">已激活</div>
          <div class="status-value" :style="{ color: operatorConfig[operator].color }">
            {{ data.activated }}
          </div>
        </div>
        <div class="status-item">
          <div class="status-label">未激活</div>
          <div class="status-value text-[#faad14]">{{ data.inactivated }}</div>
        </div>
        <div class="status-item">
          <div class="status-label">已停机</div>
          <div class="status-value text-[#ff4d4f]">{{ data.suspended }}</div>
        </div>
      </div>

      <!-- 底部总计 -->
      <div class="card-footer">
        <span class="total-label">设备总量</span>
        <span class="total-value">{{ data.total }}</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.operator-card {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  transition: all 0.3s ease;

  &.is-dark {
    background: #1e1e1e;
    border-color: #303030;
  }

  &:hover {
    box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.operator-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.operator-logo {
  width: 32px;
  height: 32px;
  padding: 4px;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;

  .is-dark & {
    background: #2a2a2a;
    border-color: #303030;
  }
}

.operator-name {
  font-size: 16px;
  font-weight: 500;
  color: v-bind("isDark ? '#fff' : '#000'");
}

.status-list {
  display: grid;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: v-bind("isDark ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.02)'");
  border-radius: 6px;
}

.status-label {
  font-size: 14px;
  color: v-bind("isDark ? 'rgba(255, 255, 255, 0.65)' : 'rgba(0, 0, 0, 0.45)'");
}

.status-value {
  font-size: 15px;
  font-weight: 500;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16px;
  border-top: 1px solid v-bind("isDark ? '#303030' : '#f0f0f0'");
}

.total-label {
  font-size: 14px;
  color: v-bind("isDark ? 'rgba(255, 255, 255, 0.45)' : 'rgba(0, 0, 0, 0.45)'");
}

.total-value {
  font-size: 16px;
  font-weight: 600;
  color: v-bind("isDark ? '#fff' : '#000'");
}

.operator-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.activation-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.activation-bar {
  width: 60px;
  height: 4px;
  overflow: hidden;
  background: v-bind("isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)'");
  border-radius: 2px;
}

.activation-progress {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.activation-text {
  font-size: 12px;
  color: v-bind("isDark ? 'rgba(255, 255, 255, 0.65)' : 'rgba(0, 0, 0, 0.45)'");
}

:deep(.ant-progress-text) {
  display: none;
}
</style> 
