<script lang="ts" setup>
import type { AnalysisOverviewItem } from '@vben/common-ui';

import { computed } from 'vue';

import { AnalysisChartCard } from '@vben/common-ui';
import { MdiFileDocument } from '@vben/icons';

import {
  AnalyticsPackageStats,
  AnalyticsYearlyOrders,
  OrderAmountTrend,
  OrderTypeDistribution,
  PaymentMethodDistribution,
  SystemTaskList,
  TotalTransactionAmount,
} from './components';

// 模拟数据
const mockOverviewData = {
  cardOrders: 156,
  totalCardOrders: 12_580,
  completedCardOrders: 11_890,
  deviceOrders: 89,
  totalDeviceOrders: 8960,
  completedDeviceOrders: 8120,
  productOrders: 245,
  totalProductOrders: 15_680,
  completedProductOrders: 14_890,
  prepaidOrders: 167,
  totalPrepaidOrders: 9870,
  completedPrepaidOrders: 9120,
};

// 计算展示数据
const overviewItems = computed<AnalysisOverviewItem[]>(() => {
  return [
    {
      icon: MdiFileDocument,
      title: '卡片订单',
      value: mockOverviewData.cardOrders,
      valueTitle: '今日订单',
      totalValue: mockOverviewData.totalCardOrders,
      totalTitle: '总订单',
      totalTitle2: '已完成',
      totalValue2: mockOverviewData.completedCardOrders,
    },
    {
      icon: MdiFileDocument,
      title: '设备订单',
      value: mockOverviewData.deviceOrders,
      valueTitle: '今日订单',
      totalValue: mockOverviewData.totalDeviceOrders,
      totalTitle: '总订单',
      totalTitle2: '已完成',
      totalValue2: mockOverviewData.completedDeviceOrders,
    },
    {
      icon: MdiFileDocument,
      title: '商品订单',
      value: mockOverviewData.productOrders,
      valueTitle: '今日订单',
      totalValue: mockOverviewData.totalProductOrders,
      totalTitle: '总订单',
      totalTitle2: '已完成',
      totalValue2: mockOverviewData.completedProductOrders,
    },
    {
      icon: MdiFileDocument,
      title: '预存订单',
      value: mockOverviewData.prepaidOrders,
      valueTitle: '今日订单',
      totalValue: mockOverviewData.totalPrepaidOrders,
      totalTitle: '总订单',
      totalTitle2: '已完成',
      totalValue2: mockOverviewData.completedPrepaidOrders,
    },
  ];
});
</script>

<template>
  <div class="p-4">
    <div class="grid grid-cols-1 gap-5 md:grid-cols-12">
      <div class="md:col-span-8">
        <AnalysisChartCard title="交易统计分析">
          <TotalTransactionAmount />
        </AnalysisChartCard>
      </div>
      <div class="md:col-span-4">
        <AnalysisChartCard title="年度订单分析">
          <AnalyticsYearlyOrders />
        </AnalysisChartCard>
      </div>
    </div>
    <div class="mt-5 grid grid-cols-1 gap-5 md:grid-cols-12">
      <div class="md:col-span-4">
        <AnalysisChartCard title="订单金额趋势">
          <OrderAmountTrend />
        </AnalysisChartCard>
        <div class="mt-5">
          <AnalysisChartCard title="订单类型分布">
            <OrderTypeDistribution />
          </AnalysisChartCard>
        </div>
      </div>
      <div class="md:col-span-4">
        <div>
          <AnalysisChartCard title="支付方式分布">
            <PaymentMethodDistribution />
          </AnalysisChartCard>
        </div>
        <div class="mt-5">
          <AnalysisChartCard title="套餐统计分析">
            <AnalyticsPackageStats />
          </AnalysisChartCard>
        </div>
      </div>
      <div class="md:col-span-4">
        <AnalysisChartCard title="系统任务" class="h-[calc(100%)]">
          <SystemTaskList />
        </AnalysisChartCard>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 确保图表容器有最小高度 */

/* :deep(.ant-card-body) {
  min-height: 300px;
} */

/* 响应式调整 */
@media (max-width: 640px) {
  :deep(.ant-card-body) {
    min-height: 250px;
  }
}

/* 确保卡片内容不会被压缩 */

/* :deep(.ant-card) {
  display: flex;
  flex-direction: column;
  height: 100%;
} */

/* :deep(.ant-card-body) {
  flex: 1;
  display: flex;
  flex-direction: column;
} */
</style>
