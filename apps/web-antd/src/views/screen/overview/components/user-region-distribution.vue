<script lang="ts" setup>
import type { EChartsOption } from 'echarts';

import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { Card, message, Select } from 'ant-design-vue';
import * as echarts from 'echarts';

import { getBigChickenDataApi } from '#/api/core/dataAnalysis';

// 导入地图数据
import china from './china.json';
import city from './city.json';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

const data = ref<{ name: string; value: number }[]>([]);
const regionType = ref(2); // 默认显示省级数据
const queryType = ref(2); // 默认显示订单分布
const loading = ref(false);

const regionOptions = ref([
  // { label: '国家', value: 1 },
  { label: '省份', value: 2 },
  { label: '城市', value: 3 },
  // { label: '区域', value: 4 },
]);

const queryOptions = ref([
  { label: '卡片定位', value: 1 },
  { label: '订单分布', value: 2 },
]);

const chinaJSONData = ref(regionType.value === 2 ? china : city);

// 获取实时订单分布数据
// 名称标准化：去掉常见后缀，便于匹配
// 获取实时订单分布数据
// 名称标准化：去掉常见后缀，便于匹配（避免误伤“广西壮族自治区”等）
const normalizeName = (name: string) =>
  name
    .replace(/(省|市|特别行政区|自治区|壮族自治区|回族自治区|维吾尔自治区)/g, '')
    .replace(/\s/g, '') // 移除空格
    .trim();

const fetchDistributionData = async () => {
  try {
    loading.value = true;
    const response = await getBigChickenDataApi({
      type: queryType.value,
      regionType: regionType.value,
    });

    if (response.code === 1) {
      const apiData = response.data;

      // 构建接口数据 Map，key 是处理后的名字
      const apiMap = new Map<string, number>();
      for (const item of apiData) {
        const key = normalizeName(item.name);
        const val = isNaN(item.value) || item.value == null ? 0 : item.value;
        apiMap.set(key, val);
      }

      // 遍历地图所有区域，合并数据
      const fullData = chinaJSONData.value.features.map((feature) => {
        let name = feature.properties.name || '';

        // 替换空名为“南海诸岛”
        if (!name) {
          name = '南海诸岛';
        }

        const normalized = normalizeName(name);
        const value = apiMap.has(normalized) ? apiMap.get(normalized)! : 0;

        return { name, value };
      });

      data.value = fullData;

      if (chartRef.value) {
        renderEcharts(getChartOptions());
      }

      console.log(fullData, '✅ 最终合并后的地图数据');
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};


// const fetchDistributionData = async () => {
//   try {
//     loading.value = true;
//     const response = await getBigChickenDataApi({
//       type: queryType.value,
//       regionType: regionType.value,
//     });
//     if (response.code === 1) {
//       // 处理数据，将NaN或null转换为0
//       data.value = response.data.map((item) => ({
//         name: item.name,
//         value: isNaN(item.value) || item.value === null ? 0 : item.value,
//       }));
//       if (chartRef.value) {
//         renderEcharts(getChartOptions());
//       }
//       const allRegions = chinaJSONData.value.features.map(f => f.properties.name);

//       console.log(data.value, '8888');
//       console.log(chinaJSONData.value, '8888');
//       console.log(allRegions, '8888');
//     }
//   } catch (error) {
//     console.error('获取数据失败:', error);
//     message.error('获取数据失败');
//   } finally {
//     loading.value = false;
//   }
// };

const getChartOptions = (): EChartsOption => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} 单',
    confine: true,
  },
  visualMap: {
    min: 0.1,
    left: '10%',
    bottom: '20%',
    text: ['高', '低'],
    calculable: true,
    inRange: {
      color: ['#B8C6FF', '#7B8CFF', '#4B5EFF'],
    },
    textStyle: {
      color: '#666',
    },
    formatter(value) {
      return value < 0.1 ? '无数据' : value;
    },
  },
  geo: {
    map: regionType.value === 2 ? 'china' : 'city',
    roam: true,
    scaleLimit: {
      min: 0.7,
      max: 3,
    },
    zoom: 0.8,
    top: 1,
    bottom: 0,
    left: 0,
    right: 0,
    layoutCenter: ['50%', '50%'],
    layoutSize: '100%',
    label: {
      show: false,
    },
    itemStyle: {
      areaColor: '#F8F9FF',
      borderColor: '#E8EDFF',
      borderWidth: 1,
    },
    emphasis: {
      itemStyle: {
        areaColor: '#7B8CFF',
        shadowColor: 'rgba(123, 140, 255, 0.3)',
        shadowBlur: 10,
      },
      label: {
        show: true,
        color: '#fff',
        fontWeight: 'bold',
      },
    },
  },
  series: [
    {
      name: queryType.value === 1 ? '卡片定位' : '订单分布',
      type: 'map',
      map: regionType.value === 2 ? 'china' : 'city',
      geoIndex: 0,
      data: data.value,
    },
  ],
});

// 监听地域类型变化
const handleRegionTypeChange = async (value: number) => {
  regionType.value = value;
  // 根据选择的地域类型更新地图数据
  chinaJSONData.value = value === 2 ? china : city;
  // 注册地图
  echarts.registerMap(
    value === 2 ? 'china' : 'city',
    chinaJSONData.value as any,
  );
  // 获取数据并渲染图表
  await fetchDistributionData();
  message.success(`已切换至${value === 2 ? '省份' : '城市'}视图`);
};

// 监听查询类型变化
const handleQueryTypeChange = async (value: number) => {
  queryType.value = value;
  // 获取数据并渲染图表
  await fetchDistributionData();
  message.success(`已切换至${value === 1 ? '卡片定位' : '订单分布'}视图`);
};

onMounted(async () => {
  // 注册地图
  echarts.registerMap('china', chinaJSONData.value as any);
  // 获取数据并渲染图表
  await fetchDistributionData();
});
</script>

<template>
  <div class="map-container p-4">
    <Card>
      <div class="selector-container">
        <div class="selector-item">
          <Select v-model:value="regionType" :options="regionOptions" style="width: 120px" :disabled="loading"
            @change="handleRegionTypeChange" />
        </div>
        <div class="selector-item">
          <Select v-model:value="queryType" :options="queryOptions" style="width: 120px" :disabled="loading"
            @change="handleQueryTypeChange" />
        </div>
      </div>
      <Spin :spinning="loading">
        <EchartsUI ref="chartRef" height="100vh" width="100%" />
      </Spin>
    </Card>
  </div>
</template>

<style scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

.selector-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1;
  display: flex;
  gap: 10px;
}

.selector-item {
  display: flex;
  align-items: center;
}

:deep(.echarts) {
  /* width: 100% !important;
  height: 100% !important;
  min-height: 500px !important; */
}
</style>
