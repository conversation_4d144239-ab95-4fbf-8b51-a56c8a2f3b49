<script lang="ts" setup>
import { onMounted } from 'vue';

import { getCardPackageStatistics } from '#/api/core/dataStatistics';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

// 搜索条件配置
const basicSearchItems = [
  {
    field: 'card_package_name',
    label: '套餐名称',
    component: 'Input' as const,
    props: {
      placeholder: '请输入套餐名称',
      allowClear: true,
    },
  },
];

// 表格列配置
const columns = [
  {
    title: '套餐名称',
    dataIndex: 'card_package_name',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '累计充值次数',
    dataIndex: 'card_package_count',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '累计订单实收',
    dataIndex: 'total_income',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '累计成本',
    dataIndex: 'total_cost',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '累计盈利',
    dataIndex: 'total_profit',
    width: 150,
    align: 'center' as const,
  },
  {
    title: '累计订单总额',
    dataIndex: 'total_amount',
    width: 150,
    align: 'center' as const,
  },
];

// 使用 useTable hook 管理表格数据和搜索
const {
  loading,
  searchParams,
  tableData,
  pagination,
  handleSearch,
  handleReset,
  handleTableChange,
  getList,
} = useTable({
  api: getCardPackageStatistics,
  defaultParams: {
    type: 1, // 1: 卡片套餐统计
  },
});

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="card-package-stats">
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="basicSearchItems"
      :loading="loading"
      :compact="true"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 数据表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :pagination="pagination"
      :row-key="(record) => record.packageId"
      @change="handleTableChange"
    />
  </div>
</template>

<style lang="less" scoped>
.card-package-stats {
  :deep(.ant-card-body) {
    padding: 16px;
  }
}
</style>
