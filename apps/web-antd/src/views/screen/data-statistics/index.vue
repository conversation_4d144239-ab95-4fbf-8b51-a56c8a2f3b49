<script lang="ts" setup>
import type { Key } from 'ant-design-vue/es/table/interface';

import { ref } from 'vue';

import { Card, Tabs } from 'ant-design-vue';

import CardPackageStats from './components/card-package-stats.vue';
import DevicePackageStats from './components/device-package-stats.vue';
import MyIncomeStats from './components/my-income-stats.vue';
import ProxyActivationStats from './components/proxy-activation-stats.vue';

// Tab 页签数据
const tabItems = [
  {
    key: '1',
    label: '代理激活统计',
    component: ProxyActivationStats,
  },
  {
    key: '2',
    label: '卡片套餐统计',
    component: CardPackageStats,
  },
  {
    key: '3',
    label: '设备套餐统计',
    component: DevicePackageStats,
  },
  {
    key: '4',
    label: '我的收益',
    component: MyIncomeStats,
  },
];

const activeKey = ref<Key>('1');

const handleTabChange = (key: Key) => {
  activeKey.value = key;
};
</script>

<template>
  <div class="data-statistics p-2">
    <Card>
      <Tabs v-model:active-key="activeKey" @change="handleTabChange">
        <Tabs.TabPane
          v-for="item in tabItems"
          :key="item.key"
          :tab="item.label"
        >
          <component :is="item.component" />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<style lang="less" scoped>
.data-statistics {
  background-color: var(--background-deep);
  min-height: 100%;

  :deep(.ant-tabs-content) {
    margin-top: 16px;
  }
}
</style>
