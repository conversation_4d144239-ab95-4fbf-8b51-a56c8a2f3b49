<script setup>
import { onMounted, onUnmounted, ref } from 'vue';

import {
  MdiAlertCircle,
  MdiChartBar,
  MdiHarddisk,
  MdiLayers,
  MdiNetwork,
  MdiPulse,
  MdiRefresh,
} from '@vben/icons';

import { Card } from 'ant-design-vue';

import { getServerBasicInfo } from '#/api/core/monitor';

import AlertList from './components/alert-list.vue';
import DiskUsageList from './components/disk-usage-list.vue';
import HighResourceProcesses from './components/high-resource-processes.vue';
import NetworkConnectionsList from './components/network-connections-list.vue';
import PerformanceMetrics from './components/performance-metrics.vue';
import ServerStatusOverview from './components/server-status-overview.vue';

// Mock data for demonstration
const generateMockData = () => {
  const now = new Date();
  const cpuData = Array.from({ length: 24 })
    .fill(0)
    .map((_, i) => {
      const baseValue = 65 + Math.sin(i / 4) * 15; // 使用正弦函数创造波动
      return {
        time: new Date(
          now.getTime() - (23 - i) * 5 * 60_000,
        ).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        value: Math.min(
          95,
          Math.max(30, Math.floor(baseValue + (Math.random() - 0.5) * 10)),
        ),
      };
    });

  // 生成更真实的CPU指标数据
  const cpuMetrics = {
    userSpace: Math.floor(Math.random() * 20 + 40), // 40-60%
    systemSpace: Math.floor(Math.random() * 15 + 15), // 15-30%
    ioWait: Math.floor(Math.random() * 8 + 2), // 2-10%
    idle: 0, // 将在下面计算
  };
  // 计算空闲时间
  cpuMetrics.idle =
    100 - (cpuMetrics.userSpace + cpuMetrics.systemSpace + cpuMetrics.ioWait);
  const memoryData = Array.from({ length: 24 })
    .fill(0)
    .map((_, i) => ({
      time: new Date(now.getTime() - (23 - i) * 5 * 60_000).toLocaleTimeString(
        [],
        { hour: '2-digit', minute: '2-digit' },
      ),
      value: Math.floor(Math.random() * 30) + 40,
    }));

  const networkData = Array.from({ length: 24 })
    .fill(0)
    .map((_, i) => ({
      time: new Date(now.getTime() - (23 - i) * 5 * 60_000).toLocaleTimeString(
        [],
        { hour: '2-digit', minute: '2-digit' },
      ),
      in: Math.floor(Math.random() * 50) + 10,
      out: Math.floor(Math.random() * 30) + 5,
    }));

  const diskData = Array.from({ length: 24 })
    .fill(0)
    .map((_, i) => ({
      time: new Date(now.getTime() - (23 - i) * 5 * 60_000).toLocaleTimeString(
        [],
        { hour: '2-digit', minute: '2-digit' },
      ),
      read: Math.floor(Math.random() * 20) + 5,
      write: Math.floor(Math.random() * 15) + 3,
    }));

  const memoryMetrics = {
    application: Math.floor(Math.random() * 30) + 40,
    cache: Math.floor(Math.random() * 20) + 10,
    buffer: Math.floor(Math.random() * 10) + 5,
    available: Math.floor(Math.random() * 40) + 20,
  };

  return {
    cpuData,
    memoryData,
    networkData,
    diskData,
    cpuMetrics,
    memoryMetrics,
  };
};

const data = ref(generateMockData());
const serverBasicInfo = ref({
  uptime: '',
  status: '正常运行',
  cpu_cores: 1,
  active_processes: 0,
  high_cpu_cores: 0,
  system_load: 0,
  system_info: '',
  system_architecture: '',
});

const fetchServerBasicInfo = async () => {
  try {
    const { data } = await getServerBasicInfo();
    serverBasicInfo.value = data;
  } catch (error) {
    console.error('获取服务器基本信息失败:', error);
  }
};
const lastUpdated = ref(new Date());
const isRefreshing = ref(false);
const timeRange = ref('2h');
const selectedServer = ref('prod-server-01');

const refreshData = () => {
  isRefreshing.value = true;
  setTimeout(() => {
    data.value = generateMockData();
    lastUpdated.value = new Date();
    isRefreshing.value = false;
  }, 800);
};

// Auto refresh every 30 seconds
let refreshInterval;
onMounted(() => {
  fetchServerBasicInfo();
  refreshInterval = setInterval(() => {
    refreshData();
    fetchServerBasicInfo();
  }, 30_000);
});

onUnmounted(() => {
  clearInterval(refreshInterval);
});
const handleViewAllAlerts = () => {
  // 处理查看全部告警的逻辑
  console.log('查看全部告警');
};

const alerts = ref([
  {
    type: 'error',
    title: '磁盘空间警告',
    message: '/var 分区使用率超过 80%，建议清理不必要的文件。',
    timestamp: '2023-03-08 14:22:45',
    icon: MdiAlertCircle,
  },
  {
    type: 'info',
    title: '系统更新完成',
    message: '系统内核已更新至最新版本 5.15.0-76-generic。',
    timestamp: '2023-03-08 10:15:32',
    icon: MdiPulse,
  },
  {
    type: 'info',
    title: '服务重启',
    message: '数据库服务 postgresql 已自动重启。',
    timestamp: '2023-03-07 23:45:12',
    icon: MdiRefresh,
  },
]);
</script>

<template>
  <div class="p-2">
    <Card>
      <ServerStatusOverview
        :linux-version="serverBasicInfo.system_info || 'Linux'"
        :architecture="serverBasicInfo.system_architecture || 'x86_64'"
        :server-status="{
          status: 'success',
          uptime: serverBasicInfo.uptime,
        }"
        :cpu-info="{
          cores: serverBasicInfo.cpu_cores,
          status: '正常运行',
        }"
        :active-processes="{
          total: serverBasicInfo.active_processes,
          highCpuCount: serverBasicInfo.high_cpu_cores,
        }"
        :system-load="{
          value: serverBasicInfo.system_load,
          period: '当前值',
        }"
      />
      <!-- Section Header: Performance Metrics -->
      <div class="mb-4 mt-8 flex items-center gap-2">
        <MdiChartBar class="text-primary h-5 w-5" />
        <h2 class="text-xl font-semibold">性能指标</h2>
      </div>
      <!-- Main Metrics -->
      <PerformanceMetrics
        :cpu-data="
          data.cpuData.map((item) => ({ time: item.time, value: item.value }))
        "
        :memory-data="
          data.memoryData.map((item) => ({
            time: item.time,
            value: item.value,
          }))
        "
        :cpu-metrics="{
          userSpace: data.cpuMetrics.userSpace,
          systemSpace: data.cpuMetrics.systemSpace,
          ioWait: data.cpuMetrics.ioWait,
          idle: data.cpuMetrics.idle,
        }"
        :memory-metrics="{
          application: data.memoryMetrics.application,
          cache: data.memoryMetrics.cache,
          buffer: data.memoryMetrics.buffer,
          available: data.memoryMetrics.available,
        }"
      />
      <!-- Section Header: Network and Storage -->
      <!--  <div class="mb-4 mt-8 flex items-center gap-2">
        <MdiNetwork class="text-primary h-5 w-5" />
        <h2 class="text-xl font-semibold">网络与存储</h2>
      </div> -->
      <!-- Network and Disk -->
      <!--  <NetworkAndStorage
        :network-data="data.networkData"
        :disk-data="data.diskData"
      />
      -->

      <!-- Section Header: Disk Usage -->
      <div class="mb-4 mt-8 flex items-center gap-2">
        <MdiHarddisk class="text-primary h-5 w-5" />
        <h2 class="text-xl font-semibold">磁盘使用情况</h2>
      </div>
      <!-- Disk Usage -->
      <DiskUsageList
        :disks="[
          {
            path: '/ (根目录)',
            fileSystem: 'ext4',
            used: 1200,
            total: 2000,
          },
          {
            path: '/home',
            fileSystem: 'ext4',
            used: 800,
            total: 1000,
          },
          {
            path: '/var',
            fileSystem: 'ext4',
            used: 400,
            total: 500,
          },
          {
            path: '/tmp',
            fileSystem: 'ext4',
            used: 50,
            total: 100,
          },
        ]"
      />
      <!-- Section Header: System Alerts -->
      <div class="mb-4 mt-8 flex items-center gap-2">
        <MdiAlertCircle class="text-primary h-5 w-5" />
        <h2 class="text-xl font-semibold">系统告警</h2>
      </div>
      <!-- System Alerts -->
      <AlertList :alerts="alerts" @view-all="handleViewAllAlerts" />
      <!-- Section Header: High Resource Processes -->
      <div class="mb-4 mt-8 flex items-center gap-2">
        <MdiLayers class="text-primary h-5 w-5" />
        <h2 class="text-xl font-semibold">高资源占用进程</h2>
      </div>
      <!-- Top Processes -->
      <HighResourceProcesses
        :processes="[
          {
            id: 12458,
            name: 'nginx',
            cpu_usage: 28.5,
            memory_usage: 30,
            status: '正常',
          },
          {
            id: 8932,
            name: 'postgresql',
            cpu_usage: 15.2,
            memory_usage: 45,
            status: '正常',
          },
          {
            id: 23145,
            name: 'node',
            cpu_usage: 12.8,
            memory_usage: 35,
            status: '正常',
          },
          {
            id: 7621,
            name: 'redis-server',
            cpu_usage: 8.4,
            memory_usage: 25,
            status: '正常',
          },
          {
            id: 1024,
            name: 'docker',
            cpu_usage: 6.2,
            memory_usage: 20,
            status: '正常',
          },
        ]"
      />
      <!-- Section Header: Network Connections -->
      <div class="mb-4 mt-8 flex items-center gap-2">
        <MdiNetwork class="text-primary h-5 w-5" />
        <h2 class="text-xl font-semibold">网络连接</h2>
      </div>
      <!-- Network Connections -->
      <NetworkConnectionsList
        :connections="[
          {
            protocol: 'TCP',
            localAddress: '*************:80',
            remoteAddress: '***********:52631',
            status: '正常',
            pid: 12458,
          },
          {
            protocol: 'TCP',
            localAddress: '*************:443',
            remoteAddress: '************:38752',
            status: '正常',
            pid: 12458,
          },
          {
            protocol: 'TCP',
            localAddress: '*************:5432',
            remoteAddress: '*************:49821',
            status: '正常',
            pid: 8932,
          },
          {
            protocol: 'UDP',
            localAddress: '*************:53',
            remoteAddress: '***********:63251',
            status: '正常',
            pid: 3214,
          },
          {
            protocol: 'TCP',
            localAddress: '127.0.0.1:6379',
            remoteAddress: '127.0.0.1:52125',
            status: '正常',
            pid: 7621,
          },
        ]"
      />
    </Card>
  </div>
</template>

<style scoped>
/* 可以添加组件特定的样式 */
</style>
