<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { Card, message, Modal } from 'ant-design-vue';

import { getAgentVerificationList } from '#/api/core/risk';
import { getUserOptionsApi } from '#/api/core/user';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import FormModal from './components/form-modal.vue';
import { columns, searchItems } from './config';

// 状态定义
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getAgentVerificationList,
  defaultParams: {},
});

// 状态定义
const formVisible = ref(false);
const formLoading = ref(false);
const currentRecord = ref<Record<string, any>>();
const modalType = ref<'audit' | 'edit'>('edit');
const agentOptions = ref([]);

// 获取代理账号列表
const loadAgentOptions = async () => {
  try {
    const res = await getUserOptionsApi();
    console.log(res, 'res');
    if (res.code === 1) {
      agentOptions.value = res.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));
      // 更新搜索配置中的选项
      if (searchItems[0] && searchItems[0].props) {
        searchItems[0].props.options = agentOptions.value;
      }
    }
  } catch (error) {
    console.error('获取代理账号列表失败:', error);
    message.error('获取代理账号列表失败');
  }
};

// 新增处理
const handleAdd = () => {
  modalType.value = 'edit';
  currentRecord.value = undefined;
  formVisible.value = true;
};

// 编辑处理
const handleEdit = (record: Record<string, any>) => {
  modalType.value = 'edit';
  currentRecord.value = record;
  formVisible.value = true;
};

// 审核处理
const handleAudit = (record: Record<string, any>) => {
  modalType.value = 'audit';
  currentRecord.value = record;
  formVisible.value = true;
};

// 删除处理
const handleDelete = (record: Record<string, any>) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该记录吗？',
    onOk: async () => {
      try {
        // TODO: 调用删除接口
        // const res = await deleteAgentVerification(record.id);
        // if (res.code === 0) {
        //   message.success('删除成功');
        //   getList();
        // }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 表单提交处理
const handleFormSubmit = async (values: Record<string, any>) => {
  formLoading.value = true;
  try {
    if (modalType.value === 'audit') {
      // TODO: 调用审核接口
      // const res = await auditAgentVerification({
      //   id: currentRecord.value?.id,
      //   ...values,
      // });
      // if (res.code === 0) {
      //   message.success('审核成功');
      //   formVisible.value = false;
      //   getList();
      // }
    } else {
      // TODO: 调用新增/编辑接口
      // const res = await (currentRecord.value?.id
      //   ? updateAgentVerification({
      //       id: currentRecord.value.id,
      //       ...values,
      //     })
      //   : addAgentVerification(values));
      // if (res.code === 0) {
      //   message.success(
      //     `${currentRecord.value?.id ? '编辑' : '新增'}成功`,
      //   );
      //   formVisible.value = false;
      //   getList();
      // }
    }
    formLoading.value = false;
  } catch (error) {
    console.error('提交失败:', error);
    message.error('提交失败');
    formLoading.value = false;
  }
};

// 操作按钮配置
const actionButtons = [
  {
    key: 'audit',
    text: '审核',
    onClick: handleAudit,
    show: (record: any) => record.status === 0,
  },
  {
    key: 'edit',
    text: '编辑',
    onClick: handleEdit,
  },
  {
    key: 'delete',
    text: '删除',
    onClick: handleDelete,
  },
];

onMounted(() => {
  loadAgentOptions(); // 加载代理账号列表
  getList(); // 加载表格数据
});
</script>

<template>
  <div class="risk-agent-verification p-2">
    <Card>
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        :custom-buttons="[
          // {
          //   text: '新增',
          //   type: 'primary',
          //   onClick: handleAdd,
          // },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="actionButtons"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <FormModal
        v-model:visible="formVisible"
        :loading="formLoading"
        :record="currentRecord"
        :type="modalType"
        @submit="handleFormSubmit"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.risk-agent-verification {
  background-color: var(--background-deep);
}
</style>
