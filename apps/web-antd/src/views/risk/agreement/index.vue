<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { Card, message } from 'ant-design-vue';

import { getAgreementList } from '#/api/core/risk';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import FormModal from './components/form-modal.vue';
import { columns, searchItems } from './config';

// 状态定义
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getAgreementList,
  columns,
  searchItems,
});

const formVisible = ref(false);
const formLoading = ref(false);
const currentRecord = ref<Record<string, any>>();
const modalType = ref<'edit' | 'view'>('view');

// 查看处理
const handleView = (record: Record<string, any>) => {
  modalType.value = 'view';
  currentRecord.value = record;
  formVisible.value = true;
};

// 表单提交处理
const handleFormSubmit = async (values: Record<string, any>) => {
  formLoading.value = true;
  try {
    // TODO: 调用更新接口
    formLoading.value = false;
    formVisible.value = false;
    getList();
  } catch (error) {
    console.error('提交失败:', error);
    message.error('提交失败');
    formLoading.value = false;
  }
};

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="risk-agreement p-2">
    <Card>
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'view',
            text: '查看',
            onClick: handleView,
          },
          {
            key: 'del',
            text: '删除',
            danger: true,
            onClick: handleView,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <FormModal
        v-model:visible="formVisible"
        :loading="formLoading"
        :record="currentRecord"
        :type="modalType"
        @submit="handleFormSubmit"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.risk-agreement {
  background-color: var(--background-deep);
}
</style>
