<script lang="ts" setup>
import { onMounted } from 'vue';

import { Card } from 'ant-design-vue';

import { getRiskLimitList } from '#/api/core/area';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { columns, searchItems } from './config';

// 状态定义
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getRiskLimitList,
  defaultParams: {},
});

// 添加风险地区
const handleAdd = () => {
  // TODO: 实现添加逻辑
};

// 查看编辑
const handleView = () => {
  // TODO: 实现查看编辑逻辑
};

// 编辑地区
const handleEdit = () => {
  // TODO: 实现编辑地区逻辑
};

// 删除地区
const handleDelete = () => {
  // TODO: 实现删除地区逻辑
};

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="risk-area p-2">
    <Card>
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <BasicTable
        :loading="loading"
        :columns="columns"
        :action-buttons="[
          // 查看编辑
          {
            key: 'view',
            text: '查看',
            onClick: handleView,
          },
          // 编辑地区
          {
            key: 'edit',
            text: '编辑',
            onClick: handleEdit,
          },
          // 删除地区
          {
            key: 'delete',
            text: '删除',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :show-action="true"
        :data-source="tableData"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.risk-area {
  background-color: var(--background-deep);
}
</style>
