import { computed, defineComponent, h } from 'vue';

import { MdiDownload, MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  createQrcodeConfig,
  deleteQrcodeConfig,
  editQrcodeConfig,
  exportQrcodeConfig,
  getQrcodeConfigList,
} from '#/api';
import { useAnsheng } from '#/hooks/useAnsheng';
import { handleFileDownload } from '#/utils/export';

import { basicSearchItems, createColumns } from '../config';

// 定义活码配置项的类型
interface QrcodeConfigItem {
  id: number;
  primary_domain_name: string;
  points_domain_name: string;
  remarks: null | string;
  creation_time: string;
  update_time: string;
}

// 定义API参数类型
interface QrcodeConfigListParams {
  page: number;
  pageSize: number;
  primary_domain_name?: string;
  points_domain_name?: string;
  remarks?: string;
}

/**
 * 活码配置管理Hook
 * 基于useAnsheng实现活码配置的CRUD功能
 */
export function useQrcodeConfig() {
  // 使用useAnsheng统一管理表格、表单和搜索功能
  const {
    tableData,
    loading,
    pagination,
    searchToolbarBind,
    tableBind: origTableBind,
    handleTableChange,
    handleSearch,
    handleReset,
    initialize,
    form,
    table,
  } = useAnsheng<QrcodeConfigItem, QrcodeConfigListParams>({
    // 表单配置
    formOptions: {
      title: (isEdit) => (isEdit ? '编辑活码' : '新增活码'),
      draggable: true,
      width: 600,
      simpleLayout: {
        showGroupTitle: false, // 不显示分组标题
        padding: 0, // 更小的内边距
      },
      defaultValues: {
        primary_domain_name: '',
        points_domain_name: '',
        remarks: '',
      } as Partial<QrcodeConfigItem>,
      // 创建活码配置
      create: async (values) => {
        // 处理remarks，转换null为undefined
        const params = {
          ...values,
          remarks: values.remarks || undefined,
        };
        return await createQrcodeConfig(params);
      },
      // 更新活码配置
      update: async (id, values) => {
        // 处理remarks，转换null为undefined
        const params = {
          ...values,
          remarks: values.remarks || undefined,
        };
        // 不要重复传递id
        return await editQrcodeConfig({ ...params, id });
      },
      // 表单验证规则
      rules: {
        primary_domain_name: [
          { required: true, message: '请输入主域名' },
          {
            validator: (_, value) => {
              if (value && value.endsWith('/')) {
                return Promise.reject(new Error('域名不能以/结尾'));
              }
              return Promise.resolve();
            },
          },
        ],
        points_domain_name: [
          { required: true, message: '请输入指向域名' },
          {
            validator: (_, value) => {
              if (value && value.endsWith('/')) {
                return Promise.reject(new Error('域名不能以/结尾'));
              }
              return Promise.resolve();
            },
          },
        ],
      },
      // 成功回调
      onSuccess: () => {
        message.success('操作成功');
        table.getList(); // 刷新表格数据
      },
    },
    // 表格配置
    tableOptions: {
      api: getQrcodeConfigList,
      columns: createColumns(),
      // 操作按钮配置
      actionButtons: [
        {
          key: 'edit',
          text: '编辑',
          type: 'link',
          onClick: (record) => {
            // 使用form.show()打开编辑表单
            form.show(record);
          },
        },
        {
          key: 'delete',
          text: '删除',
          type: 'link',
          danger: true,
          onClick: (record) => {
            Modal.confirm({
              title: '确认删除',
              content: '确定要删除该活码配置吗？',
              onOk: async () => {
                try {
                  const res = await deleteQrcodeConfig(record.id);
                  if (res.code === 1) {
                    message.success('删除成功');
                    table.getList();
                  } else {
                    throw new Error(res.msg || '删除失败');
                  }
                } catch (error) {
                  message.error(
                    error instanceof Error ? error.message : '删除失败',
                  );
                }
              },
            });
          },
        },
      ],
    },
    // 搜索配置
    searchOptions: {
      basicItems: basicSearchItems,
      customButtons: [
        {
          text: '导出',
          icon: h(MdiDownload),
          onClick: handleExport,
        },
        {
          text: '新增活码',
          icon: h(MdiPlus),
          type: 'primary',
          onClick: () => {
            // 使用form.show()打开新增表单
            form.show();
          },
        },
      ],
    },
    // 删除API不再需要单独指定，在actionButtons中直接处理
  });

  const tableBind = computed(() => ({
    ...origTableBind.value,
    onChange: handleTableChange,
  }));

  // 处理导出功能
  async function handleExport() {
    try {
      loading.value = true;
      const params: QrcodeConfigListParams = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...table.searchParams,
      };

      const res = await exportQrcodeConfig(params);
      await handleFileDownload(
        res,
        `活码配置列表_${dayjs().format('YYYY-MM-DD')}.xlsx`,
      );
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    } finally {
      loading.value = false;
    }
  }

  // 按照微信小程序案例中的方式创建表单组件
  const formComponent = defineComponent({
    setup() {
      return () =>
        form.renderFormModal([
          {
            title: '活码配置信息',
            fields: [
              {
                name: 'primary_domain_name',
                label: '主域名',
                component: 'Input',
                rules: [
                  { required: true, message: '请输入主域名' },
                  {
                    validator: (_, value) => {
                      if (value && value.endsWith('/')) {
                        return Promise.reject(new Error('域名不能以/结尾'));
                      }
                      return Promise.resolve();
                    },
                  },
                ],
                props: {
                  placeholder: '请输入主域名',
                  allowClear: true,
                },
              },
              {
                name: 'points_domain_name',
                label: '指向域名',
                component: 'Input',
                rules: [
                  { required: true, message: '请输入指向域名' },
                  {
                    validator: (_, value) => {
                      if (value && value.endsWith('/')) {
                        return Promise.reject(new Error('域名不能以/结尾'));
                      }
                      return Promise.resolve();
                    },
                  },
                ],
                props: {
                  placeholder: '请输入指向域名',
                  allowClear: true,
                },
              },
              {
                name: 'remarks',
                label: '备注',
                component: 'Input',
                props: {
                  placeholder: '请输入备注',
                  allowClear: true,
                },
              },
            ],
          },
        ]);
    },
  });

  return {
    // 状态和数据
    tableData,
    loading,
    pagination,

    // 方法
    handleTableChange,
    handleSearch,
    handleReset,
    initialize,

    // 绑定对象
    searchToolbarBind,
    tableBind,

    // 组件
    formComponent,
  };
}
