<script lang="ts" setup>
import { onMounted } from 'vue';

import { Card } from 'ant-design-vue';

import { BasicTable, SearchToolbar } from '#/hooks/useAnsheng';

import { useQrcodeConfig } from './hooks/useQrcodeConfig';

// 使用useQrcodeConfig hook获取所有状态和方法
const { initialize, searchToolbarBind, tableBind, formComponent } =
  useQrcodeConfig();

// 组件挂载时加载数据
onMounted(() => {
  initialize();
});
</script>

<template>
  <div class="qrcode-config p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 - 使用绑定对象简化属性绑定 -->
      <SearchToolbar v-bind="searchToolbarBind" />

      <!-- 表格 - 使用绑定对象简化属性绑定 -->
      <BasicTable v-bind="tableBind" />

      <!-- 编辑/新增表单 -->
      <component :is="formComponent" />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.qrcode-config {
  background-color: var(--background-deep);
}
</style>
