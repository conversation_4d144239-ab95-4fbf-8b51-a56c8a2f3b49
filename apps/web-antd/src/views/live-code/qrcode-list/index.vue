<script lang="ts" setup>
import { onMounted } from 'vue';

import { Card } from 'ant-design-vue';

import { BasicTable, SearchToolbar } from '#/hooks/useAnsheng';

import { useQrcodeList } from './hooks/useQrcodeList';

// 使用useQrcodeList hook管理表格部分，通过回调函数方式与hooks交互
const {
  initialize,
  searchToolbarBind,
  tableBind,
  addQrcodeFormComponent,
  batchAddFormComponent,
} = useQrcodeList();

// 组件挂载时加载数据
onMounted(() => {
  initialize();
});
</script>

<template>
  <div class="qrcode-list p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 - 使用绑定对象简化属性绑定 -->
      <SearchToolbar v-bind="searchToolbarBind" />

      <!-- 表格 - 使用绑定对象简化属性绑定 -->
      <BasicTable v-bind="tableBind" />

      <component :is="addQrcodeFormComponent" />
      <component :is="batchAddFormComponent" />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.qrcode-list {
  background-color: var(--background-deep);
}
</style>
