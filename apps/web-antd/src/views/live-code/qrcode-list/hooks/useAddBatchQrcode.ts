// apps/web-antd/src/views/live-code/qrcode-list/hooks/useAddBatchQrcode.ts
import type { QrcodeConfigItem } from '#/api';

import { defineComponent, onMounted, ref } from 'vue';

import { batchAddQrcode, getAllQrcodeConfig } from '#/api';
import { useForm } from '#/hooks/useAnsheng';

interface BatchQrcodeFormValues {
  config_id?: number;
  ac_id?: number;
  start_device_no: string;
  end_device_no: string;
  jump_type: 1 | 2;
  button_content?: string;
  url_path?: string;
  remarks?: string;
}

export function useAddBatchQrcode(refreshList: () => void) {
  const form = useForm<BatchQrcodeFormValues>({
    title: '批量新增活码',
    width: 500,
    simpleLayout: {
      showGroupTitle: false,
      padding: 0,
    },
    defaultValues: {
      ac_id: undefined,
      jump_type: undefined,
      button_content: '',
      url_path: '',
    },
    create: async (values) => {
      const params: any = {
        start_device_no: values.start_device_no,
        end_device_no: values.end_device_no,
        ac_id: values.ac_id || values.config_id,
        jump_type: values.jump_type,
        remarks: values.remarks,
      };

      // 根据跳转类型添加相应参数
      if (params.jump_type === 1) {
        params.button_content = values.button_content;
      } else if (params.jump_type === 2) {
        params.url_path = values.url_path;
      }

      return await batchAddQrcode(params);
    },
    onSuccess: () => {
      refreshList(); // 刷新表格数据
    },
  });

  const batchAddFormComponent = defineComponent({
    setup() {
      const configList = ref<QrcodeConfigItem[]>([]);
      const getConfigList = async () => {
        const res = await getAllQrcodeConfig();
        if (res.code === 1 && res.data) {
          configList.value = res.data.rows || [];
        }
      };
      onMounted(() => {
        getConfigList();
      });
      return () =>
        form.renderFormModal([
          {
            title: '批量新增活码',
            fields: [
              {
                name: 'ac_id',
                label: '活码配置',
                component: 'Select',
                rules: [{ required: true, message: '请选择活码配置' }],
                props: {
                  placeholder: '请选择活码配置',
                  allowClear: true,
                  options: configList.value.map((item) => ({
                    label:
                      item.primary_domain_name +
                      (item.remarks ? `(${item.remarks})` : ''),
                    value: item.id,
                  })),
                },
              },
              {
                name: 'start_device_no',
                label: '开始卡号',
                component: 'Input',
                rules: [{ required: true, message: '请输入开始卡号' }],
                props: {
                  placeholder: '请输入开始卡号',
                  allowClear: true,
                },
              },
              {
                name: 'end_device_no',
                label: '结束卡号',
                component: 'Input',
                rules: [{ required: true, message: '请输入结束卡号' }],
                props: {
                  placeholder: '请输入结束卡号',
                  allowClear: true,
                },
              },
              {
                name: 'jump_type',
                label: '跳转类型',
                component: 'Select',
                rules: [{ required: true, message: '请选择跳转类型' }],
                props: {
                  placeholder: '请选择跳转类型',
                  allowClear: true,
                  options: [
                    { label: '系统充值', value: 1 },
                    { label: '第三方链接', value: 2 },
                  ],
                },
              },
              {
                name: 'button_content',
                label: '按钮内容',
                component: 'Input',
                rules: [{ required: true, message: '请输入按钮内容' }],
                props: {
                  placeholder: '请输入按钮内容',
                  allowClear: true,
                },
                show: (formData) => formData.jump_type === 1,
              },
              {
                name: 'url_path',
                label: '跳转链接',
                component: 'Input',
                rules: [{ required: true, message: '请输入跳转链接' }],
                props: {
                  placeholder: '请输入跳转链接',
                  allowClear: true,
                },
                show: (formData) => formData.jump_type === 2,
              },
              {
                name: 'remarks',
                label: '备注',
                component: 'Input',
                props: {
                  placeholder: '请输入备注',
                  allowClear: true,
                },
              },
            ],
          },
        ]);
    },
  });

  return {
    batchAddFormComponent,
    batchAddForm: form,
  };
}
