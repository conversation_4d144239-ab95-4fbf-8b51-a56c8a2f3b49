import type { QrcodeConfigItem } from '#/api';

import { defineComponent, onMounted, ref } from 'vue';

import { addQrcode, editQrcode, getAllQrcodeConfig } from '#/api';
import { useForm } from '#/hooks/useAnsheng';

// 定义类型接口
interface QrcodeFormValues {
  id?: number;
  ac_id?: number;
  jump_type: 1 | 2;
  button_content: string;
  url_path: string;
  device__device_no?: string;
  remarks?: string;
}

export function useAddQrcode(refreshList: () => void) {
  const form = useForm<QrcodeFormValues>({
    title: (isEdit) => (isEdit ? '编辑活码' : '新增活码'),
    draggable: true,
    fullscreenable: true,
    width: 500,
    simpleLayout: {
      showGroupTitle: false,
      padding: 0,
    },
    defaultValues: {
      ac_id: undefined,
      jump_type: undefined,
      button_content: '',
      url_path: '',
    },
    // 创建活码配置
    create: async (values) => {
      // 新增模式：只传必要参数
      const params: any = {
        device__device_no: values.device__device_no,
        ac_id: values.ac_id,
        jump_type: values.jump_type,
        remarks: values.remarks,
      };

      // 根据跳转类型添加相应参数
      if (params.jump_type === 1) {
        params.button_content = values.button_content;
      } else if (params.jump_type === 2) {
        params.url_path = values.url_path;
      }

      return await addQrcode(params);
    },
    // 更新活码配置
    update: async (id, values) => {
      // 编辑模式：直接使用原始记录，只更新修改的字段
      const params: any = {
        ...values,
        ac_id: values.ac_id,
        device__device_no: values.device__device_no,
        jump_type: values.jump_type,
        remarks: values.remarks,
      };

      // 根据跳转类型添加相应参数
      if (params.jump_type === 1) {
        params.button_content = values.button_content;
        params.url_path = undefined;
      } else if (params.jump_type === 2) {
        params.url_path = values.url_path;
        params.button_content = undefined;
      }
      // 不要重复传递id
      return await editQrcode(params);
    },
    // 成功回调
    onSuccess: () => {
      // message.success('操作成功');
      refreshList(); // 刷新表格数据
    },
  });

  const addQrcodeFormComponent = defineComponent({
    setup() {
      const configList = ref<QrcodeConfigItem[]>([]);
      const getConfigList = async () => {
        const res = await getAllQrcodeConfig();
        if (res.code === 1 && res.data) {
          configList.value = res.data.rows || [];
        }
      };
      onMounted(() => {
        getConfigList();
      });
      return () =>
        form.renderFormModal([
          {
            title: '新增活码',
            fields: [
              {
                name: 'ac_id',
                label: '活码配置',
                component: 'Select',
                rules: [{ required: true, message: '请选择活码配置' }],
                props: {
                  placeholder: '请选择活码配置',
                  allowClear: true,
                  options: configList.value.map((item) => ({
                    label:
                      item.primary_domain_name +
                      (item.remarks ? `(${item.remarks})` : ''),
                    value: item.id,
                  })),
                },
              },
              {
                name: 'device__device_no',
                label: '设备号',
                component: 'Input',
                rules: [{ required: true, message: '请输入设备号' }],
                props: {
                  placeholder: '请输入设备号',
                  allowClear: true,
                },
              },
              {
                name: 'jump_type',
                label: '跳转类型',
                component: 'Select',
                rules: [{ required: true, message: '请选择跳转类型' }],
                props: {
                  placeholder: '请选择跳转类型',
                  allowClear: true,
                  options: [
                    { label: '系统充值', value: 1 },
                    { label: '第三方链接', value: 2 },
                  ],
                },
              },
              {
                name: 'button_content',
                label: '按钮内容',
                component: 'Input',
                rules: [{ required: true, message: '请输入按钮内容' }],
                props: {
                  placeholder: '请输入按钮内容',
                  allowClear: true,
                },
                show: (formData) => formData.jump_type === 1,
              },
              {
                name: 'url_path',
                label: '跳转链接',
                component: 'Input',
                rules: [{ required: true, message: '请输入跳转链接' }],
                props: {
                  placeholder: '请输入跳转链接',
                  allowClear: true,
                },
                show: (formData) => formData.jump_type === 2,
              },
              {
                name: 'remarks',
                label: '备注',
                component: 'Input',
                props: {
                  placeholder: '请输入备注',
                  allowClear: true,
                },
              },
            ],
          },
        ]);
    },
  });

  return {
    addQrcodeFormComponent,
    addQrcodeForm: form,
  };
}
