// 修改 useQrcodeList.ts
import { computed, h, ref } from 'vue';

import { MdiDownload, MdiPlus, MdiTablePlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import { deleteQrcode, exportQrcodeList, getQrcodeList } from '#/api';
import { useAnsheng } from '#/hooks/useAnsheng';
import { handleFileDownload } from '#/utils/export';

import { basicSearchItems, createColumns } from '../config';
import { useAddBatchQrcode } from './useAddBatchQrcode';
import { useAddQrcode } from './useAddQrcode';

/**
 * 活码配置管理Hook
 * 基于useAnsheng实现活码配置的CRUD功能
 */
export function useQrcodeList() {
  // 创建一个ref用于存储addQrcodeForm
  const addQrcodeFormRef = ref<any>(null);
  const batchAddQrcodeFormRef = ref<any>(null);

  const {
    tableData,
    loading,
    pagination,
    searchToolbarBind,
    tableBind: origTableBind,
    handleTableChange,
    handleSearch,
    handleReset,
    initialize,
    table,
  } = useAnsheng({
    formOptions: {
      title: '',
      defaultValues: {} as any,
    },
    // 表格配置
    tableOptions: {
      api: getQrcodeList,
      columns: createColumns(),
      // 操作按钮配置
      actionButtons: [
        {
          key: 'edit',
          text: '编辑',
          type: 'link',
          onClick: async (record) => {
            // 使用ref的value
            addQrcodeFormRef.value?.show(record);
          },
        },
        {
          key: 'delete',
          text: '删除',
          type: 'link',
          danger: true,
          onClick: (record) => {
            Modal.confirm({
              title: '确认删除',
              content: '确定要删除该活码配置吗？',
              onOk: async () => {
                try {
                  const res = await deleteQrcode(record.id);
                  if (res.code === 1) {
                    message.success('删除成功');
                    table.getList();
                  } else {
                    throw new Error(res.msg || '删除失败');
                  }
                } catch (error) {
                  message.error(
                    error instanceof Error ? error.message : '删除失败',
                  );
                }
              },
            });
          },
        },
      ],
    },
    // 搜索配置
    searchOptions: {
      basicItems: basicSearchItems,
      customButtons: [
        {
          key: 'export',
          icon: h(MdiDownload),
          text: '导出',
          onClick: handleExport,
        },
        {
          key: 'batchAdd',
          icon: h(MdiTablePlus),
          text: '批量新增',
          onClick: () => {
            batchAddQrcodeFormRef.value?.show();
          },
        },
        {
          key: 'add',
          icon: h(MdiPlus),
          text: '新增活码',
          type: 'primary',
          onClick: () => {
            // 使用ref的value
            addQrcodeFormRef.value?.show();
          },
        },
      ],
    },
  });

  // 处理导出功能
  async function handleExport() {
    try {
      loading.value = true;
      const params = {
        page: pagination.current,
        pageSize: pagination.pageSize,
        ...table.searchParams,
      };

      const res = await exportQrcodeList(params);
      await handleFileDownload(
        res,
        `活码列表_${dayjs().format('YYYY-MM-DD')}.xlsx`,
      );
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    } finally {
      loading.value = false;
    }
  }

  // 创建一个刷新函数
  const refreshList = () => table.getList();

  // 调用useAddQrcode并将结果存入ref中
  const { addQrcodeFormComponent, addQrcodeForm } = useAddQrcode(refreshList);
  const { batchAddFormComponent, batchAddForm } =
    useAddBatchQrcode(refreshList);
  addQrcodeFormRef.value = addQrcodeForm;
  batchAddQrcodeFormRef.value = batchAddForm;

  const tableBind = computed(() => ({
    ...origTableBind.value,
    onChange: handleTableChange,
  }));

  return {
    // 状态和数据
    tableData,
    loading,
    pagination,

    // 方法
    handleTableChange,
    handleSearch,
    handleReset,
    initialize,

    // 绑定对象
    searchToolbarBind,
    tableBind,
    table,

    // 组件 - 使用异步组件
    addQrcodeFormComponent,
    batchAddFormComponent,
  };
}
