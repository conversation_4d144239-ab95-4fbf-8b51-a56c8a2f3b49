import { h } from 'vue';

import { Tag, Typography } from 'ant-design-vue';
import dayjs from 'dayjs';

const { Link } = Typography;

// 基础搜索配置
export const basicSearchItems = [
  {
    field: 'device__device_no',
    label: '设备号',
    component: 'Input' as const,
    props: {
      placeholder: '请输入设备号',
      allowClear: true,
    },
  },
  {
    field: 'remarks',
    label: '备注',
    component: 'Input' as const,
    props: {
      placeholder: '请输入备注',
      allowClear: true,
    },
  },
  {
    field: 'jump_type',
    label: '跳转类型',
    component: 'Select' as const,
    props: {
      placeholder: '请选择跳转类型',
      allowClear: true,
      options: [
        { label: '系统充值', value: 1 },
        { label: '第三方链接', value: 2 },
      ],
    },
  },
];

// 创建表格列配置
export const createColumns = () => [
  {
    title: '设备号',
    dataIndex: 'device__device_no',
    align: 'center' as const,
    width: 150,
  },
  {
    title: '跳转类型',
    dataIndex: 'jump_type',
    align: 'center' as const,
    width: 120,
    customRender: ({ text }: { text: 1 | 2 }) => {
      const color = text === 1 ? 'default' : 'success';
      const label = text === 1 ? '系统充值' : '第三方链接';
      return h(Tag, { color }, label);
    },
  },
  {
    title: '按钮内容',
    dataIndex: 'button_content',
    align: 'center' as const,
    width: 150,
    customRender: ({ text }: { text: string }) => {
      return h(Tag, { color: 'default' }, text || '-');
    },
  },
  {
    title: '活码地址',
    dataIndex: 'path_link',
    align: 'center' as const,
    width: 300,
    customRender: ({ text }: { text: string }) => {
      return h(
        Link,
        {
          href: text,
          target: '_blank',
          style: {
            display: 'inline-block',
            maxWidth: '280px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          },
        },
        text,
      );
    },
  },
  {
    title: '第三方链接',
    dataIndex: 'url_path',
    align: 'center' as const,
    width: 300,
    customRender: ({ text, record }: { record: any; text: null | string }) => {
      // 只有第三方链接类型才显示链接
      if (record.jump_type === 2 && text) {
        return h(
          Link,
          {
            href: text,
            target: '_blank',
            style: {
              display: 'inline-block',
              maxWidth: '280px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            },
          },
          text,
        );
      }
      return '-';
    },
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    align: 'center' as const,
    width: 150,
    customRender: ({ text }: { text: null | string }) => text || '-',
  },
  {
    title: '创建时间',
    dataIndex: 'creation_time',
    align: 'center' as const,
    width: 180,
    customRender: ({ text }: { text: string }) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
];
