<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue';

import type { MemberRuleAddParams } from '#/api/core/device';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h, ref, watch } from 'vue';

import { Image, message, Modal, Tag } from 'ant-design-vue';

import { deleteMemberRule, getMemberRuleList } from '#/api';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';
import { getFileUrl } from '#/utils/file';

import RuleEditModal from './rule-edit-modal.vue';

const props = defineProps<{
  limitId?: number;
  visible: boolean;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
}>();

const {
  loading,
  tableData,
  pagination,
  searchParams,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getMemberRuleList,
  defaultParams: {
    limitId: props.limitId,
  },
});

// const loading = ref(false);
// const tableData = ref<any[]>([]);
// const total = ref(0);
// const page = ref(1);
// const pageSize = ref(10);
// const searchParams = ref<{ vipName?: string }>({});

// 基础搜索项配置
const basicSearchItems: SearchItemConfig[] = [
  {
    field: 'vipName',
    label: '会员名称',
    component: 'Input',
    props: {
      placeholder: '请输入会员名称',
      allowClear: true,
    },
  },
];

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '会员名称',
    dataIndex: 'vipName',
    align: 'center',
    width: 150,
  },
  {
    title: '会员价格',
    dataIndex: 'vipPrice',
    align: 'center',
    width: 100,
    customRender: ({ text }) => `¥${text}`,
  },
  {
    title: '会员图片',
    dataIndex: 'vipImg',
    align: 'center',
    width: 120,
    customRender: ({ text }) => {
      if (!text) return '-';
      return h(Image, {
        src: getFileUrl(text),
        height: 40,
        style: { objectFit: 'contain' },
        preview: true,
      });
    },
  },
  {
    title: '是否显示',
    dataIndex: 'isShow',
    align: 'center',
    width: 100,
    customRender: ({ text }) => {
      return h(
        Tag,
        {
          color: text === 1 ? 'success' : 'warning',
        },
        text === 1 ? '是' : '否',
      );
    },
  },
  {
    title: '默认选择',
    dataIndex: 'isDefault',
    align: 'center',
    width: 100,
    customRender: ({ text }) => {
      return h(
        Tag,
        {
          color: text === 1 ? 'success' : 'warning',
        },
        text === 1 ? '是' : '否',
      );
    },
  },
  {
    title: '充值排序',
    dataIndex: 'sort',
    align: 'center',
    width: 100,
  },
];

// 编辑模态框
const editModalVisible = ref(false);
const currentEditData = ref<MemberRuleAddParams & { id?: number }>();

// 处理添加规则
const handleAddRule = () => {
  currentEditData.value = undefined;
  editModalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: any) => {
  currentEditData.value = { ...record };
  editModalVisible.value = true;
};

// 处理删除
const handleDelete = async (record: any) => {
  try {
    Modal.confirm({
      title: '提示',
      content: '确定删除该规则吗？',
      onOk: async () => {
        const res = await deleteMemberRule(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          throw new Error(res.msg || '删除失败');
        }
      },
    });
  } catch (error) {
    message.error(error instanceof Error ? error.message : '删除失败');
  }
};

// 处理编辑模态框确认
const handleEditModalOk = () => {
  getList();
};

// 自定义按钮配置
const customButtons = [
  {
    text: '添加规则',
    type: 'primary' as const,
    onClick: handleAddRule,
  },
];

// 监听 limitId 变化
watch(
  () => props.limitId,
  (val) => {
    if (val) {
      searchParams.value = {
        ...searchParams.value,
        limitId: val,
      };
      if (props.visible) {
        getList();
      }
    }
  },
  { immediate: true },
);

// 监听弹窗显示
watch(
  () => props.visible,
  (val) => {
    if (val && props.limitId) {
      getList();
    }
  },
);
</script>

<template>
  <Modal
    :visible="visible"
    title="查看规则"
    width="900px"
    :footer="null"
    @cancel="() => emit('update:visible', false)"
  >
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="basicSearchItems"
      :loading="loading"
      :custom-buttons="customButtons"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :show-action="true"
      :action-buttons="[
        {
          key: 'edit',
          text: '编辑',
          type: 'link',
          onClick: handleEdit,
        },
        {
          key: 'delete',
          text: '删除',
          type: 'link',
          danger: true,
          onClick: handleDelete,
        },
      ]"
      :pagination="pagination"
      @change="handleTableChange"
    />

    <!-- 编辑模态框 -->
    <RuleEditModal
      v-model:visible="editModalVisible"
      :limit-id="limitId"
      :edit-data="currentEditData"
      @ok="handleEditModalOk"
    />
  </Modal>
</template>

<style lang="less" scoped>
:deep(.ant-modal-body) {
  padding: 16px;
}
</style>
