<script lang="ts" setup>
import { computed, h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import { deleteMemberTemplate, getMemberTemplateList } from '#/api';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import EditModal from './components/edit-modal.vue';
import RuleModal from './components/rule-modal.vue';
import { basicSearchItems, createColumns } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getMemberTemplateList,
  defaultParams: {},
});

const editModalVisible = ref(false);
const editingRecord = ref<any>(null);

// 规则模态框
const ruleModalVisible = ref(false);
const currentLimitId = ref<number>();

// 处理查看规则
const handleViewRules = (record: any) => {
  currentLimitId.value = record.id;
  ruleModalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: any) => {
  editingRecord.value = record;
  editModalVisible.value = true;
};

// 处理删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该模板吗？',
    async onOk() {
      try {
        const res = await deleteMemberTemplate(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          throw new Error(res.msg || '删除失败');
        }
      } catch (error) {
        message.error(error instanceof Error ? error.message : '删除失败');
      }
    },
  });
};

// 表格列配置
const columns = computed(() => createColumns());

// 处理状态切换
const handleToggleStatus = async (record: any) => {
  try {
    // TODO: 调用状态切换接口
    // await updateTemplateStatus({
    //   id: record.id,
    //   status: record.status === 1 ? 0 : 1,
    // });
    message.success('操作成功');
    getList();
  } catch {
    message.error('操作失败');
  }
};

// 处理模态框确认
const handleModalOk = () => {
  getList();
  editModalVisible.value = false;
  editingRecord.value = null;
};

// 处理新增
const handleAdd = () => {
  editingRecord.value = null;
  editModalVisible.value = true;
};

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    icon: h(MdiPlus),
    text: '新增模板',
    type: 'primary',
    onClick: handleAdd,
  },
];

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="member-template p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :custom-buttons="toolbarButtons"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'viewRules',
            text: '查看规则',
            type: 'link',
            onClick: handleViewRules,
          },
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <!-- 编辑模态框 -->
      <EditModal
        v-model:visible="editModalVisible"
        :record="editingRecord"
        @ok="handleModalOk"
      />

      <!-- 规则模态框 -->
      <RuleModal
        v-model:visible="ruleModalVisible"
        :limit-id="currentLimitId"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.member-template {
  background-color: var(--background-deep);
}
</style>
