<script lang="ts" setup>
import type { CarouselItem } from '#/api/core/carousel';

import { h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import { deleteCarousel, getCarouselList } from '#/api/core/carousel';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import CarouselModal from './components/carousel-modal.vue';
import { basicSearchItems, createColumns } from './config';

// 状态定义
const {
  loading,
  tableData,
  searchParams,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getCarouselList,
  defaultParams: {},
});

// 使用导入的配置
const columns = createColumns();

// 弹窗控制
const modalVisible = ref(false);
const currentRecord = ref<CarouselItem>();

// 处理新增
const handleAdd = () => {
  currentRecord.value = undefined;
  modalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: CarouselItem) => {
  currentRecord.value = record;
  modalVisible.value = true;
};

// 处理删除
const handleDelete = (_record: any) => {
  // TODO: 实现删除逻辑
  Modal.confirm({
    title: '提示',
    content: '确定删除该轮播图吗？',
    onOk: async () => {
      await deleteCarousel(_record.id);
      message.success('删除成功');
      getList();
    },
  });
};

// 处理发布/取消发布
const handlePublish = (_record: any) => {
  // TODO: 实现发布/取消发布逻辑
};

// 处理成功
const handleSuccess = () => {
  getList();
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="carousel p-2">
    <Card :bordered="false">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :loading="loading"
        :custom-buttons="[
          {
            text: '新增',
            icon: h(MdiPlus),
            type: 'primary',
            onClick: handleAdd,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 新增/编辑弹窗 -->
    <CarouselModal
      v-model:visible="modalVisible"
      :record="currentRecord"
      @success="handleSuccess"
    />
  </div>
</template>

<style lang="less" scoped>
.carousel {
  background-color: var(--background-deep);
}
</style>
