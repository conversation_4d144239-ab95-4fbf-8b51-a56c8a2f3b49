<script lang="ts" setup>
import type { CouponRule } from '#/api/core/coupon';

import { onMounted, ref, watch } from 'vue';

import { Card, message, Modal } from 'ant-design-vue';

import {
  addCouponRule,
  deleteCouponRule,
  editCouponRule,
  getCouponRuleList,
} from '#/api/core/coupon';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import BatchDistributeModal from './components/batch-distribute-modal.vue';
import CustomGiftModal from './components/custom-gift-modal.vue';
import EditModal from './components/edit-modal.vue';
import GiftModal from './components/gift-modal.vue';
import RichTextModal from './components/rich-text-modal.vue';
import ViewModal from './components/view-modal.vue';
import { advancedSearchItems, basicSearchItems, columns } from './config';

// 状态定义
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getCouponRuleList,
  defaultParams: {},
});

// 批量分配弹窗状态
const distributeVisible = ref(false);
const selectedRule = ref<CouponRule>();

// 赠送弹窗状态
const giftVisible = ref(false);

// 自定义赠送弹窗状态
const customGiftVisible = ref(false);

// 富文本编辑弹窗状态
const richTextVisible = ref(false);
const currentRule = ref<CouponRule>();

// 查看模板弹窗状态
const viewVisible = ref(false);
const currentRuleId = ref<number>();

// 监听弹窗关闭，清空内容
watch(
  () => richTextVisible.value,
  (val) => {
    if (!val) {
      // 弹窗关闭时清空内容
      currentRule.value = undefined;
    }
  },
);

// 工具栏按钮配置
const toolbarButtons = [
  {
    key: 'add',
    text: '新增规则',
    type: 'primary',
    onClick: () => {
      editData.value = undefined; // 清空编辑数据
      editVisible.value = true; // 打开弹窗
    },
  },
  {
    key: 'batchDistribute',
    text: '批量分配',
    onClick: () => {
      distributeVisible.value = true;
    },
  },
  {
    key: 'gift',
    text: '赠送优惠券',
    onClick: () => {
      giftVisible.value = true;
    },
  },
  {
    key: 'customSendCoupon',
    text: '自定义赠送优惠券',
    onClick: () => {
      customGiftVisible.value = true;
    },
  },
];

// 编辑弹窗状态
const editVisible = ref(false);
const editData = ref<Partial<CouponRule>>();

// 处理编辑
const handleEdit = (record: CouponRule) => {
  editData.value = record;
  editVisible.value = true;
};

// 处理查看
const handleView = (record: CouponRule) => {
  currentRuleId.value = record.id;
  viewVisible.value = true;
};

// 处理编辑提交
const handleEditSubmit = async (values: CouponRule) => {
  try {
    const api = values.id ? editCouponRule : addCouponRule;
    const res = await api(values);

    if (res.code === 1) {
      editVisible.value = false;
      getList(); // 刷新列表
    }
  } catch (error) {
    console.error(values.id ? '修改失败:' : '新增失败:', error);
    message.error(values.id ? '修改失败' : '新增失败');
  }
};

// 处理删除
const handleDelete = (record: CouponRule) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除规则"${record.name}"吗？`,
    onOk: async () => {
      try {
        const res = await deleteCouponRule(record.id);
        if (res.code === 1) {
          getList(); // 刷新列表
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理编辑页面
const handleEditPage = (record: CouponRule) => {
  currentRule.value = record;
  richTextVisible.value = true;
};

// 处理批量分配成功
const handleDistributeSuccess = () => {
  getList();
};

// 处理赠送成功
const handleGiftSuccess = () => {
  getList();
};

// 处理自定义赠送成功
const handleCustomGiftSuccess = () => {
  getList();
};

// 处理富文本保存成功
const handleRichTextSuccess = () => {
  getList();
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
  console.log(searchParams.value, 'searchParams');
});
</script>

<template>
  <div class="promotion-coupon p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :advanced-items="advancedSearchItems"
        :custom-buttons="toolbarButtons"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :max-visible-buttons="3"
        :action-buttons="[
          // 查看模版
          {
            key: 'view',
            text: '查看模版',
            type: 'danger',
            onClick: handleView,
          },
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          // 编辑页面
          {
            key: 'editPage',
            text: '编辑页面',
            type: 'link',
            onClick: handleEditPage,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 编辑弹窗 -->
    <EditModal
      v-model:visible="editVisible"
      :data="editData"
      @submit="handleEditSubmit"
    />

    <!-- 批量分配弹窗 -->
    <BatchDistributeModal
      v-model:visible="distributeVisible"
      :rule="selectedRule"
      @success="handleDistributeSuccess"
    />

    <!-- 赠送优惠券弹窗 -->
    <GiftModal
      v-model:visible="giftVisible"
      :rule="selectedRule"
      @success="handleGiftSuccess"
    />

    <!-- 自定义赠送优惠券弹窗 -->
    <CustomGiftModal
      v-model:visible="customGiftVisible"
      @success="handleCustomGiftSuccess"
    />

    <!-- 富文本编辑弹窗 -->
    <RichTextModal
      v-model:visible="richTextVisible"
      :data="currentRule"
      @success="handleRichTextSuccess"
    />

    <!-- 查看模板弹窗 -->
    <ViewModal v-model:visible="viewVisible" :rule-id="currentRuleId" />
  </div>
</template>

<style lang="less" scoped>
.promotion-coupon {
  background-color: var(--background-deep);
}
</style>
