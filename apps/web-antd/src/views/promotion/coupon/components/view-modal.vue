<script lang="ts" setup>
import type { ColumnType } from 'ant-design-vue/es/table';

import type {
  SearchGroup,
  SearchItemConfig,
} from '#/components/SearchToolbar/types';

import { ref, watch } from 'vue';

import {
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Select,
} from 'ant-design-vue';

import {
  addCouponTemplate,
  deleteCouponTemplate,
  editCouponTemplate,
  getCouponTemplateList,
} from '#/api/core/coupon';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

// 定义模板数据类型
interface TemplateData {
  id: number;
  name: string;
  discountAmount: number;
  discountCouponType: number;
  endMonth: number;
  fullReduction: number;
  periodOfValidity: number;
  startingMonth: number;
  rechargeAmount?: number;
  returnAmount?: number;
  returnFreezeAmount?: number;
  returnCycle?: number;
  quotaNumber?: number;
  creationTime?: string;
}

const props = defineProps<{
  ruleId?: number;
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
}>();

// 添加/编辑模板弹窗状态
const modalVisible = ref(false);
const modalType = ref<'add' | 'edit'>('add');
const formRef = ref();
const formState = ref({
  id: undefined as number | undefined,
  name: '',
  discountAmount: 0,
  discountCouponType: 1,
  endMonth: 12,
  fullReduction: 0,
  periodOfValidity: 0,
  startingMonth: 1,
});

// 搜索配置
const searchItems: SearchItemConfig[] = [
  {
    field: 'name',
    label: '优惠券备注',
    component: 'Input',
    props: {
      placeholder: '请输入优惠券备注',
      allowClear: true,
    },
  },
  {
    field: 'discount_coupon_type',
    label: '优惠券类型',
    component: 'Select',
    props: {
      placeholder: '请选择优惠券类型',
      allowClear: true,
      options: [
        { label: '物联卡套餐', value: '1' },
        { label: '代发商城', value: '2' },
      ],
    },
  },
];

// 高级搜索配置
const advancedSearchItems: SearchGroup[] = [
  {
    label: '有效期月份',
    items: [
      {
        field: 'period_of_validity_min',
        label: '最小值',
        component: 'DatePicker',
        props: {
          placeholder: '请选择最小值',
          allowClear: true,
          format: 'YYYY-MM',
          picker: 'month',
          valueFormat: 'MM',
        },
      },
      {
        field: 'period_of_validity_max',
        label: '最大值',
        component: 'DatePicker',
        props: {
          placeholder: '请选择最大值',
          allowClear: true,
          format: 'YYYY-MM',
          picker: 'month',
          valueFormat: 'MM',
        },
      },
    ],
  },
  {
    label: '月份范围',
    items: [
      {
        field: 'starting_month',
        label: '开始月份',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始月份',
          allowClear: true,
          format: 'YYYY-MM',
          picker: 'month',
          valueFormat: 'MM',
        },
      },
      {
        field: 'end_month',
        label: '结束月份',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束月份',
          allowClear: true,
          format: 'YYYY-MM',
          picker: 'month',
          valueFormat: 'MM',
        },
      },
    ],
  },
  {
    label: '优惠金额',
    items: [
      {
        field: 'discount_amount_min',
        label: '最小值',
        component: 'Input',
        props: {
          placeholder: '请输入最小值',
          allowClear: true,
        },
      },
      {
        field: 'discount_amount_max',
        label: '最大值',
        component: 'Input',
        props: {
          placeholder: '请输入最大值',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '满减金额',
    items: [
      {
        field: 'full_reduction_min',
        label: '最小值',
        component: 'Input',
        props: {
          placeholder: '请输入最小值',
          allowClear: true,
        },
      },
      {
        field: 'full_reduction_max',
        label: '最大值',
        component: 'Input',
        props: {
          placeholder: '请输入最大值',
          allowClear: true,
        },
      },
    ],
  },
];

// 表格配置
const columns: ColumnType<TemplateData>[] = [
  {
    title: '模版名称',
    dataIndex: 'name',
    key: 'name',
    align: 'center',
  },
  {
    title: '优惠券类型',
    dataIndex: 'discountCouponType',
    key: 'discountCouponType',
    align: 'center',
    customRender: ({ text }) => {
      return text === 1 ? '物联卡套餐' : '代发商城';
    },
  },
  {
    title: '有效期(月)',
    dataIndex: 'periodOfValidity',
    key: 'periodOfValidity',
    align: 'center',
    customRender: ({ text }) => {
      return text === 0 ? '无限制' : text;
    },
  },
  {
    title: '可使用月份',
    key: 'monthRange',
    align: 'center',
    customRender: ({ record }) => {
      return `${record.startingMonth}月-${record.endMonth}月`;
    },
  },
  {
    title: '抵扣金额',
    dataIndex: 'discountAmount',
    key: 'discountAmount',
    align: 'center',
  },
  {
    title: '满减金额',
    dataIndex: 'fullReduction',
    key: 'fullReduction',
    align: 'center',
  },
];

// 使用表格hook
const {
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
  searchParams,
} = useTable({
  api: getCouponTemplateList,
  defaultParams: {
    rule_id: props.ruleId ? String(props.ruleId) : undefined,
  },
  beforeFetch: (params) => {
    // 确保 rule_id 始终存在
    if (props.ruleId) {
      params.rule_id = String(props.ruleId);
    }

    // 处理范围参数
    if (params.period_of_validity) {
      params.period_of_validity_min = params.period_of_validity[0];
      params.period_of_validity_max = params.period_of_validity[1];
      delete params.period_of_validity;
    }

    if (params.month_range) {
      params.starting_month = params.month_range[0];
      params.end_month = params.month_range[1];
      delete params.month_range;
    }

    if (params.discount_amount) {
      params.discount_amount_min = params.discount_amount[0];
      params.discount_amount_max = params.discount_amount[1];
      delete params.discount_amount;
    }

    if (params.full_reduction) {
      params.full_reduction_min = params.full_reduction[0];
      params.full_reduction_max = params.full_reduction[1];
      delete params.full_reduction;
    }

    return params;
  },
});

// 监听弹窗显示状态
watch(
  () => props.visible,
  (val) => {
    if (val) {
      getList();
    }
  },
);

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
};

// 处理添加模板
const handleAddTemplate = () => {
  modalType.value = 'add';
  formState.value = {
    id: undefined,
    name: '',
    discountAmount: 0,
    discountCouponType: 1,
    endMonth: 12,
    fullReduction: 0,
    periodOfValidity: 0,
    startingMonth: 1,
  };
  modalVisible.value = true;
};

// 处理编辑模板
const handleEdit = (record: TemplateData) => {
  modalType.value = 'edit';
  formState.value = {
    id: record.id,
    name: record.name,
    discountAmount: record.discountAmount,
    discountCouponType: record.discountCouponType,
    endMonth: record.endMonth,
    fullReduction: record.fullReduction,
    periodOfValidity: record.periodOfValidity,
    startingMonth: record.startingMonth,
  };
  modalVisible.value = true;
};

// 处理删除模板
const handleDelete = (record: TemplateData) => {
  Modal.confirm({
    title: '确定删除模板吗？',
    onOk: async () => {
      const res = await deleteCouponTemplate(record.id);
      message.success(res.msg);
      getList();
    },
  });
};

// 处理表单提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    if (!props.ruleId) {
      message.error('规则ID不能为空');
      return;
    }

    if (modalType.value === 'edit' && !formState.value.id) {
      message.error('模板ID不能为空');
      return;
    }

    const params = {
      ruleId: props.ruleId,
      id: formState.value.id as number,
      name: formState.value.name,
      discountAmount: formState.value.discountAmount,
      discountCouponType: formState.value.discountCouponType,
      endMonth: formState.value.endMonth,
      fullReduction: formState.value.fullReduction,
      periodOfValidity: formState.value.periodOfValidity,
      startingMonth: formState.value.startingMonth,
    };

    if (modalType.value === 'add') {
      await addCouponTemplate(params);
      message.success('添加成功');
    } else {
      await editCouponTemplate(params);
      message.success('编辑成功');
    }

    modalVisible.value = false;
    getList();
  } catch (error) {
    console.error(modalType.value === 'add' ? '添加失败:' : '编辑失败:', error);
  }
};

// 关闭弹窗
const handleModalClose = () => {
  modalVisible.value = false;
  // 重置为默认值
  formState.value = {
    id: undefined,
    name: '',
    discountAmount: 0,
    discountCouponType: 1,
    endMonth: 12,
    fullReduction: 0,
    periodOfValidity: 0,
    startingMonth: 1,
  };
  formRef.value?.resetFields();
};
</script>

<template>
  <Modal
    :visible="visible"
    title="查看模板"
    width="80%"
    :footer="null"
    @cancel="handleClose"
  >
    <div class="flex flex-col gap-4">
      <!-- 搜索栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :advanced-items="advancedSearchItems"
        :custom-buttons="[
          {
            text: '新增规则',
            type: 'primary',
            onClick: handleAddTemplate,
          },
        ]"
        @search="handleSearch"
        @reset="handleReset"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'danger',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'danger',
            onClick: handleDelete,
          },
        ]"
        @change="handleTableChange"
      />
    </div>
  </Modal>

  <!-- 添加/编辑模板弹窗 -->
  <Modal
    v-model:visible="modalVisible"
    :title="modalType === 'add' ? '添加模板' : '编辑模板'"
    @ok="handleSubmit"
    @cancel="handleModalClose"
  >
    <Form
      ref="formRef"
      :model="formState"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <Form.Item
        label="模板名称"
        name="name"
        :rules="[{ required: true, message: '请输入模板名称' }]"
      >
        <Input v-model:value="formState.name" placeholder="请输入模板名称" />
      </Form.Item>
      <Form.Item
        label="优惠券类型"
        name="discountCouponType"
        :rules="[{ required: true, message: '请选择优惠券类型' }]"
      >
        <Select
          v-model:value="formState.discountCouponType"
          placeholder="请选择优惠券类型"
        >
          <Select.Option :value="1">物联卡套餐</Select.Option>
          <Select.Option :value="2">代发商城</Select.Option>
        </Select>
      </Form.Item>
      <Form.Item
        label="抵扣金额"
        name="discountAmount"
        :rules="[{ required: true, message: '请输入抵扣金额' }]"
      >
        <InputNumber
          v-model:value="formState.discountAmount"
          placeholder="请输入抵扣金额"
          :min="0"
          :precision="2"
          style="width: 100%"
        />
      </Form.Item>
      <Form.Item
        label="满减金额"
        name="fullReduction"
        :rules="[{ required: true, message: '请输入满减金额' }]"
      >
        <InputNumber
          v-model:value="formState.fullReduction"
          placeholder="请输入满减金额"
          :min="0"
          :precision="2"
          style="width: 100%"
        />
      </Form.Item>
      <Form.Item
        label="有效期(月)"
        name="periodOfValidity"
        :rules="[{ required: true, message: '请输入有效期' }]"
      >
        <InputNumber
          v-model:value="formState.periodOfValidity"
          placeholder="请输入有效期，0表示无限制"
          :min="0"
          style="width: 100%"
        />
      </Form.Item>
      <Form.Item
        label="开始月份"
        name="startingMonth"
        :rules="[{ required: true, message: '请输入开始月份' }]"
      >
        <InputNumber
          v-model:value="formState.startingMonth"
          placeholder="请输入开始月份"
          :min="1"
          :max="12"
          style="width: 100%"
        />
      </Form.Item>
      <Form.Item
        label="结束月份"
        name="endMonth"
        :rules="[{ required: true, message: '请输入结束月份' }]"
      >
        <InputNumber
          v-model:value="formState.endMonth"
          placeholder="请输入结束月份"
          :min="1"
          :max="12"
          style="width: 100%"
        />
      </Form.Item>
    </Form>
  </Modal>
</template>
