<script lang="ts" setup>
import type { UploadChangeParam } from 'ant-design-vue';

import { ref, watch } from 'vue';

import { Form, message, Modal } from 'ant-design-vue';

import {
  downloadCustomGiftTemplate,
  importCustomGift,
} from '#/api/core/coupon';
import UploadBox from '#/components/UploadBox/index.vue';
import { handleFileDownload } from '#/utils/export';

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<{
  success: [];
  'update:visible': [value: boolean];
}>();

// 表单数据
const formRef = ref();
const formData = ref({
  file: undefined as File | undefined,
});

// 文件上传状态
const fileList = ref<any[]>([]);

// 监听 visible 变化时重置表单
watch(
  () => props.visible,
  (val) => {
    if (!val) {
      formRef.value?.resetFields();
      formData.value = {
        file: undefined,
      };
      fileList.value = [];
    }
  },
);

// 处理文件变化
const handleFileChange = (info: UploadChangeParam) => {
  fileList.value = info.fileList.slice(-1);
  if (info.fileList.length > 0) {
    formData.value.file = info.fileList[0].originFileObj;
    formRef.value?.validateFields(['file']); // 手动触发文件字段的验证
  } else {
    formData.value.file = undefined;
  }
};

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    const res = await downloadCustomGiftTemplate();
    await handleFileDownload(res, '自定义赠送优惠券模板.xlsx');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败');
  }
};

// 屏蔽回车键
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    e.preventDefault();
  }
};

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();

    if (fileList.value.length === 0) {
      message.error('请上传文件');
      return;
    }
    const uploadFile = fileList.value[0].originFileObj;
    if (!uploadFile) {
      message.error('文件上传失败，请重新上传');
      return;
    }

    const res = await importCustomGift(uploadFile);

    if (res?.code === 1) {
      message.success('任务已开始,进度查看请打开任务中心');
      emit('success');
      emit('update:visible', false);
    } else {
      message.error(res?.msg || '赠送失败');
    }
  } catch (error) {
    console.error('赠送失败:', error);
    message.error('赠送失败');
  }
};
</script>

<template>
  <Modal
    :visible="visible"
    title="自定义赠送优惠券"
    @update:visible="$emit('update:visible', $event)"
    @ok="handleSubmit"
    @keydown="handleKeyDown"
  >
    <Form ref="formRef" :model="formData" layout="vertical">
      <Form.Item
        label="文件名:"
        name="file"
        :rules="[{ required: true, message: '请点击上传文件' }]"
      >
        <!-- <div class="mb-2 flex items-center justify-between">
          <span class="text-sm text-gray-500">支持 .xlsx, .xls 格式的文件</span>
          <a class="text-primary" @click="handleDownloadTemplate">模板下载</a>
        </div>
        <div
          class="hover:border-primary rounded-lg border border-dashed border-gray-300 p-6 transition-colors"
        >
          <Upload
            :file-list="fileList"
            :before-upload="() => false"
            @change="handleFileChange"
          >
            <div class="flex flex-col items-center">
              <div class="mb-3">
                <MdiDownload class="text-2xl text-gray-400" />
              </div>
              <div class="mb-2">请点击上传文件</div>
            </div>
          </Upload>
        </div> -->

        <UploadBox
          size="small"
          accept=".xlsx,.xls"
          support-text="支持 .xlsx, .xls 格式的文件"
          upload-text="点击上传文件"
          download-text="下载模板"
          @change="handleFileChange"
          @download="handleDownloadTemplate"
        />
      </Form.Item>
    </Form>
  </Modal>
</template>

<style lang="less" scoped>
:deep(.ant-upload-list) {
  margin-top: 16px;
}

:deep(.ant-upload) {
  width: 100%;
}

:deep(.ant-upload-list-item) {
  margin-top: 8px;
}

:deep(.ant-upload-drag) {
  border: none;
  background: none;
}
</style>
