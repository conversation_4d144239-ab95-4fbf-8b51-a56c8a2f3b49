<script lang="ts" setup>
import type { UploadChangeParam } from 'ant-design-vue';

import type { CouponRule } from '#/api/core/coupon';

import { ref, watch } from 'vue';

import { Form, message, Modal, Select } from 'ant-design-vue';

import {
  getCouponRuleOptions,
  importCardDeliverCouponRule,
} from '#/api/core/coupon';
import { downloadAllocationTemplate } from '#/api/core/package';
import UploadBox from '#/components/UploadBox/index.vue';
import { handleFileDownload } from '#/utils/export';

const props = defineProps<{
  rule?: CouponRule;
  visible: boolean;
}>();

const emit = defineEmits<{
  success: [];
  'update:visible': [value: boolean];
}>();

// 表单数据
const formRef = ref();
const formData = ref({
  file: undefined as File | undefined,
  ruleId: undefined as number | undefined,
});

// 文件上传状态
const fileList = ref<any[]>([]);

// 规则选项
const ruleOptions = ref<{ label: string; value: number }[]>([]);

// 规则选项加载状态
const ruleOptionsLoading = ref(false);

// 加载规则选项
const loadRuleOptions = async () => {
  try {
    ruleOptionsLoading.value = true;
    const res = await getCouponRuleOptions();
    if (res.code === 1 && res.data) {
      ruleOptions.value = res.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));

      // 如果有传入规则，设置默认选中
      if (props.rule?.id) {
        formData.value.ruleId = props.rule.id;
      }
    }
  } catch (error) {
    console.error('加载优惠券规则选项失败:', error);
    message.error('加载优惠券规则选项失败');
  } finally {
    ruleOptionsLoading.value = false;
  }
};

// 监听 visible 变化时加载选项
watch(
  () => props.visible,
  (val) => {
    if (val) {
      loadRuleOptions();
    } else {
      formRef.value?.resetFields();
      formData.value = {
        file: undefined,
        ruleId: undefined,
      };
      fileList.value = [];
    }
  },
);

// 处理文件变化
const handleFileChange = (info: UploadChangeParam) => {
  fileList.value = info.fileList.slice(-1);
  if (info.fileList.length > 0) {
    formData.value.file = info.fileList[0].originFileObj;
    formRef.value?.validateFields(['file']); // 手动触发文件字段的验证
  } else {
    formData.value.file = undefined;
  }
};

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    const res = await downloadAllocationTemplate();
    await handleFileDownload(res, '批量分配模板.xlsx');
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败');
  }
};

// 屏蔽回车键
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    e.preventDefault();
  }
};

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    if (!formData.value.ruleId) {
      message.error('请选择优惠券规则');
      return;
    }

    if (fileList.value.length === 0) {
      message.error('请上传文件');
      return;
    }
    const uploadFile = fileList.value[0].originFileObj;
    if (!uploadFile) {
      message.error('文件上传失败，请重新上传');
      return;
    }

    const res = await importCardDeliverCouponRule({
      couponRuleId: formData.value.ruleId.toString(),
      file: uploadFile,
    });

    if (res?.code === 1) {
      message.success('任务已开始,进度查看请打开任务中心');
      emit('success');
      emit('update:visible', false);
    } else {
      message.error(res?.msg || '赠送失败');
    }
  } catch (error) {
    console.error('赠送失败:', error);
    message.error('赠送失败');
  }
};
</script>

<template>
  <Modal
    :visible="visible"
    title="赠送优惠券"
    @update:visible="$emit('update:visible', $event)"
    @ok="handleSubmit"
    @keydown="handleKeyDown"
  >
    <Form ref="formRef" :model="formData" layout="vertical">
      <Form.Item
        label="优惠券规则:"
        name="ruleId"
        :rules="[{ required: true, message: '请输入分配的优惠券规则' }]"
      >
        <Select
          v-model:value="formData.ruleId"
          placeholder="请输入分配的优惠券规则"
          :options="ruleOptions"
          :loading="ruleOptionsLoading"
          allow-clear
        />
      </Form.Item>

      <Form.Item
        label="文件名:"
        name="file"
        :rules="[{ required: true, message: '请点击上传文件' }]"
      >
        <UploadBox
          size="small"
          accept=".xlsx,.xls"
          support-text="支持 .xlsx, .xls 格式的文件"
          upload-text="点击上传文件"
          download-text="下载模板"
          @change="handleFileChange"
          @download="handleDownloadTemplate"
        />

        <!-- <div class="mb-2 flex items-center justify-between">
          <span class="text-sm text-gray-500">支持 .xlsx, .xls 格式的文件</span>
          <a class="text-primary" @click="handleDownloadTemplate">模板下载</a>
        </div>
        <div
          class="hover:border-primary rounded-lg border border-dashed border-gray-300 p-6 transition-colors"
        >
          <Upload
            :file-list="fileList"
            :before-upload="() => false"
            @change="handleFileChange"
          >
            <div class="flex flex-col items-center">
              <div class="mb-3">
                <MdiDownload class="text-2xl text-gray-400" />
              </div>
              <div class="mb-2">请点击上传文件</div>
            </div>
          </Upload>
        </div> -->
      </Form.Item>
    </Form>
  </Modal>
</template>

<style lang="less" scoped>
:deep(.ant-upload-list) {
  margin-top: 16px;
}

:deep(.ant-upload) {
  width: 100%;
}

:deep(.ant-upload-list-item) {
  margin-top: 8px;
}

:deep(.ant-upload-drag) {
  border: none;
  background: none;
}
</style>
