<script lang="ts" setup>
import type { UploadChangeParam } from 'ant-design-vue';

import type { CouponRule } from '#/api/core/coupon';

import { ref, watch } from 'vue';

import { Form, Input, message, Modal, Radio, Select } from 'ant-design-vue';

import {
  allocationCouponRuleBatch,
  allocationCouponRuleImport,
  allocationCouponRuleSerial,
  getCouponRuleOptions,
} from '#/api/core/coupon';
import { downloadAllocationTemplate } from '#/api/core/package';
import UploadBox from '#/components/UploadBox/index.vue';
import { handleFileDownload } from '#/utils/export';

const props = defineProps<{
  rule?: CouponRule;
  visible: boolean;
}>();

const emit = defineEmits<{
  success: [];
  'update:visible': [value: boolean];
}>();

// 表单数据
const formRef = ref();
const formData = ref({
  type: 'serial', // 默认连号分配
  startNo: '',
  endNo: '',
  numbers: '',
  file: undefined as File | undefined,
  ruleId: undefined as number[] | undefined,
});

// 文件上传状态
const fileList = ref<any[]>([]);

// 规则选项
const ruleOptions = ref<{ label: string; value: number }[]>([]);

// 规则选项加载状态
const ruleOptionsLoading = ref(false);

// 加载规则选项
const loadRuleOptions = async () => {
  try {
    ruleOptionsLoading.value = true;
    const res = await getCouponRuleOptions();
    if (res.code === 1 && res.data) {
      ruleOptions.value = res.data.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));

      // 如果有传入规则，设置默认选中
      if (props.rule?.id) {
        formData.value.ruleId = [props.rule.id];
      }
    }
  } catch (error) {
    console.error('加载优惠券规则选项失败:', error);
    message.error('加载优惠券规则选项失败');
  } finally {
    ruleOptionsLoading.value = false;
  }
};

// 监听 visible 变化时加载选项
watch(
  () => props.visible,
  (val) => {
    if (val) {
      loadRuleOptions();
    } else {
      formRef.value?.resetFields();
      formData.value = {
        type: 'serial',
        startNo: '',
        endNo: '',
        numbers: '',
        file: undefined,
        ruleId: undefined,
      };
      fileList.value = [];
    }
  },
);

// 处理分配类型切换
const handleTypeChange = () => {
  formData.value.startNo = '';
  formData.value.endNo = '';
  formData.value.numbers = '';
  formData.value.file = undefined;
  fileList.value = [];
  formRef.value?.clearValidate(); // 清除验证状态
};

// 处理文件变化
const handleFileChange = (info: UploadChangeParam) => {
  fileList.value = info.fileList.slice(-1);
  if (info.fileList.length > 0 && info.fileList[0]?.originFileObj) {
    formData.value.file = info.fileList[0].originFileObj;
    formRef.value?.validateFields(['file']); // 手动触发文件字段的验证
  } else {
    formData.value.file = undefined;
  }
};

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    const res = await downloadAllocationTemplate();
    console.log(res, 'res');
    await handleFileDownload(res, `批量分配模板${Date.now()}.xlsx`);
  } catch (error) {
    console.error('下载模板失败:', error);
    message.error('下载模板失败');
  }
};

// 屏蔽回车键
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter') {
    e.preventDefault();
  }
};

// 处理提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    if (!formData.value.ruleId || formData.value.ruleId.length === 0) {
      message.error('请选择优惠券规则');
      return;
    }

    let res;
    const ruleIds = formData.value.ruleId.join(',');

    switch (formData.value.type) {
      case 'file': {
        // 导入分配
        if (fileList.value.length === 0) {
          message.error('请上传文件');
          return;
        }
        const uploadFile = fileList.value[0].originFileObj;
        if (!uploadFile) {
          message.error('文件上传失败，请重新上传');
          return;
        }
        res = await allocationCouponRuleImport({
          couponRuleIds: ruleIds,
          file: uploadFile,
        });
        break;
      }

      case 'irregular': {
        // 分隔符分配
        res = await allocationCouponRuleBatch({
          couponRuleIds: ruleIds,
          cardNo: formData.value.numbers.split('\n').join(','),
        });
        break;
      }

      case 'serial': {
        // 连号分配
        res = await allocationCouponRuleSerial({
          couponRuleIds: ruleIds,
          startCardNo: formData.value.startNo,
          endCardNo: formData.value.endNo,
        });
        break;
      }
    }

    if (res?.code === 1) {
      message.success('任务已开始,进度查看请打开任务中心');
      emit('success');
      emit('update:visible', false);
    } else {
      message.error(res?.msg || '分配失败');
    }
  } catch (error) {
    console.error('分配失败:', error);
    message.error('分配失败');
  }
};
</script>

<template>
  <Modal
    :visible="visible"
    title="批量分配优惠券"
    @update:visible="$emit('update:visible', $event)"
    @ok="handleSubmit"
    @keydown="handleKeyDown"
  >
    <Form ref="formRef" :model="formData" layout="vertical">
      <Form.Item
        label="分配方式"
        name="type"
        :rules="[{ required: true, message: '请选择分配方式' }]"
      >
        <Radio.Group v-model:value="formData.type" @change="handleTypeChange">
          <Radio.Button value="serial">连号分配</Radio.Button>
          <Radio.Button value="file">文件分配</Radio.Button>
          <Radio.Button value="irregular">不规则分配</Radio.Button>
        </Radio.Group>
      </Form.Item>

      <!-- 连号分配 -->
      <template v-if="formData.type === 'serial'">
        <Form.Item
          label="优惠券规则"
          name="ruleId"
          :rules="[{ required: true, message: '请选择优惠券规则' }]"
        >
          <Select
            v-model:value="formData.ruleId"
            placeholder="请选择优惠券规则"
            :options="ruleOptions"
            :loading="ruleOptionsLoading"
            allow-clear
            mode="multiple"
          />
        </Form.Item>

        <Form.Item
          label="SIM NO启始"
          name="startNo"
          :rules="[{ required: true, message: '请输入SIM NO启始' }]"
        >
          <Input
            v-model:value="formData.startNo"
            placeholder="请输入SIM NO启始"
            allow-clear
          />
        </Form.Item>
        <Form.Item
          label="SIMNO结束"
          name="endNo"
          :rules="[{ required: true, message: '请输入SIMNO结束' }]"
        >
          <Input
            v-model:value="formData.endNo"
            placeholder="请输入SIMNO结束"
            allow-clear
          />
        </Form.Item>
      </template>

      <!-- 文件分配 -->
      <template v-if="formData.type === 'file'">
        <Form.Item
          label="优惠券规则"
          name="ruleId"
          :rules="[{ required: true, message: '请选择优惠券规则' }]"
        >
          <Select
            v-model:value="formData.ruleId"
            placeholder="请选择优惠券规则"
            :options="ruleOptions"
            :loading="ruleOptionsLoading"
            allow-clear
            mode="multiple"
          />
        </Form.Item>

        <Form.Item
          label="文件"
          name="file"
          :rules="[{ required: true, message: '请上传文件' }]"
        >
          <!-- 自定义大小和文本 -->
          <UploadBox
            size="small"
            accept=".xlsx,.xls"
            support-text="支持 .xlsx, .xls 格式的文件"
            upload-text="点击上传文件"
            download-text="下载模板"
            @change="handleFileChange"
            @download="handleDownloadTemplate"
          />
          <!-- <div class="mb-2 flex items-center justify-between">
            <span class="text-sm text-gray-500">支持 .xlsx, .xls 格式的文件</span>
            <Button class="text-primary" @click="handleDownloadTemplate">
              下载模板
            </Button>
          </div>
          <div
            class="hover:border-primary rounded-lg border border-dashed border-gray-300 p-6 transition-colors"
          >
            <Upload
              :file-list="fileList"
              :before-upload="() => false"
              @change="handleFileChange"
            >
              <div class="flex flex-col items-center">
                <div class="mb-3">
                  <MdiDownload class="text-2xl text-gray-400" />
                </div>
                <div class="mb-2">点击或拖拽文件到此区域上传</div>
              </div>
            </Upload>
          </div> -->
        </Form.Item>
      </template>

      <!-- 不规则分配 -->
      <template v-if="formData.type === 'irregular'">
        <Form.Item
          label="优惠券规则"
          name="ruleId"
          :rules="[{ required: true, message: '请选择优惠券规则' }]"
        >
          <Select
            v-model:value="formData.ruleId"
            placeholder="请选择优惠券规则"
            :options="ruleOptions"
            :loading="ruleOptionsLoading"
            allow-clear
            mode="multiple"
          />
        </Form.Item>

        <Form.Item
          label="SIM NO"
          name="numbers"
          :rules="[{ required: true, message: '请输入SIM NO' }]"
        >
          <Input.TextArea
            v-model:value="formData.numbers"
            placeholder="不同的卡号请换行，以免导致操作失败。"
            :rows="6"
            allow-clear
          />
        </Form.Item>
      </template>
    </Form>
  </Modal>
</template>

<style lang="less" scoped>
.upload-container {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.upload-box {
  flex: 1;
  border: 1px dashed var(--primary-color);
  border-radius: 8px;
  padding: 16px;
  transition: border-color 0.3s;

  &:hover {
    border-color: var(--primary-color);
  }
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-hint {
  color: #666;
  font-size: 12px;
}

.template-link {
  padding-top: 8px;
}

:deep(.ant-upload-list) {
  margin-top: 16px;
}

:deep(.ant-upload) {
  width: 100%;
}

:deep(.ant-upload-list-item) {
  margin-top: 8px;
}

:deep(.ant-upload-drag) {
  border: none;
  background: none;
}
</style>
