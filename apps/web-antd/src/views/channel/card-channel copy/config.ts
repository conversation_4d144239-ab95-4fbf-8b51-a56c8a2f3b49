import type { AlignType, TableColumnType } from 'ant-design-vue';

import type {
  SearchGroup,
  SearchItemConfig,
} from '#/components/SearchToolbar/index.vue';

import dayjs from 'dayjs';

// 搜索配置
export const searchConfig: SearchItemConfig[] = [
  {
    label: '通道名称',
    field: 'apiName',
    component: 'Input',
    props: {
      placeholder: '请输入通道名称',
      allowClear: true,
    },
  },
  {
    label: '通道类型',
    field: 'apiType',
    component: 'Select',
    props: {
      placeholder: '请选择通道类型',
      allowClear: true,
    },
    options: [
      { label: '套餐', value: 1 },
      { label: '前向流量池', value: 2 },
      { label: '后向流量池', value: 3 },
    ],
  },
  {
    label: '对接平台',
    field: 'apiPlatform',
    component: 'Input',
    props: {
      placeholder: '请输入对接平台',
      allowClear: true,
    },
  },
  {
    label: '对接版本',
    field: 'apiVersion',
    component: 'Input',
    props: {
      placeholder: '请输入对接版本',
      allowClear: true,
    },
  },
  {
    label: '换卡类型',
    field: 'changeCard',
    component: 'Select',
    props: {
      placeholder: '请选择换卡类型',
      allowClear: true,
    },
    options: [
      { label: '强制换卡', value: 1 },
      { label: '非强制换卡', value: 2 },
      { label: '关闭', value: 3 },
    ],
  },
];

// 高级搜索配置
export const advancedSearchConfig: SearchGroup[] = [
  {
    label: '时间筛选',
    items: [
      {
        label: '创建时间',
        field: 'creationBegin',
        component: 'DatePicker',
        props: {
          placeholder: '请选择创建时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
      },
      {
        label: '创建结束',
        field: 'creationEnd',
        component: 'DatePicker',
        props: {
          placeholder: '请选择创建结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
      },
      {
        label: '修改时间',
        field: 'updateBegin',
        component: 'DatePicker',
        props: {
          placeholder: '请选择修改时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
      },
      {
        label: '修改结束',
        field: 'updateEnd',
        component: 'DatePicker',
        props: {
          placeholder: '请选择修改结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
      },
    ],
  },
];

// 表格列配置
export const columns: TableColumnType[] = [
  {
    title: '通道名称',
    dataIndex: 'apiName',
    width: 200,
    align: 'center' as AlignType,
  },
  {
    title: 'API类型',
    dataIndex: 'apiType',
    width: 100,
    align: 'center' as AlignType,
  },
  {
    title: '提交类型',
    dataIndex: 'apiCardType',
    width: 100,
    align: 'center' as AlignType,
  },
  {
    title: '卡数量',
    dataIndex: 'cardCount',
    width: 100,
    align: 'center' as AlignType,
  },
  {
    title: '分组数量',
    dataIndex: 'groupCount',
    width: 100,
    align: 'center' as AlignType,
  },
  {
    title: '平台编码',
    dataIndex: 'apiPlatform',
    width: 120,
    align: 'center' as AlignType,
  },
  {
    title: '版本名称',
    dataIndex: 'apiVersion',
    width: 100,
    align: 'center' as AlignType,
  },
  {
    title: 'API余额',
    dataIndex: 'apiBalance',
    width: 100,
    align: 'center' as AlignType,
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
    width: 180,
    align: 'center' as AlignType,
    customRender: ({ text }: { text: string }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '修改时间',
    dataIndex: 'updateTime',
    width: 180,
    align: 'center' as AlignType,
    customRender: ({ text }: { text: string }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
];

// 状态选项配置
export const statusOptions = {
  apiPolling: [
    { label: '启用', value: 1, color: 'success' },
    { label: '禁用', value: 0, color: 'error' },
  ],
  apiType: [
    { label: '套餐', value: 1 },
    { label: '前向流量池', value: 2 },
    { label: '后向流量池', value: 3 },
  ],
  apiCardType: [
    { label: 'ICCID', value: 1 },
    { label: 'MIS', value: 2 },
    { label: '虚拟号', value: 3 },
  ],
  changeCard: [
    { label: '强制换卡', value: 1 },
    { label: '非强制换卡', value: 2 },
    { label: '关闭', value: 3 },
  ],
};
