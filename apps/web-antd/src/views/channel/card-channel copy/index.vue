<script setup lang="ts">
import type { ChannelItem } from '#/api/core/channel';

import { computed, onMounted, ref } from 'vue';

import { MdiExport, MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  copyChannel,
  deleteChannel,
  getChannelDetail,
  getChannelList,
  verifyChannel,
} from '#/api/core/channel';
// import BasicTable from '#/components/BasicTable/index.vue';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import ViewModal from './components/view-modal.vue';
import {
  advancedSearchConfig,
  columns,
  searchConfig,
  statusOptions,
} from './config';
import { useChannelForm } from './hooks/useChannelForm';

// const userOptionsLoading = ref(true);

// 状态定义
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getChannelList,
  defaultParams: {},
});

// 代理选项
// const userOptions = ref<{ label: string; value: number }[]>([
//   {
//     label: '测试',
//     value: 1111,
//   },
// ]);

// 基础搜索项配置
const basicItems = computed(() => searchConfig);

// 高级搜索项配置
const advancedItems = computed(() => advancedSearchConfig);

// 查看弹窗状态
const viewVisible = ref(false);
const currentItem = ref<ChannelItem>();
const viewLoading = ref(false);

// 添加通道相关
const { renderModal, show } = useChannelForm({
  onSuccess: () => {
    // message.success('操作成功');
    getList();
  },
});

// 处理添加
const handleAdd = () => {
  show();
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  message.info('导出功能暂未实现');
};

// 处理编辑
const handleEdit = (record: ChannelItem) => {
  show(record.id);
};

// 处理删除
const handleDelete = (record: ChannelItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除通道 "${record.apiName}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    okButtonProps: { danger: true },
    async onOk() {
      try {
        const res = await deleteChannel(record.id);
        if (res.code === 1) {
          message.success(res.msg || '删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理查看
const handleView = async (record: ChannelItem) => {
  currentItem.value = undefined;
  viewVisible.value = true;
  viewLoading.value = true;
  try {
    const res = await getChannelDetail(record.id);
    if (res.code === 1) {
      currentItem.value = res.data;
    } else {
      message.error(res.msg || '获取详情失败');
      viewVisible.value = false;
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    message.error('获取详情失败');
    viewVisible.value = false;
  } finally {
    viewLoading.value = false;
  }
};

// 处理验证
const handleVerify = async (record: ChannelItem) => {
  loading.value = true;
  try {
    const res = await verifyChannel({
      id: record.id,
      apiName: record.apiName,
      cardNo: '123456',
    });

    message.success(res.msg || '验证成功');
  } catch (error) {
    console.error('验证失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理复制
const handleCopy = async (record: ChannelItem) => {
  Modal.confirm({
    title: '复制通道',
    content: `确定要复制通道 "${record.apiName}" 吗？`,
    async onOk() {
      try {
        const res = await copyChannel(record.id);
        if (res.code === 1) {
          message.success('复制成功');
          getList();
        } else {
          message.error(res.msg || '复制失败');
        }
      } catch (error) {
        console.error('复制失败:', error);
        message.error('复制失败');
      }
    },
  });
};

// 修改操作按钮配置
const actionButtons = [
  {
    key: 'view',
    text: '查看',
    type: 'link' as const,
    onClick: (record: ChannelItem) => handleView(record),
  },
  {
    key: 'verify',
    text: '验证',
    type: 'link' as const,
    onClick: (record: ChannelItem) => handleVerify(record),
  },
  {
    key: 'copy',
    text: '复制',
    type: 'link' as const,
    onClick: (record: ChannelItem) => handleCopy(record),
  },
  {
    key: 'edit',
    text: '编辑',
    type: 'link' as const,
    onClick: (record: ChannelItem) => handleEdit(record),
  },
  {
    key: 'delete',
    text: '删除',
    type: 'link' as const,
    danger: true,
    onClick: (record: ChannelItem) => handleDelete(record),
  },
];

// const getUserList = async () => {
//   try {
//     userOptionsLoading.value = true;
//     const res = await getUserOptionsApi();

//     userOptions.value = res.data.map((item: { id: number; name: string }) => ({
//       label: item.name,
//       value: item.id,
//     }));
//   } catch (error) {
//     console.error('获取代理选项失败:', error);
//     message.error('获取代理选项失败');
//   } finally {
//     userOptionsLoading.value = false;
//   }
// };

// 组件挂载时加载数据
onMounted(() => {
  // fetchUserOptions();
  getList();
  // getUserList();
});
</script>

<template>
  <div class="channel-list p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicItems"
        :advanced-items="advancedItems"
        :loading="loading"
        :custom-buttons="[
          {
            text: '导出',
            type: 'primary',
            icon: MdiExport,
            onClick: () => handleExport(),
          },
          {
            text: '添加通道',
            icon: MdiPlus,
            onClick: () => handleAdd(),
          },
        ]"
        @search="handleSearch"
        @reset="handleReset"
      />

      <!-- 表格组件 -->
      <BasicTable
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :status-options="statusOptions"
        :action-buttons="actionButtons"
        show-action
        @change="handleTableChange"
      />
    </Card>

    <!-- 查看弹窗 -->
    <ViewModal
      v-model:visible="viewVisible"
      :data="currentItem"
      :loading="viewLoading"
    />

    <!-- 添加/编辑通道弹窗 -->
    <component :is="renderModal()" />
  </div>
</template>

<style lang="less" scoped>
.channel-list {
  background-color: var(--background-deep);
  min-height: 100%;
}
</style>
