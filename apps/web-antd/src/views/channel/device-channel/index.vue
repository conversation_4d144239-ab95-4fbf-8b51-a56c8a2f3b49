<script setup lang="ts">
import type { ChannelItem } from '#/api/core/channel';

import { computed, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  deleteCardByConfigId,
  deleteDeviceChannel,
  editApiConfigDevice,
  getDeviceChannelList,
  getDeviceChannelOptions,
  getUserOptionsApi,
  getVerifyDock,
} from '#/api';
import { copyChannel } from '#/api/core/channel';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { advancedSerachItems, basicSearchItems, columns } from './config';
import { useChannelForm } from './hooks/useDeviceChannelForm';

// 状态定义
const userOptionsLoading = ref(true);

// 设备通道
const deviceChannelOptions = ref<{ label: string; value: number }[]>([]);

// 分页配置
const {
  loading,
  searchParams,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
  handleCellSave,
} = useTable<ChannelItem, { page: number; pageSize: number }>({
  api: getDeviceChannelList,
  defaultParams: {},
  onSave: {
    default: async (record, value, key) => {
      const res = await editApiConfigDevice(record.id, {
        ...record,
        [key]: value,
      });
    },
  },
});
// 代理选项
const userOptions = ref<{ label: string; value: number }[]>([]);

// 基础搜索项配置
const basicItems = ref([...basicSearchItems]);

// 高级搜索项配置
const advancedItems = ref([...advancedSerachItems]);

// const basicItems = ref<SearchItemConfig[]>([
//   {
//     label: '通道名称',
//     field: 'name',
//     component: 'Input' as const,
//     props: {
//       placeholder: '请输入通道名称',
//       allowClear: true,
//     },
//   },
//   {
//     label: '对接平台',
//     field: 'apiPlatform',
//     component: 'Input' as const,
//     props: {
//       placeholder: '请输入对接平台',
//       allowClear: true,
//     },
//   },
//   {
//     label: 'API类型',
//     field: 'apiType',
//     component: 'Select' as const,
//     props: {
//       placeholder: '请选择API类型',
//       allowClear: true,
//     },
//     options: [
//       { label: '套餐', value: 1 },
//       { label: '前向流量池', value: 2 },
//       { label: '后向流量池', value: 3 },
//     ],
//   },
//   {
//     label: '归属代理',
//     field: 'userId',
//     component: 'Select' as const,
//     props: {
//       placeholder: '请选择归属代理',
//       allowClear: true,
//       loading: userOptionsLoading,
//     },
//     options: userOptions.value,
//   },
// ]);

// 添加通道相关
// const { renderModal, show } = useChannelForm({
const { renderModal, showModal: show } = useChannelForm({
  onSuccess: () => {
    // message.success('操作成功');
    getList();
  },
});

// 处理添加
const handleAdd = () => {
  show();
};

// 处理编辑
const handleEdit = (record: ChannelItem) => {
  // show(record.id);
  show(record);
};

// 处理删除
const handleDelete = (record: ChannelItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除通道 "${record.apiName}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    okButtonProps: { danger: true },
    async onOk() {
      try {
        await deleteDeviceChannel(record.id);
        getList();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理删除所有卡
const handleDeleteAll = (record: ChannelItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除通道 "${record.apiName}" 的所有卡吗？`,
    okText: '确定',
    cancelText: '取消',
    okButtonProps: { danger: true },
    async onOk() {
      await deleteCardByConfigId(String(record.id));
    },
  });
};

const deviceNo = ref('');

// 处理验证
const handleVerify = async (record: ChannelItem) => {
  try {
    // const res =
    await getVerifyDock({
      configId: record.id,
      deviceNo: '123456',
    });
  } catch (error) {
    console.error('验证失败:', error);
  } finally {
    deviceNo.value = '';
  }
};

// 处理复制
const handleCopy = async (record: ChannelItem) => {
  Modal.confirm({
    title: '复制通道',
    content: `确定要复制通道 "${record.apiName}" 吗？`,
    async onOk() {
      try {
        const res = await copyChannel(record.id);
        if (res.code === 1) {
          message.success('复制成功');
          getList();
        } else {
          message.error(res.msg || '复制失败');
        }
      } catch (error) {
        console.error('复制失败:', error);
        message.error('复制失败');
      }
    },
  });
};

// 修改操作按钮配置
const actionButtons = [
  // {
  //   key: 'view',
  //   text: '查看',
  //   type: 'link',
  //   onClick: (record) => handleView(record),
  // },
  {
    key: 'verify',
    text: '验证',
    type: 'link',
    onClick: (record) => handleVerify(record),
  },
  // {
  //   key: 'copy',
  //   text: '复制',
  //   type: 'link',
  //   onClick: (record) => handleCopy(record),
  // },
  {
    key: 'edit',
    text: '编辑',
    type: 'link',
    onClick: (record) => handleEdit(record),
  },
  {
    key: 'delete',
    text: '删除通道',
    type: 'link',
    danger: true,
    onClick: (record) => handleDelete(record),
  },
  {
    key: 'deleteAll',
    text: '删除所有卡',
    type: 'link',
    danger: true,
    onClick: (record) => handleDeleteAll(record),
  },
];

// 状态选项配置
const statusOptions = {
  apiPolling: [
    { label: '启用', value: 1, color: 'success' },
    { label: '禁用', value: 0, color: 'error' },
  ],
  apiType: [
    { label: '套餐', value: 1 },
    { label: '前向流量池', value: 2 },
    { label: '后向流量池', value: 3 },
  ],
  apiCardType: [
    { label: 'ICCID', value: 1 },
    { label: 'MIS', value: 2 },
    { label: '虚拟号', value: 3 },
  ],
};

// 获取选项数据
const fetchOptions = async () => {
  try {
    userOptionsLoading.value = true;
    const res = await getUserOptionsApi();
    const channelRes = await getDeviceChannelOptions();

    deviceChannelOptions.value = channelRes.data.map((item) => ({
      label: item.name,
      value: item.id,
    }));
    userOptions.value = res.data.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  } catch (error) {
    console.error('获取代理选项失败:', error);
    message.error('获取代理选项失败');
  } finally {
    userOptionsLoading.value = false;
  }
};

// 获取代理选项
const fetchUserOptions = async () => {
  try {
    userOptionsLoading.value = true;
    userOptions.value = [];
    const res = await getUserOptionsApi();
    console.log(res, 'res');
    res.data.map((item) => {
      console.log(item);
      userOptions.value.push(item);
    });

    console.log(userOptions.value);
    userOptions.value = [
      {
        label: '测试1',
        value: 12,
      },
      {
        label: '测试1',
        value: 12,
      },
    ];
    // if (res.code === 1) {
    //   userOptions.value = res.data.map((item) => ({
    //     label: item.name,
    //     value: item.id,
    //   }));
    // }
  } catch (error) {
    console.error('获取代理选项失败:', error);
    message.error('获取代理选项失败');
  } finally {
    userOptionsLoading.value = false;
  }
};

// 使用 computed 来动态更新配置项
const computedBasicItems = computed(() => {
  return basicItems.value.map((item) => {
    if (item.field === 'userId') {
      item.options = userOptions.value; // 更新 '归属代理' 的选项
      item.props.loading = userOptionsLoading.value; // 更新 loading 状态
    }
    if (item.field === 'deviceConfigIds') {
      item.options = deviceChannelOptions.value; // 更新 '通道' 的选项
      item.props.loading = userOptionsLoading.value; // 更新 loading 状态
    }
    return item;
  });
});

// 组件挂载时加载数据
onMounted(() => {
  // fetchUserOptions();
  getList();
  fetchOptions();
});
</script>

<template>
  <div class="channel-list p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="computedBasicItems"
        :advanced-items="advancedItems"
        :loading="loading"
        :custom-buttons="[
          // 添加
          {
            text: '添加通道',
            icon: MdiPlus,
            onClick: handleAdd,
          },
        ]"
        @search="handleSearch"
        @reset="handleReset"
      />

      <!-- 表格组件 -->
      <BasicTable
        :columns="columns"
        :data-source="tableData"
        :editable-columns="['apiName', 'notes', 'apiAccount', 'apiPassword']"
        @cell-save="handleCellSave"
        :loading="loading"
        :pagination="pagination"
        :status-options="statusOptions"
        :action-buttons="actionButtons"
        show-action
        @change="handleTableChange"
      />
    </Card>

    <!-- 添加/编辑通道弹窗 -->
    <component :is="renderModal()" />
  </div>
</template>

<style lang="less" scoped>
.channel-list {
  background-color: var(--background-deep);
  min-height: 100%;
}
</style>
