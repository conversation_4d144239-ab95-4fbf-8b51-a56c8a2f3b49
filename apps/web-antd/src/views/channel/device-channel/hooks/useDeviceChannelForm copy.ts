import type { SelectValue } from 'ant-design-vue/es/select';

import type { <PERSON><PERSON><PERSON>hannel } from '#/api';
import type { FormGroup } from '#/hooks/common/useFormModal';

import { h, ref } from 'vue';

import { MdiInformationOutline } from '@vben/icons';

import { useDebounceFn } from '@vueuse/core';
import {
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Select,
  Switch,
  Tooltip,
} from 'ant-design-vue';

import { addDeviceChannel, editApiConfigDevice } from '#/api';
import { getApiVersions } from '#/api/core/channel';
import { useFormModal } from '#/hooks/common/useFormModal';

const FormItem = Form.Item;

export interface UseChannelFormOptions {
  onSuccess?: () => void;
}

export function useChannelForm(options: UseChannelFormOptions = {}) {
  // 创建版本选项状态
  const versionOptions = ref<{ label: string; value: string }[]>([]);
  const versionLoading = ref(false);

  // 加载API版本的函数
  const loadApiVersions = useDebounceFn(async (platform: string) => {
    if (!platform?.trim()) {
      versionOptions.value = [];
      return;
    }

    try {
      versionLoading.value = true;
      const res = await getApiVersions(platform);
      versionOptions.value = res?.data?.length
        ? res.data.map((item: any) => ({
            label: item.versionName,
            value: item.platformIdentifying,
          }))
        : [];
    } catch (error) {
      console.error('Failed to get API versions:', error);
      message.error('获取API版本失败');
      versionOptions.value = [];
    } finally {
      versionLoading.value = false;
    }
  }, 300);

  // 设置API类型选项
  const apiTypeOptions = [
    { label: '套餐', value: 1 },
    { label: '前向流量池', value: 2 },
    { label: '后向流量池', value: 3 },
  ];

  // 删除或标记未使用的变量
  const _apiSubmitCardOptions = [
    { label: 'ICCID', value: 1 },
    { label: 'MIS', value: 2 },
    { label: '虚拟号', value: 3 },
  ];

  // 设置信息获取选项
  const apiSourcesOptions = [
    { label: '上游获取', value: 1 },
    { label: '系统自定义', value: 2 },
  ];

  // 使用通用表单模态框
  const {
    formData,
    visible,
    loading,
    isEdit: _isEdit,
    currentId: _currentId,
    show,
    close,
    setFormData: _setFormData,
    renderFormModal,
    handleSubmit: _handleSubmit,
  } = useFormModal<DeviceChannel>({
    title: (isEdit) => (isEdit ? '编辑通道' : '添加通道'),
    width: 900,
    onSuccess: options.onSuccess,
    // 为了配合 DeviceChannel 的类型，设置默认值
    defaultValues: {
      apiName: '',
      apiAccount: '',
      apiPassword: '',
      apiKey: '',
      apiKey2: '',
      requestAddress: '',
      apiType: 1,
      apiCardType: 1,
      apiSources: 1,
      apiPlatform: '',
      apiVersion: undefined,
      apiPolling: 1,
      singleCardSleep: 1000,
      apiOrderSubmission: 0,
      notes: '',
      // 仅为满足类型要求，不在UI中显示
      stopType: 0,
      pollingCardType: 1,
      rePolling: 0,
      orderMachine: 0,
    },
    // 创建通道方法
    create: async (params) => {
      return await addDeviceChannel(params);
    },
    // 更新通道方法
    update: async (id, params) => {
      return await editApiConfigDevice(id, params);
    },
  });

  // 监听平台变化加载版本选项
  const handlePlatformChange = (value: string) => {
    formData.apiPlatform = value;
    formData.apiVersion = undefined;
    versionOptions.value = [];
    if (value) {
      loadApiVersions(value);
    }
  };

  // 定义表单分组
  const formGroups: FormGroup[] = [
    {
      title: '基本信息',
      content: (formData: any) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '通道名称',
              name: 'apiName',
              rules: [{ required: true, message: '请输入通道名称' }],
            },
            () =>
              h(Input, {
                value: formData.apiName,
                'onUpdate:value': (val: string) => (formData.apiName = val),
                placeholder: '请输入通道名称',
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '对接平台',
              name: 'apiPlatform',
              rules: [{ required: true, message: '请输入对接平台' }],
            },
            () =>
              h(Input, {
                value: formData.apiPlatform,
                'onUpdate:value': (val: string) => handlePlatformChange(val),
                placeholder: '请输入对接平台',
                allowClear: true,
              }),
          ),
        ]),
        versionOptions.value.length > 0 &&
          h(Col, { span: 12 }, [
            h(
              FormItem,
              {
                label: '对接版本',
                name: 'apiVersion',
              },
              () =>
                h(Select, {
                  value: formData.apiVersion,
                  'onUpdate:value': (val: SelectValue): void => {
                    formData.apiVersion = String(val ?? '');
                  },
                  loading: versionLoading.value,
                  options: versionOptions.value,
                  placeholder: versionLoading.value
                    ? '加载中...'
                    : '请选择对接版本',
                  allowClear: true,
                  disabled: versionLoading.value,
                  showSearch: true,
                  filterOption: (input: string, option: any) => {
                    return option.label
                      .toLowerCase()
                      .includes(input.toLowerCase());
                  },
                }),
            ),
          ]),
      ],
    },
    {
      title: 'API配置',
      content: (formData: any) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API账号',
              name: 'apiAccount',
              rules: [{ required: true, message: '请输入API账号' }],
            },
            () =>
              h(Input, {
                value: formData.apiAccount,
                'onUpdate:value': (val: string) => (formData.apiAccount = val),
                placeholder: '请输入API账号',
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API密码',
              name: 'apiPassword',
              rules: [{ required: true, message: '请输入API密码' }],
            },
            () =>
              h(Input.Password, {
                value: formData.apiPassword,
                'onUpdate:value': (val: string) => (formData.apiPassword = val),
                placeholder: '请输入API密码',
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API密钥',
              name: 'apiKey',
            },
            () =>
              h(Input, {
                value: formData.apiKey,
                'onUpdate:value': (val: string) => (formData.apiKey = val),
                placeholder: '请输入API密钥',
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API密钥2',
              name: 'apiKey2',
            },
            () =>
              h(Input, {
                value: formData.apiKey2,
                'onUpdate:value': (val: string) => (formData.apiKey2 = val),
                placeholder: '请输入API密钥2',
                allowClear: true,
              }),
          ),
        ]),
      ],
    },
    {
      title: '接口配置',
      content: (formData: any) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API地址',
              name: 'requestAddress',
              rules: [{ required: true, message: '请输入API请求地址' }],
            },
            () =>
              h(Input, {
                value: formData.requestAddress,
                'onUpdate:value': (val: string) =>
                  (formData.requestAddress = val),
                placeholder: '请输入API请求地址',
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API类型',
              name: 'apiType',
              rules: [{ required: true, message: '请选择API类型' }],
            },
            () =>
              h(Select, {
                value: formData.apiType,
                'onUpdate:value': (val: SelectValue): void => {
                  formData.apiType = Number(val ?? 1);
                },
                options: apiTypeOptions,
                placeholder: '请选择API类型',
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '订购后复机',
              name: 'orderMachine',
            },
            () =>
              h(Switch, {
                checked: formData.orderMachine === 1,
                onChange: (checked: any) => {
                  formData.orderMachine = checked ? 1 : 2;
                },
                checkedChildren: '是',
                unCheckedChildren: '否',
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '信息获取',
              name: 'apiSources',
              rules: [{ required: true, message: '请选择信息获取方式' }],
            },
            () =>
              h(Select, {
                value: formData.apiSources,
                'onUpdate:value': (val: SelectValue): void => {
                  formData.apiSources = Number(val ?? 1);
                },
                options: apiSourcesOptions,
                placeholder: '请选择信息获取方式',
              }),
          ),
        ]),
      ],
    },
    {
      title: '轮询设置',
      content: (formData: any) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '接口轮询',
              name: 'apiPolling',
            },
            () =>
              h(Switch, {
                checked: formData.apiPolling === 2,
                onChange: (checked: any) => {
                  formData.apiPolling = checked ? 2 : 1;
                },
                checkedChildren: '开启',
                unCheckedChildren: '关闭',
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '休眠(ms)',
              name: 'singleCardSleep',
              rules: [{ required: true, message: '请输入单卡休眠时间' }],
            },
            () =>
              h('div', { class: 'flex items-center' }, [
                h(InputNumber, {
                  value: formData.singleCardSleep,
                  'onUpdate:value': (value: null | number | string) => {
                    formData.singleCardSleep =
                      typeof value === 'number' ? value : 1000;
                  },
                  min: 0,
                  style: { width: '120px' },
                  placeholder: '请输入',
                }),
                h(
                  Tooltip,
                  {
                    title: '单卡休眠时间',
                  },
                  () =>
                    h(MdiInformationOutline, {
                      class: 'ml-2 text-gray-400 cursor-help',
                      style: 'font-size: 18px',
                    }),
                ),
              ]),
          ),
        ]),
      ],
    },
    {
      title: '其他设置',
      content: (formData: any) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '订单递交',
              name: 'apiOrderSubmission',
            },
            () =>
              h(Switch, {
                checked: formData.apiOrderSubmission === 1,
                onChange: (checked: any) => {
                  formData.apiOrderSubmission = checked ? 1 : 2;
                },
                checkedChildren: '是',
                unCheckedChildren: '否',
              }),
          ),
        ]),
        h(Col, { span: 24 }, [
          h(
            FormItem,
            {
              label: '备注',
              name: 'notes',
              rules: [],
              labelCol: { span: 3 },
              wrapperCol: { span: 20 },
            },
            () =>
              h(Input.TextArea, {
                value: formData.notes,
                'onUpdate:value': (val: string) => (formData.notes = val),
                placeholder: '请输入备注',
                rows: 4,
                style: { width: '100%', resize: 'none' },
                allowClear: true,
              }),
          ),
        ]),
      ],
    },
  ];

  // 显示模态框
  const showModal = async (params?: any) => {
    if (params?.id) {
      // 编辑模式，直接使用传入的数据
      show(params.id);
      // 设置表单数据
      Object.keys(formData).forEach((key) => {
        if (params[key] !== undefined) {
          // 转换数据类型
          formData[key] =
            typeof formData[key] === 'number' && typeof params[key] === 'string'
              ? Number(params[key])
              : params[key];
        }
      });

      // 处理对接版本
      if (params.apiPlatform) {
        await loadApiVersions(params.apiPlatform);
      }
    } else {
      // 新增模式
      show();
    }
  };

  return {
    formData,
    visible,
    loading,
    showModal,
    show,
    closeModal: close,
    renderModal: () => renderFormModal(formGroups),
  };
}
