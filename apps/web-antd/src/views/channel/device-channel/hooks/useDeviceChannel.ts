// hooks/useDeviceChannel.ts
import type { ChannelItem } from '#/api/core/channel';

import { computed, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import {
  deleteCardByConfigId,
  deleteDeviceChannel,
  editApiConfigDevice,
  getDeviceChannelList,
  getDeviceChannelOptions,
  getUserOptionsApi,
  getVerifyDock,
} from '#/api';
import { useAnsheng } from '#/hooks/useAnsheng';

import { advancedSerachItems, basicSearchItems, columns } from '../config';
import { useChannelForm } from './useDeviceChannelForm';

/**
 * 设备通道管理Hook
 */
export function useDeviceChannel() {
  // 状态定义
  const userOptionsLoading = ref(true);
  const deviceChannelOptions = ref<{ label: string; value: number }[]>([]);
  const userOptions = ref<{ label: string; value: number }[]>([]);
  const formComponent = ref<any>(null);

  // 处理验证
  const handleVerify = async (record: ChannelItem) => {
    try {
      await getVerifyDock({
        configId: record.id,
        deviceNo: '123456',
      });
    } catch (error) {
      console.error('验证失败:', error);
    }
  };

  // 处理删除
  const handleDelete = (record: ChannelItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除通道 "${record.apiName}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      okButtonProps: { danger: true },
      async onOk() {
        try {
          await deleteDeviceChannel(record.id);
          table.getList();
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  // 处理删除所有卡
  const handleDeleteAll = (record: ChannelItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除通道 "${record.apiName}" 的所有卡吗？`,
      okText: '确定',
      cancelText: '取消',
      okButtonProps: { danger: true },
      async onOk() {
        await deleteCardByConfigId(String(record.id));
      },
    });
  };

  // 使用useAnsheng整合表格、搜索和表单功能
  const {
    tableData,
    loading,
    pagination,
    searchToolbarBind: origSearchToolbarBind,
    tableBind: origTableBind,
    handleTableChange,
    handleSearch,
    handleReset,
    table,
    form,
  } = useAnsheng({
    formOptions: {
      title: (isEdit) => (isEdit ? '编辑通道' : '添加通道'),
      width: 900,
      defaultValues: {
        apiName: '',
        apiAccount: '',
        apiPassword: '',
        apiKey: '',
        apiKey2: '',
        requestAddress: '',
        apiType: 1,
        apiCardType: 1,
        apiSources: 1,
        apiPlatform: '',
        apiVersion: undefined,
        apiPolling: 1,
        singleCardSleep: 1000,
        apiOrderSubmission: 0,
        notes: '',
        stopType: 0,
        pollingCardType: 1,
        rePolling: 0,
        orderMachine: 0,
      },
      create: async (params) => {
        return await addDeviceChannel(params);
      },
      update: async (id, params) => {
        return await editApiConfigDevice(id, params);
      },
      onSuccess: () => {
        table.getList();
      },
    },
    tableOptions: {
      api: getDeviceChannelList,
      columns,
      editableColumns: ['apiName', 'notes', 'apiAccount', 'apiPassword'],
      onSave: async (record, column, value) => {
        return await editApiConfigDevice(record.id, {
          ...record,
          [column]: value,
        });
      },
      actionButtons: [
        {
          key: 'verify',
          text: '验证',
          type: 'link',
          onClick: (record) => handleVerify(record),
        },
        {
          key: 'edit',
          text: '编辑',
          type: 'link',
          onClick: (record) => formComponent.value.show(record),
        },
        {
          key: 'delete',
          text: '删除通道',
          type: 'link',
          danger: true,
          onClick: (record) => handleDelete(record),
        },
        {
          key: 'deleteAll',
          text: '删除所有卡',
          type: 'link',
          danger: true,
          onClick: (record) => handleDeleteAll(record),
        },
      ],
    },
    searchOptions: {
      basicItems: basicSearchItems,
      advancedItems: advancedSerachItems,
      defaultValues: {},
      customButtons: [
        {
          text: '添加通道',
          icon: MdiPlus,
          onClick: () => formComponent.value.show(),
        },
      ],
    },
    deleteApi: deleteDeviceChannel,
  });

  // 获取选项数据
  const fetchOptions = async () => {
    try {
      userOptionsLoading.value = true;
      const res = await getUserOptionsApi();
      const channelRes = await getDeviceChannelOptions();

      deviceChannelOptions.value = channelRes.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));
      userOptions.value = res.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    } catch (error) {
      console.error('获取代理选项失败:', error);
      message.error('获取代理选项失败');
    } finally {
      userOptionsLoading.value = false;
    }
  };

  // 使用 computed 来动态更新搜索项
  const searchToolbarBind = computed(() => {
    const bind = { ...origSearchToolbarBind.value };

    // 更新基础搜索项的选项
    if (bind.basicItems) {
      bind.basicItems = bind.basicItems.map((item) => {
        if (item.field === 'userId') {
          return {
            ...item,
            options: userOptions.value,
            props: { ...item.props, loading: userOptionsLoading.value },
          };
        }
        if (item.field === 'deviceConfigIds') {
          return {
            ...item,
            options: deviceChannelOptions.value,
            props: { ...item.props, loading: userOptionsLoading.value },
          };
        }
        return item;
      });
    }

    return bind;
  });

  // 表格绑定属性
  const tableBind = computed(() => ({
    ...origTableBind.value,
    statusOptions: {
      apiPolling: [
        { label: '启用', value: 1, color: 'success' },
        { label: '禁用', value: 0, color: 'error' },
      ],
      apiType: [
        { label: '套餐', value: 1 },
        { label: '前向流量池', value: 2 },
        { label: '后向流量池', value: 3 },
      ],
      apiCardType: [
        { label: 'ICCID', value: 1 },
        { label: 'MIS', value: 2 },
        { label: '虚拟号', value: 3 },
      ],
    },
  }));

  // 初始化数据
  const initialize = async () => {
    await table.getList();
    await fetchOptions();
  };

  // 创建表单组件
  formComponent.value = useChannelForm({
    onSuccess: () => {
      table.getList();
    },
  });

  return {
    // 状态
    tableData,
    loading,
    pagination,
    userOptions,
    deviceChannelOptions,
    userOptionsLoading,

    // 绑定属性
    searchToolbarBind,
    tableBind,

    // 方法
    handleTableChange,
    handleSearch,
    handleReset,
    handleDelete,
    handleDeleteAll,
    handleVerify,
    initialize,

    // 子组件
    formComponent: formComponent.value.renderModal,
    form,
    table,
  };
}
