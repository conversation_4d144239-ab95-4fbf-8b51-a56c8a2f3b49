import type { Device<PERSON>hannel } from '#/api';
import type { FormGroup } from '#/hooks/useAnsheng/types';

import { h, ref, watch } from 'vue';

import { MdiInformationOutline, MdiListBox, MdiPencil } from '@vben/icons';

import { useDebounceFn } from '@vueuse/core';
import { Input, InputNumber, Select, Switch, Tooltip } from 'ant-design-vue';

import {
  addDeviceChannel,
  editApiConfigDevice,
  getPlatformDevice,
} from '#/api';
import { getApiVersions } from '#/api/core/channel';
import { useForm } from '#/hooks/useAnsheng/useForm';

export interface UseChannelFormOptions {
  onSuccess?: () => void;
}

export function useChannelForm(options: UseChannelFormOptions = {}) {
  // 创建版本选项状态
  const versionOptions = ref<
    { label: string; prompt?: string; value: string }[]
  >([]);
  const versionLoading = ref(false);

  // 添加平台输入模式控制
  const isPlatformInputMode = ref(false);
  const platformOptions = ref<
    { label: string; prompt?: string; type?: number; value: string }[]
  >([]);
  const platformLoading = ref(false);

  // 新增：平台提示信息映射
  const platformPromptMap = ref<Record<string, string>>({});
  // 新增：版本提示信息映射
  const versionPromptMap = ref<Record<string, string>>({});

  // 加载平台选项
  const loadPlatformOptions = async () => {
    try {
      platformLoading.value = true;
      const res = await getPlatformDevice();
      if (res?.data?.length) {
        platformOptions.value = res.data.map((item) => ({
          label: item.name,
          value: item.id,
          prompt: item.prompt, // 提示信息
          type: item.type, // 类型
        }));

        // 构建提示信息映射 - 使用id作为key
        res.data.forEach((item) => {
          if (item.prompt) {
            platformPromptMap.value[item.id] = item.prompt;
          }
        });
      }
    } catch (error) {
      console.error('Failed to load platform options:', error);
    } finally {
      platformLoading.value = false;
    }
  };

  // 初始加载平台选项
  loadPlatformOptions();

  // 切换平台输入模式
  const togglePlatformInputMode = () => {
    isPlatformInputMode.value = !isPlatformInputMode.value;
  };

  // 加载API版本的函数
  const loadApiVersions = useDebounceFn(async (platform: string) => {
    if (!platform?.trim()) {
      versionOptions.value = [];
      return;
    }

    try {
      versionLoading.value = true;
      const res = await getApiVersions(platform);
      versionOptions.value = res?.data?.length
        ? res.data.map((item: any) => ({
            label: item.versionName,
            value: item.platformIdentifying,
            prompt: item.prompt,
          }))
        : [];

      // 构建提示信息映射
      res.data.forEach((item: any) => {
        if (item.prompt) {
          versionPromptMap.value[item.platformIdentifying] = item.prompt;
        }
      });
    } catch (error) {
      console.error('Failed to get API versions:', error);
      versionOptions.value = [];
    } finally {
      versionLoading.value = false;
    }
  }, 300);

  // 获取平台的提示信息
  const getPlatformPrompt = (platform: string): string | undefined => {
    return platform ? platformPromptMap.value[platform] : undefined;
  };

  // 获取版本的提示信息
  const getVersionPrompt = (version: string): string | undefined => {
    return version ? versionPromptMap.value[version] : undefined;
  };

  // 设置API类型选项
  const apiTypeOptions = [
    { label: '套餐', value: 1 },
    { label: '前向流量池', value: 2 },
    { label: '后向流量池', value: 3 },
  ];

  // 设置信息获取选项
  const apiSourcesOptions = [
    { label: '上游获取', value: 1 },
    { label: '系统自定义', value: 2 },
  ];

  const formHook = useForm<DeviceChannel>({
    title: (isEdit) => (isEdit ? '编辑通道' : '添加通道'),
    width: 900,
    onSuccess: options.onSuccess,
    // 默认值
    defaultValues: {
      apiName: '',
      apiAccount: '',
      apiPassword: '',
      apiKey: '',
      apiKey2: '',
      requestAddress: '',
      apiType: 1,
      apiCardType: 1,
      apiSources: 1,
      apiPlatform: undefined,
      apiVersion: undefined,
      apiPolling: 1,
      singleCardSleep: 1000,
      apiOrderSubmission: 0,
      notes: '',
      // 仅为满足类型要求的字段
      stopType: 0,
      pollingCardType: 1,
      rePolling: 0,
      orderMachine: 0,
    },
    // 接口调用
    create: async (params) => {
      return await addDeviceChannel(params);
    },
    update: async (id, params) => {
      return await editApiConfigDevice(id, params);
    },
  });

  const { formData } = formHook;

  // 或者使用watch监听visible变化
  watch(
    () => formHook.visible.value,
    (newVisible) => {
      if (newVisible) {
        // 当窗口打开时，可以在这里添加初始化逻辑
      } else {
        // 当窗口关闭时
        // 重置为默认状态
        isPlatformInputMode.value = false;
        versionOptions.value = [];

        // 可选：如果要在下次打开前预加载平台选项
        if (platformOptions.value.length === 0) {
          loadPlatformOptions();
        }
      }
    },
  );

  // 监听平台变化加载版本选项
  const handlePlatformChange = (value: string) => {
    formData.apiPlatform = value;
    formData.apiVersion = undefined;
    versionOptions.value = [];
    if (value) {
      loadApiVersions(value);
    }
  };

  // 定义表单分组 - 使用声明式配置
  const formGroups: FormGroup[] = [
    {
      title: '基本信息',
      fields: [
        {
          name: 'apiName',
          label: '通道名称',
          component: 'Input',
          rules: [{ required: true, message: '请输入通道名称' }],
          props: {
            placeholder: '请输入通道名称',
            allowClear: true,
            trim: 'both',
          },
          col: { span: 12 },
        },
        {
          name: 'apiPlatform',
          label: '对接平台',
          component: 'Input',
          rules: [{ required: true, message: '请输入对接平台' }],
          props: {
            placeholder: '请输入对接平台',
            allowClear: true,
            trim: 'both',
            onChange: (e: any) => handlePlatformChange(e.target.value),
          },
          col: { span: 12 },
          render: () => {
            // 获取当前平台的提示信息
            const prompt = formData.apiPlatform
              ? platformPromptMap.value[formData.apiPlatform]
              : undefined;

            return h('div', [
              h('div', { style: 'display: flex; align-items: center;' }, [
                // 根据当前模式渲染不同的组件
                isPlatformInputMode.value
                  ? h(Input, {
                      value: formData.apiPlatform,
                      'onUpdate:value': (val: string) =>
                        handlePlatformChange(val),
                      placeholder: '请输入对接平台',
                      allowClear: true,
                      style: { flex: 1 },
                    })
                  : h(Select, {
                      value: formData.apiPlatform,
                      'onUpdate:value': (val: string) =>
                        handlePlatformChange(val),
                      options: platformOptions.value,
                      loading: platformLoading.value,
                      placeholder: '请选择对接平台',
                      allowClear: true,
                      showSearch: true,
                      filterOption: (input: string, option: any) => {
                        return option.label
                          .toLowerCase()
                          .includes(input.toLowerCase());
                      },
                      style: { flex: 1 },
                    }),
                // 切换按钮
                h(
                  'div',
                  {
                    style: 'margin-left: 8px; cursor: pointer;',
                    onClick: togglePlatformInputMode,
                    title: isPlatformInputMode.value
                      ? '切换为选择模式'
                      : '切换为输入模式',
                  },
                  [
                    h(isPlatformInputMode.value ? MdiListBox : MdiPencil, {
                      style: 'font-size: 18px;',
                    }),
                  ],
                ),
              ]),
              // 如果有提示信息，显示在下方
              prompt
                ? h('div', { class: 'text-red-500 text-xs mt-1' }, prompt)
                : null,
            ]);
          },
        },
        {
          name: 'apiVersion',
          label: '对接版本',
          component: 'Select',
          props: {
            placeholder: versionLoading.value ? '加载中...' : '请选择对接版本',
            allowClear: true,
            disabled: versionLoading.value,
            showSearch: true,
            filterOption: (input: string, option: any) => {
              return option.label.toLowerCase().includes(input.toLowerCase());
            },
            loading: versionLoading.value,
            options: versionOptions,
          },
          col: { span: 12 },
          // 只在有版本选项时显示
          show: () => versionOptions.value.length > 0,
          render: () => {
            // 获取当前版本的提示信息
            const prompt = formData.apiVersion
              ? versionPromptMap.value[formData.apiVersion]
              : undefined;

            return h('div', [
              h(Select, {
                value: formData.apiVersion,
                'onUpdate:value': (val: any): void => {
                  formData.apiVersion = val;
                },
                loading: versionLoading.value,
                options: versionOptions.value,
                placeholder: versionLoading.value
                  ? '加载中...'
                  : '请选择对接版本',
                allowClear: true,
                disabled: versionLoading.value,
                showSearch: true,
                filterOption: (input: string, option: any) => {
                  return option.label
                    .toLowerCase()
                    .includes(input.toLowerCase());
                },
              }),

              // 提示信息 - 使用pre标签保留换行符
              prompt
                ? h(
                    'pre',
                    {
                      class:
                        'text-red-500 text-xs mt-1 whitespace-pre-wrap break-words',
                      style: 'margin: 0; font-family: inherit;',
                    },
                    prompt,
                  )
                : null,
            ]);
          },
        },
      ],
    },
    {
      title: 'API配置',
      fields: [
        {
          name: 'apiAccount',
          label: 'API账号',
          component: 'Input',
          rules: [{ required: true, message: '请输入API账号' }],
          props: {
            placeholder: '请输入API账号',
            allowClear: true,
            trim: 'both',
          },
          col: { span: 12 },
        },
        {
          name: 'apiPassword',
          label: 'API密码',
          component: 'Input',
          rules: [{ required: true, message: '请输入API密码' }],
          props: {
            type: 'password',
            placeholder: '请输入API密码',
            allowClear: true,
          },
          col: { span: 12 },
        },
        {
          name: 'apiKey',
          label: 'API密钥',
          component: 'Input',
          props: {
            placeholder: '请输入API密钥',
            allowClear: true,
            trim: 'both',
          },
          col: { span: 12 },
        },
        {
          name: 'apiKey2',
          label: 'API密钥2',
          component: 'Input',
          props: {
            placeholder: '请输入API密钥2',
            allowClear: true,
            trim: 'both',
          },
          col: { span: 12 },
        },
      ],
    },
    {
      title: '接口配置',
      fields: [
        {
          name: 'requestAddress',
          label: 'API地址',
          component: 'Input',
          rules: [{ required: true, message: '请输入API请求地址' }],
          props: {
            placeholder: '请输入API请求地址',
            allowClear: true,
            trim: 'both',
          },
          col: { span: 12 },
        },
        {
          name: 'apiType',
          label: 'API类型',
          component: 'Select',
          rules: [{ required: true, message: '请选择API类型' }],
          props: {
            placeholder: '请选择API类型',
            options: apiTypeOptions,
          },
          col: { span: 12 },
        },
        {
          name: 'orderMachine',
          label: '订购后复机',
          component: 'Switch',
          props: {
            checkedChildren: '是',
            unCheckedChildren: '否',
            // 转换值：1 为开，2 为关
            checked: formData.orderMachine === 1,
            onChange: (checked: boolean) => {
              formData.orderMachine = checked ? 1 : 2;
            },
          },
          col: { span: 12 },
          render: () => {
            return h(Switch, {
              checked: formData.orderMachine === 1,
              onChange: (checked: boolean) => {
                formData.orderMachine = checked ? 1 : 2;
              },
              checkedChildren: '是',
              unCheckedChildren: '否',
            });
          },
        },
        {
          name: 'apiSources',
          label: '信息获取',
          component: 'Select',
          rules: [{ required: true, message: '请选择信息获取方式' }],
          props: {
            placeholder: '请选择信息获取方式',
            options: apiSourcesOptions,
          },
          col: { span: 12 },
        },
      ],
    },
    {
      title: '轮询设置',
      fields: [
        {
          name: 'apiPolling',
          label: '接口轮询',
          component: 'Switch',
          props: {
            checkedChildren: '开启',
            unCheckedChildren: '关闭',
            // 转换值：2 为开，1 为关
            checked: formData.apiPolling === 2,
            onChange: (checked: boolean) => {
              formData.apiPolling = checked ? 2 : 1;
            },
          },
          col: { span: 12 },
          render: () => {
            return h(Switch, {
              checked: formData.apiPolling === 2,
              onChange: (checked: boolean) => {
                formData.apiPolling = checked ? 2 : 1;
              },
              checkedChildren: '开启',
              unCheckedChildren: '关闭',
            });
          },
        },
        {
          name: 'singleCardSleep',
          label: '休眠(ms)',
          component: 'InputNumber',
          rules: [{ required: true, message: '请输入单卡休眠时间' }],
          props: {
            placeholder: '请输入',
            min: 0,
            style: { width: '120px' },
          },
          col: { span: 12 },
          // 自定义渲染添加提示图标
          render: () =>
            h('div', { class: 'flex items-center' }, [
              h('div', {}, [
                h(InputNumber, {
                  value: formData.singleCardSleep,
                  'onUpdate:value': (val: any) => {
                    formData.singleCardSleep =
                      typeof val === 'number' ? val : Number.parseInt(val) || 0;
                  },
                  min: 0,
                  style: { width: '120px' },
                  placeholder: '请输入',
                }),
              ]),
              h(
                Tooltip,
                {
                  title: '单卡休眠时间',
                },
                () =>
                  h(MdiInformationOutline, {
                    class: 'ml-2 text-gray-400 cursor-help',
                    style: 'font-size: 18px',
                  }),
              ),
            ]),
        },
      ],
    },
    {
      title: '其他设置',
      fields: [
        {
          name: 'apiOrderSubmission',
          label: '订单递交',
          component: 'Switch',
          props: {
            checkedChildren: '是',
            unCheckedChildren: '否',
            // 转换值：1 为是，2 为否
            checked: formData.apiOrderSubmission === 1,
            onChange: (checked: boolean) => {
              formData.apiOrderSubmission = checked ? 1 : 2;
            },
          },
          col: { span: 12 },
          render: () => {
            return h(Switch, {
              checked: formData.apiOrderSubmission === 1,
              onChange: (checked: boolean) => {
                formData.apiOrderSubmission = checked ? 1 : 2;
              },
              checkedChildren: '是',
              unCheckedChildren: '否',
            });
          },
        },
        {
          name: 'notes',
          label: '备注',
          component: 'TextArea',
          props: {
            placeholder: '请输入备注',
            rows: 4,
            style: { width: '100%', resize: 'none' },
            allowClear: true,
            trim: 'both',
          },
          col: { span: 24 },
          labelCol: { span: 3 },
          wrapperCol: { span: 20 },
        },
      ],
    },
  ];

  // 显示模态框，支持编辑模式
  const showModal = async (params?: any) => {
    if (params?.id) {
      // 编辑模式
      await formHook.show(params.id);

      // 手动设置表单数据
      Object.keys(formData).forEach((key) => {
        if (params[key] !== undefined) {
          // 转换数据类型
          formData[key] =
            typeof formData[key] === 'number' && typeof params[key] === 'string'
              ? Number(params[key])
              : params[key];
        }
      });

      // 处理对接版本
      if (params.apiPlatform) {
        await loadApiVersions(params.apiPlatform);
      }
    } else {
      // 新增模式
      await formHook.show();
    }
  };

  return {
    formData,
    visible: formHook.visible,
    loading: formHook.loading,
    showModal,
    show: formHook.show,
    closeModal: formHook.close,
    renderModal: () => formHook.renderFormModal(formGroups),
  };
}
