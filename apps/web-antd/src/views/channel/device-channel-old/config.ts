import type { ColumnType } from 'ant-design-vue/es/table';

import type { ChannelItem } from '#/api/core/channel';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import dayjs from 'dayjs';

// API类型选项
const apiTypeOptions = [
  { label: '第三方套餐', value: 1 },
  { label: '前向流量池', value: 2 },
];

// 信息来源选项
const apiSourcesOptions = [
  { label: '上游获取', value: 1 },
  { label: '系统自定义', value: 2 },
];

// 开关选项
const switchOptions = [
  { label: '开启', value: 1 },
  { label: '关闭', value: 2 },
];

// 是否选项
const yesNoOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 2 },
];

// 基础搜索配置
export const basicSearchItems: SearchItemConfig[] = [
  {
    field: 'apiName',
    label: '通道名称',
    component: 'Input',
    props: {
      placeholder: '请输入通道名称',
      allowClear: true,
    },
  },
  {
    field: 'apiType',
    label: 'API类型',
    component: 'Select',
    props: {
      placeholder: '请选择API类型',
      allowClear: true,
      options: apiTypeOptions,
    },
  },
  {
    field: 'apiPlatform',
    label: '对接标识',
    component: 'Input',
    props: {
      placeholder: '请输入对接标识',
      allowClear: true,
    },
  },
];

// 高级搜索配置
export const advancedSerachItems = [
  {
    label: '基本信息',
    items: [
      {
        field: 'notes',
        label: '备注',
        component: 'Input',
        props: {
          placeholder: '请输入备注',
          allowClear: true,
        },
      },
      {
        field: 'apiSources',
        label: '信息来源',
        component: 'Select',
        props: {
          placeholder: '请选择信息来源',
          allowClear: true,
          options: apiSourcesOptions,
        },
      },
      {
        field: 'requestAddress',
        label: '请求域名',
        component: 'Input',
        props: {
          placeholder: '请输入请求域名',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '账户信息',
    items: [
      {
        field: 'apiAccount',
        label: '对接账户',
        component: 'Input',
        props: {
          placeholder: '请输入对接账户',
          allowClear: true,
        },
      },
      {
        field: 'apiPassword',
        label: '对接密码',
        component: 'Input',
        props: {
          placeholder: '请输入对接密码',
          allowClear: true,
        },
      },
      {
        field: 'apiKey',
        label: 'API密钥',
        component: 'Input',
        props: {
          placeholder: '请输入API密钥',
          allowClear: true,
        },
      },
      {
        field: 'apiKey2',
        label: '额外密钥',
        component: 'Input',
        props: {
          placeholder: '请输入额外密钥',
          allowClear: true,
        },
      },
    ],
  },
  {
    label: '预存区间',
    items: [
      {
        field: 'apiBalanceMin',
        label: '最小预存',
        component: 'Input',
        props: {
          placeholder: '请输入最小预存',
          allowClear: true,
          type: 'number',
          min: 0,
          step: 0.01,
        },
      },
      {
        field: 'apiBalanceMax',
        label: '最大预存',
        component: 'Input',
        props: {
          placeholder: '请输入最大预存',
          allowClear: true,
          type: 'number',
          min: 0,
          step: 0.01,
        },
      },
    ],
  },
  {
    label: '轮询设置',
    items: [
      {
        field: 'apiPolling',
        label: '设备轮询',
        component: 'Select',
        props: {
          placeholder: '请选择设备轮询',
          allowClear: true,
          options: switchOptions,
        },
      },
      {
        field: 'singleCardSleepMin',
        label: '最小休眠时间(ms)',
        component: 'Input',
        props: {
          placeholder: '请输入最小休眠时间',
          allowClear: true,
          type: 'number',
          min: 0,
        },
      },
      {
        field: 'singleCardSleepMax',
        label: '最大休眠时间(ms)',
        component: 'Input',
        props: {
          placeholder: '请输入最大休眠时间',
          allowClear: true,
          type: 'number',
          min: 0,
        },
      },
    ],
  },
  {
    label: '订单设置',
    items: [
      {
        field: 'apiOrderSubmission',
        label: '订单递交',
        component: 'Select',
        props: {
          placeholder: '请选择订单递交',
          allowClear: true,
          options: yesNoOptions,
        },
      },
      {
        field: 'orderMachine',
        label: '订购套餐时调用复机',
        component: 'Select',
        props: {
          placeholder: '请选择是否调用复机',
          allowClear: true,
          options: yesNoOptions,
        },
      },
    ],
  },
  {
    label: '时间区间',
    items: [
      {
        field: 'creationTimeBegin',
        label: '创建时间(开始)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          style: { width: '100%' },
        },
      },
      {
        field: 'creationTimeEnd',
        label: '创建时间(结束)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          style: { width: '100%' },
        },
      },
      {
        field: 'updateTimeBegin',
        label: '修改时间(开始)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择开始时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          style: { width: '100%' },
        },
      },
      {
        field: 'updateTimeEnd',
        label: '修改时间(结束)',
        component: 'DatePicker',
        props: {
          placeholder: '请选择结束时间',
          allowClear: true,
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          style: { width: '100%' },
        },
      },
    ],
  },
];

// 表格列定义
export const columns: ColumnType<ChannelItem>[] = [
  {
    title: '通道名称',
    dataIndex: 'apiName',
    width: 200,
    align: 'center' as const,
  },
  {
    title: '通道备注',
    dataIndex: 'notes',
    width: 100,
    align: 'center' as const,
    ellipsis: true,
  },
  {
    title: 'API类型',
    dataIndex: 'apiType',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '对接标识',
    dataIndex: 'apiPlatform',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '上游预存',
    dataIndex: 'apiBalanceMin',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '对接账户',
    dataIndex: 'apiAccount',
    width: 120,
    align: 'center' as const,
  },
  {
    title: '对接密码',
    dataIndex: 'apiPassword',
    width: 100,
    align: 'center' as const,
  },
  {
    title: 'API密钥',
    dataIndex: 'apiKey',
    width: 100,
    align: 'center' as const,
  },
  {
    title: 'API密钥2',
    dataIndex: 'apiKey2',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '信息来源',
    dataIndex: 'apiSources',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '请求域名',
    dataIndex: 'requestAddress',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '设备轮询',
    dataIndex: 'apiPolling',
    width: 100,
    align: 'center' as const,
    customRender: ({ text }: { text: string }) =>
      text === 1 ? '开启' : '关闭',
  },
  {
    title: '请求地址',
    dataIndex: 'requestAddress',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '轮询休眠',
    dataIndex: 'singleCardSleepMan',
    width: 100,
    align: 'center' as const,
  },
  {
    title: '订单提交',
    dataIndex: 'apiOrderSubmission',
    width: 100,
    align: 'center' as const,
    customRender: ({ text }: { text: string }) => (text === 1 ? '是' : '否'),
  },
  {
    title: '订购后复机',
    dataIndex: 'orderMachine',
    width: 100,
    align: 'center' as const,
    ellipsis: true,
    customRender: ({ text }: { text: string }) => (text === 1 ? '是' : '否'),
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
    width: 180,
    align: 'center' as const,
    customRender: ({ text }: { text: string }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
  {
    title: '修改时间',
    dataIndex: 'creationTime',
    width: 180,
    align: 'center' as const,
    customRender: ({ text }: { text: string }) =>
      text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
  },
];
