<script setup lang="ts">
import { onMounted } from 'vue';

import { Card } from 'ant-design-vue';

import { BasicTable, SearchToolbar } from '#/hooks/useAnsheng';

import ViewModal from './components/view-modal.vue';
import { useCardChannel } from './hooks/useCardChannel';

defineOptions({ name: 'CardChannelList' });

// 使用封装的useCardChannel hook
const {
  // 状态
  viewVisible,
  currentItem,
  viewLoading,

  // 表格和搜索相关
  loading,
  searchToolbarBind,
  tableBind,
  handleSearch,
  handleReset,

  // 方法
  initialize,
  renderModal,
} = useCardChannel();

// 组件挂载时加载数据
onMounted(() => {
  initialize();
});
</script>

<template>
  <div class="channel-list p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar v-bind="searchToolbarBind" />

      <!-- 表格组件 -->
      <BasicTable v-bind="tableBind" />
    </Card>

    <!-- 查看弹窗 -->
    <ViewModal
      v-model:visible="viewVisible"
      :data="currentItem"
      :loading="viewLoading"
    />

    <!-- 添加/编辑通道弹窗 -->
    <component :is="renderModal()" />
  </div>
</template>

<style lang="less" scoped>
.channel-list {
  background-color: var(--background-deep);
  min-height: 100%;
}
</style>
