// useCardChannel.ts
// 使用useAnsheng实现卡通道管理功能

import type { ChannelItem } from '#/api/core/channel';

import { computed, ref } from 'vue';

import { message, Modal } from 'ant-design-vue';

import {
  copyChannel,
  deleteChannel,
  excelApiConfigList,
  getChannelDetail,
  getChannelList,
  verifyChannel,
} from '#/api/core/channel';
import { useAnsheng } from '#/hooks/useAnsheng';
import { useExportNotify } from '#/hooks/useExportNotify';

import { advancedSearchConfig, columns, searchConfig } from '../config';
import { useChannelForm } from './useChannelForm';

/**
 * 卡通道管理Hook
 */
export function useCardChannel() {
  // 查看弹窗状态
  const viewVisible = ref(false);
  const currentItem = ref<ChannelItem>();
  const viewLoading = ref(false);

  const { showExportModal } = useExportNotify();

  // 使用useAnsheng整合表格、搜索和表单功能
  const {
    tableData,
    loading,
    pagination,
    searchToolbarBind,
    tableBind: origTableBind,
    handleTableChange,
    handleSearch,
    handleReset,
    initialize,
    table,
  } = useAnsheng({
    // 表单配置
    formOptions: {
      draggable: true,
      title: (isEdit) => (isEdit ? '编辑通道' : '添加通道'),
      defaultValues: {
        name: '',
        apiName: '',
        status: 1,
      },
    },
    // 表格配置
    tableOptions: {
      api: getChannelList,
      columns,
      actionButtons: [
        {
          key: 'view',
          text: '查看',
          type: 'link',
          onClick: (record: ChannelItem) => handleView(record),
        },
        {
          key: 'verify',
          text: '验证',
          type: 'link',
          onClick: (record: ChannelItem) => handleVerify(record),
        },
        {
          key: 'copy',
          text: '复制',
          type: 'link',
          onClick: (record: ChannelItem) => handleCopy(record),
        },
        {
          key: 'edit',
          text: '编辑',
          type: 'link',
          onClick: (record: ChannelItem) => handleEdit(record),
        },
        {
          key: 'delete',
          text: '删除',
          type: 'link',
          danger: true,
          onClick: (record: ChannelItem) => handleDelete(record),
        },
      ],
    },
    // 搜索配置
    searchOptions: {
      basicItems: searchConfig,
      advancedItems: advancedSearchConfig,
      customButtons: [
        {
          text: '导出',
          type: 'primary',
          icon: 'MdiExport',
          onClick: () => handleExport(),
        },
        {
          text: '添加通道',
          icon: 'MdiPlus',
          onClick: () => handleAdd(),
        },
      ],
    },
  });

  // 添加通道相关
  const { renderModal, show } = useChannelForm({
    onSuccess: () => {
      initialize();
    },
  });

  // 处理添加
  const handleAdd = () => {
    show();
  };

  // 处理导出
  const handleExport = async () => {
    loading.value = true;
    await excelApiConfigList(table.searchParams);
    loading.value = false;
    showExportModal();
  };

  // 处理编辑
  const handleEdit = (record: ChannelItem) => {
    show(record.id);
  };

  // 处理删除
  const handleDelete = (record: ChannelItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除通道 "${record.apiName}" 吗？`,
      okText: '确定',
      cancelText: '取消',
      okButtonProps: { danger: true },
      async onOk() {
        try {
          const res = await deleteChannel(record.id);
          if (res.code === 1) {
            message.success(res.msg || '删除成功');
            initialize();
          } else {
            message.error(res.msg || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  // 处理查看
  const handleView = async (record: ChannelItem) => {
    currentItem.value = undefined;
    viewVisible.value = true;
    viewLoading.value = true;
    try {
      const res = await getChannelDetail(record.id);
      if (res.code === 1) {
        currentItem.value = res.data;
      } else {
        message.error(res.msg || '获取详情失败');
        viewVisible.value = false;
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      message.error('获取详情失败');
      viewVisible.value = false;
    } finally {
      viewLoading.value = false;
    }
  };

  // 处理验证
  const handleVerify = async (record: ChannelItem) => {
    loading.value = true;
    try {
      const res = await verifyChannel({
        id: record.id,
        apiName: record.apiName,
        cardNo: '123456',
      });

      message.success(res.msg || '验证成功');
    } catch (error) {
      console.error('验证失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 处理复制
  const handleCopy = async (record: ChannelItem) => {
    Modal.confirm({
      title: '复制通道',
      content: `确定要复制通道 "${record.apiName}" 吗？`,
      async onOk() {
        try {
          const res = await copyChannel(record.id);
          if (res.code === 1) {
            message.success('复制成功');
            initialize();
          } else {
            message.error(res.msg || '复制失败');
          }
        } catch (error) {
          console.error('复制失败:', error);
          message.error('复制失败');
        }
      },
    });
  };

  // 表格绑定对象 - 直接使用固定的actionButtons
  const tableBind = computed(() => ({
    ...origTableBind.value,
    onChange: handleTableChange,
  }));

  return {
    // 状态
    viewVisible,
    currentItem,
    viewLoading,

    // 表格和搜索相关
    tableData,
    loading,
    pagination,
    searchToolbarBind,
    tableBind,
    handleTableChange,
    handleSearch,
    handleReset,

    // 方法
    initialize,
    handleAdd,
    handleExport,
    handleEdit,
    handleDelete,
    handleView,
    handleVerify,
    handleCopy,

    // 表单相关
    renderModal,
  };
}
