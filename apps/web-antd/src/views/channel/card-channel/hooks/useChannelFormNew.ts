import { ref } from 'vue';

import { MdiInformationOutline } from '@vben/icons';

import { useDebounceFn } from '@vueuse/core';

import {
  createChannel,
  getApiVersions,
  getChannelDetail,
  updateChannel,
} from '#/api/core/channel';
import { useForm } from '#/hooks/useAnsheng';

export interface UseChannelFormOptions {
  onSuccess?: () => void;
}

export interface ChannelFormState {
  name: string;
  apiAccount: string;
  apiPassword: string;
  apiKeys: Record<string, string>;
  apiUrl: string;
  apiUrl2: string;
  apiType: string;
  apiSubmitCard: string;
  infoGet: string;
  platform: string | undefined;
  apiVersion?: string;
  pollStopType: string;
  apiPolling: number;
  sleepTime: number;
  orderSubmit: number;
  notes?: string;
  maximumDosage?: string;
}

// 平台类型映射
const platformTypeLabels: Record<string, string> = {
  '1': '流量池',
  '2': '物联卡',
  '3': '行业卡',
  '4': 'NB卡',
};

export function useChannelFormNew(options: UseChannelFormOptions = {}) {
  // 平台数据映射
  const platformTypeMap = ref<
    Record<string, { prompt?: string; type: string }>
  >({});

  // API版本选项
  const versionLoading = ref(false);
  const versionOptions = ref<
    { label: string; prompt?: string; value: string }[]
  >([]);

  // 固定选项
  const apiTypeOptions = [
    { label: '第三方套餐', value: '1' },
    { label: '直连运营商', value: '2' },
  ];

  const apiSubmitCardOptions = [
    { label: 'ICCID', value: '1' },
    { label: 'MIS', value: '2' },
    { label: '虚拟号', value: '3' },
  ];

  const infoGetOptions = [
    { label: '上游获取', value: '1' },
    { label: '系统自定义', value: '2' },
  ];

  const pollStopTypeOptions = [
    { label: '停机复机', value: '1' },
    { label: '断网复网', value: '2' },
  ];

  // 初始表单状态
  const defaultFormState: ChannelFormState = {
    name: '',
    apiAccount: '',
    apiPassword: '',
    apiKeys: {
      apiKey: '',
    },
    apiUrl: '',
    apiUrl2: '',
    apiType: '1',
    apiSubmitCard: '1',
    infoGet: '1',
    platform: undefined,
    apiVersion: undefined,
    pollStopType: '1',
    apiPolling: 0,
    sleepTime: 1000,
    orderSubmit: 0,
    notes: '',
    maximumDosage: '',
  };

  // 加载API版本
  const debouncedLoadApiVersions = useDebounceFn(async (platform: string) => {
    if (!platform?.trim()) {
      versionOptions.value = [];
      return;
    }

    try {
      versionLoading.value = true;
      const res = await getApiVersions(platform);
      versionOptions.value = res?.data?.length
        ? res.data.map((item) => ({
            label: item.versionName,
            value: item.platformIdentifying,
            prompt: item.prompt,
          }))
        : [];
    } catch (error) {
      console.error('Failed to get API versions:', error);
      versionOptions.value = [];
    } finally {
      versionLoading.value = false;
    }
  }, 300);

  // 获取版本的prompt提示信息
  const getVersionPrompt = (versionValue: string | undefined) => {
    if (!versionValue) return '';
    const version = versionOptions.value.find((v) => v.value === versionValue);
    return version?.prompt || '';
  };

  // 数据转换函数
  const transformChannelDetailToFormState = (detail: any): ChannelFormState => {
    const apiKeys: Record<string, string> = {
      apiKey: detail.apiKey || '',
    };

    for (let i = 2; i <= 20; i++) {
      const keyName = `apiKey${i}`;
      if (detail[keyName] !== undefined) {
        apiKeys[keyName] = detail[keyName] || '';
      }
    }

    return {
      name: detail.apiName,
      apiAccount: detail.apiAccount,
      apiPassword: detail.apiPassword,
      apiKeys,
      apiUrl: detail.requestAddress,
      apiUrl2: detail?.requestAddress2 || '',
      apiType: String(detail.apiType),
      apiSubmitCard: String(detail.apiCardType),
      infoGet: String(detail.apiSources),
      platform: detail.apiPlatform,
      apiVersion: detail.apiVersion,
      pollStopType: String(detail.stopType),
      apiPolling: detail.apiPolling,
      sleepTime: detail.singleCardSleep,
      orderSubmit: detail.apiOrderSubmission,
      notes: detail.notes || '',
      maximumDosage: detail.maximumDosage,
    };
  };

  const transformFormStateToParams = (formState: ChannelFormState) => {
    const platformId = formState.platform || '';
    const platformType = platformId
      ? platformTypeMap.value[platformId]?.type
      : undefined;

    const params: Record<string, any> = {
      apiName: formState.name,
      apiPlatform: formState.platform || '',
      apiVersion: formState.apiVersion,
      apiAccount: formState.apiAccount,
      apiPassword: formState.apiPassword,
      apiSources: Number(formState.infoGet),
      apiType: Number(formState.apiType),
      apiCardType: Number(formState.apiSubmitCard),
      apiOrderSubmission: formState.orderSubmit,
      apiPolling: formState.apiPolling,
      singleCardSleep: formState.sleepTime,
      notes: formState.notes,
      pollingCardType: 1,
      rePolling: 1,
      orderMachine: 1,
      requestAddress: formState.apiUrl,
      requestAddress2: formState.apiUrl2 || undefined,
      stopType: Number(formState.pollStopType),
      platformType: platformType ? Number(platformType) : undefined,
      maximumDosage: formState.maximumDosage,
    };

    // Object.entries(formState.apiKeys).forEach(([key, value]) => {
    //   if (value?.trim()) {
    //     params[key] = value;
    //   }
    // });
    // 修改这里：所有的 apiKeys 都要传入，即使是空值
    Object.entries(formState.apiKeys).forEach(([key, value]) => {
      // 不管是否为空，都传入值
      params[key] = value?.trim() || '';
    });

    return params;
  };

  // 使用 useForm
  const {
    formRef,
    formData,
    visible,
    loading,
    show: showModal,
    renderModal,
  } = useForm<ChannelFormState>({
    draggable: true,
    title: (isEdit) => (isEdit ? '编辑通道' : '添加通道'),
    width: 900,
    defaultValues: defaultFormState,

    // API配置
    getDetail: async (id) => {
      try {
        const res = await getChannelDetail(id);
        if (res.code === 1 && res.data) {
          const formState = transformChannelDetailToFormState(res.data);
          if (res.data.apiPlatform) {
            await debouncedLoadApiVersions(res.data.apiPlatform);
          }
          return {
            code: 1,
            data: formState,
            msg: res.msg,
          };
        }
        return {
          code: res.code,
          data: {} as ChannelFormState,
          msg: res.msg,
        };
      } catch (error) {
        console.error('Error loading channel detail:', error);
        return {
          code: 0,
          data: {} as ChannelFormState,
          msg: '获取通道详情失败',
        };
      }
    },

    create: async (params) => {
      const transformedParams = transformFormStateToParams(params);
      return await createChannel(transformedParams);
    },

    update: async (id, params) => {
      const transformedParams = transformFormStateToParams(params);
      return await updateChannel(id, transformedParams);
    },

    onSuccess: options.onSuccess,

    // 表单字段配置
    fields: [
      {
        group: '基本信息',
        items: [
          {
            field: 'name',
            label: '通道名称',
            component: 'Input',
            required: true,
            componentProps: {
              placeholder: '请输入通道名称',
              allowClear: true,
            },
          },
          {
            field: 'platform',
            label: '对接平台',
            component: 'Select',
            required: true,
            componentProps: {
              placeholder: '请选择接口对接平台',
              allowClear: true,
              showSearch: true,
              options: [],
              onChange: (value: string) => {
                formData.apiVersion = undefined;
                versionOptions.value = [];
                if (value) {
                  debouncedLoadApiVersions(value);
                }
              },
              filterOption: (input: string, option: any) => {
                return option.label.toLowerCase().includes(input.toLowerCase());
              },
            },
            render: ({ value }) => {
              if (!value) return null;
              const type = platformTypeMap.value[value]?.type;
              if (!type) return null;
              return {
                suffix: () =>
                  h(
                    Tag,
                    {
                      color: 'warning',
                    },
                    () => platformTypeLabels[type] || `类型-${type}`,
                  ),
              };
            },
          },
          {
            field: 'apiVersion',
            label: '对接版本',
            component: 'Select',
            required: true,
            show: () => versionOptions.value.length > 0,
            componentProps: {
              placeholder: '请选择API对接版本',
              allowClear: true,
              options: versionOptions,
              loading: versionLoading,
              showSearch: true,
              filterOption: (input: string, option: any) => {
                return option.label.toLowerCase().includes(input.toLowerCase());
              },
            },
          },
        ],
      },
      {
        group: 'API配置',
        items: [
          {
            field: 'apiAccount',
            label: 'API账号',
            component: 'Input',
            required: true,
            componentProps: {
              placeholder: '请输入API账号',
              allowClear: true,
            },
          },
          {
            field: 'apiPassword',
            label: 'API密码',
            component: 'Input',
            required: true,
            componentProps: {
              placeholder: '请输入API密码',
              allowClear: true,
            },
          },
          // API密钥字段使用自定义渲染
          {
            field: 'apiKeys',
            label: 'API密钥',
            component: 'Custom',
            render: ({ value, onChange }) => {
              return Object.entries(value).map(([key, val]) => ({
                content: () => [
                  h('div', { class: 'flex items-center gap-2' }, [
                    h(Input, {
                      value: val,
                      'onUpdate:value': (newVal: string) => {
                        const newApiKeys = { ...value };
                        newApiKeys[key] = newVal;
                        onChange(newApiKeys);
                      },
                      placeholder: `请输入API密钥${key === 'apiKey' ? '' : key.replace('apiKey', '')}`,
                      allowClear: true,
                    }),
                    key !== 'apiKey' &&
                      h(
                        Button,
                        {
                          type: 'link',
                          danger: true,
                          onClick: () => {
                            const { [key]: _, ...rest } = value;
                            onChange(rest);
                          },
                        },
                        () => '删除',
                      ),
                    key === Object.keys(value).slice(-1)[0] &&
                      h(
                        Button,
                        {
                          type: 'link',
                          onClick: () => {
                            const existingKeys = Object.keys(value);
                            const maxIndex = Math.max(
                              ...existingKeys.map((k) => {
                                const match = k.match(/apiKey(\d+)/);
                                return match && match[1]
                                  ? Number.parseInt(match[1], 10)
                                  : 1;
                              }),
                            );
                            const nextIndex = maxIndex + 1;
                            onChange({
                              ...value,
                              [`apiKey${nextIndex}`]: '',
                            });
                          },
                        },
                        () => '添加密钥',
                      ),
                  ]),
                ],
              }));
            },
          },
        ],
      },
      {
        group: '接口配置',
        items: [
          {
            field: 'apiUrl',
            label: 'API地址',
            component: 'Input',
            required: true,
            componentProps: {
              placeholder: '请输入API请求地址',
              allowClear: true,
            },
          },
          {
            field: 'apiUrl2',
            label: 'API地址2',
            component: 'Input',
            componentProps: {
              placeholder: '请输入API请求地址2',
              allowClear: true,
            },
          },
          {
            field: 'apiType',
            label: 'API类型',
            component: 'Select',
            required: true,
            componentProps: {
              options: apiTypeOptions,
              placeholder: '请选择API类型',
              allowClear: true,
            },
          },
          {
            field: 'apiSubmitCard',
            label: '提交卡号',
            component: 'Select',
            required: true,
            componentProps: {
              options: apiSubmitCardOptions,
              placeholder: '请选择API提交卡号',
            },
            extra:
              'api的递交卡号，看上游平台支持什么卡号调用api按上游需要的选择(注意要根据自己导入到我方系统后台的对应)',
          },
          {
            field: 'infoGet',
            label: '信息获取',
            component: 'Select',
            required: true,
            componentProps: {
              options: infoGetOptions,
              placeholder: '请选择信息获取方式',
            },
            extra:
              '流量池选择无效自动系统自定义,第三方套餐如果想虚量必须选择系统自定义,上游获取是完全根据上游返回的总流量和使用流量显示轮询类型 》停机复机为默认模式,如上游系统卡片停机了无法再复机可选择断网复网来控制停复机',
          },
          {
            field: 'maximumDosage',
            label: '限制用量',
            component: 'Input',
            componentProps: {
              placeholder: '请输入自然月限制用量(MB)',
              allowClear: true,
            },
          },
        ],
      },
      {
        group: '轮询设置',
        items: [
          {
            field: 'pollStopType',
            label: '轮询类型',
            component: 'Select',
            required: true,
            componentProps: {
              options: pollStopTypeOptions,
              placeholder: '请选择轮询停机类型',
            },
          },
          {
            field: 'apiPolling',
            label: '接口轮询',
            component: 'Switch',
            componentProps: {
              checkedChildren: '开启',
              unCheckedChildren: '关闭',
            },
          },
          {
            field: 'sleepTime',
            label: '休眠(ms)',
            component: 'InputNumber',
            required: true,
            componentProps: {
              min: 0,
              style: { width: '120px' },
              placeholder: '请输入',
            },
            suffix: () =>
              h(
                Tooltip,
                {
                  title: '单卡休眠时间',
                },
                () =>
                  h(MdiInformationOutline, {
                    class: 'ml-2 text-gray-400 cursor-help',
                    style: 'font-size: 18px',
                  }),
              ),
          },
        ],
      },
      {
        group: '其他设置',
        items: [
          {
            field: 'orderSubmit',
            label: '订单递交',
            component: 'Switch',
            componentProps: {
              checkedChildren: '是',
              unCheckedChildren: '否',
            },
          },
          {
            field: 'notes',
            label: '备注',
            component: 'TextArea',
            componentProps: {
              placeholder: '请输入备注',
              rows: 4,
              style: { width: '100%', resize: 'none' },
              allowClear: true,
            },
          },
        ],
      },
    ],
  });

  // 公开的API
  const show = (id?: number) => {
    if (!id) {
      formData.apiKeys = { apiKey: '' };
    }
    showModal(id);
  };

  return {
    formRef,
    formData,
    loading,
    visible,
    show,
    renderModal,
  };
}
