import type { SelectValue } from 'ant-design-vue/es/select';

import type { FormGroup } from '#/hooks/common/useFormModal';

import { h, ref } from 'vue';

import { MdiInformationOutline } from '@vben/icons';

import { useDebounceFn } from '@vueuse/core';
import {
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Tag,
  Tooltip,
} from 'ant-design-vue';
// 为了解决平台选择组件中的类型错误

import {
  createChannel,
  getApiVersions,
  getChannelDetail,
  getPlatform,
  updateChannel,
} from '#/api/core/channel';
import { useFormModal } from '#/hooks/common/useFormModal';

const FormItem = Form.Item;

// 专门用于平台选择的包装组件
const Platform = Select;

export interface UseChannelFormOptions {
  onSuccess?: () => void;
}

export interface ChannelFormState {
  name: string; // 通道名称
  apiAccount: string; // API账号
  apiPassword: string; // API密码
  apiKeys: Record<string, string>; // API密钥集合
  apiUrl: string; // API请求地址
  apiUrl2: string; // API请求地址2
  apiType: string; // API类型
  apiSubmitCard: string; // API提交卡号
  infoGet: string; // 信息获取
  platform: string | undefined; // 对接平台
  apiVersion?: string; // API版本
  pollStopType: string; // 轮询停机类型
  apiPolling: number; // 接口轮询
  sleepTime: number; // 单卡休眠时间(ms)
  orderSubmit: number; // 订单递交
  notes?: string; // 备注
  maximumDosage?: string; // 限制用量
}

interface ApiVersionItem {
  versionName: string;
  platformIdentifying: string;
  prompt?: string;
}

interface ApiVersionResponse {
  code: number;
  data: ApiVersionItem[];
}

// 平台类型映射
const platformTypeLabels = {
  '1': '流量池',
  '2': '物联卡',
  '3': '行业卡',
  '4': 'NB卡',
};

export function useChannelForm(options: UseChannelFormOptions = {}) {
  // 平台数据映射，用于存储平台ID与类型的关系以及相关提示信息
  const platformTypeMap = ref<
    Record<string, { prompt?: string; type: string }>
  >({});

  // API版本选项，需要单独管理因为它是根据平台动态加载的
  const versionLoading = ref(false);
  const versionOptions = ref<
    { label: string; prompt?: string; value: string }[]
  >([]);

  // 固定选项
  const apiTypeOptions = [
    { label: '第三方套餐', value: '1' },
    { label: '直连运营商', value: '2' },
  ];

  const apiSubmitCardOptions = [
    { label: 'ICCID', value: '1' },
    { label: 'MIS', value: '2' },
    { label: '虚拟号', value: '3' },
  ];

  const infoGetOptions = [
    { label: '上游获取', value: '1' },
    { label: '系统自定义', value: '2' },
  ];

  const pollStopTypeOptions = [
    { label: '停机复机', value: '1' },
    { label: '断网复网', value: '2' },
  ];

  // 初始表单状态
  const defaultFormState: ChannelFormState = {
    name: '',
    apiAccount: '',
    apiPassword: '',
    apiKeys: {
      apiKey: '',
    },
    apiUrl: '',
    apiUrl2: '',
    apiType: '1',
    apiSubmitCard: '1',
    infoGet: '1',
    platform: undefined,
    apiVersion: undefined,
    pollStopType: '1',
    apiPolling: 0,
    sleepTime: 1000,
    orderSubmit: 0,
    notes: '',
    maximumDosage: '',
  };

  // 加载平台列表
  const loadPlatformOptions = async () => {
    try {
      const res = await getPlatform();
      if (res.code === 1 && res.data?.length) {
        // 创建平台ID到类型及提示的映射关系
        platformTypeMap.value = {};
        res.data.forEach((item) => {
          if (item.id !== null) {
            platformTypeMap.value[String(item.id)] = {
              type: item.type,
              prompt: item.prompt,
            };
          }
        });

        return res.data.map((item) => ({
          label: item.name,
          value: item.id === null ? '' : String(item.id), // 转为字符串
          // 添加提示信息
          ...(item.prompt ? { prompt: item.prompt } : {}),
        }));
      }
      return [];
    } catch (error) {
      console.error('Failed to get platforms:', error);
      return [];
    }
  };

  // 加载API版本
  const debouncedLoadApiVersions = useDebounceFn(async (platform: string) => {
    if (!platform?.trim()) {
      versionOptions.value = [];
      return;
    }

    try {
      versionLoading.value = true;
      const res = (await getApiVersions(
        platform,
      )) as unknown as ApiVersionResponse;
      versionOptions.value = res?.data?.length
        ? res.data.map((item) => ({
            label: item.versionName,
            value: item.platformIdentifying,
            prompt: item.prompt,
          }))
        : [];
    } catch (error) {
      console.error('Failed to get API versions:', error);
      versionOptions.value = [];
    } finally {
      versionLoading.value = false;
    }
  }, 300);

  // 获取版本的prompt提示信息
  const getVersionPrompt = (versionValue: string | undefined) => {
    if (!versionValue) return '';
    const version = versionOptions.value.find((v) => v.value === versionValue);
    return version?.prompt || '';
  };

  // 处理API改造
  const transformChannelDetailToFormState = (detail: any): ChannelFormState => {
    // 处理 API 密钥
    const apiKeys: Record<string, string> = {
      apiKey: detail.apiKey || '', // 保持第一个密钥固定
    };

    // 动态添加存在的密钥
    for (let i = 2; i <= 20; i++) {
      const keyName = `apiKey${i}`;
      if (detail[keyName] !== undefined) {
        apiKeys[keyName] = detail[keyName] || '';
      }
    }

    const formState = {
      name: detail.apiName,
      apiAccount: detail.apiAccount,
      apiPassword: detail.apiPassword,
      apiKeys,
      apiUrl: detail.requestAddress,
      apiUrl2: detail?.requestAddress2 || '',
      apiType: String(detail.apiType),
      apiSubmitCard: String(detail.apiCardType),
      infoGet: String(detail.apiSources),
      platform: detail.apiPlatform,
      apiVersion: detail.apiVersion,
      pollStopType: String(detail.stopType),
      apiPolling: detail.apiPolling,
      sleepTime: detail.singleCardSleep,
      orderSubmit: detail.apiOrderSubmission,
      notes: detail.notes || '',
      maximumDosage: detail.maximumDosage,
    };

    return formState;
  };

  const transformFormStateToParams = (formState: ChannelFormState) => {
    const platformId = formState.platform || '';
    const platformType = platformId
      ? platformTypeMap.value[platformId]?.type
      : undefined;

    // 构建基础参数
    const params: Record<string, any> = {
      apiName: formState.name,
      apiPlatform: formState.platform || '',
      apiVersion: formState.apiVersion,
      apiAccount: formState.apiAccount,
      apiPassword: formState.apiPassword,
      apiSources: Number(formState.infoGet),
      apiType: Number(formState.apiType),
      apiCardType: Number(formState.apiSubmitCard),
      apiOrderSubmission: formState.orderSubmit,
      apiPolling: formState.apiPolling,
      singleCardSleep: formState.sleepTime,
      notes: formState.notes,
      pollingCardType: 1,
      rePolling: 1,
      orderMachine: 1,
      requestAddress: formState.apiUrl,
      requestAddress2: formState.apiUrl2 || undefined,
      stopType: Number(formState.pollStopType),
      platformType: platformType ? Number(platformType) : undefined,
      maximumDosage: formState.maximumDosage,
    };

    // 添加所有非空的 API 密钥
    Object.entries(formState.apiKeys).forEach(([key, value]) => {
      if (value?.trim()) {
        params[key] = value;
      }
    });

    return params;
  };

  // 使用useFormModal hook
  const {
    formRef,
    formData,
    visible,
    loading,
    isEdit: _isEdit,
    show: showModal,
    renderFormModal,
    selectOptions,
    onCancel,
    setFormData,
  } = useFormModal<ChannelFormState>({
    draggable: true,
    title: (isEdit) => (isEdit ? '编辑通道' : '添加通道'),
    width: 900,
    defaultValues: defaultFormState,
    getDetail: async (id) => {
      try {
        await loadPlatformOptions();
        const res = await getChannelDetail(id);
        if (res.code === 1 && res.data) {
          const formState = transformChannelDetailToFormState(res.data);

          // 如果有平台ID，加载对应的API版本
          if (res.data.apiPlatform) {
            await debouncedLoadApiVersions(res.data.apiPlatform);
          }

          return {
            code: 1,
            data: formState,
            msg: res.msg,
          };
        }
        return {
          code: res.code,
          data: {} as ChannelFormState,
          msg: res.msg,
        };
      } catch (error) {
        console.error('Error loading channel detail:', error);
        return {
          code: 0,
          data: {} as ChannelFormState,
          msg: '获取通道详情失败',
        };
      }
    },
    create: async (params) => {
      const transformedParams = transformFormStateToParams(params);
      const res = await createChannel(transformedParams);
      return res;
    },
    update: async (id, params) => {
      const transformedParams = transformFormStateToParams(params);
      const res = await updateChannel(id, transformedParams);
      return res;
    },
    onSuccess: options.onSuccess,
    // onCancel: () => {
    //   // 重置为默认的 API 密钥结构
    //   formData.value.apiKeys = { apiKey: formData.value.apiKeys.apiKey || '' };
    //   setFormData({
    //     ...formData.value,
    //     apiKeys: { apiKey: formData.value.apiKeys.apiKey || '' },
    //   });
    // },
    selectApis: {
      platform: {
        api: getPlatform,
        transform: (data) => {
          // 创建平台ID到类型及提示的映射关系
          platformTypeMap.value = {};
          data.forEach((item) => {
            if (item.id !== null) {
              platformTypeMap.value[String(item.id)] = {
                type: item.type,
                prompt: item.prompt,
              };
            }
          });

          return data.map((item) => ({
            label: item.name,
            value: item.id === null ? '' : String(item.id), // 转为字符串
            // 添加提示信息
            ...(item.prompt ? { prompt: item.prompt } : {}),
          }));
        },
      },
    },
  });

  // 定义表单组
  const formGroups: FormGroup[] = [
    {
      title: '基本信息',
      content: (formData) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '通道名称',
              name: 'name',
              rules: [{ required: true, message: '请输入通道名称' }],
            },
            () =>
              h(Input, {
                value: formData.name,
                'onUpdate:value': (val: string) => (formData.name = val),
                placeholder: '请输入通道名称',
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '对接平台',
              name: 'platform',
              rules: [{ required: true, message: '请选择对接平台' }],
              ...(formData.platform && versionOptions.value.length === 0
                ? {
                    extra: h(
                      'span',
                      { class: 'text-red-500 text-xs' },
                      platformTypeMap.value[formData.platform]?.prompt || '',
                    ),
                  }
                : {}),
            },
            () =>
              h('div', { class: 'flex items-center space-x-2' }, [
                h(Platform, {
                  value: formData.platform,
                  'onUpdate:value': (val: SelectValue): void => {
                    const platformId = String(val ?? '');
                    formData.platform = platformId;

                    // 清空版本
                    formData.apiVersion = undefined;
                    versionOptions.value = [];
                    if (val) {
                      debouncedLoadApiVersions(String(val));
                    }
                  },
                  options: selectOptions.value.platform || [],
                  placeholder: '请选择接口对接平台',
                  allowClear: true,
                  showSearch: true,
                  filterOption: (input: string, option: any) => {
                    return option.label
                      .toLowerCase()
                      .includes(input.toLowerCase());
                  },
                }),
                // 如果有平台类型，显示类型标签
                formData.platform &&
                platformTypeMap.value[formData.platform]?.type
                  ? h(
                      Tag,
                      {
                        color: 'warning',
                      },
                      () => {
                        const typeValue =
                          platformTypeMap.value[formData.platform]?.type || '';
                        // 安全地获取类型标签
                        let typeLabel = '';
                        if (typeValue && platformTypeLabels[typeValue]) {
                          typeLabel = platformTypeLabels[typeValue];
                        } else if (typeValue) {
                          typeLabel = `类型-${typeValue}`;
                        }
                        return typeLabel;
                      },
                    )
                  : null,
              ]),
          ),
        ]),
        h(Col, { span: 12 }, [
          versionOptions.value.length > 0 &&
            h(
              FormItem,
              {
                label: '对接版本',
                name: 'apiVersion',
                rules: [{ required: true, message: '请选择对接版本' }],
                ...(formData.apiVersion
                  ? {
                      extra: h(
                        'span',
                        { class: 'text-red-500 text-xs' },
                        getVersionPrompt(formData.apiVersion) ||
                          '所选API版本的说明信息',
                      ),
                    }
                  : {}),
              },
              () =>
                h(Select, {
                  value: formData.apiVersion,
                  'onUpdate:value': (val: SelectValue): void => {
                    formData.apiVersion = String(val ?? '');
                  },
                  loading: versionLoading.value,
                  options: versionOptions.value,
                  placeholder: '请选择API对接版本',
                  allowClear: true,
                  disabled: versionLoading.value,
                  showSearch: true,
                  optionLabelProp: 'label',
                  filterOption: (input: string, option: any) => {
                    return option.label
                      .toLowerCase()
                      .includes(input.toLowerCase());
                  },
                }),
            ),
        ]),
      ],
    },
    {
      title: 'API配置',
      content: (formData) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API账号',
              name: 'apiAccount',
              rules: [
                {
                  required: true,
                  message: '请根据对接平台或对接版本提示填写',
                },
              ],
            },
            () =>
              h(Input, {
                value: formData.apiAccount,
                'onUpdate:value': (val: string) => (formData.apiAccount = val),
                placeholder: '请输入API账号',
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API密码',
              name: 'apiPassword',
              rules: [
                { required: true, message: '请根据对接平台或对接版本提示填写' },
              ],
            },
            () =>
              h(Input, {
                value: formData.apiPassword,
                'onUpdate:value': (val: string) => (formData.apiPassword = val),
                placeholder: '请输入API密码',
                allowClear: true,
              }),
          ),
        ]),
        // 动态渲染 API 密钥输入框
        ...Object.entries(formData.apiKeys).map(([key, value]) =>
          h(Col, { span: 12 }, [
            h(
              FormItem,
              {
                label: `API密钥${key === 'apiKey' ? '' : key.replace('apiKey', '')}`,
                name: ['apiKeys', key],
              },
              () =>
                h('div', { class: 'flex items-center gap-2' }, [
                  h(Input, {
                    value: value as string,
                    'onUpdate:value': (val: string) =>
                      (formData.apiKeys[key] = val),
                    placeholder: `请输入API密钥${key === 'apiKey' ? '' : key.replace('apiKey', '')}`,
                    allowClear: true,
                  }),
                  // 第一个密钥不能删除,其他的都可以删除
                  key !== 'apiKey' &&
                    h(
                      Button,
                      {
                        type: 'link',
                        danger: true,
                        onClick: () => {
                          const { [key]: _, ...rest } = formData.apiKeys;
                          formData.apiKeys = rest;
                        },
                      },
                      () => '删除',
                    ),
                  // 如果是最后一个输入框，显示添加按钮
                  key === Object.keys(formData.apiKeys).slice(-1)[0] &&
                    h(
                      Button,
                      {
                        type: 'link',
                        onClick: () => {
                          const existingKeys = Object.keys(formData.apiKeys);
                          // 找到当前最大的索引
                          const maxIndex = Math.max(
                            ...existingKeys.map((k) => {
                              const match = k.match(/apiKey(\d+)/);
                              return match ? Number.parseInt(match[1], 10) : 1;
                            }),
                          );
                          // 使用下一个索引
                          const nextIndex = maxIndex + 1;
                          formData.apiKeys[`apiKey${nextIndex}`] = '';
                        },
                      },
                      () => '添加密钥',
                    ),
                ]),
            ),
          ]),
        ),
      ],
    },
    {
      title: '接口配置',
      content: (formData) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API地址',
              name: 'apiUrl',
              rules: [{ required: true, message: '上游平台提供' }],
            },
            () =>
              h(Input, {
                value: formData.apiUrl,
                'onUpdate:value': (val: string) => (formData.apiUrl = val),
                placeholder: '请输入API请求地址',
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API地址2',
              name: 'apiUrl2',
            },
            () =>
              h(Input, {
                value: formData.apiUrl2,
                'onUpdate:value': (val: string) => (formData.apiUrl2 = val),
                placeholder: '请输入API请求地址2',
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: 'API类型',
              name: 'apiType',
              rules: [{ required: true, message: '请选择API类型' }],
            },
            () =>
              h(Select, {
                value: formData.apiType,
                'onUpdate:value': (val: SelectValue): void => {
                  formData.apiType = String(val ?? '');
                },
                options: apiTypeOptions,
                placeholder: '请选择API类型',
                allowClear: true,
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '提交卡号',
              name: 'apiSubmitCard',
              rules: [{ required: true, message: '请选择API提交卡号' }],
              extra: h(
                'span',
                { class: 'text-red-500 text-xs' },
                'api的递交卡号，看上游平台支持什么卡号调用api按上游需要的选择(注意要根据自己导入到我方系统后台的对应)',
              ),
            },
            () =>
              h(Select, {
                value: formData.apiSubmitCard,
                'onUpdate:value': (val: SelectValue): void => {
                  formData.apiSubmitCard = String(val ?? '');
                },
                options: apiSubmitCardOptions,
                placeholder: '请选择API提交卡号',
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '信息获取',
              name: 'infoGet',
              rules: [{ required: true, message: '请选择信息获取方式' }],
              extra: h(
                'span',
                { class: 'text-red-500 text-xs' },
                `流量池选择无效自动系统自定义,第三方套餐如果想虚量必须选择系统自定义,上游获取是完全根据上游返回的总流量和使用流量显示轮询类型 》停机复机为默认模式,如上游系统卡片停机了无法再复机可选择断网复网来控制停复机`,
              ),
            },
            () =>
              h(Select, {
                value: formData.infoGet,
                'onUpdate:value': (val: SelectValue): void => {
                  formData.infoGet = String(val ?? '');
                },
                options: infoGetOptions,
                placeholder: '请选择信息获取方式',
              }),
          ),
        ]),

        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '限制用量',
              name: 'maximumDosage',
              // rules: [{ required: true, message: '请输入自然月限制用量' }],
            },
            () =>
              h('div', { class: 'flex items-center' }, [
                h(Input, {
                  value: formData.maximumDosage,
                  'onUpdate:value': (val: string) =>
                    (formData.maximumDosage = val),
                  placeholder: '请输入自然月限制用量(MB)',
                  allowClear: true,
                }),
              ]),
          ),
        ]),
      ],
    },
    {
      title: '轮询设置',
      content: (formData) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '轮询类型',
              name: 'pollStopType',
              rules: [{ required: true, message: '请选择轮询停机类型' }],
            },
            () =>
              h(Select, {
                value: formData.pollStopType,
                'onUpdate:value': (val: SelectValue): void => {
                  formData.pollStopType = String(val ?? '');
                },
                options: pollStopTypeOptions,
                placeholder: '请选择轮询停机类型',
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '接口轮询',
              name: 'apiPolling',
            },
            () =>
              h(Switch, {
                checked: formData.apiPolling === 1,
                onChange: (checked: any) => {
                  formData.apiPolling = checked ? 1 : 0;
                },
                checkedChildren: '开启',
                unCheckedChildren: '关闭',
              }),
          ),
        ]),
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '休眠(ms)',
              name: 'sleepTime',
              rules: [{ required: true, message: '请输入单卡休眠时间' }],
            },
            () =>
              h('div', { class: 'flex items-center' }, [
                h(InputNumber, {
                  value: formData.sleepTime,
                  'onUpdate:value': (value: null | number | string) => {
                    formData.sleepTime =
                      typeof value === 'number' ? value : 1000;
                  },
                  min: 0,
                  style: { width: '120px' },
                  placeholder: '请输入',
                }),
                h(
                  Tooltip,
                  {
                    title: '单卡休眠时间',
                  },
                  () =>
                    h(MdiInformationOutline, {
                      class: 'ml-2 text-gray-400 cursor-help',
                      style: 'font-size: 18px',
                    }),
                ),
              ]),
          ),
        ]),
      ],
    },
    {
      title: '其他设置',
      content: (formData) => [
        h(Col, { span: 12 }, [
          h(
            FormItem,
            {
              label: '订单递交',
              name: 'orderSubmit',
            },
            () =>
              h(Switch, {
                checked: formData.orderSubmit === 1,
                onChange: (checked: any) => {
                  formData.orderSubmit = checked ? 1 : 0;
                },
                checkedChildren: '是',
                unCheckedChildren: '否',
              }),
          ),
        ]),
        h(Col, { span: 24 }, [
          h(
            FormItem,
            {
              label: '备注',
              name: 'notes',
              rules: [],
              labelCol: { span: 3 },
              wrapperCol: { span: 20 },
            },
            () =>
              h(Input.TextArea, {
                value: formData.notes,
                'onUpdate:value': (val: string) => (formData.notes = val),
                placeholder: '请输入备注',
                rows: 4,
                style: { width: '100%', resize: 'none' },
                allowClear: true,
              }),
          ),
        ]),
      ],
    },
  ];

  // 公开的API
  const show = (id?: number) => {
    // 重置 apiKeys 为初始状态
    if (!id) {
      // 只在创建时重置
      formData.value = {
        ...formData.value,
        apiKeys: { apiKey: '' }, // 重置为只有一个默认的 apiKey
      };
    }
    showModal(id);
  };

  return {
    formRef,
    formData,
    loading,
    visible,
    show,
    renderModal: () => renderFormModal(formGroups),
  };
}
