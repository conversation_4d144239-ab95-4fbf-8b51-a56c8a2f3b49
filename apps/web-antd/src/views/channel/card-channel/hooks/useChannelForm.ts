import { h, ref } from 'vue';

import { MdiInformationOutline } from '@vben/icons';

import {
  Button,
  Col,
  FormItem,
  Input,
  InputNumber,
  Row,
  Select,
  Switch,
  Tag,
  Tooltip,
} from 'ant-design-vue';

import {
  createChannel,
  getApiVersions,
  getChannelDetail,
  getPlatform,
  updateChannel,
} from '#/api/core/channel';
import { useForm } from '#/hooks/useAnsheng';

export interface UseChannelFormOptions {
  onSuccess?: () => void;
}

export interface ChannelFormState {
  name: string;
  apiAccount: string;
  apiPassword: string;
  apiKeys: Record<string, string>;
  apiUrl: string;
  apiUrl2: string;
  apiType: string;
  apiSubmitCard: string;
  infoGet: string;
  platform: string | undefined;
  apiVersion?: string;
  pollStopType: string;
  apiPolling: number;
  sleepTime: number;
  orderSubmit: number;
  notes?: string;
  maximumDosage?: string;
}

// 平台类型映射
const platformTypeLabels: Record<string, string> = {
  '1': '流量池',
  '2': '物联卡',
  '3': '行业卡',
  '4': 'NB卡',
};

// 添加 CreateChannelData 类型以匹配 API 需求
interface CreateChannelData {
  apiName: string;
  apiPlatform: string;
  apiAccount: string;
  apiPassword: string;
  apiSources: number;
  apiType: number;
  apiCardType: number;
  apiOrderSubmission: number;
  apiPolling: number;
  singleCardSleep: number;
  pollingCardType: number;
  rePolling: number;
  orderMachine: number;
  requestAddress: string;
  requestAddress2?: string;
  stopType: number;
  platformType?: number;
  maximumDosage?: string;
  notes?: string;
  // 允许额外的 API 密钥
  [key: string]: any;
}

export function useChannelForm(options: UseChannelFormOptions = {}) {
  // 平台数据映射
  const platformTypeMap = ref<
    Record<string, { prompt?: string; type: string }>
  >({});

  // 平台选项数据
  const platformOptions = ref<{ label: string; value: string }[]>([]);
  const platformLoading = ref(false);

  // API版本选项
  const versionLoading = ref(false);
  const versionOptions = ref<
    { label: string; prompt?: string; value: string }[]
  >([]);

  // 初始表单状态
  const defaultFormState: ChannelFormState = {
    name: '',
    apiAccount: '',
    apiPassword: '',
    apiKeys: {
      apiKey: '',
    },
    apiUrl: '',
    apiUrl2: '',
    apiType: '1',
    apiSubmitCard: '1',
    infoGet: '1',
    platform: undefined,
    apiVersion: undefined,
    pollStopType: '1',
    apiPolling: 0,
    sleepTime: 1000,
    orderSubmit: 0,
    notes: '',
    maximumDosage: '',
  };

  // 加载平台列表
  const loadPlatformOptions = async () => {
    try {
      platformLoading.value = true;
      const res = await getPlatform();
      if (res.code === 1 && res.data?.length) {
        // 创建平台ID到类型及提示的映射
        platformTypeMap.value = {};
        res.data.forEach((item) => {
          if (item.id !== null) {
            platformTypeMap.value[String(item.id)] = {
              type: item.type,
              prompt: item.prompt,
            };
          }
        });

        // 更新平台选项
        platformOptions.value = res.data.map((item) => ({
          label: item.name,
          value: item.id === null ? '' : String(item.id),
          // 添加提示信息
          ...(item.prompt ? { prompt: item.prompt } : {}),
        }));

        return platformOptions.value;
      }
      return [];
    } catch (error) {
      console.error('Failed to get platforms:', error);
      return [];
    } finally {
      platformLoading.value = false;
    }
  };

  // 加载API版本
  const loadApiVersions = async (platform: string) => {
    if (!platform?.trim()) {
      versionOptions.value = [];
      return;
    }

    try {
      versionLoading.value = true;
      const res = await getApiVersions(platform);
      versionOptions.value = res?.data?.length
        ? res.data.map((item) => ({
            label: item.versionName,
            value: item.platformIdentifying,
            prompt: item.prompt,
          }))
        : [];
    } catch (error) {
      console.error('Failed to get API versions:', error);
      versionOptions.value = [];
    } finally {
      // 明确设置加载状态为false
      versionLoading.value = false;
    }
  };

  // 获取平台的提示信息
  const getPlatformPrompt = (platformId: string | undefined) => {
    if (!platformId) return '';
    return platformTypeMap.value[platformId]?.prompt || '';
  };

  // 获取版本的提示信息
  const getVersionPrompt = (versionValue: string | undefined) => {
    if (!versionValue) return '';
    const version = versionOptions.value.find((v) => v.value === versionValue);
    return version?.prompt || '';
  };

  // 数据转换函数
  const transformChannelDetailToFormState = (detail: any): ChannelFormState => {
    const apiKeys: Record<string, string> = {
      apiKey: detail.apiKey || '',
    };

    for (let i = 2; i <= 20; i++) {
      const keyName = `apiKey${i}`;
      if (detail[keyName] !== undefined) {
        apiKeys[keyName] = detail[keyName] || '';
      }
    }

    return {
      name: detail.apiName,
      apiAccount: detail.apiAccount,
      apiPassword: detail.apiPassword,
      apiKeys,
      apiUrl: detail.requestAddress,
      apiUrl2: detail?.requestAddress2 || '',
      apiType: String(detail.apiType),
      apiSubmitCard: String(detail.apiCardType),
      infoGet: String(detail.apiSources),
      platform: detail.apiPlatform,
      apiVersion: detail.apiVersion,
      pollStopType: String(detail.stopType),
      apiPolling: detail.apiPolling,
      sleepTime: detail.singleCardSleep,
      orderSubmit: detail.apiOrderSubmission,
      notes: detail.notes || '',
      maximumDosage: detail.maximumDosage,
    };
  };

  const transformFormStateToParams = (
    formState: ChannelFormState,
  ): CreateChannelData => {
    const platformId = formState.platform || '';
    const platformType = platformId
      ? platformTypeMap.value[platformId]?.type
      : undefined;

    const params: CreateChannelData = {
      apiName: formState.name,
      apiPlatform: formState.platform || '',
      apiVersion: formState.apiVersion,
      apiAccount: formState.apiAccount,
      apiPassword: formState.apiPassword,
      apiSources: Number(formState.infoGet),
      apiType: Number(formState.apiType),
      apiCardType: Number(formState.apiSubmitCard),
      apiOrderSubmission: formState.orderSubmit,
      apiPolling: formState.apiPolling,
      singleCardSleep: formState.sleepTime,
      notes: formState.notes,
      pollingCardType: 1,
      rePolling: 1,
      orderMachine: 1,
      requestAddress: formState.apiUrl,
      requestAddress2: formState.apiUrl2 || undefined,
      stopType: Number(formState.pollStopType),
      platformType: platformType ? Number(platformType) : undefined,
      maximumDosage: formState.maximumDosage,
    };

    // 处理 API 密钥
    Object.entries(formState.apiKeys).forEach(([key, value]) => {
      params[key] = value?.trim() || '';
    });

    return params;
  };

  // 使用 useForm
  const {
    formRef,
    formData,
    visible,
    loading,
    show: showModal,
    renderFormModal,
  } = useForm<ChannelFormState>({
    draggable: true,
    title: (isEdit) => (isEdit ? '编辑通道' : '添加通道'),
    width: 900,
    defaultValues: defaultFormState,
    // simpleLayout: {
    //   // padding: '10px',
    //   showGroupTitle: true,
    // },
    // 添加统一的表单验证规则
    rules: {
      name: [{ required: true, message: '请输入通道名称' }],
      platform: [{ required: true, message: '请选择对接平台' }],
      apiVersion: [{ required: true, message: '请选择对接版本' }],
      apiAccount: [
        { required: true, message: '请根据对接平台或对接版本提示填写' },
      ],
      apiPassword: [
        { required: true, message: '请根据对接平台或对接版本提示填写' },
      ],
      apiUrl: [{ required: true, message: '上游平台提供' }],
      apiType: [{ required: true, message: '请选择API类型' }],
      apiSubmitCard: [{ required: true, message: '请选择API提交卡号' }],
      infoGet: [{ required: true, message: '请选择信息获取方式' }],
      pollStopType: [{ required: true, message: '请选择轮询停机类型' }],
      sleepTime: [{ required: true, message: '请输入单卡休眠时间' }],
    },

    getDetail: async (idOrRecord: number | Partial<ChannelFormState>) => {
      try {
        // 加载平台数据
        await loadPlatformOptions();

        // 确保是数字ID
        const id = typeof idOrRecord === 'number' ? idOrRecord : 0;
        if (id === 0) {
          return {
            code: 0,
            data: {} as ChannelFormState,
            msg: '无效ID',
          };
        }

        const res = await getChannelDetail(id);
        if (res.code === 1 && res.data) {
          const formState = transformChannelDetailToFormState(res.data);
          if (res.data.apiPlatform) {
            await loadApiVersions(res.data.apiPlatform);
          }
          return {
            code: 1,
            data: formState,
            msg: res.msg,
          };
        }
        return {
          code: res.code,
          data: {} as ChannelFormState,
          msg: res.msg,
        };
      } catch (error) {
        console.error('Error loading channel detail:', error);
        return {
          code: 0,
          data: {} as ChannelFormState,
          msg: '获取通道详情失败',
        };
      }
    },

    create: async (params) => {
      const transformedParams = transformFormStateToParams(params);
      return await createChannel(transformedParams);
    },

    update: async (id, params) => {
      const transformedParams = transformFormStateToParams(params);
      return await updateChannel(id, transformedParams);
    },

    onSuccess: options.onSuccess,
  });

  // 定义表单组配置 - 使用声明式布局
  const formGroups = [
    {
      title: '基本信息',
      fields: [
        {
          name: 'name',
          label: '通道名称',
          component: 'Input',
          required: true,
          col: { span: 12 },
          props: {
            placeholder: '请输入通道名称',
            allowClear: true,
          },
        },
        // 对接平台 - 使用声明式配置的 render 属性
        {
          name: 'platform',
          label: '对接平台',
          component: 'Select',
          required: true,
          col: { span: 12 },
          render: () => {
            // 获取当前平台的提示信息
            const prompt = getPlatformPrompt(formData.platform);

            return h('div', [
              h('div', { class: 'flex items-center' }, [
                h(Select, {
                  placeholder: '请选择接口对接平台',
                  allowClear: true,
                  showSearch: true,
                  options: platformOptions.value,
                  loading: platformLoading.value,
                  value: formData.platform,
                  'onUpdate:value': (val: any) => {
                    formData.platform = val;
                    formData.apiVersion = undefined;
                    versionOptions.value = [];
                    if (val) {
                      loadApiVersions(val);
                    }
                  },
                  filterOption: (input: string, option: any) => {
                    if (!option || !option.label) return false;
                    const label = String(option.label);
                    return label.toLowerCase().includes(input.toLowerCase());
                  },
                }),
                // 平台类型标签
                formData.platform &&
                platformTypeMap.value[formData.platform]?.type
                  ? h(
                      Tag,
                      {
                        color: 'warning',
                        class: 'ml-2',
                      },
                      platformTypeLabels[
                        platformTypeMap.value[formData.platform]
                          ?.type as keyof typeof platformTypeLabels
                      ] ||
                        `类型-${platformTypeMap.value[formData.platform]?.type}`,
                    )
                  : null,
              ]),
              // 如果有提示信息，显示在下方
              prompt
                ? h(
                    'div',
                    {
                      class: 'text-red-500 text-xs mt-1',
                    },
                    prompt,
                  )
                : null,
            ]);
          },
        },
        // 对接版本 - 使用更接近老版本的实现
        {
          name: 'apiVersion',
          label: '对接版本',
          component: 'Select',
          col: { span: 12 },
          required: true,
          show: () => versionOptions.value.length > 0,
          render: () => {
            // 获取当前版本的提示信息
            const prompt = getVersionPrompt(formData.apiVersion);

            return h('div', [
              h(Select, {
                placeholder: '请选择API对接版本',
                allowClear: true,
                options: versionOptions.value,
                loading: versionLoading.value,
                value: formData.apiVersion,
                'onUpdate:value': (val: any) => {
                  formData.apiVersion = val;
                },
                showSearch: true,
                filterOption: (input: string, option: any) => {
                  if (!option || !option.label) return false;
                  const label = String(option.label);
                  return label.toLowerCase().includes(input.toLowerCase());
                },
              }),
              // 如果有提示信息，显示在下方
              prompt
                ? h(
                    'div',
                    {
                      class: 'text-red-500 text-xs mt-1',
                    },
                    prompt,
                  )
                : null,
            ]);
          },
        },
      ],
    },
    {
      title: 'API配置',
      fields: [
        {
          name: 'apiAccount',
          label: 'API账号',
          component: 'Input',
          required: true,
          col: { span: 12 },
          props: {
            placeholder: '请输入API账号',
            allowClear: true,
          },
        },
        {
          name: 'apiPassword',
          label: 'API密码',
          component: 'Input',
          col: { span: 12 },
          required: true,
          props: {
            placeholder: '请输入API密码',
            allowClear: true,
          },
        },
      ],
    },
    // 为 API 密钥创建一个单独的分组
    {
      title: 'API密钥',
      // 直接使用 content 函数渲染自定义内容
      content: (fd: Record<string, any>) => {
        // 确保返回一个数组，以符合 FormGroup 接口的要求
        const rowNode = h(
          Row,
          { gutter: 16 },
          Object.entries(fd.apiKeys || {}).map(([key, value]) =>
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: `API密钥${key === 'apiKey' ? '' : key.replace('apiKey', '')}`,
                },
                () =>
                  h('div', { class: 'flex items-center gap-2' }, [
                    h(Input, {
                      value: value as string,
                      'onUpdate:value': (val: string) => {
                        fd.apiKeys[key] = val;
                      },
                      placeholder: `请输入API密钥${key === 'apiKey' ? '' : key.replace('apiKey', '')}`,
                      allowClear: true,
                    }),
                    // 第一个密钥不能删除,其他的都可以删除
                    key !== 'apiKey' &&
                      h(
                        Button,
                        {
                          type: 'link',
                          danger: true,
                          onClick: () => {
                            const { [key]: _, ...rest } = fd.apiKeys;
                            fd.apiKeys = rest;
                          },
                        },
                        () => '删除',
                      ),
                    // 如果是最后一个输入框，显示添加按钮
                    key === Object.keys(fd.apiKeys).slice(-1)[0] &&
                      h(
                        Button,
                        {
                          type: 'link',
                          onClick: () => {
                            const existingKeys = Object.keys(fd.apiKeys);
                            // 找到当前最大的索引
                            const maxIndex = Math.max(
                              ...existingKeys.map((k) => {
                                const match = k.match(/apiKey(\d+)/);
                                return match && match[1]
                                  ? Number.parseInt(match[1], 10)
                                  : 1;
                              }),
                            );
                            // 使用下一个索引
                            const nextIndex = maxIndex + 1;
                            fd.apiKeys[`apiKey${nextIndex}`] = '';
                          },
                        },
                        () => '添加密钥',
                      ),
                  ]),
              ),
            ]),
          ),
        );
        return [rowNode];
      },
    },
    {
      title: '接口配置',
      fields: [
        {
          name: 'apiUrl',
          label: 'API地址',
          component: 'Input',
          required: true,
          col: { span: 12 },
          props: {
            placeholder: '请输入API请求地址',
            allowClear: true,
          },
        },
        {
          name: 'apiUrl2',
          label: 'API地址2',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入API请求地址2',
            allowClear: true,
          },
        },
        {
          name: 'apiType',
          label: 'API类型',
          component: 'Select',
          required: true,
          col: { span: 12 },
          props: {
            options: [
              { label: '第三方套餐', value: '1' },
              { label: '直连运营商', value: '2' },
            ],
            placeholder: '请选择API类型',
            allowClear: true,
          },
        },
        {
          name: 'apiSubmitCard',
          label: '提交卡号',
          component: 'Select',
          required: true,
          col: { span: 12 },
          props: {
            options: [
              { label: 'ICCID', value: '1' },
              { label: 'MIS', value: '2' },
              { label: '虚拟号', value: '3' },
            ],
            placeholder: '请选择API提交卡号',
          },
          extra:
            'api的递交卡号，看上游平台支持什么卡号调用api按上游需要的选择(注意要根据自己导入到我方系统后台的对应)',
        },
        {
          name: 'infoGet',
          label: '信息获取',
          component: 'Select',
          required: true,
          col: { span: 12 },
          props: {
            options: [
              { label: '上游获取', value: '1' },
              { label: '系统自定义', value: '2' },
            ],
            placeholder: '请选择信息获取方式',
          },
          extra:
            '流量池选择无效自动系统自定义,第三方套餐如果想虚量必须选择系统自定义,上游获取是完全根据上游返回的总流量和使用流量显示轮询类型 》停机复机为默认模式,如上游系统卡片停机了无法再复机可选择断网复网来控制停复机',
        },
        {
          name: 'maximumDosage',
          label: '限制用量',
          component: 'Input',
          col: { span: 12 },
          props: {
            placeholder: '请输入自然月限制用量(MB)',
            allowClear: true,
          },
        },
      ],
    },
    {
      title: '轮询设置',
      fields: [
        {
          name: 'pollStopType',
          label: '轮询类型',
          component: 'Select',
          col: { span: 12 },
          required: true,
          props: {
            options: [
              { label: '停机复机', value: '1' },
              { label: '断网复网', value: '2' },
            ],
            placeholder: '请选择轮询停机类型',
          },
        },
        {
          name: 'apiPolling',
          label: '接口轮询',
          component: 'Switch',
          col: { span: 12 },
          props: {
            checkedChildren: '开启',
            unCheckedChildren: '关闭',
            checked: !!formData.apiPolling,
            'onUpdate:checked': (checked: boolean) => {
              formData.apiPolling = checked ? 1 : 0;
            },
          },
          render: () =>
            h(Switch, {
              checked: !!formData.apiPolling,
              onChange: (checked) => {
                formData.apiPolling = checked ? 1 : 0;
              },
              checkedChildren: '开启',
              unCheckedChildren: '关闭',
            }),
        },
        {
          name: 'sleepTime',
          label: '休眠(ms)',
          component: 'InputNumber',
          col: { span: 12 },
          required: true,
          props: {
            min: 0,
            style: { width: '120px' },
            placeholder: '请输入',
          },
          render: () =>
            h('div', { class: 'flex items-center' }, [
              h('div', {}, [
                h(InputNumber, {
                  value: formData.sleepTime,
                  'onUpdate:value': (val: any) => {
                    formData.sleepTime =
                      typeof val === 'number' ? val : Number.parseInt(val) || 0;
                  },
                  min: 0,
                  style: { width: '120px' },
                  placeholder: '请输入',
                }),
              ]),
              h(
                Tooltip,
                {
                  title: '单卡休眠时间',
                },
                () =>
                  h(MdiInformationOutline, {
                    class: 'ml-2 text-gray-400 cursor-help',
                    style: 'font-size: 18px',
                  }),
              ),
            ]),
        },
      ],
    },
    {
      title: '其他设置',
      fields: [
        {
          name: 'orderSubmit',
          label: '订单递交',
          component: 'Switch',
          col: { span: 12 },
          props: {
            checkedChildren: '是',
            unCheckedChildren: '否',
            checked: !!formData.orderSubmit,
          },
          render: () =>
            h(Switch, {
              checked: !!formData.orderSubmit,
              onChange: (checked) => {
                formData.orderSubmit = checked ? 1 : 0;
              },
              checkedChildren: '是',
              unCheckedChildren: '否',
            }),
        },
        {
          name: 'notes',
          label: '备注',
          component: 'TextArea',
          labelCol: { span: 3 }, // 标签占3份
          wrapperCol: { span: 20 }, // 内容区占20份
          col: { span: 24 },
          props: {
            placeholder: '请输入备注',
            rows: 4,
            style: { width: '100%', resize: 'none' },
            allowClear: true,
          },
        },
      ],
    },
  ];

  // 公开的API
  const show = (id?: number) => {
    if (!id) {
      formData.apiKeys = { apiKey: '' };
    }

    // 确保平台数据已加载
    platformLoading.value = true;
    loadPlatformOptions()
      .then(() => {
        platformLoading.value = false;
        showModal(id);
      })
      .catch((error) => {
        console.error('加载平台数据失败:', error);
        platformLoading.value = false;
        // 仍然显示表单，避免卡死
        showModal(id);
      });
  };

  return {
    formRef,
    formData,
    loading,
    visible,
    show,
    renderModal: () => renderFormModal(formGroups),
  };
}
