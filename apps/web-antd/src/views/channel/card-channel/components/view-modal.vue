<script setup lang="ts">
import type { ChannelItem } from '#/api/core/channel';

import { computed } from 'vue';

import DetailModal from '#/components/DetailModal/index.vue';

const props = defineProps<{
  data?: ChannelItem;
  loading?: boolean;
  visible: boolean;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
}>();

// 选项配置
const pollingOptions = {
  1: '启用',
  2: '禁用',
};

const apiTypeOptions = {
  1: '套餐',
  2: '前向流量池',
  3: '后向流量池',
};

const cardTypeOptions = {
  1: 'ICCID',
  2: 'MIS',
  3: '虚拟号',
};

const apiSourcesOptions = {
  1: '上游获取',
  2: '系统自定义',
};

const orderSubmissionOptions = {
  1: '类型1',
  2: '类型2',
};

const sections = computed(() => {
  if (!props.data) return [];
  const formatTime = (time?: string) => time?.replace('T', ' ') || '-';

  return [
    {
      title: '基本信息',
      items: [
        { label: '通道名称', value: props.data.apiName, bold: true },
        { label: '通道描述', value: props.data.notes },
        { label: 'API类型', value: apiTypeOptions[props.data.apiType] || '-' },
        {
          label: '卡号类型',
          value: cardTypeOptions[props.data.apiCardType] || '-',
        },
        {
          label: '信息获取',
          value: apiSourcesOptions[props.data.apiSources] || '-',
        },
        {
          label: '轮询状态',
          value: pollingOptions[props.data.apiPolling] || '-',
        },
        {
          label: '限制用量',
          value: props.data.maximumDosage || '-',
        },
      ],
    },
    {
      title: '轮询配置',
      items: [
        {
          label: '轮询卡类型',
          value: cardTypeOptions[props.data.pollingCardType] || '-',
        },
        { label: '停止类型', value: props.data.stopType },
        { label: '重新轮询', value: props.data.rePolling },
        { label: '单卡休眠', value: `${props.data.singleCardSleep}ms` },
      ],
    },
    {
      title: '平台配置',
      items: [
        { label: '平台编码', value: props.data.apiPlatform },
        { label: 'API账号', value: props.data.apiAccount },
        { label: 'API密钥1', value: props.data.apiKey || '-' },
        { label: 'API密钥2', value: props.data.apiKey2 || '-' },
        { label: 'API密码', value: props.data.apiPassword },
        { label: '请求地址', value: props.data.requestAddress },
        { label: '版本名称', value: props.data.versionName },
        { label: '版本标识', value: props.data.apiVersion },
        { label: 'API余额', value: props.data.apiBalance },
      ],
    },
    {
      title: '其他信息',
      items: [
        {
          label: '订单提交',
          value: orderSubmissionOptions[props.data.apiOrderSubmission] || '-',
        },
        { label: '订单机器', value: props.data.orderMachine },
        { label: '卡片数量', value: props.data.cardCount },
        { label: '分组数量', value: props.data.groupCount },
        { label: '换卡', value: props.data.changeCard },
        { label: '创建时间', value: formatTime(props.data.creationTime) },
        { label: '更新时间', value: formatTime(props.data.updateTime) },
      ],
    },
  ];
});
</script>

<template>
  <DetailModal
    :visible="props.visible"
    title="查看通道详情"
    :sections="sections"
    :loading="props.loading"
    @update:visible="(val) => emit('update:visible', val)"
  />
</template>

<style lang="less" scoped>
:deep(.ant-modal-body) {
  padding: 0;
}
</style>
