<script lang="ts" setup>
import type { TabItem } from '#/components/TabsView';

import { defineAsyncComponent, markRaw, ref } from 'vue';

import { Card, Tabs } from 'ant-design-vue';

// 使用异步组件导入各个日志组件，提高性能
const OperationLog = markRaw(
  defineAsyncComponent(() => import('./operation/index.vue')),
);
const LoginLog = markRaw(
  defineAsyncComponent(() => import('./login/index.vue')),
);
const SmsLog = markRaw(defineAsyncComponent(() => import('./sms/index.vue')));

const RefundLog = markRaw(
  defineAsyncComponent(() => import('./refund/index.vue')),
);

const activeKey = ref('operation');

// 定义选项卡列表
const tabList: TabItem[] = [
  {
    key: 'operation',
    tab: '操作日志',
    component: OperationLog,
    icon: 'mdi:clipboard-text-clock',
  },
  { key: 'login', tab: '登录日志', component: LoginLog, icon: 'mdi:login' },
  {
    key: 'sms',
    tab: '短信日志',
    component: SmsLog,
    icon: 'mdi:message-text-clock',
  },
  {
    key: 'refund',
    tab: '退款日志',
    component: RefundLog,
    icon: 'mdi:cash-refund',
  },
];
</script>

<template>
  <div class="unified-log p-2">
    <Card>
      <Tabs v-model:active-key="activeKey">
        <Tabs.TabPane v-for="item in tabList" :key="item.key" :tab="item.tab">
          <component :is="item.component" />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<style lang="less" scoped>
// .unified-log {
//   background-color: var(--background-deep);
//   min-height: calc(100vh - 130px);
// }
</style>
