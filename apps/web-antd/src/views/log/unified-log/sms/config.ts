import type { ColumnType } from 'ant-design-vue/es/table';
import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { SmsLogItem } from '#/api/core/task';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

// 搜索配置
export const searchItems: SearchItemConfig[] = [
  {
    field: 'sendobject',
    label: '发送对象',
    component: 'Input',
    props: {
      placeholder: '请输入发送对象',
      allowClear: true,
    },
  },
  {
    field: 'sendtype',
    label: '短信类型',
    component: 'Select',
    props: {
      placeholder: '请选择短信类型',
      allowClear: true,
      options: [
        { label: '验证码', value: '1' },
        { label: '物流激活推送', value: '2' },
        { label: '流量用尽推送', value: '3' },
        { label: '充值成功推送', value: '4' },
        { label: '套餐到期推送', value: '5' },
        { label: '代理预存不足推送', value: '6' },
        { label: '轮询异常推送', value: '7' },
      ],
    },
  },
  {
    field: 'status',
    label: '发送状态',
    component: 'Select',
    props: {
      placeholder: '请选择发送状态',
      allowClear: true,
      options: [
        { label: '发送成功', value: 1 },
        { label: '发送失败', value: 2 },
      ],
    },
  },
];

// 短信类型映射
const sendTypeMap: Record<string, string> = {
  '1': '验证码',
  '2': '物流激活推送',
  '3': '流量用尽推送',
  '4': '充值成功推送',
  '5': '套餐到期推送',
  '6': '代理预存不足推送',
  '7': '轮询异常推送',
};

// 表格列配置
export const columns: ColumnType<SmsLogItem>[] = [
  {
    title: '发送对象',
    dataIndex: 'sendobject',
    align: 'center' as AlignType,
    width: 120,
  },
  {
    title: '短信类型',
    dataIndex: 'sendtype',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }) => sendTypeMap[text] || text,
  },
  {
    title: '短信内容',
    dataIndex: 'content',
    align: 'center' as AlignType,
    width: 200,
  },
  {
    title: '发送状态',
    dataIndex: 'status',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }) => {
      const color = text === 1 ? 'success' : 'error';
      const label = text === 1 ? '成功' : '失败';
      return h(Tag, { color }, () => label);
    },
  },
  {
    title: '发送时间',
    dataIndex: 'creation_time',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
];
