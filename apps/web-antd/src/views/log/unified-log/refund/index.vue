<script lang="ts" setup>
import type { RefundLogItem } from '#/api/core/task';

import { onMounted } from 'vue';

import { message, Modal } from 'ant-design-vue';

import { deleteRefundLog, getRefundLogList } from '#/api/core/task';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { columns, searchItems } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getRefundLogList,
  defaultParams: {},
});

// 删除
const handleDelete = (row: RefundLogItem) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条退款记录吗？',
    async onOk() {
      try {
        const res = await deleteRefundLog(row.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList(); // 重新加载数据
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="log-refund">
    <!-- <Card> -->
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="searchItems"
      :loading="loading"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :show-action="true"
      :action-buttons="[
        {
          text: '删除',
          onClick: handleDelete,
          key: 'delete',
        },
      ]"
      :pagination="pagination"
      @change="handleTableChange"
    />
    <!-- </Card> -->
  </div>
</template>

<style lang="less" scoped>
.log-refund {
  background-color: var(--background-deep);
}
</style>
