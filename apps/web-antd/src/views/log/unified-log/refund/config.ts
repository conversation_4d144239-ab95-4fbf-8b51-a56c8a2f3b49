import type { ColumnType } from 'ant-design-vue/es/table';
import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { RefundLogItem } from '#/api/core/task';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import dayjs from 'dayjs';

// 搜索配置
export const searchItems: SearchItemConfig[] = [
  {
    field: 'systemOrdernumber',
    label: '系统单号',
    component: 'Input',
    props: {
      placeholder: '请输入系统单号',
      allowClear: true,
    },
  },
  {
    field: 'refundOrdernumber',
    label: '退款单号',
    component: 'Input',
    props: {
      placeholder: '请输入退款单号',
      allowClear: true,
    },
  },
  {
    field: 'refundType',
    label: '退款类型',
    component: 'Select',
    props: {
      placeholder: '请选择退款类型',
      allowClear: true,
      options: [
        { label: '不退金额', value: 0 },
        { label: '原路退回', value: 1 },
        { label: '退回余额', value: 2 },
        { label: '抹除佣金', value: 3 },
      ],
    },
  },
  {
    field: 'msg',
    label: '退款描述',
    component: 'Input',
    props: {
      placeholder: '请输入退款描述',
      allowClear: true,
    },
  },
  {
    field: 'refundAmountMin',
    label: '退款金额(最低)',
    component: 'Input',
    props: {
      placeholder: '请输入最低金额',
      allowClear: true,
    },
  },
  {
    field: 'refundAmountMax',
    label: '退款金额(最高)',
    component: 'Input',
    props: {
      placeholder: '请输入最高金额',
      allowClear: true,
    },
  },
  {
    field: 'creationTime',
    label: '退款时间开始',
    component: 'DatePicker',
    props: {
      placeholder: '请选择退款时间',
      allowClear: true,
      showTime: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'creationTimeEnd',
    label: '退款时间结束',
    component: 'DatePicker',
    props: {
      placeholder: '请选择退款时间',
      allowClear: true,
      showTime: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];

// 表格列配置
export const columns: ColumnType<RefundLogItem>[] = [
  {
    title: '系统单号',
    dataIndex: 'systemOrdernumber',
    align: 'center' as AlignType,
    width: 180,
  },
  {
    title: '退款单号',
    dataIndex: 'refundOrdernumber',
    align: 'center' as AlignType,
    width: 180,
  },
  {
    title: '退款类型',
    dataIndex: 'refundType',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }) => {
      const typeMap: Record<number, string> = {
        0: '不退金额',
        1: '原路退回',
        2: '退回余额',
        3: '抹除佣金',
      };
      return typeMap[text] || text;
    },
  },
  {
    title: '退款金额',
    dataIndex: 'refundAmount',
    align: 'center' as AlignType,
    width: 120,
    customRender: ({ text }) => `¥${text.toFixed(2)}`,
  },
  {
    title: '退款描述',
    dataIndex: 'msg',
    align: 'center' as AlignType,
    width: 100,
  },
  {
    title: '退款时间',
    dataIndex: 'creationTime',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
];
