import type { ColumnType } from 'ant-design-vue/es/table';
import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { LoginLogItem } from '#/api/core/task';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

// 搜索配置
export const searchItems: SearchItemConfig[] = [
  {
    field: 'ip',
    label: '登录IP',
    component: 'Input',
    props: {
      placeholder: '请输入登录IP',
      allowClear: true,
    },
  },
  {
    field: 'account',
    label: '登陆账号',
    component: 'Input',
    props: {
      placeholder: '请输入登陆账号',
      allowClear: true,
    },
  },
  {
    field: 'status',
    label: '登录状态',
    component: 'Select',
    props: {
      placeholder: '请选择登录状态',
      allowClear: true,
      options: [
        { label: '成功', value: 1 },
        { label: '失败', value: 2 },
      ],
    },
  },
  {
    field: 'home',
    label: '登陆地址',
    component: 'Input',
    props: {
      placeholder: '请输入登陆地址',
      allowClear: true,
    },
  },
];

// 表格列配置
export const columns: ColumnType<LoginLogItem>[] = [
  {
    title: '登录账号',
    dataIndex: 'account',
    align: 'center' as AlignType,
    width: 120,
  },
  {
    title: '登录IP',
    dataIndex: 'ip',
    align: 'center' as AlignType,
    width: 150,
  },
  {
    title: '登录地点',
    dataIndex: 'home',
    align: 'center' as AlignType,
    width: 200,
  },
  {
    title: '登录状态',
    dataIndex: 'status',
    align: 'center' as AlignType,
    width: 100,
    customRender: ({ text }) => {
      const color = text === 1 ? 'success' : 'error';
      const label = text === 1 ? '成功' : '失败';
      return h(Tag, { color }, () => label);
    },
  },
  {
    title: '登录时间',
    dataIndex: 'creation_time',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
];
