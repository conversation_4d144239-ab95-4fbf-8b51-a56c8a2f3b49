<script lang="ts" setup>
import type { OperationLogItem } from '#/api/core/task';

import { onMounted, ref } from 'vue';

import { MdiContentCopy } from '@vben/icons';

import { Card, Descriptions, message, Modal, Tooltip } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getOperationLogList } from '#/api/core/task';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { columns, searchItems } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getOperationLogList,
  defaultParams: {},
});

// 查看弹窗状态
const viewVisible = ref(false);
const currentRecord = ref<OperationLogItem>();

// 格式化 JSON 显示
const formatJson = (text: string) => {
  try {
    return JSON.stringify(JSON.parse(text), null, 2);
  } catch {
    return text;
  }
};

// 处理查看
const handleView = (record: OperationLogItem) => {
  currentRecord.value = record;
  viewVisible.value = true;
};

// 处理关闭弹窗
const handleClose = () => {
  viewVisible.value = false;
  currentRecord.value = undefined;
};

// 处理复制
const handleCopy = (text: string) => {
  navigator.clipboard.writeText(text).then(() => {
    message.success('复制成功');
  });
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="log-operation">
    <!-- <Card> -->
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="searchItems"
      :loading="loading"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :show-action="true"
      :action-buttons="[
        {
          key: 'view',
          text: '查看',
          onClick: handleView,
        },
      ]"
      :pagination="pagination"
      @change="handleTableChange"
    />
    <!-- </Card> -->

    <!-- 查看弹窗 -->
    <Modal
      v-model:visible="viewVisible"
      title="操作日志详情"
      width="800px"
      @cancel="handleClose"
    >
      <!-- 基本信息 -->
      <Descriptions
        :column="{ xs: 1, sm: 2 }"
        bordered
        size="small"
        :label-style="{ minWidth: '80px' }"
      >
        <Descriptions.Item label="操作人">
          {{ currentRecord?.userAccount }}
        </Descriptions.Item>
        <Descriptions.Item label="操作名称">
          {{ currentRecord?.operateName }}
        </Descriptions.Item>
        <Descriptions.Item label="操作IP">
          {{ currentRecord?.operateIp }}
        </Descriptions.Item>
        <Descriptions.Item label="操作地点">
          {{ currentRecord?.operateAddress }}
        </Descriptions.Item>
        <Descriptions.Item label="操作时间">
          {{
            currentRecord?.operateTime
              ? dayjs(currentRecord.operateTime).format('YYYY-MM-DD HH:mm:ss')
              : '-'
          }}
        </Descriptions.Item>
        <Descriptions.Item label="耗时">
          {{ currentRecord?.costTime }}ms
        </Descriptions.Item>
        <Descriptions.Item label="操作类名">
          {{ currentRecord?.className }}
        </Descriptions.Item>
        <Descriptions.Item label="操作方法">
          {{ currentRecord?.methodName }}
        </Descriptions.Item>
      </Descriptions>

      <!-- 参数信息 -->
      <div class="mt-4">
        <Card title="传入参数" :bordered="false" size="small">
          <template #extra>
            <!-- <Tooltip title="复制"> -->
            <MdiContentCopy
              class="hover:text-primary cursor-pointer"
              @click="
                handleCopy(
                  currentRecord?.methodParams
                    ? formatJson(currentRecord.methodParams)
                    : '',
                )
              "
            />
            <!-- </Tooltip> -->
          </template>
          <pre
            class="m-0 max-h-[300px] overflow-auto whitespace-pre-wrap break-all rounded p-2"
            >{{
              currentRecord?.methodParams
                ? formatJson(currentRecord.methodParams)
                : '-'
            }}</pre>
        </Card>
      </div>

      <!-- 返回信息 -->
      <div class="mt-4">
        <Card title="返回值" :bordered="false" size="small">
          <template #extra>
            <Tooltip title="复制">
              <CopyOutlined
                class="hover:text-primary cursor-pointer"
                @click="
                  handleCopy(
                    currentRecord?.returnValue
                      ? formatJson(currentRecord.returnValue)
                      : '',
                  )
                "
              />
            </Tooltip>
          </template>
          <pre
            class="m-0 max-h-[300px] overflow-auto whitespace-pre-wrap break-all rounded p-2"
            >{{
              currentRecord?.returnValue
                ? formatJson(currentRecord.returnValue)
                : '-'
            }}</pre>
        </Card>
      </div>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.log-operation {
  background-color: var(--background-deep);
}
</style>
