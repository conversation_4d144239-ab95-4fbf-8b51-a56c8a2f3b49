import type { ColumnType } from 'ant-design-vue/es/table';
import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { OperationLogItem } from '#/api/core/task';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h } from 'vue';

import { EllipsisText } from '@vben/common-ui';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getUserOptionsApi } from '#/api';
import { useCopyable } from '#/hooks/web/useCopyable';

const { createCopyableCell } = useCopyable();
// 搜索配置
export const searchItems: SearchItemConfig[] = [
  {
    field: 'userId',
    label: '操作人',
    component: 'Select',
    remote: {
      api: getUserOptionsApi,
      transform: (data: any) =>
        data.data.map((item: any) => ({
          label: `${item.name}`, // 组合显示
          value: item.id,
        })),
    },
    props: {
      optionFilterProp: 'label',
      placeholder: '请选择操作人',
      allowClear: true,
      // options: [],
    },
  },
  {
    field: 'className',
    label: '操作类名',
    component: 'Input',
    props: {
      placeholder: '请输入操作类名',
      allowClear: true,
    },
  },
  {
    field: 'methodName',
    label: '操作方法名',
    component: 'Input',
    props: {
      placeholder: '请输入操作方法名',
      allowClear: true,
    },
  },
  {
    field: 'returnValue',
    label: '返回参数',
    component: 'Input',
    props: {
      placeholder: '请输入返回参数',
      allowClear: true,
    },
  },
  {
    field: 'methodParams',
    label: '传入参数',
    component: 'Input',
    props: {
      placeholder: '请输入传入参数',
      allowClear: true,
    },
  },
];

const formatJsonString = (text: string) => {
  try {
    const obj = JSON.parse(text);
    // 如果是数组，展示成更友好的格式
    if (Array.isArray(obj)) {
      return `[${obj.join(', ')}]`;
    }
    // 如果是对象，只展示关键信息
    const keys = Object.keys(obj);
    if (keys.length > 3) {
      const shortObj = keys.slice(0, 3).reduce(
        (acc, key) => {
          acc[key] = obj[key];
          return acc;
        },
        {} as Record<string, any>,
      );
      return `${JSON.stringify(shortObj)}...`;
    }
    return JSON.stringify(obj);
  } catch {
    return text;
  }
};

// 表格列配置
export const columns: ColumnType<OperationLogItem>[] = [
  {
    title: '操作人',
    dataIndex: 'userAccount',
    align: 'center' as AlignType,
    width: 120,
  },
  {
    title: '操作名称',
    dataIndex: 'operateName',
    align: 'center' as AlignType,
    width: 150,
    customRender: ({ text }) => h(Tag, { color: 'default' }, () => text),
  },
  // {
  //   title: '操作类名',
  //   dataIndex: 'className',
  //   align: 'center' as AlignType,
  //   width: 300,
  //   ellipsis: true,
  //   customRender: ({ text }) => h('span', { title: text }, text),
  // },
  // {
  //   title: '操作方法',
  //   dataIndex: 'methodName',
  //   align: 'center' as AlignType,
  //   width: 150,
  // },
  {
    title: '操作IP',
    dataIndex: 'operateIp',
    align: 'center' as AlignType,
    width: 150,
  },
  {
    title: '操作地点',
    dataIndex: 'operateAddress',
    align: 'center' as AlignType,
    width: 200,
  },
  {
    title: '操作时间',
    dataIndex: 'operateTime',
    align: 'center' as AlignType,
    width: 180,
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '耗时(ms)',
    dataIndex: 'costTime',
    align: 'center' as AlignType,
    width: 100,
  },
  {
    title: '传入参数',
    dataIndex: 'methodParams',
    align: 'center' as AlignType,
    width: 200,
    customRender: ({ text }) => {
      try {
        const formattedText = JSON.stringify(JSON.parse(text), null, 2);
        return h(Tag, { color: 'processing' }, () =>
          h(
            EllipsisText,
            {
              line: 1,
              expand: true,
              maxWidth: 180,
            },
            () =>
              createCopyableCell(
                () => formatJsonString(text),
                () => formattedText,
              ),
          ),
        );
      } catch {
        return h(Tag, { color: 'processing' }, () =>
          h(EllipsisText, { line: 1, expand: true, maxWidth: 180 }, () =>
            createCopyableCell(() => text),
          ),
        );
      }
    },
  },
  {
    title: '返回值',
    dataIndex: 'returnValue',
    align: 'center' as AlignType,
    width: 300,
    customRender: ({ text }) => {
      try {
        const formattedText = JSON.stringify(JSON.parse(text), null, 2);
        return h(Tag, { color: 'success' }, () =>
          h(EllipsisText, { line: 1, expand: true, maxWidth: 280 }, () =>
            createCopyableCell(
              () => formatJsonString(text),
              () => formattedText,
            ),
          ),
        );
      } catch {
        return h(Tag, { color: 'success' }, () =>
          h(EllipsisText, { line: 1, expand: true, maxWidth: 280 }, () =>
            createCopyableCell(() => text),
          ),
        );
      }
    },
  },
];
