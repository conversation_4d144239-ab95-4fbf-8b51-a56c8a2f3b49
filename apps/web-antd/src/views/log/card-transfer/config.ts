import type { ColumnType } from 'ant-design-vue/es/table';
import type { AlignType } from 'ant-design-vue/es/vc-table/interface';

import type { CardTransferItem } from '#/api/core/task';
import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import dayjs from 'dayjs';

import { getUserOptionsApi } from '#/api';

// 搜索配置
export const searchItems: SearchItemConfig[] = [
  {
    field: 'oidCardNo',
    label: '旧卡号',
    component: 'Input',
    props: {
      placeholder: '请输入旧卡号',
      allowClear: true,
    },
  },
  {
    field: 'newCardNo',
    label: '新卡号',
    component: 'Input',
    props: {
      placeholder: '请输入新卡号',
      allowClear: true,
    },
  },
  {
    field: 'userIds',
    label: '操作账号',
    component: 'Select',
    remote: {
      api: getUserOptionsApi,
      transform: (data: any) =>
        data.data.map((item: any) => ({
          label: item.name,
          value: item.id,
        })),
    },
    props: {
      placeholder: '请选择操作账号',
      allowClear: true,
      // options: [],
    },
  },
  {
    field: 'cardUserIds',
    label: '卡片归属账号',
    component: 'Select',
    remote: {
      api: getUserOptionsApi,
      transform: (data: any) =>
        data.data.map((item: any) => ({
          label: item.name,
          value: item.id,
        })),
    },
    props: {
      placeholder: '请选择卡片归属账号',
      allowClear: true,
      // options: [],
    },
  },
  {
    field: ['creationTimeBegin', 'creationTimeEnd'],
    label: '创建时间',
    component: 'RangePicker',
    props: {
      placeholder: ['开始时间', '结束时间'],
      allowClear: true,
    },
  },
];

// 表格列配置
export const columns: ColumnType<CardTransferItem>[] = [
  {
    title: '旧卡号',
    dataIndex: 'oidCardNo',
    align: 'center' as AlignType,
    width: 180,
    ellipsis: true,
  },
  {
    title: '新卡号',
    dataIndex: 'newCardNo',
    align: 'center' as AlignType,
    width: 180,
    ellipsis: true,
  },
  {
    title: '操作账号',
    dataIndex: 'userAccount',
    align: 'center' as AlignType,
    width: 120,
    ellipsis: true,
  },
  {
    title: '卡片归属账号',
    dataIndex: 'cardUserAccount',
    align: 'center' as AlignType,
    width: 120,
    ellipsis: true,
  },
  {
    title: '描述',
    dataIndex: 'msg',
    align: 'center' as AlignType,
    width: 300,
    ellipsis: true,
  },
  {
    title: '转移时间',
    dataIndex: 'creationTime',
    align: 'center' as AlignType,
    width: 180,
    ellipsis: true,
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
];
