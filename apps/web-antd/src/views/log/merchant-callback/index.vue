<script lang="ts" setup>
import type { MerchantCallbackItem } from '#/api/core/task';

import { onMounted, ref } from 'vue';

import { Card, Descriptions, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getMerchantCallbackList } from '#/api/core/task';
// import BasicTable from '#/components/BasicTable/index.vue';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { columns, searchItems } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getMerchantCallbackList,
  defaultParams: {},
});

// 详情弹窗
const detailVisible = ref(false);
const currentRecord = ref<MerchantCallbackItem>();

// 查看详情
const handleViewDetail = (record: MerchantCallbackItem) => {
  currentRecord.value = record;
  detailVisible.value = true;
};

// 关闭弹窗
const handleClose = () => {
  detailVisible.value = false;
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="log-merchant-callback p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            text: '查看详情',
            key: 'view',
            onClick: handleViewDetail,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />
    </Card>

    <!-- 详情弹窗 -->
    <Modal
      v-model:visible="detailVisible"
      title="详细信息"
      :width="800"
      :mask-closable="true"
      :keyboard="true"
      centered
      :style="{ top: '50px' }"
      @ok="handleClose"
      class="detail-modal"
    >
      <div class="detail-content">
        <Descriptions
          :column="{ xxl: 3, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }"
          bordered
          size="middle"
        >
          <Descriptions.Item label="订单编号">
            {{ currentRecord?.orderNo }}
          </Descriptions.Item>
          <Descriptions.Item label="回调类型">
            {{
              {
                1: '卡片套餐订单',
                2: '卡片余额订单',
                3: '代理余额订单',
                4: '代理流量订单',
              }[currentRecord?.type || 0] || currentRecord?.type
            }}
          </Descriptions.Item>
          <Descriptions.Item label="支付方式">
            {{
              {
                1: '微信支付',
                2: '支付宝支付',
              }[currentRecord?.payType || 0] || currentRecord?.payType
            }}
          </Descriptions.Item>
          <Descriptions.Item label="回调状态">
            {{ currentRecord?.msg }}
          </Descriptions.Item>
          <Descriptions.Item label="回调时间">
            {{
              currentRecord?.creationTime
                ? dayjs(currentRecord.creationTime).format(
                    'YYYY-MM-DD HH:mm:ss',
                  )
                : ''
            }}
          </Descriptions.Item>
        </Descriptions>

        <div class="mt-4 flex-1 overflow-hidden">
          <div class="mb-2 text-gray-500">原始数据</div>
          <div class="h-[300px] overflow-auto rounded border p-4">
            <pre class="m-0 whitespace-pre-wrap break-all font-mono text-sm">{{
              currentRecord ? JSON.stringify(currentRecord, null, 2) : ''
            }}</pre>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
.log-merchant-callback {
  background-color: var(--background-deep);
}

:deep(.ant-descriptions-item-label) {
  width: 80px;
  background-color: #fafafa;
}

:deep(.ant-descriptions-bordered .ant-descriptions-item-content) {
  padding: 12px 16px;
}

:deep(.detail-modal) {
  .ant-modal-content {
    height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
  }

  .ant-modal-body {
    flex: 1;
    overflow: hidden;
    padding: 24px;
  }

  .ant-modal-footer {
    text-align: center;
    padding: 12px 24px;
  }
}

.detail-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

@media screen and (max-width: 768px) {
  :deep(.ant-descriptions-item-label) {
    width: auto;
    min-width: 80px;
  }

  :deep(.ant-descriptions-bordered .ant-descriptions-item-content) {
    padding: 8px 12px;
  }

  :deep(.detail-modal) {
    .ant-modal-content {
      height: calc(100vh - 40px);
    }

    .ant-modal-body {
      padding: 12px !important;
    }
  }
}
</style>
