<script lang="ts" setup>
import type { UploadChangeParam } from 'ant-design-vue/es/upload';

import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { MdiExport, MdiPlus, MdiUpload } from '@vben/icons';

import { Button, Card, message, Modal, Upload } from 'ant-design-vue';

import {
  addWechatPublic,
  deleteWechatPublic,
  getWechatMenu,
  getWechatPublicList,
  updateWechatMenu,
  updateWechatPublic,
  uploadPaymentAuthFile,
} from '#/api/core/payment';
import { getUserOptionsApi } from '#/api/core/user';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import FormModal from './components/form-modal.vue';
import MenuModal from './components/menu-modal.vue';
import MenuPreviewModal from './components/menu-preview-modal.vue';
import { columns, createSearchItems } from './config';

const router = useRouter();
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getWechatPublicList,
  defaultParams: {},
});

const formLoading = ref(false);
const formVisible = ref(false);
const currentRecord = ref();
const userOptions = ref([]);
// 搜索配置
const searchItems = ref(createSearchItems());

const menuVisible = ref(false);
const menuLoading = ref(false);
const currentMenuRecord = ref();

// 菜单预览相关状态
const menuPreviewVisible = ref(false);
const menuPreviewLoading = ref(false);
const menuPreviewData = ref({
  button: [],
});

// 加载用户选项
const loadUserOptions = async () => {
  try {
    const res = await getUserOptionsApi();
    if (res.code === 1) {
      userOptions.value = res.data.map((item: any) => ({
        label: item.user_account || item.user_name || item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error('获取用户选项失败:', error);
  }
};

// 处理新增
const handleAdd = () => {
  currentRecord.value = undefined;
  formVisible.value = true;
};

// 处理编辑
const handleEdit = (record: any) => {
  currentRecord.value = record;
  formVisible.value = true;
};

// 处理自定义菜单
const handleCustomMenu = (record: any) => {
  currentMenuRecord.value = record;
  menuVisible.value = true;
};

// 处理删除
const handleDelete = async (record: any) => {
  Modal.confirm({
    title: '提示',
    content: '确定删除该公众号吗？',
    onOk: async () => {
      try {
        await deleteWechatPublic(record.id);
        message.success('删除成功');
        getList();
      } catch (error) {
        console.error('删除失败:', error);
      }
    },
  });
};

// 处理表单提交
const handleFormSubmit = async (values: any) => {
  try {
    formLoading.value = true;
    if (currentRecord.value?.id) {
      const res = await updateWechatPublic({
        ...values,
        id: currentRecord.value.id,
      });
      if (res.code === 1) {
        message.success('修改成功');
        formVisible.value = false;
        getList();
      } else {
        message.error(res.msg || '修改失败');
      }
    } else {
      const res = await addWechatPublic(values);
      if (res.code === 1) {
        message.success('新增成功');
        formVisible.value = false;
        getList();
      } else {
        message.error(res.msg || '新增失败');
      }
    }
  } catch (error) {
    console.error('提交失败:', error);
    message.error('提交失败');
  } finally {
    formLoading.value = false;
  }
};

// 处理上传支付授权文件
const uploadVisible = ref(false);
const uploadLoading = ref(false);
const fileList = ref<any[]>([]);

const handleUpload = () => {
  uploadVisible.value = true;
};

// 处理弹窗关闭
const handleUploadModalClose = () => {
  uploadVisible.value = false;
  fileList.value = [];
};

// 处理文件变化
const handleFileChange = (info: UploadChangeParam) => {
  fileList.value = info.fileList.slice(-1); // 只保留最后一个文件
};

// 处理上传确认
const handleUploadConfirm = async () => {
  if (fileList.value.length === 0) {
    message.warning('请选择要上传的文件');
    return;
  }

  try {
    uploadLoading.value = true;
    const file = fileList.value[0].originFileObj;
    const res = await uploadPaymentAuthFile(file);
    if (res.code === 1) {
      message.success('上传成功');
      uploadVisible.value = false;
      fileList.value = [];
    } else {
      message.error(res.msg || '上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    message.error('上传失败');
  } finally {
    uploadLoading.value = false;
  }
};

// 处理导出
const handleExport = () => {
  message.success('还么开发🐛');
};

// 处理菜单提交
const handleMenuSubmit = () => {
  getList();
};

// 处理菜单预览
const handleMenuPreview = (record: any) => {
  currentMenuRecord.value = record;
  // 使用query参数传递record信息
  router.push({
    path: `/standalone/wechat-menu/${record.id}`,
    query: {
      name: record.name,
      appid: record.appid,
      user_name: record.user__user_name,
    },
  });

  return;

  // 获取菜单数据
  getWechatMenu(record.id)
    .then((res: any) => {
      // 数据返回后更新
      menuPreviewData.value =
        res.code === 1 && res.content?.selfmenu_info?.button
          ? {
              button: res.content.selfmenu_info.button || [],
            }
          : { button: [] };
    })
    .catch((error: any) => {
      console.error('获取菜单失败:', error);
      message.error('获取菜单失败');
    })
    .finally(() => {
      menuPreviewLoading.value = false;
    });
};

// 处理菜单编辑结束并保存
const handleMenuSave = (updatedMenuData: any) => {
  // 保存更新后的菜单数据
  menuPreviewLoading.value = true;

  // 调用API保存菜单数据
  updateWechatMenu({
    public_id: currentMenuRecord.value.id,
    content: updatedMenuData,
  })
    .then((res) => {
      if (res.code === 1) {
        message.success('菜单保存成功');
        menuPreviewVisible.value = false; // 只有保存成功才关闭弹窗
        getList(); // 刷新列表
      } else {
        message.error(res.msg || '保存失败');
        // 保存失败不关闭弹窗，让用户继续编辑
      }
    })
    .catch((error) => {
      console.error('保存菜单失败:', error);
      message.error('保存菜单失败');
      // 保存出错也不关闭弹窗
    })
    .finally(() => {
      menuPreviewLoading.value = false;
    });
};

// 表格的操作按钮列表
const actionButtons = [
  // 菜单预览
  {
    key: 'menuPreview',
    text: '菜单预览',
    onClick: handleMenuPreview,
  },
  // 自定义菜单
  {
    key: 'customMenu',
    text: '自定义菜单',
    onClick: handleCustomMenu,
  },
  {
    key: 'edit',
    text: '编辑',
    onClick: handleEdit,
  },
  {
    key: 'delete',
    text: '删除',
    onClick: handleDelete,
  },
];

onMounted(() => {
  loadUserOptions();
  getList();
});
</script>

<template>
  <div class="payment-wechat-public p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        :custom-buttons="[
          {
            icon: MdiPlus,
            text: '新增',
            type: 'primary',
            onClick: handleAdd,
          },
          // 上传支付授权文件
          {
            icon: MdiUpload,
            text: '上传支付授权文件',
            onClick: handleUpload,
          },
          // 导出
          {
            icon: MdiExport,
            text: '导出',
            onClick: handleExport,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="actionButtons"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <!-- 表单弹窗 -->
      <FormModal
        v-model:visible="formVisible"
        :loading="formLoading"
        :record="currentRecord"
        :user-options="userOptions"
        @submit="handleFormSubmit"
      />

      <!-- 菜单弹窗 -->
      <MenuModal
        v-model:visible="menuVisible"
        v-model:loading="menuLoading"
        :record="currentMenuRecord"
        @submit="handleMenuSubmit"
      />

      <!-- 菜单预览弹窗 -->
      <MenuPreviewModal
        v-model:visible="menuPreviewVisible"
        v-model:loading="menuPreviewLoading"
        :menu-data="menuPreviewData"
        :record="currentMenuRecord"
        @save="handleMenuSave"
      />

      <!-- 上传弹窗 -->
      <Modal
        title="上传支付授权文件"
        :visible="uploadVisible"
        :confirm-loading="uploadLoading"
        :mask-closable="false"
        :width="500"
        cancel-text="取消"
        ok-text="上传"
        @update:visible="handleUploadModalClose"
        @ok="handleUploadConfirm"
        @cancel="handleUploadModalClose"
      >
        <div class="upload-container">
          <Upload
            v-model:file-list="fileList"
            :before-upload="() => false"
            accept=".txt,.pem,.key"
            :max-count="1"
            class="upload-area"
            @change="handleFileChange"
          >
            <Button>
              <template #icon>
                <MdiUpload class="upload-icon" />
              </template>
              选择文件
            </Button>
            <template #tip>
              <div class="upload-tip">支持 .txt、.pem、.key 格式的文件</div>
            </template>
          </Upload>
        </div>
      </Modal>
    </Card>
  </div>
</template>

<style lang="less" scoped>
.payment-wechat-public {
  background-color: var(--background-deep);
}

.upload-container {
  padding: 24px;
  text-align: center;

  .upload-area {
    width: 100%;

    :deep(.ant-upload) {
      width: 100%;
      display: flex;
      justify-content: center;

      .ant-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;

        .upload-icon {
          margin-right: -4px;
        }
      }
    }

    :deep(.ant-upload-list) {
      text-align: left;
      margin-top: 16px;

      .ant-upload-list-item {
        padding: 8px 12px;
        border-radius: 4px;
      }
    }
  }

  .upload-tip {
    margin-top: 12px;
    color: #999;
    font-size: 13px;
    line-height: 1.5;
  }
}
</style>
