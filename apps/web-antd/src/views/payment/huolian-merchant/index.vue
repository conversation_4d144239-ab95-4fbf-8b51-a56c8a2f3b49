<script lang="ts" setup>
import type { HuolianMerchantItem } from '#/api/core/payment';

import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { But<PERSON>, Card, message, Modal } from 'ant-design-vue';

import {
  addHuolianMerchant,
  deleteHuolianMerchant,
  getHuolianMerchantList,
  updateHuolianMerchant,
} from '#/api/core/payment';
import { getUserOptionsApi } from '#/api/core/user';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import FormModal from './components/form-modal.vue';
import { advancedItems, basicItems, columns } from './config';

const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getHuolianMerchantList,
  defaultParams: {},
});

// 表单弹窗相关
const formVisible = ref(false);
const formLoading = ref(false);
const currentRecord = ref<Partial<HuolianMerchantItem>>();
const accountOptions = ref<{ label: string; value: number }[]>([]);

// 加载用户选项
const loadUserOptions = async () => {
  try {
    const res = await getUserOptionsApi();
    if (res.code === 1) {
      const options = res.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));

      // 保存账号选项用于表单
      accountOptions.value = options;

      // 更新所属账号选项
      const accountItem = basicItems.find(
        (item) => item.field === 'operatorAccount',
      );
      if (accountItem) {
        accountItem.props!.options = options;
      }
    }
  } catch (error) {
    console.error('获取用户选项失败:', error);
    message.error('获取用户选项失败');
  }
};

// 处理新增
const handleAdd = () => {
  currentRecord.value = undefined;
  formVisible.value = true;
};

// 处理编辑
const handleEdit = (record: HuolianMerchantItem) => {
  currentRecord.value = { ...record };
  formVisible.value = true;
};

// 处理删除
const handleDelete = (record: HuolianMerchantItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除商户"${record.name}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const res = await deleteHuolianMerchant(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理表单提交
const handleFormSubmit = async (values: any) => {
  formLoading.value = true;
  try {
    const data = {
      ...values,
      userId: values.operatorAccount, // 使用 operatorAccount 作为 userId
    };

    const res = await (currentRecord.value?.id
      ? updateHuolianMerchant(data)
      : addHuolianMerchant(data));

    if (res.code === 1) {
      message.success(currentRecord.value?.id ? '编辑成功' : '新增成功');
      formVisible.value = false;
      getList();
    } else {
      message.error(
        res.msg || (currentRecord.value?.id ? '编辑失败' : '新增失败'),
      );
    }
  } catch (error) {
    console.error(currentRecord.value?.id ? '编辑失败:' : '新增失败:', error);
    message.error(currentRecord.value?.id ? '编辑失败' : '新增失败');
  } finally {
    formLoading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(async () => {
  await loadUserOptions();
  getList();
});
</script>

<template>
  <div class="payment-huolian-merchant p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicItems"
        :advanced-items="advancedItems"
        :loading="loading"
        :custom-buttons="[
          {
            icon: MdiPlus,
            text: '新增',
            type: 'primary',
            onClick: handleAdd,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      >
        <template #extra>
          <Button type="primary" @click="handleAdd">新增</Button>
        </template>
      </SearchToolbar>

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <!-- 表单弹窗 -->
      <FormModal
        v-model:visible="formVisible"
        :loading="formLoading"
        :record="currentRecord"
        :account-options="accountOptions"
        @submit="handleFormSubmit"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.payment-huolian-merchant {
  background-color: var(--background-deep);
}
</style>
