<script lang="ts" setup>
import type { WechatMerchantItem } from '#/api/core/payment';

import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { But<PERSON>, Card, message, Modal } from 'ant-design-vue';

import {
  addWechatMerchant,
  deleteWechatMerchant,
  getWechatMerchantList,
  updateWechatMerchant,
} from '#/api/core/payment';
import { getUserOptionsApi } from '#/api/core/user';
import { getWechatPublicList } from '#/api/core/wechat';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import FormModal from './components/form-modal.vue';
import { columns, createSearchItems } from './config';

const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getWechatMerchantList,
  defaultParams: {},
});

const userOptions = ref<{ label: string; value: number }[]>([]);
const publicOptions = ref<{ label: string; value: number }[]>([]);

// 表单弹窗相关
const formVisible = ref(false);
const formLoading = ref(false);

const currentRecord = ref<Partial<WechatMerchantItem>>();

// 微信公众号列表
// const publicList = ref<WechatPublicItem[]>([]);
const searchItems = ref(createSearchItems(publicOptions.value));

// 加载用户选项
const loadUserOptions = async () => {
  try {
    const res = await getUserOptionsApi();
    if (res.code === 1) {
      userOptions.value = res.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error('获取用户选项失败:', error);
  }
};

// 加载微信公众号选项
const loadPublicOptions = async () => {
  try {
    const res = await getWechatPublicList({
      page: 1,
      pageSize: 100,
    });
    if (res.code === 1) {
      publicOptions.value = res.data.rows.map((item) => ({
        label: item.name,
        value: item.id,
      }));
      // 更新搜索配置
      searchItems.value = createSearchItems(publicOptions.value);
    }
  } catch (error) {
    console.error('获取微信公众号列表失败:', error);
  }
};

// 打开新增弹窗
const handleAdd = () => {
  currentRecord.value = undefined;
  formVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = (record: WechatMerchantItem) => {
  currentRecord.value = { ...record };
  formVisible.value = true;
};

// 删除
const handleDelete = (record: WechatMerchantItem) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该商户吗？',
    async onOk() {
      try {
        if (!record.id) {
          message.error('商户ID不存在');
          return;
        }
        const res = await deleteWechatMerchant(record.id);
        if (res.code === 1 || res.code === 0) {
          message[res.code === 1 ? 'success' : 'error'](res.msg);
          if (res.code === 1) {
            getList();
          }
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理表单提交
const handleFormSubmit = async (values: Omit<WechatMerchantItem, 'id'>) => {
  formLoading.value = true;
  try {
    const api = currentRecord.value?.id
      ? updateWechatMerchant({ ...values, id: currentRecord.value.id })
      : addWechatMerchant(values);
    const res = await api;
    if (res.code === 1) {
      message.success(currentRecord.value?.id ? '编辑成功' : '新增成功');
      formVisible.value = false;
      getList();
    } else {
      message.error(
        res.msg || (currentRecord.value?.id ? '编辑失败' : '新增失败'),
      );
    }
  } catch (error) {
    console.error(currentRecord.value?.id ? '编辑失败:' : '新增失败:', error);
    message.error(currentRecord.value?.id ? '编辑失败' : '新增失败');
  } finally {
    formLoading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadUserOptions();
  loadPublicOptions();
  getList();
});
</script>

<template>
  <div class="payment-wechat-merchant p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        :custom-buttons="[
          {
            icon: MdiPlus,
            text: '新增',
            type: 'primary',
            onClick: handleAdd,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      >
        <template #extra>
          <Button type="primary" @click="handleAdd">新增</Button>
        </template>
      </SearchToolbar>

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <!-- 表单弹窗 -->
      <FormModal
        v-model:visible="formVisible"
        :loading="formLoading"
        :record="currentRecord"
        :user-options="userOptions"
        :public-options="publicOptions"
        @submit="handleFormSubmit"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.payment-wechat-merchant {
  background-color: var(--background-deep);
}
</style>
