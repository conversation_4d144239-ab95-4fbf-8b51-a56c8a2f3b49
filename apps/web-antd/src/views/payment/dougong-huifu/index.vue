<script lang="ts" setup>
import type { DougongHuifuItem } from '#/api/core/payment';

import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  addDougong<PERSON>uifu,
  deleteDougong<PERSON><PERSON>fu,
  getDougong<PERSON>uifuList,
  updateDougongHuifu,
} from '#/api/core/payment';
import { getUserOptionsApi } from '#/api/core/user';
import { getWechatPublicList } from '#/api/core/wechat';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import FormModal from './components/form-modal.vue';
import { advancedItems, basicItems, columns } from './config';

const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getDougongHuifuList,
  defaultParams: {},
});

// 表单弹窗相关
const formVisible = ref(false);
const formLoading = ref(false);
const currentRecord = ref<Partial<DougongHuifuItem>>();
const accountOptions = ref<{ label: string; value: number }[]>([]);
const publicOptions = ref<{ label: string; value: number }[]>([]);

// 加载用户选项
const loadUserOptions = async () => {
  try {
    const res = await getUserOptionsApi();
    if (res.code === 1) {
      const options = res.data.map((item) => ({
        label: item.name,
        value: item.id,
      }));
      // 保存账号选项用于表单
      accountOptions.value = options;
    }
  } catch (error) {
    console.error('获取用户选项失败:', error);
    message.error('获取用户选项失败');
  }
};

// 加载公众号选项
const loadPublicOptions = async () => {
  try {
    const res = await getWechatPublicList({
      page: 1,
      pageSize: 999, // 获取所有公众号
    });
    if (res.code === 1) {
      const options = res.data.rows.map((item) => ({
        label: item.name,
        value: item.id,
      }));

      // 保存公众号选项用于表单
      publicOptions.value = options;

      // 更新搜索栏的公众号选项
      const publicGroup = advancedItems.find(
        (group) => group.title === '支付方式',
      );
      if (publicGroup) {
        const publicItem = publicGroup.items.find(
          (item) => item.field === 'wxPublicIds',
        );
        if (publicItem) {
          publicItem.props!.options = options;
        }
      }
    }
  } catch (error) {
    console.error('获取公众号选项失败:', error);
    message.error('获取公众号选项失败');
  }
};

// 打开新增弹窗
const handleAdd = () => {
  currentRecord.value = undefined;
  formVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = (record: DougongHuifuItem) => {
  currentRecord.value = { ...record };
  formVisible.value = true;
};

// 处理删除
const handleDelete = (record: DougongHuifuItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除"${record.name}"吗？`,
    async onOk() {
      try {
        const res = await deleteDougongHuifu(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理表单提交
const handleFormSubmit = async (values: Omit<DougongHuifuItem, 'id'>) => {
  formLoading.value = true;
  try {
    // 将 accountId 作为 userId 传入
    const submitData = {
      ...values,
      userId: values.accountId,
    };

    const api = currentRecord.value?.id
      ? updateDougongHuifu({ ...submitData, id: currentRecord.value.id })
      : addDougongHuifu(submitData);
    const res = await api;
    if (res.code === 1) {
      message.success(currentRecord.value?.id ? '编辑成功' : '新增成功');
      formVisible.value = false;
      getList();
    } else {
      message.error(
        res.msg || (currentRecord.value?.id ? '编辑失败' : '新增失败'),
      );
    }
  } catch (error) {
    console.error(currentRecord.value?.id ? '编辑失败:' : '新增失败:', error);
    message.error(currentRecord.value?.id ? '编辑失败' : '新增失败');
  } finally {
    formLoading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(async () => {
  await Promise.all([loadUserOptions(), loadPublicOptions()]);
  getList();
});
</script>

<template>
  <div class="payment-dougong-huifu p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicItems"
        :advanced-items="advancedItems"
        :loading="loading"
        :custom-buttons="[
          {
            icon: MdiPlus,
            text: '新增',
            type: 'primary',
            onClick: handleAdd,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <!-- 表单弹窗 -->
      <FormModal
        v-model:visible="formVisible"
        :loading="formLoading"
        :record="currentRecord"
        :account-options="accountOptions"
        :public-options="publicOptions"
        @submit="handleFormSubmit"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.payment-dougong-huifu {
  background-color: var(--background-deep);
}
</style>
