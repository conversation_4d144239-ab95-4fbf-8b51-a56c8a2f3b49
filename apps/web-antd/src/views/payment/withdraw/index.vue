<script lang="ts" setup>
import type { WithdrawConfigItem } from '#/api/core/payment';

import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Card, message, Modal } from 'ant-design-vue';

import {
  addWithdrawConfig,
  deleteWithdrawConfig,
  editWithdrawConfig,
  getWithdrawConfigList,
} from '#/api/core/payment';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import FormModal from './components/form-modal.vue';
import { basicItems, columns } from './config';

const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getWithdrawConfigList,
  defaultParams: {},
});

const formVisible = ref(false);
const formLoading = ref(false);
const currentRecord = ref<Partial<WithdrawConfigItem>>();

// 处理新增
const handleAdd = () => {
  currentRecord.value = undefined;
  formVisible.value = true;
};

// 处理编辑
const handleEdit = (record: WithdrawConfigItem) => {
  currentRecord.value = { ...record };
  formVisible.value = true;
};

// 处理删除
const handleDelete = (record: WithdrawConfigItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除配置"${record.withdrawName}"吗？`,
    onOk: async () => {
      try {
        const res = await deleteWithdrawConfig(record.id!);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理表单提交
const handleFormSubmit = async (
  values: Omit<WithdrawConfigItem, 'creationTime' | 'id' | 'updateTime'>,
) => {
  formLoading.value = true;
  try {
    const res = currentRecord.value?.id
      ? await editWithdrawConfig({ ...values, id: currentRecord.value.id })
      : await addWithdrawConfig(values);

    if (res.code === 1) {
      message.success(currentRecord.value?.id ? '修改成功' : '添加成功');
      formVisible.value = false;
      getList();
    } else {
      message.error(
        res.msg || (currentRecord.value?.id ? '修改失败' : '添加失败'),
      );
    }
  } catch (error) {
    console.error(currentRecord.value?.id ? '修改失败:' : '添加失败:', error);
    message.error(currentRecord.value?.id ? '修改失败' : '添加失败');
  } finally {
    formLoading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="payment-withdraw p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicItems"
        :loading="loading"
        :custom-buttons="[
          {
            icon: MdiPlus,
            text: '新增',
            type: 'primary',
            onClick: handleAdd,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            // type: 'primary',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <!-- 表单弹窗 -->
      <FormModal
        v-model:visible="formVisible"
        :loading="formLoading"
        :record="currentRecord"
        @submit="handleFormSubmit"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.payment-withdraw {
  background-color: var(--background-deep);
}
</style>
