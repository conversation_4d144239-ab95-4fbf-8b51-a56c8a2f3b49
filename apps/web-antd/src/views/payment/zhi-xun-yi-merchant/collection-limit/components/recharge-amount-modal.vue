<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue';

import type { SearchItemConfig } from '#/components/SearchToolbar/types';

import { h, ref, watch } from 'vue';

import { Input, InputNumber, message, Modal } from 'ant-design-vue';

import {
  addRechargeAmount,
  deleteRechargeAmount,
  getRechargeAmountListByLimitId,
  updateRechargeAmount,
} from '#/api/core/payment';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';

const props = defineProps<{
  limitId: number;
  visible: boolean;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
}>();

// 状态
const loading = ref(false);
const tableData = ref<any[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);
const searchParams = ref<{ slogan?: string }>({});

// 基础搜索项配置
const basicSearchItems: SearchItemConfig[] = [
  {
    field: 'slogan',
    label: '购买标语',
    component: 'Input',
    props: {
      placeholder: '请输入购买标语',
      allowClear: true,
    },
  },
];

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '收款面额',
    dataIndex: 'collectionBalance',
    key: 'collectionBalance',
    align: 'center',
    width: 120,
  },
  {
    title: '赠送面额',
    dataIndex: 'giftBalance',
    key: 'giftBalance',
    align: 'center',
    width: 120,
  },
  {
    title: '购买价格',
    dataIndex: 'price',
    key: 'price',
    align: 'center',
    width: 120,
    customRender: ({ text }) => `¥${text}`,
  },
  {
    title: '购买标语',
    dataIndex: 'slogan',
    key: 'slogan',
    align: 'center',
    width: 200,
  },
];

// 表单数据
const formData = ref({
  collectionBalance: 0,
  giftBalance: 0,
  price: 0,
  slogan: '',
});

// 加载数据
const loadData = async () => {
  if (!props.limitId) return;

  loading.value = true;
  try {
    const params: any = {
      page: page.value,
      pageSize: pageSize.value,
      limitId: props.limitId,
    };

    // 添加搜索参数
    if (searchParams.value.slogan) {
      params.slogan = searchParams.value.slogan;
    }

    const res = await getRechargeAmountListByLimitId(params);

    if (res.code === 1 && res.data) {
      tableData.value = res.data.rows;
      total.value = res.data.total;
    }
  } catch (error) {
    console.error('获取列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理表格变化
const handleTableChange = (pagination: any) => {
  page.value = pagination.current;
  pageSize.value = pagination.pageSize;
  loadData();
};

// 处理搜索
const handleSearch = () => {
  page.value = 1;
  loadData();
};

// 处理重置
const handleReset = () => {
  page.value = 1;
  searchParams.value = {};
  loadData();
};

// 监听 limitId 变化
watch(
  () => props.limitId,
  () => {
    if (props.visible && props.limitId) {
      loadData();
    }
  },
);

// 监听弹窗显示
watch(
  () => props.visible,
  (val) => {
    if (val && props.limitId) {
      loadData();
    }
  },
);

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 处理新增
const handleAdd = () => {
  Modal.confirm({
    title: '新增收款面额',
    content: () =>
      h('div', { class: 'space-y-4' }, [
        h(InputNumber, {
          placeholder: '请输入收款面额',
          class: 'w-full',
          min: 0,
          onChange: (value) => {
            formData.value.collectionBalance = value as number;
          },
        }),
        h(InputNumber, {
          placeholder: '请输入赠送面额',
          class: 'w-full',
          min: 0,
          onChange: (value) => {
            formData.value.giftBalance = value as number;
          },
        }),
        h(InputNumber, {
          placeholder: '请输入购买价格',
          class: 'w-full',
          min: 0,
          onChange: (value) => {
            formData.value.price = value as number;
          },
        }),
        h(Input, {
          placeholder: '请输入购买标语',
          class: 'w-full',
          onChange: (e) => {
            formData.value.slogan = e.target.value || '';
          },
        }),
      ]),
    width: 400,
    centered: true,
    onOk: async () => {
      if (
        !formData.value.collectionBalance ||
        !formData.value.giftBalance ||
        !formData.value.price ||
        !formData.value.slogan
      ) {
        message.warning('请填写完整信息');
        throw new Error('请填写完整信息');
      }
      try {
        await addRechargeAmount({
          ...formData.value,
          limitId: props.limitId,
        });
        message.success('新增成功');
        loadData();
      } catch (error) {
        console.error('新增失败:', error);
        message.error('新增失败');
      }
    },
    afterClose: () => {
      formData.value = {
        collectionBalance: 0,
        giftBalance: 0,
        price: 0,
        slogan: '',
      };
    },
  });
};

// 处理编辑
const handleEdit = (record: any) => {
  Modal.confirm({
    title: '编辑收款面额',
    content: () =>
      h('div', { class: 'space-y-4' }, [
        h(InputNumber, {
          placeholder: '请输入收款面额',
          class: 'w-full',
          min: 0,
          value: record.collectionBalance,
          onChange: (value) => {
            formData.value.collectionBalance = value as number;
          },
        }),
        h(InputNumber, {
          placeholder: '请输入赠送面额',
          class: 'w-full',
          min: 0,
          value: record.giftBalance,
          onChange: (value) => {
            formData.value.giftBalance = value as number;
          },
        }),
        h(InputNumber, {
          placeholder: '请输入购买价格',
          class: 'w-full',
          min: 0,
          value: record.price,
          onChange: (value) => {
            formData.value.price = value as number;
          },
        }),
        h(Input, {
          placeholder: '请输入购买标语',
          class: 'w-full',
          value: record.slogan,
          onChange: (e) => {
            formData.value.slogan = e.target.value || '';
          },
        }),
      ]),
    width: 400,
    centered: true,
    onOk: async () => {
      if (
        !formData.value.collectionBalance ||
        !formData.value.giftBalance ||
        !formData.value.price ||
        !formData.value.slogan
      ) {
        message.warning('请填写完整信息');
        throw new Error('请填写完整信息');
      }
      try {
        await updateRechargeAmount({
          ...formData.value,
          id: record.id,
        });
        message.success('编辑成功');
        loadData();
      } catch (error) {
        console.error('编辑失败:', error);
        message.error('编辑失败');
      }
    },
    afterClose: () => {
      formData.value = {
        collectionBalance: 0,
        giftBalance: 0,
        price: 0,
        slogan: '',
      };
    },
  });
};

// 处理删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该收款面额吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteRechargeAmount(record.id);
        message.success('删除成功');
        loadData();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 自定义按钮配置
const customButtons = [
  {
    text: '新增',
    type: 'primary' as const,
    onClick: handleAdd,
  },
];
</script>

<template>
  <Modal
    title="收款面额管理"
    :visible="visible"
    width="800px"
    @cancel="handleCancel"
    :footer="null"
  >
    <div class="space-y-4">
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="basicSearchItems"
        :loading="loading"
        :custom-buttons="customButtons"
        @reset="handleReset"
        @search="handleSearch"
      />

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'edit',
            text: '编辑',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            onClick: handleDelete,
          },
        ]"
        :pagination="{
          total,
          current: page,
          pageSize,
          showSizeChanger: true,
          showQuickJumper: true,
        }"
        @change="handleTableChange"
      />
    </div>
  </Modal>
</template>

<style lang="less" scoped>
:deep(.ant-modal-body) {
  padding: 16px;
}
</style>
