<script lang="ts" setup>
import type { ZhiXunYiMerchantItem } from '#/api/core/payment';

import { h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Input, message, Modal } from 'ant-design-vue';

import {
  addCollectionLimit,
  deleteCollectionLimit,
  getCollectionLimitList,
  updateCollectionLimit,
} from '#/api/core/payment';
import { getUserOptionsApi } from '#/api/core/user';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import RechargeAmountModal from './components/recharge-amount-modal.vue';
import { basicItems, columns } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getCollectionLimitList,
  defaultParams: {},
});

// 额度名称
const name = ref('');
// 账号选项
const accountOptions = ref<{ label: string; value: number }[]>([]);

// 收款面额弹窗
const rechargeAmountVisible = ref(false);
const currentLimitId = ref<number>();

// 获取账号选项
const loadUserOptions = async () => {
  try {
    const res = await getUserOptionsApi();
    if (res.code === 1) {
      accountOptions.value = res.data.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error('获取账号选项失败:', error);
  }
};

// 处理新增
const handleAdd = () => {
  Modal.confirm({
    title: '新增',
    content: () =>
      h(Input, {
        placeholder: '请输入模板名称',
        value: name.value,
        onChange: (e: Event) => {
          const target = e.target as HTMLInputElement;
          name.value = target.value;
        },
      }),
    width: 400,
    centered: true,
    onOk: async () => {
      if (!name.value) {
        message.warning('请输入模板名称');
        throw new Error('请输入模板名称');
      }
      try {
        await addCollectionLimit(name.value);
        message.success('新增成功');
        getList();
      } catch (error) {
        console.error('新增失败:', error);
        message.error('新增失败');
      }
    },
    afterClose: () => {
      name.value = '';
    },
  });
};

// 处理编辑
const handleEdit = (record: ZhiXunYiMerchantItem) => {
  Modal.confirm({
    title: '编辑',
    content: () =>
      h(Input, {
        placeholder: '请输入模板名称',
        value: record.name,
        onChange: (e: Event) => {
          const target = e.target as HTMLInputElement;
          name.value = target.value;
        },
      }),
    width: 400,
    centered: true,
    onOk: async () => {
      await updateCollectionLimit({ id: record.id, name: name.value });
      message.success('编辑成功');
      getList();
    },
    afterClose: () => {
      name.value = '';
    },
  });
};

// 处理绑定收款面额
const handleBindRechargeAmount = (record: ZhiXunYiMerchantItem) => {
  currentLimitId.value = record.id;
  rechargeAmountVisible.value = true;
};

// 处理删除
const handleDelete = (record: ZhiXunYiMerchantItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除额度模版"${record.name}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const res = await deleteCollectionLimit(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 组件挂载时加载数据
onMounted(async () => {
  await loadUserOptions();
  getList();
});
</script>

<template>
  <div class="payment-xiaomage-merchant">
    <!-- <Card> -->
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="basicItems"
      :loading="loading"
      :custom-buttons="[
        {
          icon: MdiPlus,
          text: '新增',
          type: 'primary',
          onClick: handleAdd,
        },
      ]"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :show-action="true"
      :action-buttons="[
        // 绑定收款面额
        {
          key: 'bindRechargeAmount',
          text: '绑定收款面额',
          onClick: handleBindRechargeAmount,
        },
        {
          key: 'edit',
          text: '编辑',
          onClick: handleEdit,
        },
        {
          key: 'delete',
          text: '删除',
          onClick: handleDelete,
        },
      ]"
      :pagination="pagination"
      @change="handleTableChange"
    />
    <!-- </Card> -->

    <!-- 收款面额弹窗 -->
    <RechargeAmountModal
      v-model:visible="rechargeAmountVisible"
      :limit-id="currentLimitId"
    />
  </div>
</template>

<style lang="less" scoped>
.payment-xiaomage-merchant {
  background-color: var(--background-deep);
}
</style>
