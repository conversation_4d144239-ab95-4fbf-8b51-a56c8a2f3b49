<script lang="ts" setup>
import type { RadioChangeEvent } from 'ant-design-vue';

import type { ZhiXunYiMerchantItem } from '#/api/core/payment';

import { h, onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Button, Input, message, Modal, Radio } from 'ant-design-vue';

import {
  addZhiXunYiMerchant,
  deleteZhiXunYiMerchant,
  getZhiXunYiMerchantList,
  updateZhiXunYiMerchant,
  updateZhiXunYiMerchantCollectionLimit,
} from '#/api/core/payment';
import { getUserOptionsApi } from '#/api/core/user';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import { AgentBalanceBuyModal } from './components';
import FormModal from './components/form-modal.vue';
import { basicItems, columns } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getZhiXunYiMerchantList,
  defaultParams: {},
});

const formVisible = ref(false);
const formLoading = ref(false);
const currentRecord = ref<Partial<ZhiXunYiMerchantItem>>();
const rechargeAmount = ref<number>(0); // 充值额度
const rechargeType = ref<1 | 2>(1); // 充值类型
// 账号选项
const accountOptions = ref<{ label: string; value: number }[]>([]);

// 代理余额购买模态框显示状态
const modalVisible = ref(false);
// 代理ID
const agentId = ref<number | undefined>(undefined);

// 获取账号选项
const loadUserOptions = async () => {
  try {
    const res = await getUserOptionsApi();
    if (res.code === 1) {
      accountOptions.value = res.data.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error('获取账号选项失败:', error);
  }
};

// 处理新增
const handleAdd = () => {
  currentRecord.value = undefined;
  formVisible.value = true;
};

// 处理编辑
const handleEdit = (record: ZhiXunYiMerchantItem) => {
  currentRecord.value = { ...record };
  formVisible.value = true;
};

// 处理额度充值
const handleRecharge = (record: ZhiXunYiMerchantItem) => {
  rechargeAmount.value = 0;
  rechargeType.value = 1;

  const modalInstance = Modal.confirm({
    title: '确认充值',
    width: 400,
    content: () =>
      h('div', { class: 'space-y-4 py-4' }, [
        h('div', { class: 'flex flex-col space-y-2' }, [
          h('span', { class: 'text-gray-600 mb-1' }, '充值额度：'),
          h(Input, {
            placeholder: '请输入充值额度',
            value: rechargeAmount.value,
            onChange: (e: Event) => {
              const target = e.target as HTMLInputElement;
              rechargeAmount.value = Number(target.value);
              modalInstance.update();
            },
          }),
        ]),
        h('div', { class: 'flex flex-col space-y-2' }, [
          h('span', { class: 'text-gray-600 mb-1' }, '充值类型：'),
          h(
            Radio.Group,
            {
              value: rechargeType.value,
              onChange: (e: RadioChangeEvent) => {
                rechargeType.value = e.target.value;
                modalInstance.update();
              },
            },
            () => [
              h(Radio, { value: 1 }, { default: () => '增加' }),
              h(Radio, { value: 2 }, { default: () => '减少' }),
            ],
          ),
        ]),
      ]),
    centered: true,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      if (!rechargeAmount.value) {
        message.warning('请输入充值额度');
        throw new Error('请输入充值额度');
      }
      try {
        const res = await updateZhiXunYiMerchantCollectionLimit({
          id: record.id,
          collectionLimit: rechargeAmount.value,
          type: rechargeType.value,
        });
        if (res.code === 1) {
          message.success('充值成功');
          getList();
          rechargeAmount.value = 0;
        } else {
          message.error(res.msg || '充值失败');
        }
      } catch (error) {
        console.error('充值失败:', error);
        message.error('充值失败');
      }
    },
  });
};

// 充值弹窗显示状态
const rechargeModalVisible = ref(false);
// 当前选中的商户
const currentMerchant = ref<null | ZhiXunYiMerchantItem>(null);

// 处理在线充值
const handleOnlineRecharge = (record: ZhiXunYiMerchantItem) => {
  // modalVisible.value = true;
  // currentMerchant.value = record;
  agentId.value = record.id;
  modalVisible.value = true;
};

// 充值成功回调
const handleRechargeSuccess = () => {
  // 刷新列表
  getList();
};

// 处理删除
const handleDelete = (record: ZhiXunYiMerchantItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除商户"${record.name}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const res = await deleteZhiXunYiMerchant(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理表单提交
const handleFormSubmit = async (values: Omit<ZhiXunYiMerchantItem, 'id'>) => {
  formLoading.value = true;
  try {
    const api = currentRecord.value?.id
      ? updateZhiXunYiMerchant({ ...values, id: currentRecord.value.id })
      : addZhiXunYiMerchant(values);

    const res = await api;
    if (res.code === 1) {
      message.success(currentRecord.value?.id ? '编辑成功' : '新增成功');
      formVisible.value = false;
      getList();
    } else {
      message.error(
        res.msg || (currentRecord.value?.id ? '编辑失败' : '新增失败'),
      );
    }
  } catch (error) {
    console.error(currentRecord.value?.id ? '编辑失败:' : '新增失败:', error);
    message.error(currentRecord.value?.id ? '编辑失败' : '新增失败');
  } finally {
    formLoading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(async () => {
  await loadUserOptions();
  getList();
});
</script>

<template>
  <div class="payment-xiaomage-merchant">
    <!-- <Card> -->
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="basicItems"
      :loading="loading"
      :custom-buttons="[
        {
          icon: MdiPlus,
          text: '新增',
          type: 'primary',
          onClick: handleAdd,
        },
      ]"
      @reset="handleReset"
      @search="handleSearch"
    >
      <template #extra>
        <Button type="primary" @click="handleAdd">新增</Button>
      </template>
    </SearchToolbar>

    <!-- 表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :show-action="true"
      :action-buttons="[
        // 在线充值
        {
          key: 'onlineRecharge',
          text: '在线充值',
          onClick: handleOnlineRecharge,
        },
        // 额度充值
        {
          key: 'recharge',
          text: '额度充值',
          // type: 'primary',
          onClick: handleRecharge,
        },
        {
          key: 'edit',
          text: '编辑',
          // type: 'primary',
          onClick: handleEdit,
        },
        {
          key: 'delete',
          text: '删除',
          // type: 'danger',
          onClick: handleDelete,
        },
      ]"
      :pagination="pagination"
      @change="handleTableChange"
    />

    <!-- 表单弹窗 -->
    <FormModal
      v-model:visible="formVisible"
      :loading="formLoading"
      :record="currentRecord"
      :account-options="accountOptions"
      @submit="handleFormSubmit"
    />

    <AgentBalanceBuyModal
      v-model:visible="modalVisible"
      :agent-id="agentId"
      @success="handleRechargeSuccess"
    />

    <!-- 充值弹窗 -->
    <!-- <RechargeModal
      v-model:visible="rechargeModalVisible"
      :merchant-id="currentMerchant?.id"
      :merchant-name="currentMerchant?.merchantName"
      @success="handleRechargeSuccess"
    /> -->
    <!-- </Card> -->
  </div>
</template>

<style lang="less" scoped>
.payment-xiaomage-merchant {
  background-color: var(--background-deep);
}
</style>
