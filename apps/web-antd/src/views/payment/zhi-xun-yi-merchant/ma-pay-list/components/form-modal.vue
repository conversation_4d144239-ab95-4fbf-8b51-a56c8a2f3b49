<script lang="ts" setup>
import type { ZhiXunYiMerchantItem } from '#/api/core/payment';

import { computed, onMounted, ref, watch } from 'vue';

import { MdiClose, MdiImagePlus } from '@vben/icons';

import { Form, Image, Input, Modal, Select } from 'ant-design-vue';

import { getCollectionLimitOptions } from '#/api/core/payment';
import { useImagePreviewModal } from '#/hooks/web/useImagePreviewModal';
import { getFileUrl } from '#/utils/file';

const props = defineProps<{
  accountOptions: { label: string; value: number }[];
  loading?: boolean;
  record?: Partial<ZhiXunYiMerchantItem>;
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'submit', values: Omit<ZhiXunYiMerchantItem, 'id'>): void;
}>();

const formRef = ref();
const initialValues = ref<Partial<ZhiXunYiMerchantItem>>({
  payName: '',
  userId: undefined,
  status: 2,
  wxStatus: 2,
  wxUrl: '',
  zfbStatus: 2,
  zfbUrl: '',
  callbackPath: '',
});

const title = computed(() => (props.record?.id ? '编辑商户' : '新增商户'));

// 监听record变化，更新表单数据
watch(
  () => props.record,
  (val) => {
    initialValues.value = val
      ? { ...val }
      : {
          payName: '',
          userId: undefined,
          status: 2,
          wxStatus: 2,
          wxUrl: '',
          zfbStatus: 2,
          zfbUrl: '',
          callbackPath: '',
        };
  },
  { immediate: true },
);

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
  formRef.value?.resetFields();
};

// 处理确定
const handleOk = async () => {
  try {
    const values = await formRef.value?.validateFields();
    emit('submit', values);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 图片预览相关
const { showModal: showImageModal, ImagePreviewModal } = useImagePreviewModal();

// 处理选择图片
const handleSelectImage = (field: 'wxUrl' | 'zfbUrl') => {
  showImageModal({
    title: field === 'wxUrl' ? '选择微信支付二维码' : '选择支付宝支付二维码',
    onSelect: (image) => {
      initialValues.value[field] = image.src;
    },
  });
};

// 清除图片
const handleClearImage = (field: 'wxUrl' | 'zfbUrl') => {
  initialValues.value[field] = '';
};

const getPreviewUrl = (url: string) => {
  return getFileUrl(url);
};

// 收款额度模板选项
const collectionLimitOptions = ref<{ label: string; value: number }[]>([]);

// 获取收款额度模板选项
const loadCollectionLimitOptions = async () => {
  try {
    const res = await getCollectionLimitOptions();
    if (res.code === 1) {
      collectionLimitOptions.value = res.data.map((item: any) => ({
        label: item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error('获取收款额度模板选项失败:', error);
  }
};

// 计算微信二维码的规则
const wxUrlRules = computed(() => {
  if (initialValues.value.wxStatus === 1) {
    return [{ required: true, message: '请选择微信二维码' }];
  }
  return [];
});

// 计算支付宝二维码的规则
const zfbUrlRules = computed(() => {
  if (initialValues.value.zfbStatus === 1) {
    return [{ required: true, message: '请选择支付宝二维码' }];
  }
  return [];
});

// 组件挂载时加载数据
onMounted(() => {
  loadCollectionLimitOptions();
});
</script>

<template>
  <Modal
    :title="title"
    :visible="visible"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    width="700px"
  >
    <Form
      ref="formRef"
      :model="initialValues"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 14 }"
    >
      <div class="grid grid-cols-2 gap-4">
        <!-- 左列 -->
        <div>
          <Form.Item
            label="商户名称"
            name="payName"
            :rules="[{ required: true, message: '请输入商户名称' }]"
          >
            <Input
              v-model:value="initialValues.payName"
              placeholder="请输入商户名称"
            />
          </Form.Item>

          <Form.Item
            label="收款额度模板"
            name="collectionLimitId"
            :rules="[{ required: true, message: '请选择收款额度模板' }]"
          >
            <Select
              v-model:value="initialValues.collectionLimitId"
              placeholder="请选择收款额度模板"
              :options="collectionLimitOptions"
            />
          </Form.Item>

          <Form.Item
            label="支付状态"
            name="status"
            :rules="[{ required: true, message: '请选择支付状态' }]"
          >
            <Select
              v-model:value="initialValues.status"
              placeholder="请选择支付状态"
            >
              <Select.Option :value="1">启用</Select.Option>
              <Select.Option :value="2">关闭</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="微信支付状态"
            name="wxStatus"
            :rules="[{ required: true, message: '请选择微信支付状态' }]"
          >
            <Select
              v-model:value="initialValues.wxStatus"
              placeholder="请选择微信支付状态"
            >
              <Select.Option :value="1">启用</Select.Option>
              <Select.Option :value="2">关闭</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            v-if="initialValues.wxStatus === 1"
            label="微信二维码"
            name="wxUrl"
            :rules="wxUrlRules"
          >
            <div class="relative">
              <div
                class="hover:border-primary relative flex h-32 w-32 cursor-pointer items-center justify-center rounded border p-2"
                @click="handleSelectImage('wxUrl')"
              >
                <div
                  v-if="!initialValues.wxUrl"
                  class="hover:text-primary flex h-full w-full flex-col items-center justify-center text-gray-400"
                >
                  <MdiImagePlus class="text-2xl" />
                  <span class="mt-1 text-xs">上传二维码</span>
                </div>
                <template v-else>
                  <div class="relative h-full w-full overflow-hidden">
                    <div
                      class="absolute inset-0 flex items-center justify-center"
                    >
                      <Image
                        :src="getPreviewUrl(initialValues.wxUrl)"
                        class="h-full w-full object-contain"
                        :preview="false"
                      />
                    </div>
                    <div
                      class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 opacity-0 transition-opacity hover:opacity-100"
                    >
                      <span class="text-sm text-white">更换图片</span>
                    </div>
                  </div>
                </template>
              </div>
              <!-- 清除按钮 -->
              <div
                v-if="initialValues.wxUrl"
                class="hover:text-error absolute -right-2 -top-2 flex h-6 w-6 cursor-pointer items-center justify-center rounded-full bg-white text-gray-500 shadow-md"
                @click="handleClearImage('wxUrl')"
              >
                <MdiClose class="text-lg" />
              </div>
            </div>
          </Form.Item>
        </div>

        <!-- 右列 -->
        <div>
          <Form.Item
            label="归属账号"
            name="userId"
            :rules="[{ required: true, message: '请选择归属账号' }]"
          >
            <Select
              v-model:value="initialValues.userId"
              placeholder="请选择归属账号"
              :options="accountOptions"
            />
          </Form.Item>
          <Form.Item
            label="支付宝状态"
            name="zfbStatus"
            :rules="[{ required: true, message: '请选择支付宝状态' }]"
          >
            <Select
              v-model:value="initialValues.zfbStatus"
              placeholder="请选择支付宝状态"
            >
              <Select.Option :value="1">启用</Select.Option>
              <Select.Option :value="2">关闭</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item label="回调地址" name="callbackPath">
            <Input
              v-model:value="initialValues.callbackPath"
              placeholder="请输入回调地址"
            />
          </Form.Item>

          <Form.Item
            v-if="initialValues.zfbStatus === 1"
            label="支付宝二维码"
            name="zfbUrl"
            :rules="zfbUrlRules"
          >
            <div class="relative">
              <div
                class="hover:border-primary relative flex h-32 w-32 cursor-pointer items-center justify-center rounded border p-2"
                @click="handleSelectImage('zfbUrl')"
              >
                <div
                  v-if="!initialValues.zfbUrl"
                  class="hover:text-primary flex h-full w-full flex-col items-center justify-center text-gray-400"
                >
                  <MdiImagePlus class="text-2xl" />
                  <span class="mt-1 text-xs">上传二维码</span>
                </div>
                <template v-else>
                  <div class="relative h-full w-full overflow-hidden">
                    <div
                      class="absolute inset-0 flex items-center justify-center"
                    >
                      <Image
                        :src="getPreviewUrl(initialValues.zfbUrl)"
                        class="h-full w-full object-contain"
                        :preview="false"
                      />
                    </div>
                    <div
                      class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 opacity-0 transition-opacity hover:opacity-100"
                    >
                      <span class="text-sm text-white">更换图片</span>
                    </div>
                  </div>
                </template>
              </div>
              <!-- 清除按钮 -->
              <div
                v-if="initialValues.zfbUrl"
                class="hover:text-error absolute -right-2 -top-2 flex h-6 w-6 cursor-pointer items-center justify-center rounded-full bg-white text-gray-500 shadow-md"
                @click="handleClearImage('zfbUrl')"
              >
                <MdiClose class="text-lg" />
              </div>
            </div>
          </Form.Item>
        </div>
      </div>
    </Form>
    <ImagePreviewModal />
  </Modal>
</template>
