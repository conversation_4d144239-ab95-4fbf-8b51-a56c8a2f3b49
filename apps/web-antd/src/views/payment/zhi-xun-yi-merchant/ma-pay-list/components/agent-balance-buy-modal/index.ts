import type { App } from 'vue';

import AgentBalanceBuyModal from './agent-balance-buy-modal.vue';

AgentBalanceBuyModal.install = (app: App) => {
  app.component('AgentBalanceBuyModal', AgentBalanceBuyModal);
};

export { AgentBalanceBuyModal };
export default AgentBalanceBuyModal;

export {
  type AgentBalanceBuyModalEmits,
  type AgentBalanceBuyModalProps,
  type AgentBalancePackage,
} from './types';
