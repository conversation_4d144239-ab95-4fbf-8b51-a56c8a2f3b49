<script lang="ts" setup>
import type {
  AgentBalanceBuyModalEmits,
  AgentBalanceBuyModalProps,
  AgentBalancePackage,
} from './types';

import { computed, ref, watch } from 'vue';

import { useUserStore } from '@vben/stores';

import {
  Button,
  Card,
  Divider,
  Empty,
  message,
  Modal,
  QRCode,
  Radio,
  Spin,
} from 'ant-design-vue';

import {
  createAgentBalanceOrderAs,
  getPaymentStatus,
  getPayParameter,
  getRechargeAmountList,
} from '#/api/core/payment';
import { getDomainName } from '#/utils/domain';
import { getFileUrl } from '#/utils/file';

const props = defineProps<AgentBalanceBuyModalProps>();
const emit = defineEmits<AgentBalanceBuyModalEmits>();
const userStore = useUserStore();

// 余额套餐列表
const balancePackages = ref<AgentBalancePackage[]>([]);
const loadingPackages = ref(false);
const selectedPackage = ref<AgentBalancePackage | null>(null);

// 支付状态
const paymentMethods = ref<any[]>([]);
const selectedPayment = ref<any | null>(null);
const loadingPaymentMethods = ref(false);

// 订单和支付相关
const orderInfo = ref<any>(null);
const qrcodeUrl = ref('');
const loading = ref(false);
const qrcodeLoading = ref(false);

// 获取支付类型显示名称
const getPayTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    wxpay: '微信支付',
    alipay: '支付宝支付',
    hfpay: '汇付',
    Mapay: '码支付',
    dogohfpay: '斗拱',
  };
  return typeMap[type] || type;
};

// 获取支付方式描述
const getPaymentDescription = (type: string, payWay: null | string) => {
  if (type === 'Mapay') {
    if (payWay === '1') return '微信扫码支付';
    if (payWay === '2') return '支付宝扫码支付';
  }

  const descMap: Record<string, string> = {
    wxpay: '微信扫码支付',
    alipay: '支付宝扫码支付',
    hfpay: '汇付支付',
    Mapay: '码支付',
    dogohfpay: '斗拱支付',
  };
  return descMap[type] || '';
};

// 按支付类型分组
const paymentGroups = computed(() => {
  const groups: Record<string, any[]> = {};

  paymentMethods.value.forEach((method) => {
    if (method && method.type) {
      if (!groups[method.type]) {
        groups[method.type] = [];
      }

      const group = groups[method.type];
      if (group) {
        group.push(method);
      }
    }
  });

  return Object.entries(groups).map(([type, methods]) => ({
    type,
    typeName: getPayTypeName(type),
    methods,
  }));
});

// 重置支付状态
const resetPaymentState = () => {
  selectedPackage.value = null;
  selectedPayment.value = null;
  orderInfo.value = null;
  qrcodeUrl.value = '';
};

// 获取API预存面额列表
const fetchBalancePackages = async () => {
  try {
    loadingPackages.value = true;
    // 这里需要替换为实际的API调用
    const response = await getRechargeAmountList(props.agentId);
    console.log('response', response);
    if (response && response.code === 1 && response.data) {
      balancePackages.value = Array.isArray(response.data)
        ? response.data
        : [response.data];
    }
  } catch (error) {
    console.error('获取代理余额套餐失败:', error);
    message.error('获取代理余额套餐失败，请稍后重试');
  } finally {
    loadingPackages.value = false;
  }
};

// 获取支付方式列表
const fetchPaymentMethods = async () => {
  try {
    loadingPaymentMethods.value = true;
    // 获取用户ID，如果props中没有，则使用当前登录用户的ID
    const userId = userStore.userInfo?.id;

    // 使用场景7: 代理购买余额
    const response = await getPaymentStatus({
      type: 7, // 代理购买余额
      userId: userId ? userId.toString() : '',
    });

    if (response && response.code === 1) {
      // 处理API返回的支付状态数据 - 可能是数组或单个对象
      const dataArray = Array.isArray(response.data)
        ? response.data
        : [response.data];

      // 添加描述信息
      paymentMethods.value = dataArray.map((item: any) => {
        const description = getPaymentDescription(item.type, item.payWay);
        return {
          ...item,
          key: `${item.type}${item.payWay || ''}${item.payId}`,
          description,
        };
      });
    }
  } catch (error) {
    console.error('获取支付方式失败:', error);
    message.error('获取支付方式失败，请稍后重试');
  } finally {
    loadingPaymentMethods.value = false;
  }
};

// 创建代理余额订单
const createOrder = async () => {
  if (!selectedPackage.value) {
    message.warning('请先选择余额套餐');
    return null;
  }

  try {
    loading.value = true;
    const response = await createAgentBalanceOrderAs(
      selectedPackage.value.id.toString(),
    );

    if (response && response.code === 1 && response.data) {
      // 创建订单成功
      orderInfo.value = Array.isArray(response.data)
        ? response.data[0]
        : response.data;
      return orderInfo.value;
    } else {
      message.error(response?.msg || '创建订单失败');
      return null;
    }
  } catch (error) {
    console.error('创建代理余额订单失败:', error);
    message.error('创建订单失败，请稍后重试');
    return null;
  } finally {
    loading.value = false;
  }
};

// 获取支付参数
const getPaymentParams = async (orderNo: string) => {
  if (!selectedPayment.value) {
    message.warning('请先选择支付方式');
    return null;
  }

  try {
    qrcodeLoading.value = true;
    // 使用getPayParameter方法获取支付参数
    const response = await getPayParameter({
      orderNo,
      orderType: '7', // 7: 代理购买余额
      payType: selectedPayment.value.type,
      payWay: selectedPayment.value.payWay || undefined,
      payId: selectedPayment.value.payId.toString(),
      type: '2', // 扫码支付
      notifyUrl: getDomainName(),
    });

    if (response && response.code === 1 && response.data) {
      return response.data;
    } else {
      message.error(response?.msg || '获取支付参数失败');
      return null;
    }
  } catch (error) {
    console.error('获取支付参数失败:', error);
    message.error('获取支付参数失败，请稍后重试');
    return null;
  } finally {
    qrcodeLoading.value = false;
  }
};

// 处理支付参数，显示二维码
const handlePaymentParams = (params: any) => {
  if (!params) return;

  // 根据支付类型和支付方式显示不同的支付界面
  switch (selectedPayment.value?.type || '') {
    case 'alipay': {
      // 显示二维码
      if (params) {
        qrcodeUrl.value = typeof params === 'string' ? params : '';
        message.success(`请使用${selectedPayment.value?.name}扫码支付`);
      } else {
        message.error('支付二维码生成失败');
        qrcodeUrl.value = '';
      }
      break;
    }
    case 'dogohfpay':
    case 'hfpay': {
      // // 显示支付链接或跳转页面
      // 显示二维码
      if (params) {
        qrcodeUrl.value = typeof params === 'string' ? params : '';
        message.success(`请使用${selectedPayment.value?.name}扫码支付`);
      } else {
        message.error('支付二维码生成失败');
        qrcodeUrl.value = '';
      }
      break;
      // if (params.payUrl) {
      //   window.open(params.payUrl, '_blank');
      //   qrcodeUrl.value = '';
      // } else {
      //   message.error('支付链接生成失败');
      //   qrcodeUrl.value = '';
      // }
      // break;
    }
    case 'Mapay': {
      // 显示二维码
      if (params.data?.url) {
        qrcodeUrl.value = getFileUrl(params.data.url);
        message.success(`请使用${selectedPayment.value?.name}扫码支付`);
      } else {
        message.error('支付二维码生成失败');
        qrcodeUrl.value = '';
      }
      break;
    }
    case 'wxpay': {
      // 显示二维码
      if (params) {
        qrcodeUrl.value = typeof params === 'string' ? params : '';
        message.success(`请使用${selectedPayment.value?.name}扫码支付`);
      } else {
        message.error('支付二维码生成失败');
        qrcodeUrl.value = '';
      }
      break;
    }
    default: {
      message.warning('暂不支持该支付方式');
      qrcodeUrl.value = '';
      break;
    }
  }
};

// 监听弹窗可见性变化
const handleVisibleChange = async (visible: boolean) => {
  if (visible) {
    resetPaymentState();
    await Promise.all([fetchBalancePackages(), fetchPaymentMethods()]);
  }
};

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  resetPaymentState();
};

// 选择套餐
const handleSelectPackage = (pkg: AgentBalancePackage) => {
  selectedPackage.value = pkg;
};

// 确认购买
const handleConfirmPurchase = async () => {
  if (!selectedPackage.value) {
    message.warning('请先选择余额套餐');
    return;
  }

  if (!selectedPayment.value) {
    message.warning('请先选择支付方式');
    return;
  }

  try {
    loading.value = true;

    // 1. 创建订单
    const order = await createOrder();
    if (!order || !order.systemOrdernumber) {
      return;
    }

    // 2. 获取支付参数
    const payParams = await getPaymentParams(order.systemOrdernumber);
    if (payParams) {
      handlePaymentParams(payParams);
    }
  } catch (error) {
    console.error('代理余额充值失败:', error);
    message.error('充值失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 支付完成
const handlePaymentComplete = () => {
  if (selectedPackage.value) {
    emit('success', selectedPackage.value);
    handleClose();
  }
};

// 计算总价值
const getTotalValue = (pkg: AgentBalancePackage) => {
  const balance = Number(pkg.balance || 0);
  const giftBalance = Number(pkg.giftBalance || 0);
  return (balance + giftBalance).toFixed(2);
};

// 监听visible变化
watch(() => props.visible, handleVisibleChange);
</script>

<template>
  <Modal
    :visible="visible"
    title="代理余额充值"
    @cancel="handleClose"
    :footer="null"
    width="800px"
    :body-style="{ padding: '24px' }"
  >
    <Spin
      :spinning="
        loadingPackages || loadingPaymentMethods || loading || qrcodeLoading
      "
    >
      <div class="balance-modal-container">
        <!-- 套餐选择 -->
        <div v-if="!qrcodeUrl" class="packages-section">
          <h3 class="section-title">选择代理余额套餐</h3>

          <!-- 套餐列表为空时显示空状态 -->
          <Empty
            v-if="balancePackages.length === 0 && !loadingPackages"
            description="暂无可用代理余额套餐"
          />

          <!-- 套餐卡片 -->
          <div class="package-cards">
            <Card
              v-for="pkg in balancePackages"
              :key="pkg.id"
              class="package-card"
              :class="{ selected: selectedPackage?.id === pkg.id }"
              hoverable
              :bordered="true"
              @click="handleSelectPackage(pkg)"
            >
              <div class="card-content">
                <div class="pkg-price">¥{{ pkg.price }}</div>
                <div class="pkg-balance">余额: ¥{{ pkg.balance }}</div>
                <div v-if="pkg.giftBalance > 0" class="pkg-gift">
                  赠送余额: ¥{{ pkg.giftBalance }}
                </div>
                <div v-if="pkg.giftBalance > 0" class="pkg-total-value">
                  总价值: ¥{{ getTotalValue(pkg) }}
                </div>
                <div v-if="pkg.description" class="pkg-description">
                  {{ pkg.description }}
                </div>
              </div>
              <div v-if="selectedPackage?.id === pkg.id" class="selected-badge">
                <span class="selected-icon">✓</span>
              </div>
            </Card>
          </div>
        </div>

        <!-- 支付方式选择 -->
        <div v-if="selectedPackage && !qrcodeUrl" class="payment-section">
          <Divider />
          <h3 class="section-title">选择支付方式</h3>

          <Empty
            v-if="paymentGroups.length === 0 && !loadingPaymentMethods"
            description="暂无可用支付方式"
          />

          <div v-else class="payment-groups">
            <div
              v-for="group in paymentGroups"
              :key="group.type"
              class="payment-group"
            >
              <div class="payment-group-title">{{ group.typeName }}</div>
              <div class="payment-methods">
                <Radio.Group
                  v-model:value="selectedPayment"
                  button-style="solid"
                >
                  <Radio.Button
                    v-for="method in group.methods"
                    :key="method.key"
                    :value="method"
                    class="payment-method-item"
                  >
                    {{ method.name }}
                  </Radio.Button>
                </Radio.Group>
              </div>
            </div>
          </div>
        </div>

        <!-- 支付二维码 -->
        <div v-if="qrcodeUrl" class="qrcode-section">
          <div class="qrcode-header">
            <h3 class="section-title">请扫码支付</h3>
            <div class="order-info">
              <div class="order-item">
                <span class="label">订单号:</span>
                <span class="value">{{ orderInfo?.systemOrdernumber }}</span>
              </div>
              <div class="order-item">
                <span class="label">金额:</span>
                <span class="value price">¥{{ selectedPackage?.price }}</span>
              </div>
            </div>
          </div>

          <div class="qrcode-container">
            <Spin :spinning="qrcodeLoading" tip="加载中...">
              <div class="qrcode">
                <QRCode :value="qrcodeUrl" :size="200" />
              </div>
            </Spin>
            <div class="qrcode-tips">
              <p>请使用{{ selectedPayment?.name }}扫码支付</p>
              <p>支付完成后请点击"支付完成"按钮</p>
            </div>
          </div>

          <div class="qrcode-actions">
            <Button @click="qrcodeUrl = ''">返回</Button>
            <Button type="primary" @click="handlePaymentComplete">
              支付完成
            </Button>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div v-if="!qrcodeUrl" class="action-section">
          <Button @click="handleClose">取消</Button>
          <Button
            type="primary"
            :disabled="!selectedPackage || !selectedPayment"
            :loading="loading"
            @click="handleConfirmPurchase"
          >
            立即充值
          </Button>
        </div>
      </div>
    </Spin>
  </Modal>
</template>

<style lang="scss" scoped>
.balance-modal-container {
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
  }

  .packages-section {
    margin-bottom: 24px;

    .package-cards {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;

      .package-card {
        position: relative;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-4px);
        }

        &.selected {
          background-color: var(--primary-1);
          border-color: var(--primary-color);
        }

        .card-content {
          text-align: center;

          .pkg-price {
            margin-bottom: 16px;
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
          }

          .pkg-balance {
            margin-bottom: 8px;
            font-size: 14px;
          }

          .pkg-gift {
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--success-color);
          }

          .pkg-total-value {
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
          }

          .pkg-description {
            font-size: 12px;
            color: var(--text-color-secondary);
          }
        }

        .selected-badge {
          position: absolute;
          top: -10px;
          right: -10px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          background-color: var(--primary-color);
          border-radius: 50%;
          box-shadow: 0 2px 6px rgb(0 0 0 / 15%);

          .selected-icon {
            font-size: 14px;
            color: #fff;
          }
        }
      }
    }
  }

  .payment-section {
    margin-bottom: 24px;

    .payment-groups {
      .payment-group {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .payment-group-title {
          font-weight: 500;
          margin-bottom: 12px;
          color: var(--text-color-secondary);
        }

        .payment-methods {
          margin-top: 8px;

          .payment-method-item {
            margin-right: 12px;
            margin-bottom: 12px;
          }
        }
      }
    }
  }

  .qrcode-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;

    .qrcode-header {
      width: 100%;
      text-align: center;
      margin-bottom: 24px;

      .section-title {
        margin-bottom: 16px;
      }

      .order-info {
        background-color: var(--background-light);
        padding: 12px 16px;
        border-radius: 8px;
        display: inline-block;

        .order-item {
          display: flex;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            width: 60px;
            color: var(--text-color-secondary);
          }

          .value {
            font-weight: 500;

            &.price {
              color: var(--primary-color);
              font-size: 18px;
            }
          }
        }
      }
    }

    .qrcode-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 24px;

      .qrcode {
        padding: 16px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .qrcode-tips {
        margin-top: 16px;
        text-align: center;
        color: var(--text-color-secondary);
        font-size: 14px;

        p {
          margin-bottom: 8px;
        }
      }
    }

    .qrcode-actions {
      display: flex;
      gap: 12px;
    }
  }

  .action-section {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
  }
}
</style>
