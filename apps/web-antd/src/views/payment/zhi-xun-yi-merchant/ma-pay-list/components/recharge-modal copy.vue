<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

import { But<PERSON>, Card, Divider, message, Modal, Spin } from 'ant-design-vue';

import { getRechargeAmountList } from '#/api/core/payment';
import { usePayment } from '#/hooks/usePayment';

interface RechargeAmountItem {
  collectionBalance: number; // 收款面额
  giftBalance: number; // 赠送面额
  id: number;
  limitId: number;
  price: number; // 购买价格
  slogan: string; // 购买标语说明
}

const props = defineProps<{
  merchantId: number;
  merchantName: string;
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}>();

// 充值额度列表
const rechargeList = ref<RechargeAmountItem[]>([]);
// 选中的充值额度
const selectedAmount = ref<null | RechargeAmountItem>(null);

// 使用支付hook
const {
  loading,
  selectedPayWay,
  checkPaymentStatus,
  createOrderAndPay,
  resetPaymentState,
  renderPaymentSelection,
} = usePayment();

// 获取支付选择布局渲染函数
const { renderPaymentSelector, renderQrCode } = renderPaymentSelection();

// 获取充值额度列表
const fetchRechargeList = async () => {
  try {
    loading.value = true;
    const res = await getRechargeAmountList(props.merchantId);
    if (res.code === 1 && res.data) {
      // 处理返回的数据结构
      if (res.data.rows && Array.isArray(res.data.rows)) {
        rechargeList.value = res.data.rows as RechargeAmountItem[];
      } else if (Array.isArray(res.data)) {
        rechargeList.value = res.data as RechargeAmountItem[];
      }
    }
  } catch (error) {
    console.error('获取充值额度列表失败:', error);
    message.error('获取充值额度列表失败');
  } finally {
    loading.value = false;
  }
};

// 选择充值额度
const handleSelectAmount = (amount: RechargeAmountItem) => {
  selectedAmount.value = amount;
};

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  selectedAmount.value = null;
  resetPaymentState();
};

// 确认充值
const handleConfirm = async () => {
  if (!selectedAmount.value) {
    message.warning('请选择充值额度');
    return;
  }

  const success = await createOrderAndPay(
    selectedAmount.value,
    props.merchantId,
  );

  if (success) {
    emit('success');
  }
};

// 添加样式到文档
const addStyles = () => {
  const styleId = 'payment-custom-styles';

  // 如果已经存在样式元素，先移除
  const existingStyle = document.querySelector(`#${styleId}`);
  if (existingStyle) {
    existingStyle.remove();
  }

  // 创建样式元素
  const styleElement = document.createElement('style');
  styleElement.id = styleId;

  // 将样式添加到头部
  document.head.append(styleElement);
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (val) => {
    if (val) {
      fetchRechargeList();
      // 收款额度充值场景使用类型14
      checkPaymentStatus(14); // 使用类型14表示收款额度充值
    }
  },
);

onMounted(() => {
  if (props.visible) {
    fetchRechargeList();
    // 收款额度充值场景使用类型14
    checkPaymentStatus(14); // 使用类型14表示收款额度充值
  }
  // 添加样式
  addStyles();
});
</script>

<template>
  <Modal
    :visible="visible"
    title="在线充值"
    width="800px"
    :footer="null"
    @cancel="handleClose"
  >
    <Spin :spinning="loading">
      <div class="recharge-modal">
        <div class="merchant-info">
          <h3>{{ merchantName }}</h3>
        </div>

        <div class="amount-list">
          <Card
            v-for="item in rechargeList"
            :key="item.id"
            class="amount-card"
            :class="{ selected: selectedAmount?.id === item.id }"
            @click="handleSelectAmount(item)"
          >
            <div class="amount-content">
              <div class="amount-title">
                <span class="price">¥{{ item.price }}</span>
                <span class="collection">
                  收款: ¥{{ item.collectionBalance }}
                </span>
              </div>
              <div class="amount-desc">
                <span class="gift">赠送: ¥{{ item.giftBalance }}</span>
                <span class="slogan">{{ item.slogan }}</span>
              </div>
            </div>
          </Card>
        </div>

        <div v-if="selectedAmount">
          <Divider />
          <component :is="renderPaymentSelector" />
        </div>

        <component :is="renderQrCode" />

        <div class="action-bar">
          <Button @click="handleClose">取消</Button>
          <Button
            type="primary"
            @click="handleConfirm"
            :disabled="!selectedAmount || !selectedPayWay"
            :loading="loading"
          >
            确认充值
          </Button>
        </div>
      </div>
    </Spin>
  </Modal>
</template>

<style lang="less" scoped>
.recharge-modal {
  min-height: 400px;
  display: flex;
  flex-direction: column;

  .merchant-info {
    margin-bottom: 24px;
    text-align: center;

    h3 {
      margin: 0;
      font-size: 18px;
      color: var(--text-color);
    }
  }

  .amount-list {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
    overflow-y: auto;
    max-height: 400px;
    padding-right: 8px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--border-color);
      border-radius: 3px;
    }

    .amount-card {
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.selected {
        border-color: var(--primary-color);
        background-color: var(--primary-1);
      }

      .amount-content {
        text-align: center;

        .amount-title {
          margin-bottom: 8px;

          .price {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            margin-right: 8px;
          }

          .collection {
            font-size: 14px;
            color: var(--text-color-secondary);
          }
        }

        .amount-desc {
          font-size: 12px;
          color: var(--text-color-secondary);

          .gift {
            color: var(--success-color);
            margin-right: 8px;
          }

          .slogan {
            color: var(--warning-color);
          }
        }
      }
    }
  }

  .action-bar {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 24px;
  }
}
</style>
