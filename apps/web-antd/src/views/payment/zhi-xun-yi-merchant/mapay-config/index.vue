<script lang="ts" setup>
import { h, onMounted, ref } from 'vue';

import { message, Modal, Tag } from 'ant-design-vue';

import { deleteMapayConfig, getMapayConfigList } from '#/api/core/payment';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import EditModal from './components/edit-modal.vue';

// 搜索项配置
const basicItems = [
  {
    field: 'configName',
    label: '配置名称',
    component: 'Input' as const,
    props: {
      placeholder: '请输入配置名称',
    },
  },
  {
    field: 'merchantId',
    label: '商户ID',
    component: 'Input' as const,
    props: {
      placeholder: '请输入商户ID',
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select' as const,
    props: {
      placeholder: '请选择状态',
      options: [
        { label: '启用', value: '1' },
        { label: '关闭', value: '2' },
      ],
    },
  },
];

// 表格列配置
const columns = [
  {
    title: '配置名称',
    dataIndex: 'configName',
    key: 'configName',
    align: 'center' as const,
    width: 150,
  },
  {
    title: '商户ID',
    dataIndex: 'merchantId',
    key: 'merchantId',
    align: 'center' as const,
    width: 120,
  },
  {
    title: 'API地址',
    dataIndex: 'apiUrl',
    key: 'apiUrl',
    align: 'center' as const,
    width: 200,
  },
  {
    title: '对接账号',
    dataIndex: 'appId',
    key: 'appId',
    align: 'center' as const,
    width: 120,
  },
  {
    title: '手续费',
    dataIndex: 'commission',
    key: 'commission',
    align: 'center' as const,
    width: 100,
    customRender: ({ text }: { text: number }) => `${text}%`,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    align: 'center' as const,
    width: 100,
    customRender: ({ text }: { text: number }) => {
      return h(
        Tag,
        {
          color: text === 1 ? 'success' : 'warning',
        },
        text === 1 ? '启用' : '关闭',
      );
    },
  },
  {
    title: '微信收款码',
    dataIndex: 'wxSm',
    key: 'wxSm',
    align: 'center' as const,
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return h(
        Tag,
        {
          color: text === 1 ? 'success' : 'warning',
        },
        text === 1 ? '开启' : '关闭',
      );
    },
  },
  {
    title: '支付宝收款码',
    dataIndex: 'zfbSm',
    key: 'zfbSm',
    align: 'center' as const,
    width: 120,
    customRender: ({ text }: { text: number }) => {
      return h(
        Tag,
        {
          color: text === 1 ? 'success' : 'warning',
        },
        text === 1 ? '开启' : '关闭',
      );
    },
  },
  {
    title: '创建时间',
    dataIndex: 'creationTime',
    key: 'creationTime',
    align: 'center' as const,
    width: 180,
  },
];

// 使用表格hook
const {
  getList,
  loading,
  tableData,
  pagination,
  searchParams,
  handleSearch,
  handleReset,
  handleTableChange,
} = useTable({
  api: getMapayConfigList,
  columns,
  searchParams: {
    configName: '',
    merchantId: '',
    status: '',
  },
});

// 编辑模态框显示状态
const editModalVisible = ref(false);
const editData = ref<any>(null);

// 处理添加
const handleAdd = () => {
  editData.value = null;
  editModalVisible.value = true;
};

// 处理编辑
const handleEdit = (record: any) => {
  editData.value = record;
  editModalVisible.value = true;
};

// 处理删除
const handleDelete = (record: any) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该配置吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteMapayConfig(record.id);
        message.success('删除成功');
        handleSearch();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理编辑成功
const handleEditSuccess = () => {
  handleSearch();
};

// 自定义按钮配置
const customButtons = [
  {
    text: '添加配置',
    type: 'primary' as const,
    onClick: handleAdd,
  },
];

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="mapay-config">
    <!-- 搜索工具栏 -->
    <SearchToolbar
      v-model="searchParams"
      :basic-items="basicItems"
      :loading="loading"
      :custom-buttons="customButtons"
      @reset="handleReset"
      @search="handleSearch"
    />

    <!-- 表格 -->
    <BasicTable
      :loading="loading"
      :columns="columns"
      :data-source="tableData"
      :show-action="true"
      :action-buttons="[
        {
          key: 'edit',
          text: '编辑',
          onClick: handleEdit,
        },
        {
          key: 'delete',
          text: '删除',
          onClick: handleDelete,
        },
      ]"
      :pagination="pagination"
      @change="handleTableChange"
    />

    <!-- 编辑模态框 -->
    <EditModal
      v-model:visible="editModalVisible"
      :data="editData"
      @success="handleEditSuccess"
    />
  </div>
</template>

<style lang="less" scoped>
.mapay-config {
  .space-y-4 {
    > div {
      margin-bottom: 16px;
    }
  }
}
</style>
