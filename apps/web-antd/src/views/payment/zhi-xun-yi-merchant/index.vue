<script lang="ts" setup>
import { ref } from 'vue';

import { Card, Tabs } from 'ant-design-vue';

// 导入各个模板组件
import CollectionLimit from './collection-limit/index.vue';
import MaPayList from './ma-pay-list/index.vue';
import MapayConfig from './mapay-config/index.vue';

const activeKey = ref('maPayList');

// Tab 配置
const tabs = [
  {
    key: 'maPayList',
    tab: '商户列表',
    component: MaPayList,
  },
  {
    key: 'collectionLimit',
    tab: '收款模版',
    component: CollectionLimit,
  },
  {
    key: 'mapayConfig',
    tab: '码支付商户',
    component: MapayConfig,
  },
];
</script>

<template>
  <div class="p-2">
    <Card>
      <Tabs v-model:active-key="activeKey">
        <Tabs.TabPane v-for="item in tabs" :key="item.key" :tab="item.tab">
          <component :is="item.component" />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<style lang="less" scoped>
.automation-manage {
  position: relative;
  min-height: 100vh;
}
</style>
