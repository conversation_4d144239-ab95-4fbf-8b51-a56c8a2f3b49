<script lang="ts" setup>
import type { DougongAggregateItem } from '#/api/core/payment';

import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { Button, Card, message, Modal } from 'ant-design-vue';

import {
  addDougongAggregate,
  deleteDougongAggregate,
  getDougongAggregateList,
  updateDougongAggregate,
} from '#/api/core/payment';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import FormModal from './components/form-modal.vue';
import { columns, searchItems } from './config';

// 状态定义
const {
  searchParams,
  loading,
  tableData,
  pagination,
  getList,
  handleTableChange,
  handleSearch,
  handleReset,
} = useTable({
  api: getDougongAggregateList,
  defaultParams: {},
});

// 表单弹窗相关
const formVisible = ref(false);
const formLoading = ref(false);
const currentRecord = ref<Partial<DougongAggregateItem>>();

// 打开新增弹窗
const handleAdd = () => {
  currentRecord.value = undefined;
  formVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = (record: DougongAggregateItem) => {
  currentRecord.value = { ...record };
  formVisible.value = true;
};

// 处理删除
const handleDelete = (record: DougongAggregateItem) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除"${record.name}"吗？`,
    async onOk() {
      try {
        const res = await deleteDougongAggregate(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理表单提交
const handleFormSubmit = async (values: Omit<DougongAggregateItem, 'id'>) => {
  formLoading.value = true;
  try {
    const api = currentRecord.value?.id
      ? updateDougongAggregate({ ...values, id: currentRecord.value.id })
      : addDougongAggregate(values);
    const res = await api;
    if (res.code === 1) {
      message.success(currentRecord.value?.id ? '编辑成功' : '新增成功');
      formVisible.value = false;
      getList();
    } else {
      message.error(
        res.msg || (currentRecord.value?.id ? '编辑失败' : '新增失败'),
      );
    }
  } catch (error) {
    console.error(currentRecord.value?.id ? '编辑失败:' : '新增失败:', error);
    message.error(currentRecord.value?.id ? '编辑失败' : '新增失败');
  } finally {
    formLoading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="payment-dougong-aggregate p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        :custom-buttons="[
          {
            icon: MdiPlus,
            text: '新增',
            type: 'primary',
            onClick: handleAdd,
          },
          // 申请商户资质
          // {
          //   icon: MdiPlus,
          //   text: '申请资质',
          //   onClick: handleAdd,
          // },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      >
        <template #extra>
          <Button type="primary" @click="handleAdd">新增</Button>
        </template>
      </SearchToolbar>

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'view',
            text: '查看',
            type: 'link',
            onClick: handleView,
          },
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <!-- 表单弹窗 -->
      <FormModal
        v-model:visible="formVisible"
        :loading="formLoading"
        :record="currentRecord"
        @submit="handleFormSubmit"
      />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.payment-dougong-aggregate {
  background-color: var(--background-deep);
}
</style>
