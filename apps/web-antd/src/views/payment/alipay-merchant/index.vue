<script lang="ts" setup>
import type { AlipayMerchantItem } from '#/api/core/payment';

import { onMounted, ref } from 'vue';

import { MdiPlus } from '@vben/icons';

import { But<PERSON>, Card, message, Modal } from 'ant-design-vue';

import {
  addAlipayMerchant,
  deleteAlipayMerchant,
  getAlipayMerchantList,
  updateAlipayMerchant,
} from '#/api/core/payment';
import SearchToolbar from '#/components/SearchToolbar/index.vue';
// import BasicTable from '#/components/BasicTable/index.vue';
import BasicTable from '#/hooks/useAnsheng/components/BasicTable.vue';
import { useTable } from '#/hooks/useTable';

import FormModal from './components/form-modal.vue';
import ViewModal from './components/view-modal.vue';
import { columns, searchItems } from './config';

// 使用 useTable hook
const {
  loading,
  searchParams,
  tableData,
  pagination,
  handleSearch,
  handleReset,
  handleTableChange,
  getList,
} = useTable<AlipayMerchantItem, AlipayMerchantListParams>({
  api: getAlipayMerchantList,
  defaultParams: {},
});

// 表单弹窗相关
const formVisible = ref(false);
const formLoading = ref(false);
const currentRecord = ref<Partial<AlipayMerchantItem>>();

// 查看弹窗相关
const viewVisible = ref(false);
const currentViewRecord = ref<Partial<AlipayMerchantItem>>();

// 打开新增弹窗
const handleAdd = () => {
  currentRecord.value = undefined;
  formVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = (record: AlipayMerchantItem) => {
  currentRecord.value = { ...record };
  formVisible.value = true;
};

// 打开查看弹窗
const handleView = (record: AlipayMerchantItem) => {
  currentViewRecord.value = record;
  viewVisible.value = true;
};

// 删除
const handleDelete = (record: AlipayMerchantItem) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该商户吗？',
    async onOk() {
      try {
        if (!record.id) {
          message.error('商户ID不存在');
          return;
        }
        const res = await deleteAlipayMerchant(record.id);
        if (res.code === 1) {
          message.success('删除成功');
          getList();
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 处理表单提交
const handleFormSubmit = async (values: Omit<AlipayMerchantItem, 'id'>) => {
  formLoading.value = true;
  try {
    const api = currentRecord.value?.id
      ? updateAlipayMerchant({ ...values, id: currentRecord.value.id })
      : addAlipayMerchant(values);
    const res = await api;
    if (res.code === 1) {
      message.success(currentRecord.value?.id ? '编辑成功' : '新增成功');
      formVisible.value = false;
      getList();
    } else {
      message.error(
        res.msg || (currentRecord.value?.id ? '编辑失败' : '新增失败'),
      );
    }
  } catch (error) {
    console.error(currentRecord.value?.id ? '编辑失败:' : '新增失败:', error);
    message.error(currentRecord.value?.id ? '编辑失败' : '新增失败');
  } finally {
    formLoading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  getList();
});
</script>

<template>
  <div class="payment-alipay-merchant p-2">
    <Card>
      <!-- 搜索工具栏 -->
      <SearchToolbar
        v-model="searchParams"
        :basic-items="searchItems"
        :loading="loading"
        :custom-buttons="[
          {
            icon: MdiPlus,
            text: '新增',
            type: 'primary',
            onClick: handleAdd,
          },
        ]"
        @reset="handleReset"
        @search="handleSearch"
      >
        <template #extra>
          <Button type="primary" @click="handleAdd">新增</Button>
        </template>
      </SearchToolbar>

      <!-- 表格 -->
      <BasicTable
        :loading="loading"
        :columns="columns"
        :data-source="tableData"
        :show-action="true"
        :action-buttons="[
          {
            key: 'view',
            text: '查看',
            type: 'link',
            onClick: handleView,
          },
          {
            key: 'edit',
            text: '编辑',
            type: 'link',
            onClick: handleEdit,
          },
          {
            key: 'delete',
            text: '删除',
            type: 'link',
            danger: true,
            onClick: handleDelete,
          },
        ]"
        :pagination="pagination"
        @change="handleTableChange"
      />

      <!-- 表单弹窗 -->
      <FormModal
        v-model:visible="formVisible"
        :loading="formLoading"
        :record="currentRecord"
        @submit="handleFormSubmit"
      />

      <!-- 查看弹窗 -->
      <ViewModal v-model:visible="viewVisible" :record="currentViewRecord" />
    </Card>
  </div>
</template>

<style lang="less" scoped>
.payment-alipay-merchant {
  background-color: var(--background-deep);
}
</style>
