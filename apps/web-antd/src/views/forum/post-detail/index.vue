<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import {
  MdiComment,
  MdiEye,
  MdiPin,
  MdiShare,
  MdiStar,
  MdiThumbUp,
} from '@vben/icons';

import {
  Avatar,
  Button,
  Card,
  Divider,
  Input,
  message,
  Skeleton,
  Tag,
  Tooltip,
} from 'ant-design-vue';

const route = useRoute();
const router = useRouter();

// 加载状态
const loading = ref(true);

// 帖子数据
const post = ref({
  id: 1,
  title: '欢迎加入IoT开发者社区！',
  content:
    '这是一个专为IoT开发者打造的交流平台，在这里我们分享经验、讨论技术、解决问题。这是一个专为IoT开发者打造的交流平台，在这里我们分享经验、讨论技术、解决问题。\n\n物联网技术正在快速发展，作为开发者，我们需要不断学习和交流。希望这个平台能够帮助大家解决问题，分享经验，共同进步。\n\n欢迎加入我们的社区！',
  author: {
    id: 1,
    name: '社区管理员',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=1',
    level: 10,
    isAdmin: true,
  },
  tags: ['公告', '置顶'],
  createTime: '2024-03-20 10:00:00',
  replyCount: 5,
  viewCount: 100,
  likeCount: 20,
  isTop: true,
  isEssence: true,
});

// 评论列表
const comments = ref([
  {
    id: 1,
    content: '感谢分享，这个社区很棒！期待与大家一起交流学习。',
    author: {
      id: 2,
      name: '技术专家',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=2',
      level: 8,
    },
    createTime: '2024-03-20 11:00:00',
    likeCount: 5,
  },
  {
    id: 2,
    content:
      '物联网技术发展非常迅速，希望可以和大家一起探讨最新的技术动态和应用案例。',
    author: {
      id: 3,
      name: '安全研究员',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=3',
      level: 7,
    },
    createTime: '2024-03-20 12:00:00',
    likeCount: 3,
  },
]);

// 评论内容
const commentContent = ref('');

// 是否已点赞
const hasLiked = ref(false);

// 时间格式化
const formatTime = (timeStr: string) => {
  const date = new Date(timeStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 加载帖子详情
const loadPostDetail = async () => {
  try {
    loading.value = true;
    // 获取帖子ID
    // const postId = route.params.id;
    // TODO: 调用获取帖子详情API
    // const res = await getPostDetail(postId);
    // post.value = res.data;

    // 模拟加载延迟
    setTimeout(() => {
      loading.value = false;
    }, 800);
  } catch (error) {
    console.error('加载帖子详情失败:', error);
    message.error('加载失败，请重试');
  }
};

// 加载评论列表
const loadComments = async () => {
  try {
    // 获取帖子ID
    // const postId = route.params.id;
    // TODO: 调用获取评论列表API
    // const res = await getComments(postId);
    // comments.value = res.data;
  } catch (error) {
    console.error('加载评论失败:', error);
    message.error('加载失败，请重试');
  }
};

// 返回列表
const goBack = () => {
  router.push('/forum');
};

// 发表评论
const handleComment = async () => {
  if (!commentContent.value.trim()) {
    message.warning('请输入评论内容');
    return;
  }

  try {
    // TODO: 调用发表评论API
    // await createComment({
    //   postId: post.value.id,
    //   content: commentContent.value,
    // });

    // 模拟添加评论
    comments.value.unshift({
      id: Math.floor(Math.random() * 1000) + 100,
      content: commentContent.value,
      author: {
        id: 10,
        name: '游客用户',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=10',
        level: 2,
      },
      createTime: new Date().toISOString(),
      likeCount: 0,
    });

    message.success('评论成功');
    commentContent.value = '';
  } catch (error) {
    console.error('评论失败:', error);
    message.error('评论失败，请重试');
  }
};

// 点赞帖子
const handleLikePost = async () => {
  try {
    // TODO: 调用点赞API
    // await likePost(post.value.id);

    if (hasLiked.value) {
      post.value.likeCount--;
      hasLiked.value = false;
      message.success('已取消点赞');
    } else {
      post.value.likeCount++;
      hasLiked.value = true;
      message.success('点赞成功');
    }
  } catch (error) {
    console.error('点赞失败:', error);
    message.error('点赞失败，请重试');
  }
};

// 点赞评论
const handleLikeComment = async (commentId: number) => {
  try {
    // TODO: 调用点赞评论API
    // await likeComment(commentId);
    const comment = comments.value.find((c) => c.id === commentId);
    if (comment) {
      comment.likeCount++;
    }
    message.success('点赞成功');
  } catch (error) {
    console.error('点赞失败:', error);
    message.error('点赞失败，请重试');
  }
};

// 分享帖子
const handleShare = () => {
  navigator.clipboard
    .writeText(window.location.href)
    .then(() => {
      message.success('链接已复制到剪贴板');
    })
    .catch(() => {
      message.error('复制失败');
    });
};

onMounted(() => {
  loadPostDetail();
  loadComments();
});
</script>

<template>
  <div class="post-detail-container">
    <div class="container mx-auto px-5">
      <!-- 返回按钮 -->
      <div class="back-nav">
        <Button type="link" @click="goBack"> 返回论坛首页 </Button>
      </div>

      <!-- 帖子内容 -->
      <Skeleton active :loading="loading">
        <Card class="post-content-card" :bordered="false">
          <div class="post-header">
            <div class="title-section">
              <div class="post-tags">
                <Tag v-if="post.isTop" class="tag-top">
                  <template #icon><MdiPin /></template>
                  置顶
                </Tag>
                <Tag v-if="post.isEssence" class="tag-essence">
                  <template #icon><MdiStar /></template>
                  精华
                </Tag>
                <Tag v-for="tag in post.tags" :key="tag" class="tag-normal">
                  {{ tag }}
                </Tag>
              </div>
              <h1 class="post-title">{{ post.title }}</h1>
              <div class="post-meta">
                <div class="post-author">
                  <Avatar :src="post.author.avatar" :size="40" />
                  <div class="author-info">
                    <div class="author-name">
                      {{ post.author.name }}
                      <span v-if="post.author.isAdmin" class="admin-tag">
                        管理员
                      </span>
                    </div>
                    <div class="author-meta">
                      <span class="level-tag">Lv.{{ post.author.level }}</span>
                      <span class="dot-divider"></span>
                      <span class="post-time">
                        {{ formatTime(post.createTime) }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="post-stats">
                  <Tooltip title="浏览次数">
                    <div class="stat-item">
                      <MdiEye />
                      <span>{{ post.viewCount }}</span>
                    </div>
                  </Tooltip>
                  <Tooltip title="评论数">
                    <div class="stat-item">
                      <MdiComment />
                      <span>{{ post.replyCount }}</span>
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>
          </div>

          <Divider />

          <div class="post-body">
            <div class="post-content">
              {{ post.content }}
            </div>

            <div class="post-actions">
              <Button
                class="action-btn like-btn"
                :class="{ active: hasLiked }"
                @click="handleLikePost"
              >
                <MdiThumbUp />
                <span>
                  {{ hasLiked ? '已点赞' : '点赞' }} {{ post.likeCount }}
                </span>
              </Button>
              <Button class="action-btn" @click="handleShare">
                <MdiShare />
                <span>分享</span>
              </Button>
            </div>
          </div>
        </Card>

        <!-- 评论区 -->
        <Card class="comment-card" :bordered="false">
          <template #title>
            <div class="card-title">
              <span>全部评论</span>
              <span class="comment-count">{{ comments.length }}</span>
            </div>
          </template>

          <!-- 评论输入框 -->
          <div class="comment-editor">
            <div class="comment-input-header">
              <Avatar
                size="large"
                src="https://api.dicebear.com/7.x/avataaars/svg?seed=10"
              />
              <div class="editor-placeholder">添加你的评论...</div>
            </div>
            <Input.TextArea
              v-model:value="commentContent"
              placeholder="输入评论内容..."
              :auto-size="{ minRows: 3, maxRows: 6 }"
              :maxlength="500"
              show-count
            />
            <div class="editor-footer">
              <Button type="primary" class="submit-btn" @click="handleComment">
                发表评论
              </Button>
            </div>
          </div>

          <!-- 评论列表 -->
          <div class="comment-list">
            <div
              v-for="comment in comments"
              :key="comment.id"
              class="comment-item"
            >
              <div class="comment-author">
                <Avatar :src="comment.author.avatar" :size="40" />
              </div>
              <div class="comment-main">
                <div class="comment-header">
                  <div class="author-info">
                    <span class="author-name">{{ comment.author.name }}</span>
                    <span class="level-tag small">
                      Lv.{{ comment.author.level }}
                    </span>
                  </div>
                  <div class="comment-time">
                    {{ formatTime(comment.createTime) }}
                  </div>
                </div>
                <div class="comment-content">{{ comment.content }}</div>
                <div class="comment-actions">
                  <Button
                    type="text"
                    class="action-link"
                    @click="handleLikeComment(comment.id)"
                  >
                    <MdiThumbUp class="action-icon" />
                    <span>点赞 {{ comment.likeCount }}</span>
                  </Button>
                  <Button type="text" class="action-link">
                    <MdiComment class="action-icon" />
                    <span>回复</span>
                  </Button>
                </div>
              </div>
            </div>

            <div v-if="comments.length === 0" class="empty-comments">
              暂无评论，快来抢沙发吧！
            </div>
          </div>
        </Card>
      </Skeleton>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.post-detail-container {
  min-height: 100vh;
  background-color: #f9fafb;
  padding: 20px 0 40px;

  .container {
    margin: 0 auto;
  }

  .back-nav {
    margin-bottom: 16px;

    button {
      color: #4f6ef7;
      padding: 0;
      height: auto;

      &:hover {
        color: #3b5de7;
      }
    }
  }

  .post-content-card,
  .comment-card {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    margin-bottom: 24px;
  }

  .post-header {
    .title-section {
      .post-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 16px;

        .tag-top {
          background: #fff2f0;
          border: none;
          color: #ff4d4f;
        }

        .tag-essence {
          background: #fffbe6;
          border: none;
          color: #faad14;
        }

        .tag-normal {
          background: #f0f5ff;
          border: none;
          color: #2f54eb;
        }
      }

      .post-title {
        font-size: 24px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 20px;
        line-height: 1.4;
      }

      .post-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .post-author {
          display: flex;
          align-items: center;
          gap: 12px;

          .author-info {
            .author-name {
              font-weight: 500;
              font-size: 16px;
              color: #1e293b;
              margin-bottom: 4px;
              display: flex;
              align-items: center;
              gap: 8px;

              .admin-tag {
                background: #4f6ef7;
                color: white;
                padding: 1px 6px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: normal;
              }
            }

            .author-meta {
              font-size: 12px;
              color: #64748b;
              display: flex;
              align-items: center;

              .level-tag {
                background: rgba(79, 110, 247, 0.08);
                color: #4f6ef7;
                padding: 1px 6px;
                border-radius: 4px;
                font-weight: 500;
              }

              .dot-divider {
                display: inline-block;
                width: 3px;
                height: 3px;
                border-radius: 50%;
                background: #cbd5e1;
                margin: 0 8px;
              }
            }
          }
        }

        .post-stats {
          display: flex;
          align-items: center;
          gap: 16px;

          .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #64748b;
            font-size: 14px;
          }
        }
      }
    }
  }

  .post-body {
    .post-content {
      font-size: 16px;
      line-height: 1.8;
      color: #334155;
      margin-bottom: 40px;
      white-space: pre-line;
    }

    .post-actions {
      display: flex;
      gap: 16px;
      justify-content: center;

      .action-btn {
        height: 40px;
        padding: 0 24px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 14px;
        transition: all 0.25s ease;
        border: 1px solid #e2e8f0;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        &.like-btn {
          background: #f8fafc;

          &.active {
            background: #4f6ef7;
            color: white;
            border-color: #4f6ef7;
          }
        }
      }
    }
  }

  .comment-card {
    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
      display: flex;
      align-items: center;
      gap: 8px;

      .comment-count {
        color: #64748b;
        font-size: 14px;
        font-weight: normal;
      }
    }

    .comment-editor {
      margin-bottom: 32px;

      .comment-input-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;

        .editor-placeholder {
          color: #94a3b8;
          font-size: 14px;
        }
      }

      .editor-footer {
        margin-top: 16px;
        display: flex;
        justify-content: flex-end;

        .submit-btn {
          height: 36px;
          border-radius: 18px;
          background: linear-gradient(135deg, #4f6ef7, #3b5de7);
          border: none;
          padding: 0 24px;
          box-shadow: 0 4px 12px rgba(59, 93, 231, 0.2);

          &:hover {
            background: linear-gradient(135deg, #5a78f8, #4668e8);
            transform: translateY(-1px);
            box-shadow: 0 6px 14px rgba(59, 93, 231, 0.25);
          }
        }
      }
    }

    .comment-list {
      .comment-item {
        display: flex;
        gap: 16px;
        padding: 20px 0;
        border-bottom: 1px solid #f1f5f9;

        &:last-child {
          border-bottom: none;
        }

        .comment-main {
          flex: 1;
          min-width: 0;

          .comment-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;

            .author-info {
              display: flex;
              align-items: center;
              gap: 8px;

              .author-name {
                font-weight: 500;
                color: #1e293b;
              }

              .level-tag.small {
                font-size: 10px;
                padding: 0 4px;
              }
            }

            .comment-time {
              font-size: 12px;
              color: #94a3b8;
            }
          }

          .comment-content {
            font-size: 15px;
            line-height: 1.6;
            color: #334155;
            margin-bottom: 12px;
            word-break: break-word;
          }

          .comment-actions {
            display: flex;
            gap: 20px;

            .action-link {
              display: flex;
              align-items: center;
              gap: 6px;
              color: #64748b;
              padding: 4px;
              font-size: 13px;

              .action-icon {
                font-size: 14px;
              }

              &:hover {
                color: #4f6ef7;
              }
            }
          }
        }
      }

      .empty-comments {
        text-align: center;
        padding: 32px 0;
        color: #94a3b8;
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 640px) {
  .post-detail-container {
    padding: 16px 0 30px;

    .container {
      padding: 0 16px;
    }

    .post-header {
      .title-section {
        .post-title {
          font-size: 20px;
        }

        .post-meta {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;

          .post-stats {
            align-self: flex-end;
          }
        }
      }
    }

    .post-body {
      .post-content {
        font-size: 15px;
      }

      .post-actions {
        .action-btn {
          flex: 1;
          padding: 0 12px;
          height: 36px;
        }
      }
    }

    .comment-list {
      .comment-item {
        .comment-main {
          .comment-header {
            flex-direction: column;
            gap: 4px;
          }
        }
      }
    }
  }
}
</style>
