<script lang="ts" setup>
import { h, ref } from 'vue';
import { useRouter } from 'vue-router';

import { MdiFire, MdiMagnify, MdiPlus, MdiTrendingUp } from '@vben/icons';

import {
  Avatar,
  Button,
  Card,
  Col,
  Empty,
  Input,
  Row,
  Skeleton,
  Tabs,
} from 'ant-design-vue';

import CompactPostList from '../components/compact-post-list/index.vue';
import ForumBanner from '../components/forum-banner/index.vue';
import PostCard from '../components/post-card/index.vue';
import PostEditor from '../components/post-editor/index.vue';

const router = useRouter();

// 模拟帖子数据
const posts = ref([
  {
    id: 1,
    title: '欢迎加入IoT开发者社区！',
    content:
      '这是一个专为IoT开发者打造的交流平台，在这里我们分享经验、讨论技术、解决问题。',
    author: {
      id: 1,
      name: '社区管理员',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=1',
      level: 10,
      isAdmin: true,
    },
    tags: ['公告', '置顶'],
    createTime: '2024-03-20 10:00:00',
    replyCount: 15,
    viewCount: 320,
    likeCount: 42,
    isTop: true,
    isEssence: true,
  },
  {
    id: 2,
    title: 'MQTT协议在智能家居系统中的最佳实践',
    content:
      '本文详细介绍了如何在智能家居系统中高效使用MQTT协议，包括主题设计、QoS选择、保留消息和遗嘱消息的应用场景。',
    author: {
      id: 2,
      name: '技术专家',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=2',
      level: 8,
    },
    tags: ['技术分享', '智能家居', 'MQTT'],
    createTime: '2024-03-19 15:30:00',
    replyCount: 23,
    viewCount: 186,
    likeCount: 37,
  },
  {
    id: 3,
    title: '低功耗蓝牙（BLE）在工业物联网中的应用案例分析',
    content:
      '本文分析了BLE在工业物联网中的几个成功应用案例，探讨了其优势、局限性以及未来发展趋势。',
    author: {
      id: 3,
      name: '物联网工程师',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=3',
      level: 7,
    },
    tags: ['BLE', '工业物联网', '案例分析'],
    createTime: '2024-03-18 09:15:00',
    replyCount: 18,
    viewCount: 152,
    likeCount: 29,
  },
  {
    id: 4,
    title: '如何保护您的智能家居设备不受网络攻击？',
    content:
      '随着智能家居设备的普及，安全问题日益凸显。本文提供了一些实用的安全措施，帮助您保护智能家居免受黑客攻击。',
    author: {
      id: 4,
      name: '安全专家',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=4',
      level: 9,
    },
    tags: ['安全', '智能家居', '防护措施'],
    createTime: '2024-03-17 14:20:00',
    replyCount: 27,
    viewCount: 203,
    likeCount: 45,
  },
  {
    id: 5,
    title: 'ESP32与树莓派：选择哪一个作为IoT项目的核心？',
    content:
      '本文对比了ESP32和树莓派在IoT项目中的应用场景、优缺点和成本效益，帮助开发者根据项目需求做出最佳选择。',
    author: {
      id: 5,
      name: '硬件爱好者',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=5',
      level: 6,
    },
    tags: ['ESP32', '树莓派', '硬件选型'],
    createTime: '2024-03-16 20:45:00',
    replyCount: 31,
    viewCount: 245,
    likeCount: 53,
  },
]);

// 最新帖子
const latestPosts = ref([
  {
    id: 6,
    title: '5G技术如何改变工业物联网的未来？',
    content: '5G技术的高速率、低延迟特性将为工业物联网带来革命性变化...',
    author: {
      id: 6,
      name: '通信专家',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=6',
      level: 8,
    },
    tags: ['5G', '工业物联网', '趋势'],
    createTime: '2024-03-22 11:30:00',
    replyCount: 5,
    viewCount: 98,
    likeCount: 21,
  },
  {
    id: 7,
    title: '物联网安全最佳实践指南',
    content: '本指南涵盖了物联网设备安全配置、通信加密和固件更新等关键方面...',
    author: {
      id: 7,
      name: '安全顾问',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=7',
      level: 9,
    },
    tags: ['安全', '最佳实践', '指南'],
    createTime: '2024-03-21 16:45:00',
    replyCount: 8,
    viewCount: 87,
    likeCount: 19,
  },
]);

// 精华帖子
const essencePosts = ref([
  {
    id: 8,
    title: 'LoRaWAN网络部署全攻略',
    content: '从硬件选型到网络规划，一步步教你如何构建高效的LoRaWAN网络...',
    author: {
      id: 8,
      name: 'LoRa专家',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=8',
      level: 10,
    },
    tags: ['LoRaWAN', '网络部署', '教程'],
    createTime: '2024-03-15 13:20:00',
    replyCount: 42,
    viewCount: 356,
    likeCount: 87,
    isEssence: true,
  },
  {
    id: 9,
    title: '物联网数据分析：从采集到洞察',
    content:
      '详解物联网数据的采集、处理、存储和分析流程，以及如何从中获取商业价值...',
    author: {
      id: 9,
      name: '数据科学家',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=9',
      level: 9,
    },
    tags: ['数据分析', 'AI', '商业价值'],
    createTime: '2024-03-12 09:30:00',
    replyCount: 36,
    viewCount: 298,
    likeCount: 76,
    isEssence: true,
  },
]);

// 热门话题
const hotTopics = ref([
  { id: 1, name: '智能家居', count: 128 },
  { id: 2, name: '安全隐私', count: 96 },
  { id: 3, name: '5G技术', count: 87 },
  { id: 4, name: '边缘计算', count: 72 },
  { id: 5, name: '物联网标准', count: 65 },
]);

// 热门用户
const hotUsers = ref([
  {
    id: 1,
    name: '技术领袖',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=20',
    level: 10,
    postCount: 56,
    followCount: 1200,
  },
  {
    id: 2,
    name: '创新者',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=21',
    level: 9,
    postCount: 48,
    followCount: 980,
  },
  {
    id: 3,
    name: '解决方案专家',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=22',
    level: 8,
    postCount: 42,
    followCount: 850,
  },
]);

// 加载状态
const loading = ref(false);

// 搜索关键词
const searchKeyword = ref('');

// 发帖组件显示状态
const postEditorVisible = ref(false);

// 当前活动选项卡
const activeTab = ref('hot');

// 显示发帖组件
const handleCreatePost = () => {
  postEditorVisible.value = true;
};

// 发帖成功回调
const handlePostSuccess = () => {
  // 刷新帖子列表
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};

// 搜索帖子
const handleSearch = () => {
  if (!searchKeyword.value.trim()) return;

  loading.value = true;
  // TODO: 实现搜索功能
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};

// 处理帖子点击
const handlePostClick = (postId) => {
  router.push(`/forum/post/${postId}`);
};

// 加载更多
const loadMore = () => {
  loading.value = true;
  // TODO: 加载更多帖子
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};

// 切换选项卡
const handleTabChange = (key) => {
  activeTab.value = key;
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 500);
};
</script>

<template>
  <div class="forum-container">
    <!-- 顶部创作栏 -->
    <div class="creation-bar">
      <div class="container mx-auto px-5">
        <div class="search-area">
          <Avatar
            size="large"
            src="https://api.dicebear.com/7.x/avataaars/svg?seed=10"
          />
          <Button class="create-thought-btn" @click="handleCreatePost">
            有什么技术问题想要讨论？
          </Button>
        </div>
        <div class="action-area">
          <Button type="primary" class="post-btn" @click="handleCreatePost">
            <MdiPlus />
            发布
          </Button>
        </div>
      </div>
    </div>

    <!-- 广告位 -->
    <div class="banner-wrapper mx-auto px-5">
      <ForumBanner />
    </div>

    <!-- 主要内容区 -->
    <div class="container mx-auto px-5">
      <Row :gutter="[24, 24]">
        <!-- 左侧主要内容 -->
        <Col :xs="24" :md="16">
          <!-- 热门帖子选项卡 -->
          <Card class="content-card" :bordered="false">
            <Tabs v-model:active-key="activeTab" @change="handleTabChange">
              <Tabs.TabPane key="hot" tab="热门">
                <div class="post-list" v-if="!loading">
                  <PostCard
                    v-for="post in posts"
                    :key="post.id"
                    :post="post"
                    @click="handlePostClick(post.id)"
                  />
                  <div v-if="posts.length === 0" class="empty-state">
                    <Empty description="还没有相关帖子" />
                  </div>
                  <div v-else class="load-more">
                    <Button @click="loadMore">加载更多</Button>
                  </div>
                </div>
                <Skeleton active v-else :paragraph="{ rows: 10 }" />
              </Tabs.TabPane>

              <Tabs.TabPane key="latest" tab="最新">
                <div class="post-list" v-if="!loading">
                  <PostCard
                    v-for="post in latestPosts"
                    :key="post.id"
                    :post="post"
                    @click="handlePostClick(post.id)"
                  />
                  <div v-if="latestPosts.length === 0" class="empty-state">
                    <Empty description="还没有相关帖子" />
                  </div>
                  <div v-else class="load-more">
                    <Button @click="loadMore">加载更多</Button>
                  </div>
                </div>
                <Skeleton active v-else :paragraph="{ rows: 10 }" />
              </Tabs.TabPane>

              <Tabs.TabPane key="essence" tab="精华">
                <div class="post-list" v-if="!loading">
                  <PostCard
                    v-for="post in essencePosts"
                    :key="post.id"
                    :post="post"
                    @click="handlePostClick(post.id)"
                  />
                  <div v-if="essencePosts.length === 0" class="empty-state">
                    <Empty description="还没有相关帖子" />
                  </div>
                  <div v-else class="load-more">
                    <Button @click="loadMore">加载更多</Button>
                  </div>
                </div>
                <Skeleton active v-else :paragraph="{ rows: 10 }" />
              </Tabs.TabPane>

              <Tabs.TabPane key="following" tab="关注">
                <div class="empty-state">
                  <Empty description="关注用户后可以在这里查看他们的帖子" />
                </div>
              </Tabs.TabPane>
            </Tabs>
          </Card>
        </Col>

        <!-- 右侧边栏 -->
        <Col :xs="24" :md="8">
          <!-- 搜索框 -->
          <Card class="sidebar-card search-card" :bordered="false">
            <div class="search-box">
              <Input
                v-model:value="searchKeyword"
                placeholder="搜索帖子、用户或标签"
                :prefix="h(MdiMagnify)"
                @press-enter="handleSearch"
              />
              <Button type="primary" @click="handleSearch">搜索</Button>
            </div>
          </Card>

          <!-- 热门简洁文章列表 -->
          <CompactPostList
            title="今日热门"
            :posts="posts.slice(0, 5)"
            @post-click="handlePostClick"
          />

          <!-- 热门话题卡片 -->
          <Card class="sidebar-card topic-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <MdiFire class="icon" />
                <span>热门话题</span>
              </div>
            </template>
            <div class="topic-list">
              <div
                v-for="topic in hotTopics"
                :key="topic.id"
                class="topic-item"
              >
                <div class="topic-name"># {{ topic.name }}</div>
                <div class="topic-meta">
                  <span class="topic-count">{{ topic.count }}个帖子</span>
                  <Button type="link" size="small">关注</Button>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <Button type="link">查看全部话题</Button>
            </div>
          </Card>

          <!-- 热门用户卡片 -->
          <Card class="sidebar-card user-card" :bordered="false">
            <template #title>
              <div class="card-title">
                <MdiTrendingUp class="icon" />
                <span>活跃用户</span>
              </div>
            </template>
            <div class="user-list">
              <div v-for="user in hotUsers" :key="user.id" class="user-item">
                <div class="user-info">
                  <Avatar :src="user.avatar" :size="40" />
                  <div class="user-detail">
                    <div class="user-name">{{ user.name }}</div>
                    <div class="user-meta">
                      <span class="user-level">Lv.{{ user.level }}</span>
                      <span class="user-stats">
                        {{ user.postCount }}帖 / {{ user.followCount }}粉丝
                      </span>
                    </div>
                  </div>
                </div>
                <Button size="small">关注</Button>
              </div>
            </div>
            <div class="card-footer">
              <Button type="link">发现更多用户</Button>
            </div>
          </Card>
        </Col>
      </Row>
    </div>

    <!-- 发帖组件 -->
    <PostEditor
      v-model:visible="postEditorVisible"
      @success="handlePostSuccess"
    />
  </div>
</template>

<style lang="scss" scoped>
.forum-container {
  min-height: 100vh;
  background-color: #f9fafb;
  padding-bottom: 40px;

  // 创作栏
  .creation-bar {
    background: #fff;
    padding: 16px 0;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);

    .container {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .search-area {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
    }

    .create-thought-btn {
      height: 48px;
      border-radius: 24px;
      background: #f5f5f5;
      color: #777;
      font-size: 15px;
      padding: 0 24px;
      text-align: left;
      flex: 1;
      max-width: 400px;
      border: none;
      box-shadow: none;

      &:hover {
        background: #f0f0f0;
        color: #555;
      }
    }

    .action-area {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .post-btn {
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      background: linear-gradient(135deg, #4f6ef7, #3b5de7);
      border: none;
      padding: 0 20px;
      box-shadow: 0 4px 12px rgba(59, 93, 231, 0.25);

      &:hover {
        background: linear-gradient(135deg, #5a78f8, #4668e8);
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(59, 93, 231, 0.3);
      }
    }
  }

  // 广告横幅
  .banner-wrapper {
    margin: 24px auto;
  }

  // 主内容区
  .container {
    margin-top: 24px;
  }

  .content-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 24px;

    :deep(.ant-card-body) {
      padding: 0;
    }

    :deep(.ant-tabs-nav) {
      margin-bottom: 0;
      padding: 0 20px;
    }

    :deep(.ant-tabs-content-holder) {
      background: white;
    }

    :deep(.ant-tabs-content) {
      padding: 20px;
    }

    .post-list {
      .empty-state {
        padding: 40px 0;
        text-align: center;
      }

      .load-more {
        text-align: center;
        padding: 20px 0 0;
      }
    }
  }

  // 侧边栏卡片
  .sidebar-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 24px;

    &.search-card {
      padding: 16px;

      .search-box {
        display: flex;
        gap: 12px;

        :deep(.ant-input-affix-wrapper) {
          flex: 1;
          border-radius: 8px;
        }

        .ant-btn {
          border-radius: 8px;
        }
      }
    }

    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;

      .icon {
        color: #4f6ef7;
        font-size: 18px;
      }
    }

    .card-footer {
      border-top: 1px solid #f1f5f9;
      padding-top: 12px;
      text-align: center;
    }

    // 话题列表
    .topic-list {
      .topic-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f1f5f9;

        &:last-child {
          border-bottom: none;
        }

        .topic-name {
          font-weight: 500;
          color: #334155;
        }

        .topic-meta {
          display: flex;
          align-items: center;
          gap: 12px;

          .topic-count {
            font-size: 12px;
            color: #64748b;
          }
        }
      }
    }

    // 用户列表
    .user-list {
      .user-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f1f5f9;

        &:last-child {
          border-bottom: none;
        }

        .user-info {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .user-detail {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .user-name {
          font-weight: 500;
          color: #334155;
        }

        .user-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #64748b;

          .user-level {
            background: #e0e7ff;
            color: #4f46e5;
            padding: 2px 4px;
            border-radius: 4px;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .forum-container {
    .creation-bar {
      .create-thought-btn {
        display: none;
      }

      .action-area {
        margin-left: auto;
      }
    }

    .sidebar-card {
      &.search-card {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
