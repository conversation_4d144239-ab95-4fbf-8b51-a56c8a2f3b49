<script lang="ts" setup>
import { reactive, ref } from 'vue';

import { MdiTag } from '@vben/icons';

import { Button, Form, Input, message, Modal, Select } from 'ant-design-vue';

// Props定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:visible', 'success']);

const FormItem = Form.Item;

// 表单实例引用
const formRef = ref();

// 表单状态
const formState = reactive({
  title: '',
  content: '',
  tags: [],
});

// 标签选项
const tagOptions = [
  { value: '物联网', label: '物联网' },
  { value: '智能家居', label: '智能家居' },
  { value: '传感器', label: '传感器' },
  { value: '安全', label: '安全' },
  { value: '云计算', label: '云计算' },
  { value: '5G', label: '5G' },
  { value: 'MQTT', label: 'MQTT' },
  { value: 'ESP32', label: 'ESP32' },
  { value: 'Arduino', label: 'Arduino' },
  { value: '边缘计算', label: '边缘计算' },
  { value: '问题求助', label: '问题求助' },
  { value: '技术分享', label: '技术分享' },
  { value: '项目展示', label: '项目展示' },
];

// 提交表单
const handleSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      // 模拟提交
      const loadingMessage = message.loading('发布中...', 0);

      setTimeout(() => {
        loadingMessage();
        message.success('发布成功！');

        // 重置表单
        formRef.value.resetFields();

        // 关闭对话框并通知父组件
        emit('update:visible', false);
        emit('success');
      }, 1500);
    })
    .catch((error: unknown) => {
      console.error('表单验证失败:', error);
      message.error('请检查表单填写是否正确');
    });
};

// 关闭对话框
const handleCancel = () => {
  emit('update:visible', false);
};
</script>

<template>
  <Modal
    :visible="props.visible"
    title="发布新帖子"
    width="700px"
    :mask-closable="false"
    :destroy-on-close="true"
    @update:visible="(val) => emit('update:visible', val)"
    @cancel="handleCancel"
  >
    <Form ref="formRef" :model="formState" layout="vertical" class="post-form">
      <FormItem
        label="标题"
        name="title"
        :rules="[
          { required: true, message: '请输入标题' },
          { min: 5, max: 100, message: '标题长度应在5-100个字符之间' },
        ]"
      >
        <Input
          v-model:value="formState.title"
          placeholder="请输入能够吸引读者眼球的标题（5-100字）"
          :maxlength="100"
          show-count
          class="title-input"
        />
      </FormItem>

      <FormItem
        label="内容"
        name="content"
        :rules="[
          { required: true, message: '请输入内容' },
          { min: 20, message: '内容不能少于20个字符' },
        ]"
      >
        <Input.TextArea
          v-model:value="formState.content"
          placeholder="详细描述您的问题或想法（至少20字）"
          :auto-size="{ minRows: 8, maxRows: 15 }"
          :maxlength="10000"
          show-count
          class="content-input"
        />
      </FormItem>

      <FormItem
        label="标签"
        name="tags"
        class="tag-input"
        :rules="[
          { required: true, message: '至少选择一个标签' },
          { type: 'array', max: 5, message: '最多选择5个标签' },
        ]"
      >
        <div class="tag-header">
          <span>
            <MdiTag class="tag-icon" />
            选择最多5个相关标签
          </span>
        </div>
        <Select
          v-model:value="formState.tags"
          mode="multiple"
          placeholder="选择或输入标签"
          :options="tagOptions"
          :max-tag-count="5"
          :max-tag-text-length="10"
          allow-clear
          class="tag-select"
        />
      </FormItem>
    </Form>

    <template #footer>
      <Button class="cancel-btn" @click="handleCancel">取消</Button>
      <Button type="primary" @click="handleSubmit" class="submit-btn">
        发布
      </Button>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
.post-form {
  .title-input {
    font-size: 16px;
    height: 48px;
    border-radius: 10px;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      border-color: #4f6ef7;
      box-shadow: 0 0 0 2px rgba(79, 110, 247, 0.1);
    }
  }

  .content-input {
    font-size: 15px;
    line-height: 1.6;
    border-radius: 10px;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      border-color: #4f6ef7;
      box-shadow: 0 0 0 2px rgba(79, 110, 247, 0.1);
    }
  }

  .tag-input {
    .tag-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      color: #64748b;

      .tag-icon {
        margin-right: 8px;
        color: #4f6ef7;
      }
    }

    .tag-select {
      width: 100%;
      border-radius: 10px;
      transition: all 0.3s ease;

      &:hover,
      &:focus {
        border-color: #4f6ef7;
        box-shadow: 0 0 0 2px rgba(79, 110, 247, 0.1);
      }
    }
  }
}

:deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: #334155;
  font-size: 15px;
}

:deep(.ant-modal-content) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

:deep(.ant-modal-header) {
  border-bottom: 1px solid #f1f5f9;
  padding: 18px 24px;
}

:deep(.ant-modal-title) {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

:deep(.ant-modal-body) {
  padding: 24px;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #f1f5f9;
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .cancel-btn {
    border-radius: 8px;
    font-weight: 500;
    height: 40px;
    padding: 0 20px;
    border: 1px solid #e2e8f0;
    color: #64748b;

    &:hover {
      border-color: #cbd5e1;
      color: #334155;
      background: #f8fafc;
    }
  }

  .submit-btn {
    background: linear-gradient(135deg, #4f6ef7, #3b5de7);
    border: none;
    font-weight: 500;
    height: 40px;
    padding: 0 24px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(59, 93, 231, 0.2);

    &:hover {
      background: linear-gradient(135deg, #5a78f8, #4668e8);
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(59, 93, 231, 0.3);
    }
  }
}

@media (max-width: 768px) {
  :deep(.ant-modal-title) {
    font-size: 16px;
  }

  .post-form {
    .title-input {
      height: 44px;
    }
  }

  :deep(.ant-form-item-label > label) {
    font-size: 14px;
  }
}
</style>
