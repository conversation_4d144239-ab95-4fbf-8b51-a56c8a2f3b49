<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { ExternalLink, MdiArrowDown, MdiArrowLeft } from '@vben/icons';

import { Button, Carousel } from 'ant-design-vue';

// 模拟广告数据
const banners = ref([
  {
    id: 1,
    title: '物联网开发大赛',
    subtitle: '2024年度最具影响力',
    description: '携手全球领先企业，打造物联网创新生态',
    image: 'https://picsum.photos/id/180/800/500',
    bgStart: '#1a365d',
    bgEnd: '#2563eb',
    link: 'https://example.com/events/iot-conference',
    position: 'right',
  },
  {
    id: 2,
    title: '工业物联网安全',
    subtitle: '白皮书发布',
    description: '深入分析工业场景下的安全风险与防护策略',
    image: 'https://picsum.photos/id/160/800/500',
    bgStart: '#4c1d95',
    bgEnd: '#8b5cf6',
    link: 'https://example.com/resources/industrial-iot-security',
    position: 'left',
  },
  {
    id: 3,
    title: '智能家居生态系统',
    subtitle: '全场景解决方案',
    description: '探索AI驱动的下一代智能家居集成平台',
    image: 'https://picsum.photos/id/96/800/500',
    bgStart: '#065f46',
    bgEnd: '#10b981',
    link: 'https://example.com/solutions/smart-home',
    position: 'right',
  },
]);

// 侧边广告数据
const sideAds = ref([
  {
    id: 101,
    title: '物联网网关',
    subtitle: '新品发布',
    image: 'https://picsum.photos/id/239/400/250',
    bgStart: '#991b1b',
    bgEnd: '#dc2626',
    link: 'https://example.com/products/iot-gateway',
  },
  {
    id: 102,
    title: '开发者社区',
    subtitle: '加入我们',
    image: 'https://picsum.photos/id/3/400/250',
    bgStart: '#155e75',
    bgEnd: '#0891b2',
    link: 'https://example.com/community',
  },
]);

const activeIndex = ref(0);
const carouselRef = ref();
const autoplayTimer = ref(null);
const autoplayInterval = 5000; // 5秒

// 重置自动播放计时器
const resetAutoplayTimer = () => {
  if (autoplayTimer.value) {
    clearInterval(autoplayTimer.value);
  }

  autoplayTimer.value = setInterval(() => {
    next();
  }, autoplayInterval);
};

// 切换到前一个广告
const prev = () => {
  carouselRef.value?.prev();
  resetAutoplayTimer();
};

// 切换到后一个广告
const next = () => {
  carouselRef.value?.next();
  resetAutoplayTimer();
};

// 设置当前激活的索引
const setActiveIndex = (index) => {
  activeIndex.value = index;
};

// 直接跳转到指定索引的幻灯片
const goTo = (index) => {
  carouselRef.value?.goTo(index);
  resetAutoplayTimer();
};

// 组件挂载时启动自动播放
onMounted(() => {
  resetAutoplayTimer();
});
</script>

<template>
  <div class="forum-banner-layout">
    <!-- 左侧主轮播图 -->
    <div class="main-banner">
      <Carousel
        ref="carouselRef"
        effect="fade"
        :after-change="setActiveIndex"
        class="banner-carousel"
      >
        <div v-for="banner in banners" :key="banner.id" class="banner-slide">
          <div
            class="banner-content"
            :class="[`banner-position-${banner.position}`]"
            :style="{
              background: `linear-gradient(135deg, ${banner.bgStart} 0%, ${banner.bgEnd} 100%)`,
            }"
          >
            <div class="banner-text">
              <div class="banner-title-wrapper">
                <h2 class="banner-title">{{ banner.title }}</h2>
                <span class="banner-subtitle">{{ banner.subtitle }}</span>
              </div>
              <p class="banner-description">{{ banner.description }}</p>
              <a
                :href="banner.link"
                target="_blank"
                rel="noopener noreferrer"
                class="banner-link"
              >
                了解更多
                <ExternalLink class="link-icon" />
              </a>
            </div>

            <div class="banner-image-wrapper">
              <div class="banner-graphic-elements">
                <div class="circle circle-1"></div>
                <div class="circle circle-2"></div>
                <div class="circle circle-3"></div>
              </div>
              <div class="image-container">
                <img
                  :src="banner.image"
                  :alt="banner.title"
                  class="banner-img"
                  loading="lazy"
                />
              </div>
            </div>
          </div>
        </div>
      </Carousel>

      <!-- 导航指示器 -->
      <div class="banner-indicators">
        <button
          v-for="(banner, index) in banners"
          :key="`indicator-${banner.id}`"
          class="indicator-dot"
          :class="{ active: activeIndex === index }"
          @click="goTo(index)"
        ></button>
      </div>

      <!-- 轮播导航按钮 -->
      <div class="banner-nav">
        <Button
          type="primary"
          shape="circle"
          class="nav-btn prev-btn"
          @click="prev"
        >
          <MdiArrowLeft />
        </Button>
        <Button
          type="primary"
          shape="circle"
          class="nav-btn next-btn"
          @click="next"
        >
          <MdiArrowDown />
        </Button>
      </div>
    </div>

    <!-- 右侧两个广告位 -->
    <div class="side-ads">
      <a
        v-for="ad in sideAds"
        :key="ad.id"
        :href="ad.link"
        target="_blank"
        rel="noopener noreferrer"
        class="side-ad-item"
        :style="{
          background: `linear-gradient(135deg, ${ad.bgStart} 0%, ${ad.bgEnd} 100%)`,
        }"
      >
        <div class="side-ad-content">
          <h3 class="side-ad-title">{{ ad.title }}</h3>
          <span class="side-ad-subtitle">{{ ad.subtitle }}</span>
          <ExternalLink class="side-ad-icon" />
        </div>
        <div class="side-ad-image">
          <img
            :src="ad.image"
            :alt="ad.title"
            class="side-ad-img"
            loading="lazy"
          />
        </div>
      </a>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.forum-banner-layout {
  display: flex;
  gap: 20px;
  width: 100%;

  @media (max-width: 1024px) {
    flex-direction: column;
  }
}

// 主轮播图样式
.main-banner {
  position: relative;
  flex: 1;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);

  &:hover {
    .nav-btn {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .banner-carousel {
    width: 100%;
    height: 350px;

    :deep(.slick-dots) {
      display: none !important;
    }
  }

  .banner-slide {
    height: 350px;
    width: 100%;
  }

  .banner-content {
    display: flex;
    height: 100%;
    color: #fff;
    padding: 0;
    overflow: hidden;
    position: relative;

    &.banner-position-left {
      flex-direction: row;

      .banner-text {
        order: 1;
      }

      .banner-image-wrapper {
        order: 2;
      }
    }

    &.banner-position-right {
      flex-direction: row;

      .banner-text {
        order: 2;
      }

      .banner-image-wrapper {
        order: 1;
      }
    }
  }

  .banner-text {
    flex: 1;
    padding: 40px 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 3;
    position: relative;
  }

  .banner-title-wrapper {
    margin-bottom: 12px;
  }

  .banner-title {
    font-size: 32px;
    font-weight: 800;
    margin: 0;
    line-height: 1.1;
    letter-spacing: -0.02em;
    position: relative;
    display: inline-block;
  }

  .banner-subtitle {
    font-size: 18px;
    font-weight: 500;
    opacity: 0.8;
    margin-top: 5px;
    display: block;
  }

  .banner-description {
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 24px;
    max-width: 450px;
    opacity: 0.9;
  }

  .banner-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 10px 20px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    width: fit-content;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

    .link-icon {
      font-size: 14px;
      transition: transform 0.3s ease;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);

      .link-icon {
        transform: translateX(3px);
      }
    }
  }

  .banner-image-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .banner-graphic-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;

    .circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);

      &.circle-1 {
        width: 150px;
        height: 150px;
        top: -50px;
        right: 10%;
      }

      &.circle-2 {
        width: 80px;
        height: 80px;
        bottom: 20px;
        right: 40%;
      }

      &.circle-3 {
        width: 200px;
        height: 200px;
        bottom: -100px;
        left: 10%;
        background: rgba(255, 255, 255, 0.05);
      }
    }
  }

  .image-container {
    position: relative;
    width: 90%;
    height: 80%;
    z-index: 2;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }
  }

  .banner-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .banner-indicators {
    position: absolute;
    left: 30px;
    bottom: 20px;
    display: flex;
    gap: 10px;
    z-index: 10;

    .indicator-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.4);
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      padding: 0;

      &.active {
        background: #fff;
        width: 30px;
        border-radius: 10px;
      }

      &:hover:not(.active) {
        background: rgba(255, 255, 255, 0.7);
        transform: scale(1.2);
      }
    }
  }

  .banner-nav {
    position: absolute;
    bottom: 20px;
    right: 30px;
    display: flex;
    gap: 12px;
    z-index: 10;

    .nav-btn {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: #fff;
      font-size: 18px;
      backdrop-filter: blur(5px);
      transition: all 0.3s ease;
      opacity: 0.7;
      transform: translateY(10px);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        opacity: 1;
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
      }
    }
  }
}

// 右侧广告样式
.side-ads {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 300px;

  @media (max-width: 1024px) {
    width: 100%;
    flex-direction: row;

    .side-ad-item {
      width: 50%;
    }
  }

  @media (max-width: 640px) {
    flex-direction: column;

    .side-ad-item {
      width: 100%;
    }
  }

  .side-ad-item {
    position: relative;
    height: 165px;
    border-radius: 16px;
    overflow: hidden;
    color: white;
    display: flex;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25);

      .side-ad-img {
        transform: scale(1.05);
      }

      .side-ad-icon {
        transform: translate(3px, -3px);
      }
    }
  }

  .side-ad-content {
    position: relative;
    z-index: 2;
    padding: 20px;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .side-ad-title {
    font-size: 20px;
    font-weight: 700;
    margin: 0 0 5px;
  }

  .side-ad-subtitle {
    font-size: 14px;
    opacity: 0.8;
  }

  .side-ad-icon {
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 18px;
    transition: all 0.3s ease;
  }

  .side-ad-image {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    opacity: 0.2;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, rgba(0, 0, 0, 0.4), transparent);
    }
  }

  .side-ad-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }
}

@media (max-width: 768px) {
  .main-banner {
    border-radius: 12px;

    .banner-carousel,
    .banner-slide {
      height: 300px;
    }

    .banner-content {
      flex-direction: column !important;

      .banner-text,
      .banner-image-wrapper {
        order: unset !important;
      }
    }

    .banner-text {
      padding: 25px 20px;
      justify-content: flex-start;
    }

    .banner-title {
      font-size: 24px;
    }

    .banner-subtitle {
      font-size: 15px;
    }

    .banner-description {
      font-size: 14px;
      margin-bottom: 16px;
    }

    .banner-image-wrapper {
      height: 180px;
      align-items: flex-start;
    }

    .image-container {
      width: 100%;
      height: 100%;
      border-radius: 0;
    }

    .banner-indicators {
      left: 20px;
      bottom: 15px;
    }

    .banner-nav {
      right: 20px;
      bottom: 15px;

      .nav-btn {
        width: 36px;
        height: 36px;
      }
    }
  }

  .side-ads .side-ad-item {
    height: 140px;

    .side-ad-title {
      font-size: 18px;
    }
  }
}
</style>
