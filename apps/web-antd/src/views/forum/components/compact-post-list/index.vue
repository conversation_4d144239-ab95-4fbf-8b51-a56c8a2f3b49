<script lang="ts" setup>
import { MdiCalendar<PERSON><PERSON>, MdiComment, MdiEye, MdiThumbUp } from '@vben/icons';

import { Avatar, Tag } from 'ant-design-vue';

// 定义属性
const props = defineProps({
  title: {
    type: String,
    default: '热门帖子',
  },
  posts: {
    type: Array,
    required: true,
  },
  showAction: {
    type: Boolean,
    default: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showMore: {
    type: Boolean,
    default: true,
  },
});

// 定义事件
const emit = defineEmits(['post-click', 'load-more']);

// 点击帖子
const handlePostClick = (postId) => {
  emit('post-click', postId);
};

// 加载更多
const handleLoadMore = () => {
  emit('load-more');
};

// 日期格式化
const formatDate = (dateString) => {
  const date = new Date(dateString);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${month}/${day}`;
};
</script>

<template>
  <div class="compact-post-list">
    <div v-if="title" class="list-header">
      <h3 class="list-title">{{ title }}</h3>
      <slot name="header-action"></slot>
    </div>

    <div class="list-content">
      <div
        v-for="(post, index) in posts"
        :key="post.id"
        class="post-item"
        :class="{ 'is-top': post.isTop, 'is-essence': post.isEssence }"
        @click="handlePostClick(post.id)"
      >
        <div class="post-index">{{ index + 1 }}</div>

        <div class="post-info">
          <div class="post-title-wrapper">
            <div class="post-badges">
              <div v-if="post.isTop" class="badge top-badge">置顶</div>
              <div v-if="post.isEssence" class="badge essence-badge">精华</div>
              <Tag v-if="post.tags && post.tags.length > 0" class="tag-badge">
                {{ post.tags[0] }}
              </Tag>
            </div>
            <div class="post-title">{{ post.title }}</div>
          </div>

          <div v-if="showAction" class="post-meta">
            <div class="author-info">
              <Avatar :src="post.author.avatar" :size="16" />
              <span class="author-name">{{ post.author.name }}</span>
            </div>

            <div class="meta-data">
              <div class="meta-item">
                <MdiCalendarClock class="meta-icon" />
                <span>{{ formatDate(post.createTime) }}</span>
              </div>

              <div class="meta-item">
                <MdiEye class="meta-icon" />
                <span>{{ post.viewCount }}</span>
              </div>

              <div class="meta-item">
                <MdiComment class="meta-icon" />
                <span>{{ post.replyCount }}</span>
              </div>

              <div class="meta-item">
                <MdiThumbUp class="meta-icon" />
                <span>{{ post.likeCount }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="showMore && posts.length > 0" class="list-footer">
        <button class="load-more-btn" @click.stop="handleLoadMore">
          查看更多
        </button>
      </div>

      <div v-if="posts.length === 0" class="empty-list">
        <p class="empty-text">暂无内容</p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.compact-post-list {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f1f5f9;

    .list-title {
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
    }
  }

  .list-content {
    padding: 8px 0;
  }

  .post-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    border-left: 3px solid transparent;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 20px;
      right: 20px;
      height: 1px;
      background: #f1f5f9;
    }

    &:last-child::after {
      display: none;
    }

    &:hover {
      background-color: #f8fafc;

      .post-title {
        color: #4f6ef7;
      }
    }

    &.is-top {
      background-color: rgba(79, 110, 247, 0.03);
      border-left-color: #ff4d4f;
    }

    &.is-essence {
      border-left-color: #faad14;
    }
  }

  .post-index {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background: #f1f5f9;
    color: #64748b;
    font-size: 13px;
    font-weight: 600;
    margin-right: 12px;
    flex-shrink: 0;

    // 前三名样式特殊处理
    .post-item:nth-child(1) & {
      background: #ec4899;
      color: white;
    }

    .post-item:nth-child(2) & {
      background: #8b5cf6;
      color: white;
    }

    .post-item:nth-child(3) & {
      background: #3b82f6;
      color: white;
    }
  }

  .post-info {
    flex: 1;
    min-width: 0;
  }

  .post-title-wrapper {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
  }

  .post-badges {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-right: 8px;
    flex-shrink: 0;

    .badge {
      font-size: 12px;
      line-height: 1;
      padding: 3px 6px;
      border-radius: 4px;
      font-weight: 500;
    }

    .top-badge {
      background-color: #fff0f0;
      color: #ff4d4f;
    }

    .essence-badge {
      background-color: #fffbe6;
      color: #faad14;
    }

    .tag-badge {
      font-size: 12px;
      background-color: #f0f9ff;
      color: #0ea5e9;
      border: none;
    }
  }

  .post-title {
    font-size: 14px;
    font-weight: 500;
    color: #334155;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: color 0.2s;
  }

  .post-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    color: #94a3b8;

    .author-info {
      display: flex;
      align-items: center;
      gap: 6px;

      .author-name {
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .meta-data {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .meta-item {
      display: flex;
      align-items: center;
      gap: 4px;

      .meta-icon {
        font-size: 14px;
      }
    }
  }

  .list-footer {
    text-align: center;
    padding: 12px 0;
    border-top: 1px solid #f1f5f9;

    .load-more-btn {
      background: none;
      border: none;
      color: #64748b;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        color: #4f6ef7;
      }
    }
  }

  .empty-list {
    padding: 40px 0;
    text-align: center;

    .empty-text {
      color: #94a3b8;
      font-size: 14px;
    }
  }
}

@media (max-width: 640px) {
  .compact-post-list {
    .post-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .meta-data {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}
</style>
