<script lang="ts" setup>
import { computed } from 'vue';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>di<PERSON><PERSON>,
  MdiShareVariant,
  MdiStar,
  MdiThumbUp,
} from '@vben/icons';

import { Avatar, Tag, Tooltip } from 'ant-design-vue';

const props = defineProps<{
  post: {
    author: {
      avatar: string;
      id: number;
      isAdmin?: boolean;
      level: number;
      name: string;
    };
    content: string;
    createTime: string;
    id: number;
    isEssence?: boolean;
    isTop?: boolean;
    likeCount: number;
    replyCount: number;
    tags: string[];
    title: string;
    viewCount: number;
  };
}>();

// 计算内容预览
const contentPreview = computed(() => {
  return props.post.content.length > 100
    ? `${props.post.content.slice(0, 100)}...`
    : props.post.content;
});

// 计算发布时间
const timeAgo = computed(() => {
  const now = new Date();
  const postTime = new Date(props.post.createTime);
  const diff = now.getTime() - postTime.getTime();
  const minutes = Math.floor(diff / 1000 / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days}天前`;
  if (hours > 0) return `${hours}小时前`;
  if (minutes > 0) return `${minutes}分钟前`;
  return '刚刚';
});

// 随机阅读时间（根据内容长度生成）
const readingTime = computed(() => {
  return Math.max(1, Math.ceil(props.post.content.length / 400));
});
</script>

<template>
  <div
    class="post-card relative mb-5 cursor-pointer rounded-2xl bg-white p-6 shadow-sm transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
    :class="{ 'is-top': post.isTop, 'is-essence': post.isEssence }"
  >
    <!-- 作者信息 -->
    <div class="post-author mb-4 flex items-center gap-3">
      <div class="author-avatar relative">
        <Avatar :src="post.author.avatar" :size="44" />
        <div
          v-if="post.author.isAdmin"
          class="admin-badge absolute -bottom-0.5 -right-0.5 rounded border-2 border-white bg-blue-600 px-1 text-xs text-white"
        >
          管理
        </div>
      </div>
      <div class="author-info flex-1">
        <div class="author-name mb-1 text-base font-semibold text-slate-800">
          {{ post.author.name }}
        </div>
        <div class="post-meta flex items-center text-xs text-gray-500">
          <span
            class="author-level rounded bg-blue-50 px-1.5 py-0.5 font-medium text-blue-600"
          >
            Lv.{{ post.author.level }}
          </span>
          <span
            class="dot-divider mx-2 inline-block h-1 w-1 rounded-full bg-gray-300"
          ></span>
          <span class="post-time">{{ timeAgo }}</span>
          <Tooltip v-if="post.isTop" title="置顶帖">
            <MdiPin class="post-icon ml-2 text-red-500" />
          </Tooltip>
          <Tooltip v-if="post.isEssence" title="精华帖">
            <MdiStar class="post-icon ml-2 text-yellow-500" />
          </Tooltip>
        </div>
      </div>
    </div>

    <!-- 帖子内容 -->
    <div class="post-content pl-14">
      <h3
        class="post-title mb-3 text-lg font-semibold leading-snug text-slate-800 transition-colors duration-300"
      >
        {{ post.title }}
      </h3>
      <p
        class="post-excerpt mb-5 line-clamp-2 text-sm leading-relaxed text-gray-500"
      >
        {{ contentPreview }}
      </p>

      <div class="post-footer flex flex-wrap items-center justify-between">
        <div class="post-tags flex flex-wrap items-center gap-2">
          <Tag
            v-for="tag in post.tags"
            :key="tag"
            class="post-tag rounded border-none bg-slate-100 px-2 py-0.5 text-xs text-blue-600 hover:bg-slate-200"
          >
            {{ tag }}
          </Tag>
          <span
            class="reading-time flex items-center pl-2 text-xs text-gray-400"
          >
            {{ readingTime }}分钟阅读
          </span>
        </div>

        <div class="post-stats flex items-center gap-4">
          <div
            class="stat-item flex items-center gap-1 text-sm text-gray-500 transition-all duration-200 hover:text-blue-600"
          >
            <MdiEye class="stat-icon text-base" />
            <span>{{ post.viewCount }}</span>
          </div>
          <div
            class="stat-item flex items-center gap-1 text-sm text-gray-500 transition-all duration-200 hover:text-blue-600"
          >
            <MdiComment class="stat-icon text-base" />
            <span>{{ post.replyCount }}</span>
          </div>
          <div
            class="stat-item flex items-center gap-1 text-sm text-gray-500 transition-all duration-200 hover:text-blue-600"
          >
            <MdiThumbUp class="stat-icon text-base" />
            <span>{{ post.likeCount }}</span>
          </div>
          <div
            class="stat-item flex items-center gap-1 text-sm text-gray-500 transition-all duration-200 hover:text-blue-600"
          >
            <MdiShareVariant class="stat-icon text-base" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.post-card {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 0;
    background: linear-gradient(to bottom, #4f6ef7, #3b5de7);
    transition: height 0.3s ease;
  }

  &:hover {
    &::before {
      height: 100%;
    }

    .post-title {
      color: #3b5de7;
    }
  }

  &.is-top {
    background: linear-gradient(
      to right,
      rgba(79, 110, 247, 0.03),
      transparent
    );

    &::before {
      background: linear-gradient(to bottom, #ff4d4f, #ff7875);
      height: 100%;
    }
  }
}

@media (max-width: 640px) {
  .post-card {
    padding: 16px;

    .post-content {
      padding-left: 0;

      .post-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .post-tags {
          width: 100%;
        }

        .post-stats {
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }
}
</style>
