<script lang="ts" setup>
import { ref } from 'vue';

import { MdiFire, MdiTag } from '@vben/icons';

import { Button, Card, List, Tag } from 'ant-design-vue';

// 分类列表
const categories = ref([
  { id: 1, name: '全部', count: 100 },
  { id: 2, name: '公告', count: 10 },
  { id: 3, name: '讨论', count: 50 },
  { id: 4, name: '问答', count: 30 },
  { id: 5, name: '分享', count: 20 },
]);

// 热门标签
const hotTags = ref([
  { id: 1, name: '新手入门', count: 100 },
  { id: 2, name: '问题反馈', count: 80 },
  { id: 3, name: '使用技巧', count: 60 },
  { id: 4, name: '功能建议', count: 40 },
  { id: 5, name: '经验分享', count: 30 },
]);

// 热门帖子
const hotPosts = ref([
  {
    id: 1,
    title: '新手必读：如何快速上手使用我们的产品',
    viewCount: 1000,
  },
  {
    id: 2,
    title: '最新功能更新说明',
    viewCount: 800,
  },
  {
    id: 3,
    title: '常见问题解答',
    viewCount: 600,
  },
]);

// 处理分类点击
const handleCategoryClick = (categoryId: number) => {
  // TODO: 实现分类筛选
  console.log('选择分类:', categoryId);
};

// 处理标签点击
const handleTagClick = (tagId: number) => {
  // TODO: 实现标签筛选
  console.log('选择标签:', tagId);
};
</script>

<template>
  <div class="forum-sidebar">
    <!-- 分类列表 -->
    <Card class="sidebar-card" title="分类" :bordered="false">
      <div class="category-list">
        <Button
          v-for="category in categories"
          :key="category.id"
          type="text"
          class="category-item"
          @click="handleCategoryClick(category.id)"
        >
          <span class="category-name">{{ category.name }}</span>
          <Tag class="category-count">{{ category.count }}</Tag>
        </Button>
      </div>
    </Card>

    <!-- 热门标签 -->
    <Card class="sidebar-card" title="热门标签" :bordered="false">
      <div class="tag-list">
        <Tag
          v-for="tag in hotTags"
          :key="tag.id"
          class="tag-item"
          @click="handleTagClick(tag.id)"
        >
          <MdiTag />
          {{ tag.name }}
          <span class="tag-count">({{ tag.count }})</span>
        </Tag>
      </div>
    </Card>

    <!-- 热门帖子 -->
    <Card class="sidebar-card" title="热门帖子" :bordered="false">
      <List :data-source="hotPosts" :split="false" size="small">
        <template #renderItem="{ item }">
          <List.Item>
            <div class="hot-post-item">
              <MdiFire class="hot-icon" />
              <span class="post-title">{{ item.title }}</span>
              <Tag class="view-count">{{ item.viewCount }} 浏览</Tag>
            </div>
          </List.Item>
        </template>
      </List>
    </Card>
  </div>
</template>

<style lang="scss" scoped>
.forum-sidebar {
  .sidebar-card {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .category-list {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .category-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--primary-1);
      }

      .category-name {
        font-weight: 500;
      }

      .category-count {
        background-color: var(--primary-1);
        color: var(--primary-color);
      }
    }
  }

  .tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .tag-item {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .tag-count {
        margin-left: 4px;
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }

  .hot-post-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;

    .hot-icon {
      color: var(--error-color);
    }

    .post-title {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .view-count {
      font-size: 12px;
    }
  }
}
</style>
