<script setup lang="ts">
import { ref } from 'vue';

import { Card, Tabs } from 'ant-design-vue';

import DynamicFormDemo from './components/DynamicFormDemo.vue';
import PermissionDemo from './components/PermissionDemo.vue';
import StepFormDemo from './components/StepFormDemo.vue';
import TableDemo from './components/TableDemo.vue';

const activeTab = ref('table');
const TabPane = Tabs.TabPane;
</script>

<template>
  <div class="useAnsheng-demo">
    <Card title="useAnsheng示例" class="mb-4">
      <Tabs v-model:active-key="activeTab">
        <TabPane key="table" tab="表格与CRUD">
          <TableDemo />
        </TabPane>
        <TabPane key="dynamicForm" tab="动态表单">
          <DynamicFormDemo />
        </TabPane>
        <TabPane key="stepForm" tab="分步表单">
          <StepFormDemo />
        </TabPane>
        <TabPane key="permission" tab="权限控制">
          <PermissionDemo />
        </TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<style scoped>
.useAnsheng-demo {
  padding: 24px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
