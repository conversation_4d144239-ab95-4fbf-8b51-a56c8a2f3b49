<script setup lang="ts">
import { defineComponent, h, ref } from 'vue';

import {
  MdiExport,
  MdiEye,
  MdiPencil,
  MdiPlus,
  MdiTrashCan,
} from '@vben/icons';

import {
  <PERSON>ert,
  Button,
  Card,
  Col,
  Divider,
  FormItem,
  Input,
  message,
  Select,
  Switch,
} from 'ant-design-vue';

import { BasicTable, DetailModal, useAnsheng } from '#/hooks/useAnsheng';

// 模拟用户权限服务
const mockUserPermissionService = {
  // 权限列表
  permissions: ref([
    'user:read',
    'user:create',
    'user:update',
    'user:field:name',
    'user:field:age',
    'user:field:email',
  ]),

  // 是否为管理员
  isAdmin: ref(false),

  // 判断是否有权限
  hasPermission: (permission: string) => {
    return (
      mockUserPermissionService.isAdmin.value ||
      mockUserPermissionService.permissions.value.includes(permission)
    );
  },

  // 添加权限
  addPermission: (permission: string) => {
    if (!mockUserPermissionService.permissions.value.includes(permission)) {
      mockUserPermissionService.permissions.value.push(permission);
    }
  },

  // 移除权限
  removePermission: (permission: string) => {
    const index =
      mockUserPermissionService.permissions.value.indexOf(permission);
    if (index !== -1) {
      mockUserPermissionService.permissions.value.splice(index, 1);
    }
  },

  // 切换管理员状态
  toggleAdmin: () => {
    mockUserPermissionService.isAdmin.value =
      !mockUserPermissionService.isAdmin.value;
  },
};

// 模拟API服务
const mockUserService = {
  getList: async () => {
    await new Promise((resolve) => setTimeout(resolve, 500));

    const mockData = Array.from({ length: 10 }, (_, index) => ({
      id: index + 1,
      name: `用户${index + 1}`,
      age: Math.floor(Math.random() * 50) + 18,
      email: `user${index + 1}@example.com`,
      phone: `1${Math.floor(Math.random() * 10_000_000_000)}`,
      address: `地址${index + 1}`,
      status: Math.random() > 0.3 ? 1 : 0,
    }));

    return {
      code: 1,
      msg: '获取成功',
      data: {
        rows: mockData,
        total: mockData.length,
      },
    };
  },

  getDetail: async (id: number) => {
    await new Promise((resolve) => setTimeout(resolve, 300));
    return {
      code: 1,
      msg: '获取成功',
      data: {
        id,
        name: `用户${id}`,
        age: Math.floor(Math.random() * 50) + 18,
        email: `user${id}@example.com`,
        phone: `1${Math.floor(Math.random() * 10_000_000_000)}`,
        address: `地址${id}`,
        status: Math.random() > 0.3 ? 1 : 0,
      },
    };
  },

  create: async (data: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`创建用户: ${JSON.stringify(data)}`);
    return { code: 1, msg: '创建成功' };
  },

  update: async (id: number, data: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`更新用户: ${id}, ${JSON.stringify(data)}`);
    return { code: 1, msg: '更新成功' };
  },

  delete: async (id: number) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`删除用户: ${id}`);
    return { code: 1, msg: '删除成功' };
  },
};

// 权限选项
const permissionOptions = [
  { label: '读取用户(user:read)', value: 'user:read' },
  { label: '创建用户(user:create)', value: 'user:create' },
  { label: '更新用户(user:update)', value: 'user:update' },
  { label: '删除用户(user:delete)', value: 'user:delete' },
  { label: '导出用户(user:export)', value: 'user:export' },
  { label: '字段:姓名(user:field:name)', value: 'user:field:name' },
  { label: '字段:年龄(user:field:age)', value: 'user:field:age' },
  { label: '字段:邮箱(user:field:email)', value: 'user:field:email' },
  { label: '字段:电话(user:field:phone)', value: 'user:field:phone' },
  { label: '字段:地址(user:field:address)', value: 'user:field:address' },
];

// 用于在界面上模拟切换权限
const selectedPermissions = ref(mockUserPermissionService.permissions.value);

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

// 使用权限Hook
const {
  tableData,
  loading,
  pagination,
  formData,
  actionButtons,
  createItem,
  editItem,
  deleteItem,
  viewItem,
  initialize,
  handleTableChange,
  permissions,
  table,
  form,
  detail,
} = useAnsheng({
  // 表单配置
  formOptions: {
    title: (isEdit) => (isEdit ? '编辑用户' : '创建用户'),
    width: 700,
    defaultValues: {
      name: '',
      age: 18,
      email: '',
      phone: '',
      address: '',
      status: 1,
    },
    getDetail: mockUserService.getDetail,
    create: mockUserService.create,
    update: mockUserService.update,
    onSuccess: () => {
      message.success('操作成功');
      initialize();
    },
  },
  // 表格配置
  tableOptions: {
    api: mockUserService.getList,
    defaultPageSize: 10,
    columns: [
      { title: 'ID', dataIndex: 'id', width: 80 },
      { title: '姓名', dataIndex: 'name', width: 120 },
      { title: '年龄', dataIndex: 'age', width: 80 },
      { title: '邮箱', dataIndex: 'email', width: 180 },
      { title: '电话', dataIndex: 'phone', width: 150 },
      { title: '地址', dataIndex: 'address', width: 200 },
      {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        customRender: ({ text }) => (text === 1 ? '启用' : '禁用'),
      },
    ],
    actionButtons: [
      {
        key: 'view',
        text: '查看',
        type: 'primary',
        onClick: (record) => viewItem(record),
        icon: MdiEye,
      },
      {
        key: 'edit',
        text: '编辑',
        onClick: (record) => editItem(record.id),
        icon: MdiPencil,
      },
      {
        key: 'delete',
        text: '删除',
        danger: true,
        onClick: (record) => deleteItem(record.id),
        icon: MdiTrashCan,
        popConfirm: '确定要删除此记录吗？',
      },
    ],
  },
  // 搜索配置
  searchOptions: {
    customButtons: [
      {
        text: '新增用户',
        icon: MdiPlus,
        type: 'primary',
        onClick: () => createItem(),
      },
      {
        text: '导出',
        icon: MdiExport,
        onClick: () => {
          message.success('导出功能演示');
        },
      },
    ],
  },
  // 详情配置
  detailOptions: {
    sections: [
      {
        title: '基本信息',
        items: [
          { label: '姓名', field: 'name', value: '' },
          { label: '年龄', field: 'age', value: '' },
          { label: '邮箱', field: 'email', value: '' },
          { label: '电话', field: 'phone', value: '' },
          { label: '地址', field: 'address', value: '' },
          {
            label: '状态',
            field: 'status',
            value: '',
          },
        ],
      },
    ],
    formatters: {
      status: (val: number) => (val === 1 ? '启用' : '禁用'),
    },
  },
  deleteApi: mockUserService.delete,
  // 权限配置
  permissionOptions: {
    // 模块权限编码
    moduleCode: 'user',
    // 操作权限映射
    actionPermissions: {
      create: 'user:create',
      update: 'user:update',
      delete: 'user:delete',
      read: 'user:read',
      export: 'user:export',
    },
    // 字段权限映射
    fieldPermissions: {
      name: 'user:field:name',
      age: 'user:field:age',
      email: 'user:field:email',
      phone: 'user:field:phone',
      address: 'user:field:address',
    },
    // 管理员是否拥有所有权限
    adminHasAllPermissions: true,
    // 自定义权限检查函数
    hasPermission: mockUserPermissionService.hasPermission,
    // 自定义管理员状态
    isAdmin: mockUserPermissionService.isAdmin,
  },
});

// 监听权限变化，刷新界面
const updatePermissions = (perms) => {
  mockUserPermissionService.permissions.value = [...perms];
  selectedPermissions.value = [...perms];

  // 刷新数据和界面
  initialize();
};

// 页面加载时初始化数据
initialize();

// 创建表单模态框组件
const formComponent = defineComponent({
  setup() {
    return () =>
      form.renderFormModal([
        {
          title: '基本信息',
          content: (formData: any) => [
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '姓名',
                  name: 'name',
                  rules: [{ required: true, message: '请输入姓名' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入姓名',
                    value: formData.name,
                    'onUpdate:value': (val) => (formData.name = val),
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '年龄',
                  name: 'age',
                  rules: [{ required: true, message: '请输入年龄' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入年龄',
                    value: formData.age,
                    'onUpdate:value': (val) => (formData.age = Number(val)),
                    type: 'number',
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '邮箱',
                  name: 'email',
                  rules: [{ required: true, message: '请输入邮箱' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入邮箱',
                    value: formData.email,
                    'onUpdate:value': (val) => (formData.email = val),
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '电话',
                  name: 'phone',
                  rules: [{ required: true, message: '请输入电话' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入电话',
                    value: formData.phone,
                    'onUpdate:value': (val) => (formData.phone = val),
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '地址',
                  name: 'address',
                  rules: [{ required: true, message: '请输入地址' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入地址',
                    value: formData.address,
                    'onUpdate:value': (val) => (formData.address = val),
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '状态',
                  name: 'status',
                  rules: [{ required: true, message: '请选择状态' }],
                },
                () =>
                  h(Select, {
                    placeholder: '请选择状态',
                    value: formData.status,
                    'onUpdate:value': (val) => (formData.status = Number(val)),
                    options: statusOptions,
                  }),
              ),
            ]),
          ],
        },
      ]);
  },
});
</script>

<template>
  <div class="permission-demo">
    <!-- 权限控制面板 -->
    <Card title="权限控制面板" class="mb-4">
      <Alert
        message="此面板用于模拟用户权限变化，实际应用中权限通常由后端控制"
        type="info"
        show-icon
        class="mb-4"
      />

      <div class="mb-4">
        <div class="mb-2 flex items-center">
          <span class="mr-2">管理员：</span>
          <Switch
            v-model:checked="mockUserPermissionService.isAdmin.value"
            @change="
              mockUserPermissionService.toggleAdmin();
              initialize();
            "
          />
          <span class="ml-4 text-gray-500">
            {{
              mockUserPermissionService.isAdmin
                ? '管理员模式（拥有所有权限）'
                : '普通用户模式'
            }}
          </span>
        </div>

        <div v-if="!mockUserPermissionService.isAdmin">
          <div class="mb-2">当前权限：</div>
          <Select
            v-model:value="selectedPermissions"
            mode="multiple"
            style="width: 100%"
            placeholder="请选择权限"
            :options="permissionOptions"
            @change="updatePermissions"
          />
        </div>
      </div>

      <Divider />

      <div class="permission-status">
        <div class="mb-2 font-bold">当前权限状态：</div>
        <div class="permission-grid">
          <div class="permission-item">
            <span>创建权限：</span>
            <span
              :class="permissions.canCreate ? 'text-success' : 'text-error'"
            >
              {{ permissions.canCreate ? '✓' : '✗' }}
            </span>
          </div>
          <div class="permission-item">
            <span>读取权限：</span>
            <span :class="permissions.canRead ? 'text-success' : 'text-error'">
              {{ permissions.canRead ? '✓' : '✗' }}
            </span>
          </div>
          <div class="permission-item">
            <span>更新权限：</span>
            <span
              :class="permissions.canUpdate ? 'text-success' : 'text-error'"
            >
              {{ permissions.canUpdate ? '✓' : '✗' }}
            </span>
          </div>
          <div class="permission-item">
            <span>删除权限：</span>
            <span
              :class="permissions.canDelete ? 'text-success' : 'text-error'"
            >
              {{ permissions.canDelete ? '✓' : '✗' }}
            </span>
          </div>
          <div class="permission-item">
            <span>导出权限：</span>
            <span
              :class="permissions.canExport ? 'text-success' : 'text-error'"
            >
              {{ permissions.canExport ? '✓' : '✗' }}
            </span>
          </div>
        </div>
      </div>
    </Card>

    <!-- 用户列表 -->
    <Card title="用户列表" class="mb-4">
      <div class="mb-4 flex justify-between">
        <div>
          <Button
            v-if="permissions.canCreate"
            type="primary"
            @click="createItem"
          >
            <template #icon><MdiPlus /></template>
            新增用户
          </Button>
        </div>
        <div>
          <Button
            v-if="permissions.canExport"
            @click="() => message.success('导出功能演示')"
          >
            <template #icon><MdiExport /></template>
            导出
          </Button>
        </div>
      </div>

      <BasicTable
        :columns="table.columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :show-action="true"
        :action-buttons="actionButtons"
        @change="handleTableChange"
      />
    </Card>

    <Alert
      message="说明：删除权限或添加权限后，表格列、操作按钮和表单字段将自动根据权限进行过滤"
      type="info"
      show-icon
      class="mb-4"
    />

    <!-- 表单模态框 -->
    <component :is="formComponent" />

    <!-- 详情模态框 -->
    <DetailModal
      v-if="detail"
      v-model:visible="detail.visible"
      :sections="detail.sections"
      :title="detail.title"
      :loading="loading"
    />
  </div>
</template>

<style scoped>
.permission-demo {
  max-width: 1200px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mr-2 {
  margin-right: 8px;
}

.ml-4 {
  margin-left: 16px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.font-bold {
  font-weight: bold;
}

.text-gray-500 {
  color: #6b7280;
}

.text-success {
  color: #52c41a;
  font-weight: bold;
}

.text-error {
  color: #f5222d;
  font-weight: bold;
}

.permission-grid {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 8px;
}

.permission-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
}
</style>
