<script setup lang="ts">
import { computed, defineEmits, reactive, ref } from 'vue';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MdiChevronLeft, MdiChevronRight } from '@vben/icons';

import {
  Button,
  Card,
  DatePicker,
  Descriptions,
  Divider,
  Form,
  Input,
  InputNumber,
  Radio,
  Select,
  Steps,
  Switch,
} from 'ant-design-vue';
import dayjs from 'dayjs';

// 定义事件
const emit = defineEmits(['success']);

// 步骤数据
const currentStep = ref(0);
const stepLabels = ['基本信息', '工作信息', '附加信息', '确认提交'];

// 表单实例引用
const formRef = ref();

// 部门选项
const departmentOptions = [
  { label: '研发部', value: '研发部' },
  { label: '市场部', value: '市场部' },
  { label: '销售部', value: '销售部' },
  { label: '人事部', value: '人事部' },
];

// 职位选项和加载状态
const positionOptions = ref<{ label: string; value: string }[]>([]);
const positionLoading = ref(false);

// 表单数据
const formState = reactive({
  // 基本信息
  name: '',
  age: undefined,
  email: '',
  status: 1,

  // 工作信息
  department: undefined,
  position: undefined,
  joinDate: null,

  // 附加信息
  isVIP: false,
  points: 0,
  remarks: '',
});

// 表单验证规则
const rules = {
  // 基本信息验证规则
  name: [
    { required: true, message: '请输入姓名' },
    { min: 2, max: 20, message: '姓名长度应为2-20个字符' },
  ],
  age: [
    { required: true, message: '请输入年龄' },
    { type: 'number', min: 18, max: 70, message: '年龄应在18-70岁之间' },
  ],
  email: [
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入有效的邮箱地址' },
  ],
  status: [{ required: true, message: '请选择状态' }],

  // 工作信息验证规则
  department: [{ required: true, message: '请选择部门' }],
  position: [{ required: true, message: '请选择职位' }],
  joinDate: [{ required: true, message: '请选择入职日期' }],

  // 附加信息验证规则
  points: [
    { required: false },
    { type: 'number', min: 0, max: 1000, message: '积分范围为0-1000' },
  ],
  remarks: [{ max: 200, message: '备注最多200个字符' }],
};

// 步骤标题计算属性
const stepTitle = computed(() => stepLabels[currentStep.value]);

// 处理部门变化，加载对应职位
const handleDepartmentChange = async (value: string) => {
  if (!value) {
    positionOptions.value = [];
    formState.position = undefined;
    return;
  }

  try {
    positionLoading.value = true;
    // 模拟API请求延迟
    await new Promise((resolve) => setTimeout(resolve, 800));

    const positionMap: Record<string, string[]> = {
      研发部: ['前端工程师', '后端工程师', '全栈工程师', '技术经理', 'CTO'],
      市场部: ['市场专员', '市场经理', '品牌主管', '营销总监'],
      销售部: ['销售代表', '销售经理', '客户主管', '销售总监'],
      人事部: ['HR专员', 'HR经理', '招聘主管', '人力资源总监'],
    };

    positionOptions.value = (positionMap[value] || []).map((item) => ({
      label: item,
      value: item,
    }));
  } finally {
    positionLoading.value = false;
  }
};

// 验证当前步骤
const validateStep = async () => {
  try {
    // 根据当前步骤获取需要验证的字段
    const fieldsToValidate = getFieldsByStep(currentStep.value);

    // 验证这些字段
    await formRef.value.validateFields(fieldsToValidate);
    return true;
  } catch {
    return false;
  }
};

// 获取特定步骤的字段
const getFieldsByStep = (step: number) => {
  switch (step) {
    case 0: {
      // 基本信息
      return ['name', 'age', 'email', 'status'];
    }
    case 1: {
      // 工作信息
      return ['department', 'position', 'joinDate'];
    }
    case 2: {
      // 附加信息
      return ['points', 'remarks'];
    }
    default: {
      return [];
    }
  }
};

// 下一步
const nextStep = async () => {
  // 验证当前步骤
  const valid = await validateStep();
  if (!valid) return;

  // 如果是最后一步，则提交表单
  if (currentStep.value === stepLabels.length - 1) {
    handleSubmit();
    return;
  }

  // 否则前进到下一步
  currentStep.value += 1;
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value -= 1;
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 最终验证所有字段
    await formRef.value.validate();

    // 模拟提交到服务器的延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 触发成功事件
    emit('success', formState);

    // 重置表单和步骤
    formRef.value.resetFields();
    currentStep.value = 0;
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 格式化日期显示
const formatDate = (date: any) => {
  if (!date) return '';
  return dayjs(date).format('YYYY-MM-DD');
};

// 格式化状态显示
const formatStatus = (status: number) => {
  return status === 1 ? '启用' : '禁用';
};

// 格式化VIP显示
const formatVIP = (isVIP: boolean) => {
  return isVIP ? '是' : '否';
};
</script>

<template>
  <div class="step-form-container">
    <Steps :current="currentStep">
      <Steps.Step
        v-for="(label, index) in stepLabels"
        :key="index"
        :title="label"
      />
    </Steps>

    <Divider />

    <div class="step-content">
      <h2>{{ stepTitle }}</h2>

      <Form
        ref="formRef"
        :model="formState"
        :rules="rules"
        layout="horizontal"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 14 }"
      >
        <!-- 步骤1：基本信息 -->
        <div v-show="currentStep === 0">
          <Form.Item label="姓名" name="name">
            <Input v-model:value="formState.name" placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item label="年龄" name="age">
            <InputNumber
              v-model:value="formState.age"
              :min="18"
              :max="70"
              style="width: 100%"
            />
          </Form.Item>

          <Form.Item label="邮箱" name="email">
            <Input v-model:value="formState.email" placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item label="状态" name="status">
            <Radio.Group v-model:value="formState.status">
              <Radio :value="1">启用</Radio>
              <Radio :value="0">禁用</Radio>
            </Radio.Group>
          </Form.Item>
        </div>

        <!-- 步骤2：工作信息 -->
        <div v-show="currentStep === 1">
          <Form.Item label="部门" name="department">
            <Select
              v-model:value="formState.department"
              placeholder="请选择部门"
              @change="handleDepartmentChange"
            >
              <Select.Option
                v-for="dept in departmentOptions"
                :key="dept.value"
                :value="dept.value"
              >
                {{ dept.label }}
              </Select.Option>
            </Select>
          </Form.Item>

          <Form.Item label="职位" name="position">
            <Select
              v-model:value="formState.position"
              placeholder="请选择职位"
              :loading="positionLoading"
              :disabled="!formState.department || positionLoading"
            >
              <Select.Option
                v-for="pos in positionOptions"
                :key="pos.value"
                :value="pos.value"
              >
                {{ pos.label }}
              </Select.Option>
            </Select>
          </Form.Item>

          <Form.Item label="入职日期" name="joinDate">
            <DatePicker
              v-model:value="formState.joinDate"
              style="width: 100%"
              :disabled-date="
                (current) => current && current > dayjs().endOf('day')
              "
            />
          </Form.Item>
        </div>

        <!-- 步骤3：附加信息 -->
        <div v-show="currentStep === 2">
          <Form.Item label="VIP会员" name="isVIP">
            <Switch v-model:checked="formState.isVIP" />
          </Form.Item>

          <Form.Item label="积分" name="points">
            <InputNumber
              v-model:value="formState.points"
              :min="0"
              :max="1000"
              style="width: 100%"
              :disabled="!formState.isVIP"
            />
          </Form.Item>

          <Form.Item label="备注" name="remarks">
            <Input.TextArea
              v-model:value="formState.remarks"
              :rows="4"
              placeholder="请输入备注信息"
              :maxlength="200"
              show-count
            />
          </Form.Item>
        </div>

        <!-- 步骤4：确认信息 -->
        <div v-show="currentStep === 3">
          <Card title="信息确认" :bordered="false">
            <Descriptions bordered :column="1">
              <Descriptions.Item label="姓名">
                {{ formState.name }}
              </Descriptions.Item>
              <Descriptions.Item label="年龄">
                {{ formState.age }}
              </Descriptions.Item>
              <Descriptions.Item label="邮箱">
                {{ formState.email }}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {{ formatStatus(formState.status) }}
              </Descriptions.Item>
              <Descriptions.Item label="部门">
                {{ formState.department }}
              </Descriptions.Item>
              <Descriptions.Item label="职位">
                {{ formState.position }}
              </Descriptions.Item>
              <Descriptions.Item label="入职日期">
                {{ formatDate(formState.joinDate) }}
              </Descriptions.Item>
              <Descriptions.Item label="VIP会员">
                {{ formatVIP(formState.isVIP) }}
              </Descriptions.Item>
              <Descriptions.Item label="积分">
                {{ formState.isVIP ? formState.points : '非VIP会员' }}
              </Descriptions.Item>
              <Descriptions.Item label="备注">
                {{ formState.remarks || '无' }}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </div>
      </Form>
    </div>

    <div class="step-actions">
      <Button v-if="currentStep > 0" @click="prevStep">
        <template #icon><MdiChevronLeft /></template>
        上一步
      </Button>
      <Button type="primary" @click="nextStep">
        <template #icon>
          <MdiCheck v-if="currentStep === stepLabels.length - 1" />
          <MdiChevronRight v-else />
        </template>
        {{ currentStep === stepLabels.length - 1 ? '提交' : '下一步' }}
      </Button>
    </div>
  </div>
</template>

<style scoped>
.step-form-container {
  padding: 20px 0;
}

.step-content {
  margin: 20px 0;
  min-height: 350px;
}

.step-actions {
  margin-top: 24px;
  text-align: center;
}

.step-actions button {
  margin: 0 8px;
}
</style>
