<script setup lang="ts">
import { computed, defineEmits, defineProps, ref, watch } from 'vue';

import { MdiCog, MdiDragVertical } from '@vben/icons';

import {
  Button,
  Checkbox,
  Divider,
  Drawer,
  Select,
  Tooltip,
} from 'ant-design-vue';

// 定义属性
const props = defineProps({
  // 所有可用列
  columns: {
    type: Array,
    required: true,
  },
  // 当前显示的列
  value: {
    type: Array,
    default: () => [],
  },
});

// 定义事件
const emit = defineEmits(['update:value', 'change']);

// 抽屉显示状态
const visible = ref(false);

// 所有列和选中的列
const allColumns = ref<any[]>([]);
const selectedColumns = ref<string[]>([]);

// 固定列设置
const leftFixed = ref<null | string>(null);
const rightFixed = ref<null | string>(null);

// 拖拽相关状态
const dragItem = ref<any>(null);
const dragIndex = ref(-1);

// 初始化列数据
const initColumns = () => {
  allColumns.value = props.columns.map((col: any) => {
    const newCol = { ...col };

    // 操作列或序号列通常是必选的
    if (col.dataIndex === 'action' || col.key === 'index') {
      newCol.disabled = true;
    }

    // 检查是否有固定列
    if (col.fixed === 'left') {
      leftFixed.value = col.dataIndex;
    } else if (col.fixed === 'right') {
      rightFixed.value = col.dataIndex;
    }

    return newCol;
  });

  // 默认选中所有非禁用列
  selectedColumns.value =
    props.value.length > 0
      ? props.value.map((col: any) => col.dataIndex)
      : allColumns.value
          .filter((col: any) => !col.hidden)
          .map((col: any) => col.dataIndex);
};

// 计算当前显示的列
const displayColumns = computed(() => {
  return allColumns.value.filter((col: any) =>
    selectedColumns.value.includes(col.dataIndex),
  );
});

// 全选
const selectAll = () => {
  selectedColumns.value = allColumns.value.map((col: any) => col.dataIndex);
};

// 取消全选（只保留禁用的必选列）
const unselectAll = () => {
  selectedColumns.value = allColumns.value
    .filter((col: any) => col.disabled)
    .map((col: any) => col.dataIndex);
};

// 重置列
const resetColumns = () => {
  initColumns();
  leftFixed.value = null;
  rightFixed.value = null;
};

// 开始拖拽
const startDrag = (e: MouseEvent, item: any) => {
  dragItem.value = item;
  dragIndex.value = allColumns.value.findIndex(
    (col: any) => col.dataIndex === item.dataIndex,
  );

  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
};

// 拖拽中
const onDrag = (e: MouseEvent) => {
  // 实现拖拽逻辑
  // 这里只是一个简化的示例，实际应用中可能需要更复杂的处理
};

// 停止拖拽
const stopDrag = () => {
  dragItem.value = null;
  dragIndex.value = -1;

  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 更新固定列设置
const updateFixedColumns = () => {
  // 实际项目中这里可能需要更复杂的逻辑
  // 比如确保固定列相邻等
};

// 应用设置
const applySettings = () => {
  // 构建新的列配置
  const newColumns = allColumns.value
    .filter((col: any) => selectedColumns.value.includes(col.dataIndex))
    .map((col: any) => {
      const newCol = { ...col };

      // 设置固定列
      if (col.dataIndex === leftFixed.value) {
        newCol.fixed = 'left';
      } else if (col.dataIndex === rightFixed.value) {
        newCol.fixed = 'right';
      } else {
        delete newCol.fixed;
      }

      return newCol;
    });

  // 触发更新事件
  emit('update:value', newColumns);
  emit('change', newColumns);

  // 关闭抽屉
  visible.value = false;
};

// 监听列变化
watch(
  () => props.columns,
  () => {
    initColumns();
  },
  { deep: true },
);

// 初始化
initColumns();
</script>

<template>
  <div class="column-setting-wrapper">
    <Tooltip title="列设置">
      <Button type="text" @click="visible = true">
        <template #icon><MdiCog /></template>
      </Button>
    </Tooltip>

    <Drawer
      title="列设置"
      placement="right"
      :width="300"
      :visible="visible"
      @close="visible = false"
    >
      <div class="drawer-content">
        <div class="action-bar">
          <Button type="link" @click="selectAll">全选</Button>
          <Button type="link" @click="unselectAll">取消全选</Button>
          <Button type="link" @click="resetColumns">重置</Button>
        </div>

        <Divider style="margin: 8px 0" />

        <Checkbox.Group v-model:value="selectedColumns" class="checkbox-list">
          <div
            v-for="item in allColumns"
            :key="item.dataIndex"
            class="checkbox-item"
          >
            <Checkbox :value="item.dataIndex" :disabled="item.disabled">
              {{ item.title }}
            </Checkbox>

            <div class="draggable-handle" @mousedown="startDrag($event, item)">
              <MdiDragVertical />
            </div>
          </div>
        </Checkbox.Group>

        <div class="fixed-columns">
          <Divider orientation="left">列固定</Divider>

          <div class="fixed-item">
            <span>左侧固定：</span>
            <Select
              v-model:value="leftFixed"
              style="width: 120px"
              @change="updateFixedColumns"
            >
              <Select.Option :value="null">不固定</Select.Option>
              <Select.Option
                v-for="col in displayColumns"
                :key="col.dataIndex"
                :value="col.dataIndex"
              >
                {{ col.title }}
              </Select.Option>
            </Select>
          </div>

          <div class="fixed-item">
            <span>右侧固定：</span>
            <Select
              v-model:value="rightFixed"
              style="width: 120px"
              @change="updateFixedColumns"
            >
              <Select.Option :value="null">不固定</Select.Option>
              <Select.Option
                v-for="col in displayColumns"
                :key="col.dataIndex"
                :value="col.dataIndex"
              >
                {{ col.title }}
              </Select.Option>
            </Select>
          </div>
        </div>
      </div>

      <div class="drawer-footer">
        <Button @click="visible = false">取消</Button>
        <Button type="primary" @click="applySettings">应用</Button>
      </div>
    </Drawer>
  </div>
</template>

<style scoped>
.column-setting-wrapper {
  display: inline-block;
}

.drawer-content {
  height: calc(100% - 55px);
  overflow-y: auto;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.checkbox-list {
  display: block;
  width: 100%;
}

.checkbox-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.checkbox-item:hover {
  background-color: #f5f5f5;
}

.draggable-handle {
  cursor: move;
  color: #999;
}

.fixed-columns {
  margin-top: 16px;
}

.fixed-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  left: 0;
  background: #fff;
  border-radius: 0 0 4px 4px;
}

.drawer-footer button:not(:first-child) {
  margin-left: 8px;
}
</style>
