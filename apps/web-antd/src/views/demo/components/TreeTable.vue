<script setup lang="ts">
import { defineEmits, onMounted, ref } from 'vue';

import { <PERSON>di<PERSON><PERSON><PERSON>, <PERSON>diEye, MdiPencil } from '@vben/icons';

import { message } from 'ant-design-vue';

import { BasicTable, DetailModal } from '#/hooks/useAnsheng';

// 自定义事件
const emit = defineEmits(['edit', 'delete', 'view']);

// 表格加载状态
const loading = ref(false);

// 详情弹窗状态
const detailVisible = ref(false);
const currentRecord = ref<any>(null);

// 详情配置
const detailOptions = {
  sections: [
    {
      title: '基本信息',
      items: [
        { field: 'key', value: 'ID' },
        { field: 'name', value: '名称' },
        { field: 'status', value: '状态' },
      ],
    },
    {
      title: '其他信息',
      items: [
        { field: 'dept', value: '部门' },
        { field: 'createTime', value: '创建时间' },
        { field: 'description', value: '描述' },
      ],
    },
  ],
};

// 表格列定义
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '部门',
    dataIndex: 'dept',
    key: 'dept',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
  },
];

// 树形数据
const treeData = ref<any[]>([]);

// 加载树形数据
const loadTreeData = async () => {
  loading.value = true;
  try {
    // 模拟API请求延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 模拟树形数据
    treeData.value = [
      {
        key: 1,
        name: '总公司',
        dept: '总部',
        status: 1,
        createTime: '2022-01-01',
        description: '公司总部',
        children: [
          {
            key: 11,
            name: '北京分公司',
            dept: '分部',
            status: 1,
            createTime: '2022-02-01',
            description: '北京分公司负责华北地区业务',
            children: [
              {
                key: 111,
                name: '研发部',
                dept: '研发',
                status: 1,
                createTime: '2022-02-15',
                description: '北京研发中心',
              },
              {
                key: 112,
                name: '销售部',
                dept: '销售',
                status: 1,
                createTime: '2022-02-15',
                description: '北京销售中心',
              },
            ],
          },
          {
            key: 12,
            name: '上海分公司',
            dept: '分部',
            status: 1,
            createTime: '2022-03-01',
            description: '上海分公司负责华东地区业务',
            children: [
              {
                key: 121,
                name: '研发部',
                dept: '研发',
                status: 1,
                createTime: '2022-03-15',
                description: '上海研发中心',
              },
              {
                key: 122,
                name: '销售部',
                dept: '销售',
                status: 0,
                createTime: '2022-03-15',
                description: '上海销售中心',
              },
            ],
          },
          {
            key: 13,
            name: '广州分公司',
            dept: '分部',
            status: 0,
            createTime: '2022-04-01',
            description: '广州分公司负责华南地区业务',
            children: [
              {
                key: 131,
                name: '研发部',
                dept: '研发',
                status: 0,
                createTime: '2022-04-15',
                description: '广州研发中心',
              },
              {
                key: 132,
                name: '销售部',
                dept: '销售',
                status: 1,
                createTime: '2022-04-15',
                description: '广州销售中心',
              },
            ],
          },
        ],
      },
    ];
  } finally {
    loading.value = false;
  }
};

// 处理查看
const handleView = (record: any) => {
  currentRecord.value = record;
  detailVisible.value = true;
  emit('view', record);
};

// 处理编辑
const handleEdit = (record: any) => {
  emit('edit', record);
};

// 处理删除
const handleDelete = async (record: any) => {
  try {
    // 模拟API请求
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.success(`删除成功: ${record.name}`);
    emit('delete', record);
    // 实际应用中应该刷新数据
    // loadTreeData();
  } catch {
    message.error('删除失败');
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadTreeData();
});

// 暴露方法给父组件
defineExpose({
  loadTreeData,
});
</script>

<template>
  <div class="tree-table-container">
    <BasicTable
      :columns="columns"
      :data-source="treeData"
      :row-key="(record) => record.key"
      :loading="loading"
      :pagination="false"
      :expandable="{
        defaultExpandAllRows: true,
      }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <span>{{ record.name }}</span>
        </template>

        <template v-else-if="column.dataIndex === 'status'">
          <a-tag :color="record.status === 1 ? 'success' : 'error'">
            {{ record.status === 1 ? '启用' : '禁用' }}
          </a-tag>
        </template>

        <template v-else-if="column.dataIndex === 'action'">
          <a-space>
            <a @click="handleView(record)">
              <MdiEye class="icon" />
              查看
            </a>
            <a-divider type="vertical" />
            <a @click="handleEdit(record)">
              <MdiPencil class="icon" />
              编辑
            </a>
            <a-divider type="vertical" />
            <a-popconfirm
              title="确定要删除此项吗?"
              @confirm="handleDelete(record)"
            >
              <a class="text-danger">
                <MdiDelete class="icon" />
                删除
              </a>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </BasicTable>

    <!-- 详情弹窗 -->
    <DetailModal
      v-model:visible="detailVisible"
      :detail-options="detailOptions"
      :record="currentRecord"
    />
  </div>
</template>

<style scoped>
.tree-table-container {
  width: 100%;
}

.text-danger {
  color: #ff4d4f;
}

.icon {
  margin-right: 4px;
}
</style>
