<script setup lang="ts">
import { reactive, ref } from 'vue';

import { MdiDelete, MdiPlus } from '@vben/icons';

import { Button, Card, message, Space } from 'ant-design-vue';

import { useDynamicForm } from '#/hooks/useAnsheng';

// 部门选项
const departmentOptions = [
  { label: '研发部', value: '研发部' },
  { label: '市场部', value: '市场部' },
  { label: '销售部', value: '销售部' },
  { label: '人事部', value: '人事部' },
];

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

// 创建一个基础表单
const {
  formData: basicFormData,
  formRef: basicFormRef,
  renderDynamicForm: renderBasicForm,
  handleSubmit: submitBasicForm,
  resetForm: resetBasicForm,
} = useDynamicForm({
  groups: [
    {
      title: '基本信息',
      description: '请填写用户的基本信息',
      fields: [
        {
          field: 'name',
          label: '姓名',
          type: 'input',
          required: true,
          placeholder: '请输入姓名',
        },
        {
          field: 'age',
          label: '年龄',
          type: 'number',
          required: true,
          min: 18,
          max: 100,
        },
        {
          field: 'email',
          label: '邮箱',
          type: 'input',
          required: true,
          rules: [
            { required: true, message: '请输入邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' },
          ],
        },
      ],
    },
    {
      title: '工作信息',
      fields: [
        {
          field: 'department',
          label: '部门',
          type: 'select',
          options: departmentOptions,
          required: true,
        },
        {
          field: 'position',
          label: '职位',
          type: 'input',
          required: true,
        },
        {
          field: 'status',
          label: '状态',
          type: 'select',
          options: statusOptions,
          defaultValue: 1,
        },
      ],
    },
  ],
  defaultValues: {
    name: '',
    age: 25,
    email: '',
    department: '',
    position: '',
    status: 1,
  },
  api: {
    create: async (data) => {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500));
      console.log('创建用户:', data);
      return { code: 1, msg: '创建成功' };
    },
    update: async (id, data) => {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500));
      console.log('更新用户:', id, data);
      return { code: 1, msg: '更新成功' };
    },
  },
  onSuccess: () => {
    message.success('保存成功');
  },
});

// 创建一个联动表单
const {
  formData: linkedFormData,
  formRef: linkedFormRef,
  renderDynamicForm: renderLinkedForm,
  handleSubmit: submitLinkedForm,
  resetForm: resetLinkedForm,
} = useDynamicForm({
  groups: [
    {
      title: '联动表单示例',
      description: '演示表单字段联动',
      fields: [
        {
          field: 'userType',
          label: '用户类型',
          type: 'select',
          options: [
            { label: '个人用户', value: 'personal' },
            { label: '企业用户', value: 'company' },
          ],
          required: true,
        },
        {
          field: 'companyName',
          label: '企业名称',
          type: 'input',
          required: true,
          visible: (formData) => formData.userType === 'company',
          dependencies: ['userType'],
        },
        {
          field: 'companySize',
          label: '企业规模',
          type: 'select',
          options: [
            { label: '小型企业', value: 'small' },
            { label: '中型企业', value: 'medium' },
            { label: '大型企业', value: 'large' },
          ],
          visible: (formData) => formData.userType === 'company',
          dependencies: ['userType'],
        },
        {
          field: 'idNumber',
          label: '身份证号',
          type: 'input',
          required: true,
          visible: (formData) => formData.userType === 'personal',
          dependencies: ['userType'],
        },
        {
          field: 'age',
          label: '年龄',
          type: 'number',
          visible: (formData) => formData.userType === 'personal',
          dependencies: ['userType'],
        },
        {
          field: 'newsletter',
          label: '订阅通讯',
          type: 'switch',
          defaultValue: false,
        },
        {
          field: 'email',
          label: '邮箱地址',
          type: 'input',
          required: (formData) => formData.newsletter === true,
          visible: (formData) => formData.newsletter === true,
          dependencies: ['newsletter'],
          rules: [{ type: 'email', message: '请输入有效的邮箱地址' }],
        },
      ],
    },
  ],
  defaultValues: {
    userType: 'personal',
    companyName: '',
    companySize: 'medium',
    idNumber: '',
    age: 25,
    newsletter: false,
    email: '',
  },
  api: {
    create: async (data) => {
      await new Promise((resolve) => setTimeout(resolve, 500));
      console.log('保存数据:', data);
      return { code: 1, msg: '保存成功' };
    },
  },
  onSuccess: () => {
    message.success('保存成功');
  },
});

// 动态添加表单字段示例
const dynamicFieldsGroups = reactive([
  {
    title: '基本信息',
    fields: [
      {
        field: 'name',
        label: '姓名',
        type: 'input',
        required: true,
      },
    ],
  },
  {
    title: '联系人',
    fields: [
      {
        field: 'contacts.0.name',
        label: '联系人1姓名',
        type: 'input',
        required: true,
      },
      {
        field: 'contacts.0.phone',
        label: '联系人1电话',
        type: 'input',
        required: true,
      },
    ],
  },
]);

const contactCount = ref(1);

// 添加联系人
const addContact = () => {
  const index = contactCount.value;
  dynamicFieldsGroups[1].fields.push(
    {
      field: `contacts.${index}.name`,
      label: `联系人${index + 1}姓名`,
      type: 'input',
      required: true,
    },
    {
      field: `contacts.${index}.phone`,
      label: `联系人${index + 1}电话`,
      type: 'input',
      required: true,
    },
  );
  contactCount.value++;
};

// 移除最后一个联系人
const removeContact = () => {
  if (contactCount.value <= 1) return;
  contactCount.value--;
  dynamicFieldsGroups[1].fields.pop();
  dynamicFieldsGroups[1].fields.pop();
};

// 动态字段表单
const {
  formData: dynamicFieldsData,
  formRef: dynamicFieldsRef,
  renderDynamicForm: renderDynamicFieldsForm,
  handleSubmit: submitDynamicFieldsForm,
  resetForm: resetDynamicFieldsForm,
  updateFormGroups,
} = useDynamicForm({
  groups: dynamicFieldsGroups,
  defaultValues: {
    name: '',
    contacts: [{ name: '', phone: '' }],
  },
  api: {
    create: async (data) => {
      await new Promise((resolve) => setTimeout(resolve, 500));
      console.log('保存数据:', data);
      return { code: 1, msg: '保存成功' };
    },
  },
  onSuccess: () => {
    message.success('保存成功');
  },
});

// 当动态添加/删除字段时，更新表单
const updateDynamicForm = () => {
  updateFormGroups(dynamicFieldsGroups);
};
</script>

<template>
  <div class="dynamic-form-demo">
    <!-- 基础表单示例 -->
    <Card title="基础表单" class="mb-4">
      <component :is="renderBasicForm()" />

      <Space class="mt-4">
        <Button @click="resetBasicForm">重置</Button>
        <Button type="primary" @click="submitBasicForm">提交</Button>
      </Space>
    </Card>

    <!-- 字段联动示例 -->
    <Card title="字段联动表单" class="mb-4">
      <component :is="renderLinkedForm()" />

      <Space class="mt-4">
        <Button @click="resetLinkedForm">重置</Button>
        <Button type="primary" @click="submitLinkedForm">提交</Button>
      </Space>
    </Card>

    <!-- 动态添加字段示例 -->
    <Card title="动态添加字段" class="mb-4">
      <div class="mb-4">
        <Space>
          <Button
            type="primary"
            @click="
              () => {
                addContact();
                updateDynamicForm();
              }
            "
          >
            <template #icon><MdiPlus /></template>
            添加联系人
          </Button>
          <Button
            danger
            :disabled="contactCount <= 1"
            @click="
              () => {
                removeContact();
                updateDynamicForm();
              }
            "
          >
            <template #icon><MdiDelete /></template>
            移除联系人
          </Button>
        </Space>
      </div>

      <component :is="renderDynamicFieldsForm()" />

      <Space class="mt-4">
        <Button @click="resetDynamicFieldsForm">重置</Button>
        <Button type="primary" @click="submitDynamicFieldsForm">提交</Button>
      </Space>
    </Card>
  </div>
</template>

<style scoped>
.dynamic-form-demo {
  max-width: 1200px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
