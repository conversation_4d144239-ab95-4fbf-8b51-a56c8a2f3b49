<script setup lang="ts">
import { h } from 'vue';

import {
  <PERSON>ton,
  Card,
  DatePicker,
  FormItem,
  Input,
  InputNumber,
  message,
  Select,
  Space,
} from 'ant-design-vue';

import { useStepForm } from '#/hooks/useAnsheng';

// 部门选项
const departmentOptions = [
  { label: '研发部', value: '研发部' },
  { label: '市场部', value: '市场部' },
  { label: '销售部', value: '销售部' },
  { label: '人事部', value: '人事部' },
];

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

// 使用分步表单
const {
  formData,
  currentStep,
  isFirstStep,
  isLastStep,
  nextStep,
  prevStep,
  submitForm,
  resetForm,
  renderStepForm,
  formRef,
} = useStepForm({
  steps: [
    {
      title: '基本信息',
      description: '填写用户基本信息',
      fields: ['name', 'age', 'gender'],
      validator: async (formData) => {
        if (!formData.name) {
          message.error('请填写姓名');
          return false;
        }

        if (!formData.age || formData.age < 18) {
          message.error('请填写正确的年龄（不小于18岁）');
          return false;
        }

        return true;
      },
    },
    {
      title: '联系方式',
      description: '填写联系方式信息',
      fields: ['email', 'phone', 'address'],
      validator: async (formData) => {
        if (!formData.email) {
          message.error('请填写邮箱');
          return false;
        }

        if (!formData.phone) {
          message.error('请填写电话');
          return false;
        }

        return true;
      },
    },
    {
      title: '工作信息',
      description: '填写工作相关信息',
      fields: ['department', 'position', 'salary', 'joinDate'],
      validator: async (formData) => {
        if (!formData.department) {
          message.error('请选择部门');
          return false;
        }

        if (!formData.position) {
          message.error('请填写职位');
          return false;
        }

        return true;
      },
    },
  ],
  defaultValues: {
    name: '',
    age: 25,
    gender: 'male',
    email: '',
    phone: '',
    address: '',
    department: '',
    position: '',
    salary: 0,
    joinDate: '',
  },
  onFinish: async (values) => {
    // 模拟API提交
    await new Promise((resolve) => setTimeout(resolve, 1000));
    console.log('表单提交成功', values);
    message.success('表单提交成功');
    return { code: 1, msg: '操作成功' };
  },
});

// 渲染步骤内容
const renderStepContent = () => {
  switch (currentStep.value) {
    case 0: {
      return h('div', {}, [
        h(
          FormItem,
          {
            label: '姓名',
            name: 'name',
            rules: [{ required: true, message: '请输入姓名' }],
          },
          () =>
            h(Input, {
              value: formData.name,
              'onUpdate:value': (val) => (formData.name = val),
              placeholder: '请输入姓名',
            }),
        ),
        h(
          FormItem,
          {
            label: '年龄',
            name: 'age',
            rules: [{ required: true, message: '请输入年龄' }],
          },
          () =>
            h(InputNumber, {
              value: formData.age,
              'onUpdate:value': (val) => (formData.age = val),
              placeholder: '请输入年龄',
              min: 18,
              max: 100,
              style: 'width: 100%',
            }),
        ),
        h(FormItem, { label: '性别', name: 'gender' }, () =>
          h(Select, {
            value: formData.gender,
            'onUpdate:value': (val) => (formData.gender = val),
            options: [
              { label: '男', value: 'male' },
              { label: '女', value: 'female' },
            ],
            placeholder: '请选择性别',
            style: 'width: 100%',
          }),
        ),
      ]);
    }
    case 1: {
      return h('div', {}, [
        h(
          FormItem,
          {
            label: '邮箱',
            name: 'email',
            rules: [
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ],
          },
          () =>
            h(Input, {
              value: formData.email,
              'onUpdate:value': (val) => (formData.email = val),
              placeholder: '请输入邮箱',
            }),
        ),
        h(
          FormItem,
          {
            label: '电话',
            name: 'phone',
            rules: [{ required: true, message: '请输入电话' }],
          },
          () =>
            h(Input, {
              value: formData.phone,
              'onUpdate:value': (val) => (formData.phone = val),
              placeholder: '请输入电话',
            }),
        ),
        h(FormItem, { label: '地址', name: 'address' }, () =>
          h(Input, {
            value: formData.address,
            'onUpdate:value': (val) => (formData.address = val),
            placeholder: '请输入地址',
          }),
        ),
      ]);
    }
    case 2: {
      return h('div', {}, [
        h(
          FormItem,
          {
            label: '部门',
            name: 'department',
            rules: [{ required: true, message: '请选择部门' }],
          },
          () =>
            h(Select, {
              value: formData.department,
              'onUpdate:value': (val) => (formData.department = val),
              options: departmentOptions,
              placeholder: '请选择部门',
              style: 'width: 100%',
            }),
        ),
        h(
          FormItem,
          {
            label: '职位',
            name: 'position',
            rules: [{ required: true, message: '请输入职位' }],
          },
          () =>
            h(Input, {
              value: formData.position,
              'onUpdate:value': (val) => (formData.position = val),
              placeholder: '请输入职位',
            }),
        ),
        h(FormItem, { label: '薪资', name: 'salary' }, () =>
          h(InputNumber, {
            value: formData.salary,
            'onUpdate:value': (val) => (formData.salary = val),
            placeholder: '请输入薪资',
            style: 'width: 100%',
            formatter: (value) =>
              `￥ ${value}`.replaceAll(/\B(?=(\d{3})+(?!\d))/g, ','),
            parser: (value) => value.replaceAll(/￥\s?|(,*)/g, ''),
          }),
        ),
        h(FormItem, { label: '入职日期', name: 'joinDate' }, () =>
          h(DatePicker, {
            value: formData.joinDate,
            'onUpdate:value': (val) => (formData.joinDate = val),
            placeholder: '请选择入职日期',
            style: 'width: 100%',
          }),
        ),
      ]);
    }
    default: {
      return null;
    }
  }
};

// 第二个分步表单示例 - 分步流程
const {
  formData: wizardData,
  currentStep: wizardStep,
  isFirstStep: isWizardFirstStep,
  isLastStep: isWizardLastStep,
  nextStep: wizardNext,
  prevStep: wizardPrev,
  submitForm: submitWizard,
  renderStepForm: renderWizardForm,
} = useStepForm({
  steps: [
    {
      title: '选择项目类型',
      description: '选择要创建的项目类型',
      fields: ['projectType'],
    },
    {
      title: '项目信息',
      description: '填写项目基本信息',
      fields: ['projectName', 'projectDescription', 'startDate', 'endDate'],
    },
    {
      title: '团队成员',
      description: '添加项目团队成员',
      fields: ['teamMembers', 'manager'],
    },
    {
      title: '确认信息',
      description: '确认项目信息',
      fields: [],
    },
  ],
  defaultValues: {
    projectType: '',
    projectName: '',
    projectDescription: '',
    startDate: '',
    endDate: '',
    teamMembers: [],
    manager: '',
  },
  onFinish: async (values) => {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    message.success('项目创建成功');
    return { code: 1, msg: '操作成功' };
  },
});

// 渲染向导内容
const renderWizardContent = () => {
  switch (wizardStep.value) {
    case 0: {
      return h('div', { class: 'wizard-step' }, [
        h('h3', { class: 'wizard-title' }, '请选择项目类型'),
        h('div', { class: 'wizard-options' }, [
          h(
            'div',
            {
              class: [
                'wizard-option',
                wizardData.projectType === 'web'
                  ? 'wizard-option-selected'
                  : '',
              ],
              onClick: () => (wizardData.projectType = 'web'),
            },
            [
              h('div', { class: 'wizard-option-icon' }, '🌐'),
              h('div', { class: 'wizard-option-title' }, '网站项目'),
              h(
                'div',
                { class: 'wizard-option-desc' },
                '创建Web应用项目，包括前端、后端等',
              ),
            ],
          ),
          h(
            'div',
            {
              class: [
                'wizard-option',
                wizardData.projectType === 'mobile'
                  ? 'wizard-option-selected'
                  : '',
              ],
              onClick: () => (wizardData.projectType = 'mobile'),
            },
            [
              h('div', { class: 'wizard-option-icon' }, '📱'),
              h('div', { class: 'wizard-option-title' }, '移动应用'),
              h(
                'div',
                { class: 'wizard-option-desc' },
                '创建iOS/Android移动应用项目',
              ),
            ],
          ),
          h(
            'div',
            {
              class: [
                'wizard-option',
                wizardData.projectType === 'desktop'
                  ? 'wizard-option-selected'
                  : '',
              ],
              onClick: () => (wizardData.projectType = 'desktop'),
            },
            [
              h('div', { class: 'wizard-option-icon' }, '💻'),
              h('div', { class: 'wizard-option-title' }, '桌面应用'),
              h(
                'div',
                { class: 'wizard-option-desc' },
                '创建Windows/Mac/Linux桌面应用项目',
              ),
            ],
          ),
        ]),
      ]);
    }
    case 1: {
      return h('div', { class: 'wizard-step' }, [
        h('h3', { class: 'wizard-title' }, '填写项目信息'),
        h(
          FormItem,
          {
            label: '项目名称',
            rules: [{ required: true, message: '请输入项目名称' }],
          },
          () =>
            h(Input, {
              value: wizardData.projectName,
              'onUpdate:value': (val) => (wizardData.projectName = val),
              placeholder: '请输入项目名称',
            }),
        ),
        h(FormItem, { label: '项目描述' }, () =>
          h(Input.TextArea, {
            value: wizardData.projectDescription,
            'onUpdate:value': (val) => (wizardData.projectDescription = val),
            placeholder: '请输入项目描述',
            rows: 4,
          }),
        ),
        h('div', { class: 'form-row' }, [
          h('div', { class: 'form-col' }, [
            h(FormItem, { label: '开始日期' }, () =>
              h(DatePicker, {
                value: wizardData.startDate,
                'onUpdate:value': (val) => (wizardData.startDate = val),
                placeholder: '请选择开始日期',
                style: 'width: 100%',
              }),
            ),
          ]),
          h('div', { class: 'form-col' }, [
            h(FormItem, { label: '结束日期' }, () =>
              h(DatePicker, {
                value: wizardData.endDate,
                'onUpdate:value': (val) => (wizardData.endDate = val),
                placeholder: '请选择结束日期',
                style: 'width: 100%',
              }),
            ),
          ]),
        ]),
      ]);
    }
    case 2: {
      return h('div', { class: 'wizard-step' }, [
        h('h3', { class: 'wizard-title' }, '设置团队成员'),
        h(FormItem, { label: '项目经理' }, () =>
          h(Select, {
            value: wizardData.manager,
            'onUpdate:value': (val) => (wizardData.manager = val),
            placeholder: '请选择项目经理',
            style: 'width: 100%',
            options: [
              { label: '张三', value: 'zhangsan' },
              { label: '李四', value: 'lisi' },
              { label: '王五', value: 'wangwu' },
            ],
          }),
        ),
        h(FormItem, { label: '团队成员' }, () =>
          h(Select, {
            value: wizardData.teamMembers,
            'onUpdate:value': (val) => (wizardData.teamMembers = val),
            placeholder: '请选择团队成员',
            style: 'width: 100%',
            mode: 'multiple',
            options: [
              { label: '张三', value: 'zhangsan' },
              { label: '李四', value: 'lisi' },
              { label: '王五', value: 'wangwu' },
              { label: '赵六', value: 'zhaoliu' },
              { label: '钱七', value: 'qianqi' },
            ],
          }),
        ),
      ]);
    }
    case 3: {
      return h('div', { class: 'wizard-step wizard-confirm' }, [
        h('h3', { class: 'wizard-title' }, '确认项目信息'),
        h('div', { class: 'confirm-item' }, [
          h('div', { class: 'confirm-label' }, '项目类型：'),
          h(
            'div',
            { class: 'confirm-value' },
            wizardData.projectType === 'web'
              ? '网站项目'
              : wizardData.projectType === 'mobile'
                ? '移动应用'
                : wizardData.projectType === 'desktop'
                  ? '桌面应用'
                  : '未选择',
          ),
        ]),
        h('div', { class: 'confirm-item' }, [
          h('div', { class: 'confirm-label' }, '项目名称：'),
          h(
            'div',
            { class: 'confirm-value' },
            wizardData.projectName || '未填写',
          ),
        ]),
        h('div', { class: 'confirm-item' }, [
          h('div', { class: 'confirm-label' }, '项目描述：'),
          h(
            'div',
            { class: 'confirm-value' },
            wizardData.projectDescription || '未填写',
          ),
        ]),
        h('div', { class: 'confirm-item' }, [
          h('div', { class: 'confirm-label' }, '开始日期：'),
          h(
            'div',
            { class: 'confirm-value' },
            wizardData.startDate
              ? wizardData.startDate.format('YYYY-MM-DD')
              : '未设置',
          ),
        ]),
        h('div', { class: 'confirm-item' }, [
          h('div', { class: 'confirm-label' }, '结束日期：'),
          h(
            'div',
            { class: 'confirm-value' },
            wizardData.endDate
              ? wizardData.endDate.format('YYYY-MM-DD')
              : '未设置',
          ),
        ]),
        h('div', { class: 'confirm-item' }, [
          h('div', { class: 'confirm-label' }, '项目经理：'),
          h(
            'div',
            { class: 'confirm-value' },
            wizardData.manager === 'zhangsan'
              ? '张三'
              : wizardData.manager === 'lisi'
                ? '李四'
                : wizardData.manager === 'wangwu'
                  ? '王五'
                  : '未选择',
          ),
        ]),
        h('div', { class: 'confirm-item' }, [
          h('div', { class: 'confirm-label' }, '团队成员：'),
          h(
            'div',
            { class: 'confirm-value' },
            wizardData.teamMembers && wizardData.teamMembers.length > 0
              ? wizardData.teamMembers
                  .map((m) =>
                    m === 'zhangsan'
                      ? '张三'
                      : m === 'lisi'
                        ? '李四'
                        : m === 'wangwu'
                          ? '王五'
                          : m === 'zhaoliu'
                            ? '赵六'
                            : m === 'qianqi'
                              ? '钱七'
                              : m,
                  )
                  .join('、')
              : '未选择',
          ),
        ]),
      ]);
    }
    default: {
      return null;
    }
  }
};
</script>

<template>
  <div class="step-form-demo">
    <!-- 标准分步表单 -->
    <Card title="标准分步表单" class="mb-6">
      <component :is="renderStepForm(renderStepContent)" />

      <div class="step-form-footer">
        <Space>
          <Button v-if="!isFirstStep" @click="prevStep">上一步</Button>
          <Button v-if="!isLastStep" type="primary" @click="nextStep">
            下一步
          </Button>
          <Button v-if="isLastStep" type="primary" @click="submitForm">
            提交
          </Button>
          <Button @click="resetForm">重置</Button>
        </Space>
      </div>
    </Card>

    <!-- 向导式表单 -->
    <Card title="向导式表单" class="mb-6">
      <component :is="renderWizardForm(renderWizardContent)" />

      <div class="step-form-footer">
        <Space>
          <Button v-if="!isWizardFirstStep" @click="wizardPrev">上一步</Button>
          <Button
            v-if="!isWizardLastStep"
            type="primary"
            @click="wizardNext"
            :disabled="wizardStep === 0 && !wizardData.projectType"
          >
            下一步
          </Button>
          <Button v-if="isWizardLastStep" type="primary" @click="submitWizard">
            确认创建
          </Button>
        </Space>
      </div>
    </Card>
  </div>
</template>

<style scoped>
.step-form-demo {
  max-width: 1200px;
}

.mb-6 {
  margin-bottom: 24px;
}

.step-form-footer {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

/* 向导式表单样式 */
.wizard-step {
  padding: 16px 0;
}

.wizard-title {
  margin-bottom: 24px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.wizard-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.wizard-option {
  flex: 1;
  min-width: 220px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.wizard-option:hover {
  border-color: #1890ff;
}

.wizard-option-selected {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.wizard-option-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.wizard-option-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.wizard-option-desc {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

.form-row {
  display: flex;
  margin: 0 -8px;
}

.form-col {
  flex: 1;
  padding: 0 8px;
}

.wizard-confirm {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 16px;
}

.confirm-item {
  display: flex;
  margin-bottom: 12px;
}

.confirm-label {
  width: 100px;
  color: rgba(0, 0, 0, 0.65);
  flex-shrink: 0;
}

.confirm-value {
  flex: 1;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
}
</style>
