<script setup lang="ts">
import { defineEmits, reactive, ref, watch } from 'vue';

import {
  MdiChevronDown,
  MdiChevronUp,
  MdiExport,
  MdiMagnify,
  MdiPlus,
  MdiRefresh,
  MdiUpload,
} from '@vben/icons';

import {
  Button,
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  Row,
  Select,
} from 'ant-design-vue';

// 自定义事件
const emit = defineEmits([
  'search',
  'reset',
  'refresh',
  'add',
  'export',
  'import',
]);

// 表单引用
const formRef = ref();

// 高级搜索状态
const advanced = ref(false);

// 部门选项
const departmentOptions = [
  { label: '研发部', value: '研发部' },
  { label: '市场部', value: '市场部' },
  { label: '销售部', value: '销售部' },
  { label: '人事部', value: '人事部' },
];

// 职位选项和加载状态
const positionOptions = ref<{ label: string; value: string }[]>([]);
const positionLoading = ref(false);

// 表单数据
const formState = reactive({
  keyword: '',
  status: undefined,
  department: undefined,
  position: undefined,
  isVIP: undefined,
  pointsRange: [0, 1000],
  joinDateRange: [],
  joinDateStart: '',
  joinDateEnd: '',
  createTimeRange: [],
  createTimeStart: '',
  createTimeEnd: '',
});

// 切换高级搜索
const toggleAdvanced = () => {
  advanced.value = !advanced.value;
};

// 处理部门变化，加载对应职位
const handleDepartmentChange = async (value: string) => {
  if (!value) {
    positionOptions.value = [];
    formState.position = undefined;
    return;
  }

  try {
    positionLoading.value = true;
    // 模拟API请求延迟
    await new Promise((resolve) => setTimeout(resolve, 800));

    const positionMap: Record<string, string[]> = {
      研发部: ['前端工程师', '后端工程师', '全栈工程师', '技术经理', 'CTO'],
      市场部: ['市场专员', '市场经理', '品牌主管', '营销总监'],
      销售部: ['销售代表', '销售经理', '客户主管', '销售总监'],
      人事部: ['HR专员', 'HR经理', '招聘主管', '人力资源总监'],
    };

    positionOptions.value = (positionMap[value] || []).map((item) => ({
      label: item,
      value: item,
    }));

    // 如果之前选择的职位不在新的选项列表中，清空选择
    if (
      formState.position &&
      !positionOptions.value.some(
        (option) => option.value === formState.position,
      )
    ) {
      formState.position = undefined;
    }
  } finally {
    positionLoading.value = false;
  }
};

// 处理日期范围变化
const handleDateRangeChange = (dates: any, dateStrings: string[]) => {
  if (dates) {
    formState.joinDateStart = dateStrings[0];
    formState.joinDateEnd = dateStrings[1];
  } else {
    formState.joinDateStart = '';
    formState.joinDateEnd = '';
  }
};

// 处理时间范围变化
const handleTimeRangeChange = (dates: any, dateStrings: string[]) => {
  if (dates) {
    formState.createTimeStart = dateStrings[0];
    formState.createTimeEnd = dateStrings[1];
  } else {
    formState.createTimeStart = '';
    formState.createTimeEnd = '';
  }
};

// 搜索处理
const handleSearch = () => {
  // 收集表单数据，转换为接口需要的格式
  const params = {
    keyword: formState.keyword,
    status: formState.status,
    department: formState.department,
    position: formState.position,
    isVIP: formState.isVIP,
    pointsMin: formState.isVIP === true ? formState.pointsRange[0] : undefined,
    pointsMax: formState.isVIP === true ? formState.pointsRange[1] : undefined,
    joinDateStart: formState.joinDateStart,
    joinDateEnd: formState.joinDateEnd,
    createTimeStart: formState.createTimeStart,
    createTimeEnd: formState.createTimeEnd,
  };

  emit('search', params);
};

// 重置处理
const handleReset = () => {
  formRef.value.resetFields();
  positionOptions.value = [];
  emit('reset');
};

// 刷新处理
const handleRefresh = () => {
  emit('refresh');
};

// 监听VIP状态变化，处理积分范围禁用逻辑
watch(
  () => formState.isVIP,
  (newVal) => {
    if (newVal === false) {
      formState.pointsRange = [0, 1000];
    }
  },
);

// 暴露方法给父组件
defineExpose({
  formState,
  handleSearch,
  handleReset,
});
</script>

<template>
  <div class="advanced-search-container">
    <Card :bordered="false">
      <Form layout="horizontal" :model="formState" ref="formRef">
        <!-- 基础搜索区域 - 始终显示 -->
        <Row :gutter="24">
          <Col :xs="24" :sm="12" :md="8" :lg="6">
            <Form.Item label="关键词" name="keyword">
              <Input
                v-model:value="formState.keyword"
                placeholder="姓名/邮箱/职位"
                allow-clear
              />
            </Form.Item>
          </Col>
          <Col :xs="24" :sm="12" :md="8" :lg="6">
            <Form.Item label="状态" name="status">
              <Select
                v-model:value="formState.status"
                placeholder="请选择状态"
                allow-clear
              >
                <Select.Option :value="1">启用</Select.Option>
                <Select.Option :value="0">禁用</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col :xs="24" :sm="12" :md="8" :lg="6">
            <Form.Item label="部门" name="department">
              <Select
                v-model:value="formState.department"
                placeholder="请选择部门"
                allow-clear
                @change="handleDepartmentChange"
              >
                <Select.Option
                  v-for="dept in departmentOptions"
                  :key="dept.value"
                  :value="dept.value"
                >
                  {{ dept.label }}
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col :xs="24" :sm="12" :md="8" :lg="6">
            <span class="table-page-search-submitButtons">
              <Button type="primary" @click="handleSearch">
                <template #icon><MdiMagnify /></template>
                查询
              </Button>
              <Button style="margin-left: 8px" @click="handleReset">
                <template #icon><MdiRefresh /></template>
                重置
              </Button>
              <a @click="toggleAdvanced" style="margin-left: 8px">
                {{ advanced ? '收起' : '展开' }}
                <component :is="advanced ? MdiChevronUp : MdiChevronDown" />
              </a>
            </span>
          </Col>
        </Row>

        <!-- 高级搜索区域 - 点击展开按钮显示 -->
        <div v-show="advanced">
          <Divider style="margin: 0" />
          <Row :gutter="24" class="mt-3">
            <Col :xs="24" :sm="12" :md="8" :lg="6">
              <Form.Item label="职位" name="position">
                <Select
                  v-model:value="formState.position"
                  placeholder="请选择职位"
                  allow-clear
                  :loading="positionLoading"
                  :disabled="!formState.department || positionLoading"
                >
                  <Select.Option
                    v-for="pos in positionOptions"
                    :key="pos.value"
                    :value="pos.value"
                  >
                    {{ pos.label }}
                  </Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col :xs="24" :sm="12" :md="8" :lg="6">
              <Form.Item label="VIP会员" name="isVIP">
                <Select
                  v-model:value="formState.isVIP"
                  placeholder="是否VIP"
                  allow-clear
                >
                  <Select.Option :value="true">是</Select.Option>
                  <Select.Option :value="false">否</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col :xs="24" :sm="12" :md="8" :lg="6">
              <Form.Item label="积分范围" name="pointsRange">
                <a-range-slider
                  v-model:value="formState.pointsRange"
                  :min="0"
                  :max="1000"
                  :disabled="formState.isVIP === false"
                />
              </Form.Item>
            </Col>
            <Col :xs="24" :sm="12" :md="8" :lg="6">
              <Form.Item label="入职日期" name="joinDateRange">
                <DatePicker.RangePicker
                  v-model:value="formState.joinDateRange"
                  style="width: 100%"
                  @change="handleDateRangeChange"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row :gutter="24" class="mt-3">
            <Col :xs="24" :sm="24" :md="24" :lg="24">
              <Form.Item label="创建时间" name="createTimeRange">
                <DatePicker.RangePicker
                  v-model:value="formState.createTimeRange"
                  show-time
                  style="width: 100%"
                  @change="handleTimeRangeChange"
                />
              </Form.Item>
            </Col>
          </Row>
        </div>
      </Form>

      <!-- 自定义按钮区 -->
      <div class="table-operator mt-3">
        <Button type="primary" @click="$emit('add')">
          <template #icon><MdiPlus /></template>
          新增
        </Button>
        <Button @click="$emit('export')" style="margin-left: 8px">
          <template #icon><MdiExport /></template>
          导出
        </Button>
        <Button @click="$emit('import')" style="margin-left: 8px">
          <template #icon><MdiUpload /></template>
          导入
        </Button>
        <Button @click="handleRefresh" style="margin-left: 8px">
          <template #icon><MdiRefresh /></template>
          刷新
        </Button>
      </div>
    </Card>
  </div>
</template>

<style scoped>
.advanced-search-container {
  margin-bottom: 16px;
}

.table-page-search-submitButtons {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.mt-3 {
  margin-top: 12px;
}

.table-operator {
  margin-bottom: 16px;
}
</style>
