<script setup lang="ts">
import { defineComponent, h, onMounted, ref, watch } from 'vue';

import {
  MdiExport,
  MdiEye,
  MdiPencil,
  MdiPlus,
  MdiTrashCan,
} from '@vben/icons';

import {
  Button,
  Card,
  Col,
  FormItem,
  Input,
  message,
  Select,
  Space,
  Switch,
} from 'ant-design-vue';

import {
  BasicTable,
  DetailModal,
  SearchToolbar,
  useAnsheng,
} from '#/hooks/useAnsheng';

// 模拟API服务
const mockUserService = {
  getList: async (params: any) => {
    // 模拟延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 模拟分页和搜索
    const pageSize = params.pageSize || 10;
    const current = params.current || 1;

    // 生成随机数据
    const mockData = Array.from({ length: 1000 }, (_, index) => ({
      id: index + 1,
      name: `用户${index + 1}`,
      age: Math.floor(Math.random() * 50) + 18,
      email: `user${index + 1}@example.com`,
      status: Math.random() > 0.3 ? 1 : 0,
      department: ['研发部', '市场部', '销售部', '人事部'][
        Math.floor(Math.random() * 4)
      ],
      position: ['工程师', '经理', '总监', '专员'][
        Math.floor(Math.random() * 4)
      ],
      joinDate: new Date(Date.now() - Math.random() * 31_536_000_000 * 5)
        .toISOString()
        .split('T')[0],
    }));

    // 搜索过滤
    let filteredData = [...mockData];
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase();
      filteredData = filteredData.filter(
        (item) =>
          item.name.toLowerCase().includes(keyword) ||
          item.email.toLowerCase().includes(keyword),
      );
    }

    if (
      params.status !== undefined &&
      params.status !== null &&
      params.status !== ''
    ) {
      filteredData = filteredData.filter(
        (item) => item.status === Number(params.status),
      );
    }

    if (params.department) {
      filteredData = filteredData.filter(
        (item) => item.department === params.department,
      );
    }

    // 分页
    const total = filteredData.length;

    // 处理虚拟滚动模式（加载更多而不是分页）
    let rows;
    if (params.virtualScroll) {
      // 虚拟滚动模式，基于已加载的数据量加载一批新数据
      const start = params.loadedCount || 0;
      const end = start + pageSize;
      rows = filteredData.slice(start, end);
    } else {
      // 普通分页模式
      const start = (current - 1) * pageSize;
      const end = start + pageSize;
      rows = filteredData.slice(start, end);
    }

    return {
      code: 1,
      msg: '获取成功',
      data: {
        rows,
        total,
      },
    };
  },

  getDetail: async (id: number) => {
    await new Promise((resolve) => setTimeout(resolve, 300));
    return {
      code: 1,
      msg: '获取成功',
      data: {
        id,
        name: `用户${id}`,
        age: Math.floor(Math.random() * 50) + 18,
        email: `user${id}@example.com`,
        status: Math.random() > 0.3 ? 1 : 0,
        department: ['研发部', '市场部', '销售部', '人事部'][
          Math.floor(Math.random() * 4)
        ],
        position: ['工程师', '经理', '总监', '专员'][
          Math.floor(Math.random() * 4)
        ],
        joinDate: new Date(Date.now() - Math.random() * 31_536_000_000 * 5)
          .toISOString()
          .split('T')[0],
      },
    };
  },

  create: async (params: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`创建用户:${JSON.stringify(params)}`);
    return {
      code: 1,
      msg: '创建成功',
    };
  },

  update: async (id: number, params: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`更新用户:${id}, ${JSON.stringify(params)}`);
    return {
      code: 1,
      msg: '更新成功',
    };
  },

  delete: async (id: number) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`删除用户:${id}`);
    return {
      code: 1,
      msg: '删除成功',
    };
  },

  updateField: async (id: number, column: string, value: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`更新用户字段:${id}, ${column}, ${value}`);
    return {
      code: 1,
      msg: '更新成功',
    };
  },
};

// 部门选项
const departmentOptions = [
  { label: '研发部', value: '研发部' },
  { label: '市场部', value: '市场部' },
  { label: '销售部', value: '销售部' },
  { label: '人事部', value: '人事部' },
];

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

// 虚拟滚动开关
const enableVirtualScroll = ref(false);

// 使用useAnsheng hook
const {
  // 状态和数据
  tableData,
  loading,
  pagination,
  actionButtons,

  // 方法
  handleTableChange,
  initialize,
  createItem,
  editItem,
  deleteItem,
  viewItem,
  handleVirtualScroll,

  // 搜索相关
  searchToolbarBind,
  handleSearch,
  handleReset,

  // 子hook
  detail,
  form,
  table,
} = useAnsheng({
  formOptions: {
    title: (isEdit) => (isEdit ? '编辑用户' : '创建用户'),
    width: 700,
    defaultValues: {
      name: '',
      age: 18,
      email: '',
      status: 1,
      department: '研发部',
      position: '',
      joinDate: '',
    },
    getDetail: mockUserService.getDetail,
    create: mockUserService.create,
    update: mockUserService.update,
    fullscreenable: true,
    draggable: true,
    onSuccess: () => {
      message.success('操作成功');
      table.getList();
    },
  },
  tableOptions: {
    api: mockUserService.getList,
    defaultPageSize: 10,
    get virtualScroll() {
      return enableVirtualScroll.value;
    },
    virtualScrollOptions: {
      scrollY: 400,
      itemHeight: 54,
      threshold: 100,
    },
    columns: [
      { title: '姓名', dataIndex: 'name', width: 100 },
      { title: '年龄', dataIndex: 'age', width: 80 },
      { title: '邮箱', dataIndex: 'email', width: 200 },
      { title: '部门', dataIndex: 'department', width: 120 },
      { title: '职位', dataIndex: 'position', width: 120 },
      { title: '入职日期', dataIndex: 'joinDate', width: 120 },
      {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        customRender: ({ text }) => (text === 1 ? '启用' : '禁用'),
        filters: [
          { text: '启用', value: 1 },
          { text: '禁用', value: 0 },
        ],
      },
    ],
    actionButtons: [
      {
        key: 'view',
        text: '查看',
        type: 'primary',
        onClick: (record) => viewItem(record),
        icon: MdiEye,
      },
      {
        key: 'edit',
        text: '编辑',
        onClick: (record) => editItem(record.id),
        icon: MdiPencil,
      },
      {
        key: 'delete',
        text: '删除',
        onClick: (record) => deleteItem(record.id),
        icon: MdiTrashCan,
        popConfirm: '确定要删除此记录吗？',
      },
    ],
  },
  searchOptions: {
    basicItems: [
      {
        field: 'keyword',
        component: 'Input',
        label: '关键词',
        span: 6,
      },
      {
        field: 'status',
        component: 'Select',
        label: '状态',
        span: 6,
        options: statusOptions,
      },
      {
        field: 'department',
        component: 'Select',
        label: '部门',
        span: 6,
        options: departmentOptions,
      },
    ],
    customButtons: [
      {
        text: '新增用户',
        icon: MdiPlus,
        type: 'primary',
        onClick: () => createItem(),
      },
      {
        text: '导出',
        icon: MdiExport,
        onClick: () => {
          message.success('导出功能演示');
        },
      },
    ],
  },
  detailOptions: {
    sections: [
      {
        title: '基本信息',
        items: [
          { label: '姓名', field: 'name' },
          { label: '年龄', field: 'age' },
          { label: '邮箱', field: 'email' },
          { label: '状态', field: 'status' },
        ],
      },
      {
        title: '工作信息',
        items: [
          { label: '部门', field: 'department' },
          { label: '职位', field: 'position' },
          { label: '入职日期', field: 'joinDate' },
        ],
      },
    ],
    formatters: {
      status: (val: number) => (val === 1 ? '启用' : '禁用'),
    },
  },
  deleteApi: mockUserService.delete,
  viewMode: 'detail',
});

// 表格选择状态
const selectedRowKeys = ref<(number | string)[]>([]);
const selectedRows = ref<any[]>([]);

// 处理表格选择变化
const handleSelectionChange = (rowKeys: (number | string)[], rows: any[]) => {
  selectedRowKeys.value = rowKeys;
  selectedRows.value = rows;
};

// 批量操作处理
const handleBatchAction = (action: string) => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一条记录');
    return;
  }

  switch (action) {
    case 'delete': {
      message.success(`批量删除: ${selectedRowKeys.value.join(', ')}`);
      break;
    }
    case 'disable': {
      message.success(`批量禁用: ${selectedRowKeys.value.join(', ')}`);
      break;
    }
    case 'enable': {
      message.success(`批量启用: ${selectedRowKeys.value.join(', ')}`);
      break;
    }
    // No default
  }

  // 操作完成后刷新表格
  table.getList();
  // 清空选择
  selectedRowKeys.value = [];
  selectedRows.value = [];
};

// 监听虚拟滚动开关变化
watch(enableVirtualScroll, () => {
  // 重新加载数据
  initialize();
});

// 页面加载时初始化数据
onMounted(() => {
  initialize();
});

// 创建表单模态框组件
const formComponent = defineComponent({
  setup() {
    return () =>
      form.renderFormModal([
        {
          title: '基本信息',
          content: (formData) => [
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '姓名',
                  name: 'name',
                  rules: [{ required: true, message: '请输入姓名' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入姓名',
                    value: formData.name,
                    'onUpdate:value': (val) => (formData.name = val),
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '年龄',
                  name: 'age',
                  rules: [{ required: true, message: '请输入年龄' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入年龄',
                    value: formData.age,
                    'onUpdate:value': (val) => (formData.age = Number(val)),
                    type: 'number',
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '邮箱',
                  name: 'email',
                  rules: [{ required: true, message: '请输入邮箱' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入邮箱',
                    value: formData.email,
                    'onUpdate:value': (val) => (formData.email = val),
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '状态',
                  name: 'status',
                  rules: [{ required: true, message: '请选择状态' }],
                },
                () =>
                  h(Select, {
                    placeholder: '请选择状态',
                    value: formData.status,
                    'onUpdate:value': (val) => (formData.status = Number(val)),
                    options: statusOptions,
                  }),
              ),
            ]),
          ],
        },
        {
          title: '工作信息',
          content: (formData) => [
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '部门',
                  name: 'department',
                  rules: [{ required: true, message: '请选择部门' }],
                },
                () =>
                  h(Select, {
                    placeholder: '请选择部门',
                    value: formData.department,
                    'onUpdate:value': (val) =>
                      (formData.department = String(val)),
                    options: departmentOptions,
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '职位',
                  name: 'position',
                  rules: [{ required: true, message: '请输入职位' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入职位',
                    value: formData.position,
                    'onUpdate:value': (val) => (formData.position = val),
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '入职日期',
                  name: 'joinDate',
                  rules: [{ required: true, message: '请选择入职日期' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请选择入职日期',
                    value: formData.joinDate,
                    'onUpdate:value': (val) => (formData.joinDate = val),
                    type: 'date',
                  }),
              ),
            ]),
          ],
        },
      ]);
  },
});
</script>

<template>
  <div class="table-demo">
    <!-- 搜索工具栏 -->
    <Card class="mb-4">
      <SearchToolbar
        v-bind="searchToolbarBind"
        @search="handleSearch"
        @reset="handleReset"
      />
    </Card>

    <!-- 批量操作按钮 -->
    <div class="batch-actions mb-4" v-if="selectedRowKeys.length > 0">
      <div class="batch-info">已选择 {{ selectedRowKeys.length }} 项</div>
      <Space>
        <Button type="primary" @click="handleBatchAction('enable')">
          批量启用
        </Button>
        <Button @click="handleBatchAction('disable')">批量禁用</Button>
        <Button danger @click="handleBatchAction('delete')">批量删除</Button>
      </Space>
    </div>

    <!-- 虚拟滚动开关 -->
    <Card class="mb-4">
      <div class="flex items-center">
        <span class="mr-2">启用虚拟滚动：</span>
        <Switch v-model:checked="enableVirtualScroll" />
        <span class="ml-4 text-gray-500">
          {{ enableVirtualScroll ? '已启用虚拟滚动' : '使用普通分页' }}
        </span>
      </div>
      <div class="mt-2 text-sm text-gray-500">
        虚拟滚动模式下，只渲染可视区域的数据，大幅减少DOM节点数量，提升表格性能。适合大数据量场景。
      </div>
    </Card>

    <!-- 标准表格 -->
    <Card title="用户列表" class="mb-4">
      <Button type="primary" class="mb-4" @click="createItem">
        <template #icon><MdiPlus /></template>
        新增用户
      </Button>

      <BasicTable
        :columns="table.columns || []"
        :data-source="tableData"
        :loading="loading"
        :pagination="enableVirtualScroll ? false : pagination"
        :scroll="enableVirtualScroll ? { y: 400 } : undefined"
        :show-action="true"
        :action-buttons="actionButtons"
        :show-selection="true"
        row-key="id"
        @selection-change="handleSelectionChange"
        @change="handleTableChange"
        @scroll="handleVirtualScroll"
      />
    </Card>

    <!-- 表单模态框 -->
    <component :is="formComponent" />

    <!-- 详情模态框 - 用于查看 -->
    <DetailModal
      v-if="detail"
      v-model:visible="detail.visible"
      :sections="detail.sections"
      :title="detail.title"
      :loading="loading"
    />
  </div>
</template>

<style scoped>
.table-demo {
  max-width: 1200px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-2 {
  margin-top: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.ml-4 {
  margin-left: 16px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.text-sm {
  font-size: 14px;
}

.text-gray-500 {
  color: #6b7280;
}

.batch-actions {
  background-color: #f0f8ff;
  padding: 12px 16px;
  border-radius: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  font-weight: 500;
  color: #1890ff;
}
</style>
