<script setup lang="ts">
import { defineComponent, h, onMounted, ref, watch } from 'vue';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MdiContentSave,
  MdiExport,
  MdiEye,
  MdiPencil,
  MdiPlus,
  MdiTrashCan,
} from '@vben/icons';

import {
  Button,
  ButtonGroup,
  Card,
  Col,
  Descriptions,
  Form,
  Input,
  message,
  Modal,
  Select,
  Space,
  Switch,
} from 'ant-design-vue';

import {
  BasicTable,
  DetailModal,
  SearchToolbar,
  useAnsheng,
} from '#/hooks/useAnsheng';

import AdvancedSearch from './components/AdvancedSearch.vue';
import ColumnSetting from './components/ColumnSetting.vue';
import StepForm from './components/StepForm.vue';
import TreeTable from './components/TreeTable.vue';

const FormItem = Form.Item;

// 模拟API服务
const mockUserService = {
  getList: async (params: any) => {
    // 模拟延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 模拟分页和搜索
    const pageSize = params.pageSize || 10;
    const current = params.current || 1;

    // 生成随机数据
    const mockData = Array.from({ length: 2000 }, (_, index) => ({
      id: index + 1,
      name: `用户${index + 1}`,
      age: Math.floor(Math.random() * 50) + 18,
      email: `user${index + 1}@example.com`,
      status: Math.random() > 0.3 ? 1 : 0,
      department: ['研发部', '市场部', '销售部', '人事部'][
        Math.floor(Math.random() * 4)
      ],
      position: ['工程师', '经理', '总监', '专员'][
        Math.floor(Math.random() * 4)
      ],
      joinDate: new Date(Date.now() - Math.random() * 31_536_000_000 * 5)
        .toISOString()
        .split('T')[0],
    }));

    // 搜索过滤
    let filteredData = [...mockData];
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase();
      filteredData = filteredData.filter(
        (item) =>
          item.name.toLowerCase().includes(keyword) ||
          item.email.toLowerCase().includes(keyword),
      );
    }

    if (
      params.status !== undefined &&
      params.status !== null &&
      params.status !== ''
    ) {
      filteredData = filteredData.filter(
        (item) => item.status === Number(params.status),
      );
    }

    if (params.department) {
      filteredData = filteredData.filter(
        (item) => item.department === params.department,
      );
    }

    // 分页
    const total = filteredData.length;

    // 处理虚拟滚动模式（加载更多而不是分页）
    let rows;
    if (params.virtualScroll) {
      // 虚拟滚动模式，基于已加载的数据量加载一批新数据
      const start = params.loadedCount || 0;
      const end = start + pageSize;
      rows = filteredData.slice(start, end);
    } else {
      // 普通分页模式
      const start = (current - 1) * pageSize;
      const end = start + pageSize;
      rows = filteredData.slice(start, end);
    }

    return {
      code: 1,
      msg: '获取成功',
      data: {
        rows,
        total,
      },
    };
  },

  getDetail: async (id: number) => {
    await new Promise((resolve) => setTimeout(resolve, 300));
    return {
      code: 1,
      msg: '获取成功',
      data: {
        id,
        name: `用户${id}`,
        age: Math.floor(Math.random() * 50) + 18,
        email: `user${id}@example.com`,
        status: Math.random() > 0.3 ? 1 : 0,
        department: ['研发部', '市场部', '销售部', '人事部'][
          Math.floor(Math.random() * 4)
        ],
        position: ['工程师', '经理', '总监', '专员'][
          Math.floor(Math.random() * 4)
        ],
        joinDate: new Date(Date.now() - Math.random() * 31_536_000_000 * 5)
          .toISOString()
          .split('T')[0],
      },
    };
  },

  create: async (params: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`创建用户:${JSON.stringify(params)}`);
    return {
      code: 1,
      msg: '创建成功',
    };
  },

  update: async (id: number, params: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`更新用户:${id}, ${JSON.stringify(params)}`);
    return {
      code: 1,
      msg: '更新成功',
    };
  },

  delete: async (id: number) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`删除用户:${id}`);
    return {
      code: 1,
      msg: '删除成功',
    };
  },

  updateField: async (id: number, column: string, value: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`更新用户字段:${id}, ${column}, ${value}`);
    return {
      code: 1,
      msg: '更新成功',
    };
  },
};

// 部门选项
const departmentOptions = [
  { label: '研发部', value: '研发部' },
  { label: '市场部', value: '市场部' },
  { label: '销售部', value: '销售部' },
  { label: '人事部', value: '人事部' },
];

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

// 表格选择状态
const selectedRowKeys = ref<(number | string)[]>([]);
const selectedRows = ref<any[]>([]);

// 处理表格选择变化
const handleSelectionChange = (rowKeys: (number | string)[], rows: any[]) => {
  selectedRowKeys.value = rowKeys;
  selectedRows.value = rows;
};

// 批量操作处理
const handleBatchAction = (action: string) => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请至少选择一条记录');
    return;
  }

  switch (action) {
    case 'delete': {
      message.success(`批量删除: ${selectedRowKeys.value.join(', ')}`);
      // 实际应用中这里应该调用批量删除API

      break;
    }
    case 'disable': {
      message.success(`批量禁用: ${selectedRowKeys.value.join(', ')}`);
      // 实际应用中这里应该调用批量禁用API

      break;
    }
    case 'enable': {
      message.success(`批量启用: ${selectedRowKeys.value.join(', ')}`);
      // 实际应用中这里应该调用批量启用API

      break;
    }
    // No default
  }

  // 操作完成后刷新表格
  table.getList();
  // 清空选择
  selectedRowKeys.value = [];
  selectedRows.value = [];
};

// 直接传值编辑的示例对象
const directEditRecord = ref({
  id: 9999,
  name: '直接编辑',
  age: 30,
  email: '<EMAIL>',
  status: 1,
  department: '研发部',
  position: '前端工程师',
  joinDate: '2023-01-01',
  isVIP: true,
  points: 888,
  lastLoginTime: '2023-06-06 12:00:00',
});

// 虚拟滚动开关
const enableVirtualScroll = ref(false);

// 使用useAnsheng hook
const {
  // 状态和数据
  formData,
  tableData,
  loading,
  pagination,
  actionButtons,
  virtualScroll,
  virtualConfig,

  // 方法
  handleTableChange,
  initialize,
  createItem,
  handleVirtualScroll,

  // 搜索相关
  searchToolbarBind,
  handleSearch,
  handleReset,
  tableBind,

  // 子hook
  detail,
  form,
  table,
} = useAnsheng({
  formOptions: {
    title: (isEdit) => (isEdit ? '编辑用户' : '创建用户'),
    width: 700,
    defaultValues: {
      name: '',
      age: 18,
      email: '',
      status: 1,
      department: '研发部',
      position: '',
      joinDate: '',
    },
    getDetail: mockUserService.getDetail,
    create: mockUserService.create,
    update: mockUserService.update,
    fullscreenable: true,
    draggable: true,
    onSuccess: () => {
      message.success('操作成功');
      table.getList();
    },
  },
  tableOptions: {
    api: mockUserService.getList,
    defaultPageSize: 10,
    get virtualScroll() {
      return enableVirtualScroll.value;
    },
    virtualScrollOptions: {
      scrollY: 400,
      itemHeight: 54,
      threshold: 100,
    },
    columns: [
      { title: '姓名', dataIndex: 'name', width: 100 },
      { title: '年龄', dataIndex: 'age', width: 80 },
      { title: '邮箱', dataIndex: 'email', width: 200 },
      { title: '部门', dataIndex: 'department', width: 120 },
      { title: '职位', dataIndex: 'position', width: 120 },
      { title: '入职日期', dataIndex: 'joinDate', width: 120 },
      {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        customRender: ({ text }) => (text === 1 ? '启用' : '禁用'),
        filters: [
          { text: '启用', value: 1 },
          { text: '禁用', value: 0 },
        ],
        onFilter: (value, record) => record.status === value,
      },
    ],
    editableColumns: ['name', 'age', 'email'],
    onSave: async (record, column, value) => {
      try {
        await mockUserService.updateField(record.id, column, value);
      } catch (error) {
        return Promise.reject(error);
      }
    },
    actionButtons: [
      {
        key: 'view',
        text: '查看',
        type: 'primary',
        onClick: (record) => detail.open(record),
        icon: MdiEye,
      },
      {
        key: 'edit',
        text: '编辑',
        onClick: (record) => form.show(record.id),
        icon: MdiPencil,
      },
      {
        key: 'direct-edit',
        text: '直接编辑',
        onClick: (record) => handleDirectEdit(record),
        icon: MdiContentSave,
      },
      {
        key: 'delete',
        text: '删除',
        onClick: (record) => handleDelete(record),
        icon: MdiTrashCan,
        popConfirm: '确定要删除此记录吗？',
      },
    ],
  },
  searchOptions: {
    basicItems: [
      {
        field: 'keyword',
        component: 'Input',
        label: '关键词',
        span: 6,
      },
      {
        field: 'status',
        component: 'Select',
        label: '状态',
        span: 6,
        options: statusOptions,
      },
      {
        field: 'department',
        component: 'Select',
        label: '部门',
        span: 6,
        options: departmentOptions,
      },
    ],
    customButtons: [
      {
        text: '新增用户',
        icon: MdiPlus,
        type: 'primary',
        onClick: () => createItem(),
      },
      {
        text: '导出',
        icon: MdiExport,
        onClick: () => {
          message.success('导出功能演示');
        },
      },
    ],
  },
  detailOptions: {
    sections: [
      {
        title: '基本信息',
        items: [
          { label: '姓名', field: 'name', value: '' },
          { label: '年龄', field: 'age', value: '' },
          { label: '邮箱', field: 'email', value: '' },
          {
            label: '状态',
            field: 'status',
            value: '',
            formatter: (val: number) => (val === 1 ? '启用' : '禁用'),
          },
        ],
      },
      {
        title: '工作信息',
        items: [
          { label: '部门', field: 'department', value: '' },
          { label: '职位', field: 'position', value: '' },
          { label: '入职日期', field: 'joinDate', value: '' },
        ],
      },
    ],
  },
  deleteApi: mockUserService.delete,
  viewMode: 'detail',
});

// 监听虚拟滚动开关变化
watch(enableVirtualScroll, (newValue) => {
  // 需要重新设置表格配置
  table.virtualScroll = newValue;
  // 重新加载数据
  initialize();
});

// 创建表单模态框组件
const formComponent = defineComponent({
  setup() {
    return () =>
      form.renderFormModal([
        {
          title: '基本信息',
          content: (formData, selectOptions) => [
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '姓名',
                  name: 'name',
                  rules: [{ required: true, message: '请输入姓名' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入姓名',
                    value: formData.name,
                    'onUpdate:value': (val) => (formData.name = val),
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '年龄',
                  name: 'age',
                  rules: [{ required: true, message: '请输入年龄' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入年龄',
                    value: formData.age,
                    'onUpdate:value': (val) => (formData.age = Number(val)),
                    type: 'number',
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '邮箱',
                  name: 'email',
                  rules: [{ required: true, message: '请输入邮箱' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入邮箱',
                    value: formData.email,
                    'onUpdate:value': (val) => (formData.email = val),
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '状态',
                  name: 'status',
                  rules: [{ required: true, message: '请选择状态' }],
                },
                () =>
                  h(Select, {
                    placeholder: '请选择状态',
                    value: formData.status,
                    'onUpdate:value': (val) => (formData.status = Number(val)),
                    options: statusOptions,
                  }),
              ),
            ]),
          ],
        },
        {
          title: '工作信息',
          content: (formData, selectOptions) => [
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '部门',
                  name: 'department',
                  rules: [{ required: true, message: '请选择部门' }],
                },
                () =>
                  h(Select, {
                    placeholder: '请选择部门',
                    value: formData.department,
                    'onUpdate:value': (val) =>
                      (formData.department = String(val)),
                    options: departmentOptions,
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '职位',
                  name: 'position',
                  rules: [{ required: true, message: '请输入职位' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请输入职位',
                    value: formData.position,
                    'onUpdate:value': (val) => (formData.position = val),
                  }),
              ),
            ]),
            h(Col, { span: 12 }, [
              h(
                FormItem,
                {
                  label: '入职日期',
                  name: 'joinDate',
                  rules: [{ required: true, message: '请选择入职日期' }],
                },
                () =>
                  h(Input, {
                    placeholder: '请选择入职日期',
                    value: formData.joinDate,
                    'onUpdate:value': (val) => (formData.joinDate = val),
                    type: 'date',
                  }),
              ),
            ]),
          ],
        },
      ]);
  },
});

// 表格列设置
const displayColumns = ref([
  { title: '姓名', dataIndex: 'name', width: 100 },
  { title: '年龄', dataIndex: 'age', width: 80 },
  { title: '邮箱', dataIndex: 'email', width: 200 },
  { title: '部门', dataIndex: 'department', width: 120 },
  { title: '职位', dataIndex: 'position', width: 120 },
  { title: '入职日期', dataIndex: 'joinDate', width: 120 },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => (text === 1 ? '启用' : '禁用'),
  },
]);

// 处理表格列更改
const handleColumnChange = (newColumns) => {
  displayColumns.value = newColumns;
};

// 步骤表单visible控制
const stepFormVisible = ref(false);

// 展示步骤表单
const showStepForm = () => {
  stepFormVisible.value = true;
};

// 处理步骤表单成功提交
const handleStepFormSuccess = (values) => {
  message.success('表单提交成功');
  stepFormVisible.value = false;
  table.getList(); // 刷新表格数据
};

// 高级搜索引用
const advancedSearchRef = ref();

// 处理高级搜索
const handleAdvancedSearch = (params) => {
  // 将搜索参数合并到表格搜索参数中
  Object.keys(params).forEach((key) => {
    if (params[key] !== undefined && params[key] !== '') {
      table.searchParams[key] = params[key];
    } else {
      delete table.searchParams[key];
    }
  });

  // 重置到第一页
  pagination.current = 1;

  // 获取列表数据
  table.getList();
};

// 处理高级搜索重置
const handleAdvancedReset = () => {
  // 清除搜索参数
  Object.keys(table.searchParams).forEach((key) => {
    if (key !== 'page' && key !== 'pageSize') {
      delete table.searchParams[key];
    }
  });

  // 重置到第一页
  pagination.current = 1;

  // 获取列表数据
  table.getList();
};

// 修改handleDirectEdit函数，使用saveFormData替代setFormData，并调整调用顺序
const handleDirectEdit = (record) => {
  // 先打开一个空表单（不传id）
  form.show();
  // 使用saveFormData保存数据（这会保留原始数据用于比较修改）
  form.saveFormData(record);
  // 设置为编辑模式
  form.isEdit.value = true;
  form.currentId.value = record.id;
};

// 处理删除
const handleDelete = async (record) => {
  try {
    await mockUserService.delete(record.id);
    message.success('删除成功');
    table.getList();
  } catch {
    message.error('删除失败');
  }
};

// 页面加载时初始化数据
onMounted(() => {
  initialize();
});
</script>

<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">useAnsheng Hooks 演示</h1>
      <p class="page-description">
        展示使用 useAnsheng hooks 实现的完整 CRUD 功能
      </p>
    </div>

    <!-- 搜索工具栏 -->
    <div class="toolbar-container">
      <SearchToolbar
        v-bind="searchToolbarBind"
        @search="handleSearch"
        @reset="handleReset"
      />
    </div>

    <!-- 批量操作按钮 -->
    <div class="batch-actions" v-if="selectedRowKeys.length > 0">
      <div class="batch-info">已选择 {{ selectedRowKeys.length }} 项</div>
      <ButtonGroup>
        <Button type="primary" @click="handleBatchAction('enable')">
          <template #icon><MdiCheck /></template>
          批量启用
        </Button>
        <Button @click="handleBatchAction('disable')"> 批量禁用 </Button>
        <Button danger @click="handleBatchAction('delete')"> 批量删除 </Button>
      </ButtonGroup>
    </div>

    <!-- 高级搜索示例 -->
    <div class="section-container">
      <h2 class="section-title">高级搜索</h2>
      <AdvancedSearch
        ref="advancedSearchRef"
        @search="handleAdvancedSearch"
        @reset="handleAdvancedReset"
        @refresh="table.getList"
        @add="showStepForm"
        @export="() => message.success('导出功能演示')"
        @import="() => message.success('导入功能演示')"
      />
    </div>

    <!-- 表格列设置示例 -->
    <div class="table-container">
      <div class="table-header">
        <h2 class="section-title">表格列设置</h2>
        <ColumnSetting
          :columns="[
            { title: '姓名', dataIndex: 'name', width: 100 },
            { title: '年龄', dataIndex: 'age', width: 80 },
            { title: '邮箱', dataIndex: 'email', width: 200 },
            { title: '部门', dataIndex: 'department', width: 120 },
            { title: '职位', dataIndex: 'position', width: 120 },
            { title: '入职日期', dataIndex: 'joinDate', width: 120 },
            {
              title: '状态',
              dataIndex: 'status',
              width: 100,
            },
            {
              title: 'VIP',
              dataIndex: 'isVIP',
              width: 80,
            },
            {
              title: '积分',
              dataIndex: 'points',
              width: 80,
            },
          ]"
          v-model:value="displayColumns"
          @change="handleColumnChange"
        />
      </div>

      <BasicTable
        :columns="displayColumns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        :show-index="true"
        :show-action="true"
        :action-buttons="actionButtons"
        :show-selection="true"
        row-key="id"
        @selection-change="handleSelectionChange"
        @change="handleTableChange"
      />
    </div>

    <!-- 树形表格示例 -->
    <div class="table-container mt-5">
      <h2 class="section-title">树形表格</h2>
      <TreeTable
        @edit="(record) => message.info(`编辑: ${record.name}`)"
        @delete="(record) => message.info(`删除: ${record.name}`)"
        @view="(record) => message.info(`查看: ${record.name}`)"
      />
    </div>

    <!-- 分步表单示例 -->
    <Modal
      v-model:visible="stepFormVisible"
      title="分步表单"
      width="800px"
      :footer="null"
      :destroy-on-close="true"
    >
      <StepForm @success="handleStepFormSuccess" />
    </Modal>

    <!-- 直接编辑示例 -->
    <div class="table-container mt-5">
      <h2 class="section-title">直接编辑示例</h2>
      <p class="table-tip">
        这个示例展示了两种编辑模式：<br />
        1. 通过API获取详情（"编辑"按钮）<br />
        2. 直接传值进行编辑（"直接编辑"按钮）
      </p>
      <Card>
        <Descriptions title="数据示例" bordered>
          <Descriptions.Item label="ID">
            {{ directEditRecord.id }}
          </Descriptions.Item>
          <Descriptions.Item label="姓名">
            {{ directEditRecord.name }}
          </Descriptions.Item>
          <Descriptions.Item label="年龄">
            {{ directEditRecord.age }}
          </Descriptions.Item>
          <Descriptions.Item label="邮箱">
            {{ directEditRecord.email }}
          </Descriptions.Item>
          <Descriptions.Item label="部门">
            {{ directEditRecord.department }}
          </Descriptions.Item>
          <Descriptions.Item label="职位">
            {{ directEditRecord.position }}
          </Descriptions.Item>
        </Descriptions>

        <Space class="mt-4">
          <Button type="primary" @click="form.show(directEditRecord.id)">
            API编辑
          </Button>
          <Button
            @click="
              () => {
                form.show();
                form.saveFormData(directEditRecord);
                form.isEdit.value = true;
                form.currentId.value = directEditRecord.id;
              }
            "
          >
            直接编辑
          </Button>
        </Space>
      </Card>
    </div>

    <!-- 表单弹窗 -->
    <component :is="formComponent" />

    <!-- 详情弹窗 -->
    <DetailModal
      v-if="detail"
      v-model:visible="detail.visible"
      :sections="detail.sections"
      :title="detail.title"
      :loading="loading"
    />

    <!-- 虚拟滚动示例 -->
    <div class="section-container">
      <h2 class="section-title">虚拟滚动</h2>
      <Card class="mb-4">
        <Space>
          <span>启用虚拟滚动：</span>
          <Switch v-model:checked="enableVirtualScroll" />
          <span class="text-gray-500">
            {{ enableVirtualScroll ? '已启用虚拟滚动' : '使用普通分页' }}
          </span>
        </Space>
        <div class="mt-2 text-sm text-gray-500">
          虚拟滚动模式下，只渲染可视区域的数据，大幅减少DOM节点数量，提升表格性能。适合大数据量场景。
        </div>
      </Card>

      <div class="virtual-scroll-table">
        <BasicTable
          :columns="displayColumns"
          :data-source="tableData"
          :loading="loading"
          :show-selection="true"
          row-key="id"
          :pagination="enableVirtualScroll ? false : pagination"
          :scroll="enableVirtualScroll ? { y: 400 } : undefined"
          :show-action="true"
          :action-buttons="actionButtons"
          @selection-change="handleSelectionChange"
          @change="handleTableChange"
          @scroll="handleVirtualScroll"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 48px);
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #1f1f1f;
}

.page-description {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.toolbar-container {
  margin-bottom: 16px;
  background-color: #fff;
  padding: 16px;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.batch-actions {
  margin-bottom: 16px;
  background-color: #f0f8ff;
  padding: 12px 16px;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  font-weight: 500;
  color: #1890ff;
}

.table-container {
  background-color: #fff;
  padding: 16px;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.section-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #1f1f1f;
}

.table-tip {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  background-color: #f9f9f9;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.mt-4 {
  margin-top: 16px;
}

.mt-5 {
  margin-top: 20px;
}

.section-container {
  margin-bottom: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.virtual-scroll-table {
  height: 400px;
  overflow: hidden;
}

.text-sm {
  font-size: 14px;
}

.text-gray-500 {
  color: #6b7280;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-2 {
  margin-top: 8px;
}
</style>
