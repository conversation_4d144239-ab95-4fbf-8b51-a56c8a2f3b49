<script setup lang="ts">
import { ref } from 'vue';

import { MdiExport, MdiPlus, MdiTrashCan } from '@vben/icons';

import { Card, message } from 'ant-design-vue';

import { AnsengCrud } from '#/hooks/useAnsheng';

// 引用AnsengCrud组件
const crudRef = ref();

// 模拟API服务
const mockUserService = {
  getList: async (params: any) => {
    // 模拟延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 模拟分页和搜索
    const pageSize = params.pageSize || 10;
    const current = params.current || 1;

    // 生成随机数据
    const mockData = Array.from({ length: 1000 }, (_, index) => ({
      id: index + 1,
      name: `用户${index + 1}`,
      age: Math.floor(Math.random() * 50) + 18,
      email: `user${index + 1}@example.com`,
      status: Math.random() > 0.3 ? 1 : 0,
      department: ['研发部', '市场部', '销售部', '人事部'][
        Math.floor(Math.random() * 4)
      ],
      position: ['工程师', '经理', '总监', '专员'][
        Math.floor(Math.random() * 4)
      ],
      joinDate: new Date(Date.now() - Math.random() * 31_536_000_000 * 5)
        .toISOString()
        .split('T')[0],
    }));

    // 搜索过滤
    let filteredData = [...mockData];
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase();
      filteredData = filteredData.filter(
        (item) =>
          item.name.toLowerCase().includes(keyword) ||
          item.email.toLowerCase().includes(keyword),
      );
    }

    if (
      params.status !== undefined &&
      params.status !== null &&
      params.status !== ''
    ) {
      filteredData = filteredData.filter(
        (item) => item.status === Number(params.status),
      );
    }

    if (params.department) {
      filteredData = filteredData.filter(
        (item) => item.department === params.department,
      );
    }

    // 分页
    const total = filteredData.length;
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    const rows = filteredData.slice(start, end);

    return {
      code: 1,
      msg: '获取成功',
      data: {
        rows,
        total,
      },
    };
  },

  getDetail: async (id: number) => {
    await new Promise((resolve) => setTimeout(resolve, 300));
    return {
      code: 1,
      msg: '获取成功',
      data: {
        id,
        name: `用户${id}`,
        age: Math.floor(Math.random() * 50) + 18,
        email: `user${id}@example.com`,
        status: Math.random() > 0.3 ? 1 : 0,
        department: ['研发部', '市场部', '销售部', '人事部'][
          Math.floor(Math.random() * 4)
        ],
        position: ['工程师', '经理', '总监', '专员'][
          Math.floor(Math.random() * 4)
        ],
        joinDate: new Date(Date.now() - Math.random() * 31_536_000_000 * 5)
          .toISOString()
          .split('T')[0],
      },
    };
  },

  create: async (params: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`创建用户:${JSON.stringify(params)}`);
    return {
      code: 1,
      msg: '创建成功',
    };
  },

  update: async (id: number, params: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`更新用户:${id}, ${JSON.stringify(params)}`);
    return {
      code: 1,
      msg: '更新成功',
    };
  },

  delete: async (id: number) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`删除用户:${id}`);
    return {
      code: 1,
      msg: '删除成功',
    };
  },
};

// 部门选项
const departmentOptions = [
  { label: '研发部', value: '研发部' },
  { label: '市场部', value: '市场部' },
  { label: '销售部', value: '销售部' },
  { label: '人事部', value: '人事部' },
];

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

// AnsengCrud 配置
const crudConfig = {
  formOptions: {
    title: (isEdit: boolean) => (isEdit ? '编辑用户' : '创建用户'),
    width: 800,
    defaultValues: {
      name: '',
      age: 18,
      email: '',
      status: 1,
      department: '研发部',
      position: '',
      joinDate: '',
    },
    getDetail: mockUserService.getDetail,
    create: mockUserService.create,
    update: mockUserService.update,
    fullscreenable: true,
    draggable: true,
    onSuccess: () => {
      message.success('操作成功');
    },
  },
  tableOptions: {
    api: mockUserService.getList,
    defaultPageSize: 10,
    columns: [
      { title: '姓名', dataIndex: 'name', width: 100 },
      { title: '年龄', dataIndex: 'age', width: 80 },
      { title: '邮箱', dataIndex: 'email', width: 200 },
      { title: '部门', dataIndex: 'department', width: 120 },
      { title: '职位', dataIndex: 'position', width: 120 },
      { title: '入职日期', dataIndex: 'joinDate', width: 120 },
      {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        customRender: ({ text }: any) => (text === 1 ? '启用' : '禁用'),
      },
    ],
    actionButtons: [
      {
        key: 'edit',
        text: '编辑',
        onClick: (record: any) => crudRef.value?.editItem(record.id),
      },
      {
        key: 'delete',
        text: '删除',
        onClick: (record: any) => crudRef.value?.deleteItem(record.id),
        popConfirm: '确定要删除此记录吗？',
      },
    ],
  },
  searchOptions: {
    basicItems: [
      {
        field: 'keyword',
        component: 'Input',
        label: '关键词',
        // span: 6,
      },
      {
        field: 'status',
        component: 'Select',
        label: '状态',
        // span: 6,
        options: statusOptions,
      },
      {
        field: 'department',
        component: 'Select',
        label: '部门',
        // span: 6,
        options: departmentOptions,
      },
    ],
    customButtons: [
      {
        text: '新增用户',
        icon: MdiPlus,
        type: 'primary',
        onClick: () => crudRef.value?.createItem(),
      },
      {
        text: '导出',
        icon: MdiExport,
        onClick: () => {
          message.success('导出功能演示');
        },
      },
    ],
  },
  detailOptions: {
    sections: [
      {
        title: '基本信息',
        items: [
          { label: '姓名', field: 'name' },
          { label: '年龄', field: 'age' },
          { label: '邮箱', field: 'email' },
          { label: '状态', field: 'status' },
        ],
      },
      {
        title: '工作信息',
        items: [
          { label: '部门', field: 'department' },
          { label: '职位', field: 'position' },
          { label: '入职日期', field: 'joinDate' },
        ],
      },
    ],
    formatters: {
      status: (val: number) => (val === 1 ? '启用' : '禁用'),
    },
  },
  deleteApi: mockUserService.delete,
  viewMode: 'detail',
};

// 自定义表单渲染
// const renderForm = (form: any) => {
//   return {
//     setup() {
//       return () =>
//         form.renderFormModal([
//           {
//             title: '基本信息',
//             content: (formData: any) => [
//               h(Col, { span: 12 }, [
//                 h(
//                   FormItem,
//                   {
//                     label: '姓名',
//                     name: 'name',
//                     rules: [{ required: true, message: '请输入姓名' }],
//                   },
//                   () =>
//                     h(Input, {
//                       placeholder: '请输入姓名',
//                       value: formData.name,
//                       'onUpdate:value': (val: string) => (formData.name = val),
//                     }),
//                 ),
//               ]),
//               h(Col, { span: 12 }, [
//                 h(
//                   FormItem,
//                   {
//                     label: '年龄',
//                     name: 'age',
//                     rules: [{ required: true, message: '请输入年龄' }],
//                   },
//                   () =>
//                     h(Input, {
//                       placeholder: '请输入年龄',
//                       value: formData.age,
//                       'onUpdate:value': (val: string) =>
//                         (formData.age = Number(val)),
//                       type: 'number',
//                     }),
//                 ),
//               ]),
//               h(Col, { span: 12 }, [
//                 h(
//                   FormItem,
//                   {
//                     label: '邮箱',
//                     name: 'email',
//                     rules: [{ required: true, message: '请输入邮箱' }],
//                   },
//                   () =>
//                     h(Input, {
//                       placeholder: '请输入邮箱',
//                       value: formData.email,
//                       'onUpdate:value': (val: string) => (formData.email = val),
//                     }),
//                 ),
//               ]),
//               h(Col, { span: 12 }, [
//                 h(
//                   FormItem,
//                   {
//                     label: '状态',
//                     name: 'status',
//                     rules: [{ required: true, message: '请选择状态' }],
//                   },
//                   () =>
//                     h(Select, {
//                       placeholder: '请选择状态',
//                       value: formData.status,
//                       'onUpdate:value': (val: number) =>
//                         (formData.status = val),
//                       options: statusOptions,
//                     }),
//                 ),
//               ]),
//             ],
//           },
//           {
//             title: '工作信息',
//             content: (formData: any) => [
//               h(Col, { span: 12 }, [
//                 h(
//                   FormItem,
//                   {
//                     label: '部门',
//                     name: 'department',
//                     rules: [{ required: true, message: '请选择部门' }],
//                   },
//                   () =>
//                     h(Select, {
//                       placeholder: '请选择部门',
//                       value: formData.department,
//                       'onUpdate:value': (val: string) =>
//                         (formData.department = val),
//                       options: departmentOptions,
//                     }),
//                 ),
//               ]),
//               h(Col, { span: 12 }, [
//                 h(
//                   FormItem,
//                   {
//                     label: '职位',
//                     name: 'position',
//                     rules: [{ required: true, message: '请输入职位' }],
//                   },
//                   () =>
//                     h(Input, {
//                       placeholder: '请输入职位',
//                       value: formData.position,
//                       'onUpdate:value': (val: string) =>
//                         (formData.position = val),
//                     }),
//                 ),
//               ]),
//               h(Col, { span: 12 }, [
//                 h(
//                   FormItem,
//                   {
//                     label: '入职日期',
//                     name: 'joinDate',
//                     rules: [{ required: true, message: '请选择入职日期' }],
//                   },
//                   () =>
//                     h(Input, {
//                       placeholder: '请选择入职日期',
//                       value: formData.joinDate,
//                       'onUpdate:value': (val: string) =>
//                         (formData.joinDate = val),
//                       type: 'date',
//                     }),
//                 ),
//               ]),
//             ],
//           },
//         ]);
//     },
//   };
// };

// 简化后的表单配置
const renderForm = (form: any) => {
  return form.renderFormModal([
    {
      title: '基本信息',
      fields: [
        {
          name: 'name',
          label: '姓名',
          component: 'Input',
          rules: [{ required: true, message: '请输入姓名' }],
          props: { placeholder: '请输入姓名' },
          col: { span: 12 },
        },
        {
          name: 'age',
          label: '年龄',
          component: 'Input',
          rules: [{ required: true, message: '请输入年龄' }],
          props: { placeholder: '请输入年龄' },
          col: { span: 12 },
        },
        {
          name: 'status',
          label: '状态',
          component: 'Select',
          options: statusOptions,
          rules: [{ required: true, message: '请选择状态' }],
          col: { span: 12 },
        },
        // 添加只在状态为"在职"时显示的奖金字段
        {
          name: 'bonus',
          label: '奖金',
          component: 'InputNumber',
          props: { placeholder: '请输入奖金' },
          col: { span: 12 },
          // 条件显示 - 只在状态为"在职"(1)时显示
          show: (formData) => formData.status === 1,
        },
        // 添加只在年龄大于30时显示的职级字段
        {
          name: 'level',
          label: '职级',
          component: 'Select',
          options: [
            { label: '初级', value: 'junior' },
            { label: '中级', value: 'mid' },
            { label: '高级', value: 'senior' },
          ],
          col: { span: 12 },
          // 条件显示 - 只在年龄大于30时显示
          show: (formData) => Number(formData.age) > 30,
        },
      ],
    },
    // 添加只在状态为"离职"时显示的分组
    {
      title: '离职信息',
      // 整个分组的条件显示
      visible: (formData) => formData.status === 0,
      fields: [
        {
          name: 'leaveDate',
          label: '离职日期',
          component: 'DatePicker',
          props: { style: 'width: 100%' },
          col: { span: 12 },
          rules: [{ required: true, message: '请选择离职日期' }],
        },
        {
          name: 'leaveReason',
          label: '离职原因',
          component: 'Textarea',
          props: { rows: 2 },
          col: { span: 12 },
        },
      ],
    },
  ]);
};

// 批量操作按钮
const batchButtons = [
  {
    text: '批量删除',
    type: 'danger',
    icon: MdiTrashCan,
    onClick: (keys: any[], _rows: any[]) => {
      message.success(`批量删除: ${keys.join(', ')}`);
    },
  },
];
</script>

<template>
  <div class="ansheng-crud-demo p-4">
    <Card title="AnsengCrud组件示例">
      <AnsengCrud
        ref="crudRef"
        :options="crudConfig"
        :show-batch-actions="true"
        :batch-buttons="batchButtons"
        :render-form="renderForm"
      >
        <!-- 自定义列渲染 -->
        <template #column-status="{ text }">
          <div :style="{ color: text === 1 ? '#52c41a' : '#ff4d4f' }">
            {{ text === 1 ? '启用' : '禁用' }}
          </div>
        </template>
      </AnsengCrud>
    </Card>
  </div>
</template>

<style scoped>
.ansheng-crud-demo {
  /* padding: 24px; */
  max-width: 1200px;
  /* margin: 0 auto; */
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
