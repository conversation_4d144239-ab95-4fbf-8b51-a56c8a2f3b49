<script setup lang="ts">
import { defineComponent, h, onMounted } from 'vue';

import { MdiExport, MdiPlus } from '@vben/icons';

import { Form, Input, message, Select } from 'ant-design-vue';

import {
  BasicTable,
  DetailModal,
  SearchToolbar,
  useAnsheng,
} from '#/hooks/useAnsheng';

// 模拟API服务
const mockUserService = {
  getList: async (params: any) => {
    // 模拟延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 模拟分页和搜索
    const pageSize = params.pageSize || 10;
    const current = params.current || 1;

    // 生成随机数据
    const mockData = Array.from({ length: 100 }, (_, index) => ({
      id: index + 1,
      name: `用户${index + 1}`,
      age: Math.floor(Math.random() * 50) + 18,
      email: `user${index + 1}@example.com`,
      status: Math.random() > 0.3 ? 1 : 0,
      department: ['研发部', '市场部', '销售部', '人事部'][
        Math.floor(Math.random() * 4)
      ],
      position: ['工程师', '经理', '总监', '专员'][
        Math.floor(Math.random() * 4)
      ],
      joinDate: new Date(Date.now() - Math.random() * 31_536_000_000 * 5)
        .toISOString()
        .split('T')[0],
    }));

    // 搜索过滤
    let filteredData = [...mockData];
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase();
      filteredData = filteredData.filter(
        (item) =>
          item.name.toLowerCase().includes(keyword) ||
          item.email.toLowerCase().includes(keyword),
      );
    }

    if (
      params.status !== undefined &&
      params.status !== null &&
      params.status !== ''
    ) {
      filteredData = filteredData.filter(
        (item) => item.status === Number(params.status),
      );
    }

    if (params.department) {
      filteredData = filteredData.filter(
        (item) => item.department === params.department,
      );
    }

    // 分页
    const total = filteredData.length;
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    const rows = filteredData.slice(start, end);

    return {
      code: 1,
      msg: '获取成功',
      data: {
        rows,
        total,
      },
    };
  },

  getDetail: async (id: number) => {
    await new Promise((resolve) => setTimeout(resolve, 300));
    return {
      code: 1,
      msg: '获取成功',
      data: {
        id,
        name: `用户${id}`,
        age: Math.floor(Math.random() * 50) + 18,
        email: `user${id}@example.com`,
        status: Math.random() > 0.3 ? 1 : 0,
        department: ['研发部', '市场部', '销售部', '人事部'][
          Math.floor(Math.random() * 4)
        ],
        position: ['工程师', '经理', '总监', '专员'][
          Math.floor(Math.random() * 4)
        ],
        joinDate: new Date(Date.now() - Math.random() * 31_536_000_000 * 5)
          .toISOString()
          .split('T')[0],
      },
    };
  },

  create: async (params: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`创建用户:${JSON.stringify(params)}`);
    return {
      code: 1,
      msg: '创建成功',
    };
  },

  update: async (id: number, params: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`更新用户:${id}, ${JSON.stringify(params)}`);
    return {
      code: 1,
      msg: '更新成功',
    };
  },

  delete: async (id: number) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    message.info(`删除用户:${id}`);
    return {
      code: 1,
      msg: '删除成功',
    };
  },
};

// 表单项
const { FormItem } = Form;

// 部门选项
const departmentOptions = [
  { label: '研发部', value: '研发部' },
  { label: '市场部', value: '市场部' },
  { label: '销售部', value: '销售部' },
  { label: '人事部', value: '人事部' },
];

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
];

// 使用useAnsheng hook
const {
  // 状态和数据
  formData,
  tableData,
  loading,
  pagination,
  actionButtons,

  // 方法
  handleTableChange,
  initialize,
  createItem,

  // 搜索相关
  searchToolbarBind,
  handleSearch,
  handleReset,

  // 表格相关
  tableBind,

  // 子hook
  detail,
  form,
  table,
} = useAnsheng({
  formOptions: {
    title: (isEdit) => (isEdit ? '编辑用户' : '创建用户'),
    width: 700,
    defaultValues: {
      name: '',
      age: 18,
      email: '',
      status: 1,
      department: '研发部',
      position: '',
      joinDate: '',
    },
    getDetail: mockUserService.getDetail,
    create: mockUserService.create,
    update: mockUserService.update,
    fullscreenable: true,
    draggable: true,
    onSuccess: () => {
      message.success('操作成功');
      table.getList();
    },
  },
  tableOptions: {
    api: mockUserService.getList,
    defaultPageSize: 10,
    columns: [
      { title: '姓名', dataIndex: 'name', width: 100 },
      { title: '年龄', dataIndex: 'age', width: 80 },
      { title: '邮箱', dataIndex: 'email', width: 200 },
      { title: '部门', dataIndex: 'department', width: 120 },
      { title: '职位', dataIndex: 'position', width: 120 },
      { title: '入职日期', dataIndex: 'joinDate', width: 120 },
      {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        customRender: ({ text }) => (text === 1 ? '启用' : '禁用'),
      },
    ],
    actionButtons: [
      {
        key: 'edit',
        text: '编辑',
        onClick: (record) => {
          form.show(record.id);
        },
      },
      {
        key: 'view',
        text: '查看',
        onClick: (record) => {
          if (detail) {
            detail.open(record);
          }
        },
      },
      {
        key: 'delete',
        text: '删除',
        danger: true,
        onClick: (record) => {
          mockUserService.delete(record.id).then(() => {
            message.success('删除成功');
            table.getList();
          });
        },
      },
    ],
  },
  searchOptions: {
    basicItems: [
      {
        field: 'keyword',
        component: 'Input',
        label: '关键词',
        span: 6,
      },
      {
        field: 'status',
        component: 'Select',
        label: '状态',
        span: 6,
        options: statusOptions,
      },
      {
        field: 'department',
        component: 'Select',
        label: '部门',
        span: 6,
        options: departmentOptions,
      },
    ],
    customButtons: [
      {
        text: '新增用户',
        icon: MdiPlus,
        type: 'primary',
        onClick: () => createItem(),
      },
      {
        text: '导出',
        icon: MdiExport,
        onClick: () => {
          message.success('导出功能演示');
        },
      },
    ],
  },
  detailOptions: {
    sections: [
      {
        title: '基本信息',
        items: [
          { label: '姓名', field: 'name' },
          { label: '年龄', field: 'age' },
          { label: '邮箱', field: 'email' },
          {
            label: '状态',
            field: 'status',
            formatter: (val: number) => (val === 1 ? '启用' : '禁用'),
          },
        ],
      },
      {
        title: '工作信息',
        items: [
          { label: '部门', field: 'department' },
          { label: '职位', field: 'position' },
          { label: '入职日期', field: 'joinDate' },
        ],
      },
    ],
  },
  deleteApi: mockUserService.delete,
  viewMode: 'detail',
});

// 创建表单模态框组件
const formComponent = defineComponent({
  setup() {
    return () =>
      form.renderFormModal([
        {
          title: '基本信息',
          content: () => [
            h(
              FormItem,
              {
                label: '姓名',
                name: 'name',
                rules: [{ required: true, message: '请输入姓名' }],
              },
              () => [
                h(Input, {
                  value: formData.name,
                  'onUpdate:value': (val) => (formData.name = val),
                }),
              ],
            ),
            h(
              FormItem,
              {
                label: '年龄',
                name: 'age',
                rules: [{ required: true, message: '请输入年龄' }],
              },
              () => [
                h(Input, {
                  value: formData.age,
                  'onUpdate:value': (val) => (formData.age = Number(val)),
                  type: 'number',
                }),
              ],
            ),
            h(
              FormItem,
              {
                label: '邮箱',
                name: 'email',
                rules: [{ required: true, message: '请输入邮箱' }],
              },
              () => [
                h(Input, {
                  value: formData.email,
                  'onUpdate:value': (val) => (formData.email = val),
                }),
              ],
            ),
            h(
              FormItem,
              {
                label: '状态',
                name: 'status',
                rules: [{ required: true, message: '请选择状态' }],
              },
              () => [
                h(Select, {
                  value: formData.status,
                  'onUpdate:value': (val) => (formData.status = Number(val)),
                  options: statusOptions,
                }),
              ],
            ),
          ],
        },
        {
          title: '工作信息',
          content: () => [
            h(
              FormItem,
              {
                label: '部门',
                name: 'department',
                rules: [{ required: true, message: '请选择部门' }],
              },
              () => [
                h(Select, {
                  value: formData.department,
                  'onUpdate:value': (val) =>
                    (formData.department = String(val)),
                  options: departmentOptions,
                }),
              ],
            ),
            h(
              FormItem,
              {
                label: '职位',
                name: 'position',
                rules: [{ required: true, message: '请输入职位' }],
              },
              () => [
                h(Input, {
                  value: formData.position,
                  'onUpdate:value': (val) => (formData.position = val),
                }),
              ],
            ),
            h(
              FormItem,
              {
                label: '入职日期',
                name: 'joinDate',
                rules: [{ required: true, message: '请选择入职日期' }],
              },
              () => [
                h(Input, {
                  value: formData.joinDate,
                  'onUpdate:value': (val) => (formData.joinDate = val),
                  type: 'date',
                }),
              ],
            ),
          ],
        },
      ]);
  },
});

// 页面加载时初始化数据
onMounted(() => {
  initialize();
});
</script>

<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">useAnsheng Hooks 演示</h1>
      <p class="page-description">
        展示使用 useAnsheng hooks 实现的完整 CRUD 功能
      </p>
    </div>

    <!-- 搜索工具栏 -->
    <div class="toolbar-container">
      <SearchToolbar
        v-bind="searchToolbarBind"
        @search="handleSearch"
        @reset="handleReset"
      />
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <BasicTable
        v-bind="tableBind"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        show-index
        show-action
        :action-buttons="actionButtons"
        @change="handleTableChange"
      />
    </div>

    <!-- 表单弹窗 -->
    <component :is="formComponent" />

    <!-- 详情弹窗 -->
    <DetailModal
      v-if="detail"
      v-model:visible="detail.visible"
      :sections="detail.sections"
      :title="detail.title"
      :record="detail.record"
    />
  </div>
</template>

<style scoped>
.page-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 48px);
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #1f1f1f;
}

.page-description {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.toolbar-container {
  margin-bottom: 16px;
  background-color: #fff;
  padding: 16px;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.table-container {
  background-color: #fff;
  padding: 16px;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}
</style>
