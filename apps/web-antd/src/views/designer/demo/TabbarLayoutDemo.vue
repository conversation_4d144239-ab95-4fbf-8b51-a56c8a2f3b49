<script setup lang="ts">
import { ref } from 'vue';

import { ProjectLayoutMode } from '@vben/lowcode/core';
import { AppLayout } from '@vben/lowcode/components';

// 演示项目配置
const demoProjectConfig = ref({
  id: 'demo-project',
  name: '演示项目',
  layoutMode: ProjectLayoutMode.TABBAR,
  tabbarConfig: {
    position: 'bottom' as const,
    fixed: true,
    tabs: [
      {
        id: 'tab-home',
        name: 'home',
        title: '首页',
        icon: 'home-o',
        pageId: 'home',
      },
      {
        id: 'tab-category',
        name: 'category', 
        title: '分类',
        icon: 'apps-o',
        pageId: 'category',
      },
      {
        id: 'tab-cart',
        name: 'cart',
        title: '购物车',
        icon: 'shopping-cart-o',
        pageId: 'cart',
      },
      {
        id: 'tab-user',
        name: 'user',
        title: '我的',
        icon: 'user-o',
        pageId: 'user',
      },
    ],
  },
  pages: [
    {
      id: 'home',
      title: '首页',
      path: '/pages/home',
      showInTabbar: true,
      components: [
        {
          id: 'text-1',
          type: 'Text',
          props: {
            text: '欢迎来到首页！',
            size: 'large',
            color: '#1890ff',
          },
          style: {
            textAlign: 'center',
            padding: '20px',
          },
        },
        {
          id: 'button-1',
          type: 'Button',
          props: {
            text: '点击我',
            type: 'primary',
            size: 'large',
          },
          style: {
            margin: '20px auto',
            display: 'block',
          },
        },
      ],
    },
    {
      id: 'category',
      title: '分类',
      path: '/pages/category',
      showInTabbar: true,
      components: [
        {
          id: 'text-2',
          type: 'Text',
          props: {
            text: '这是分类页面',
            size: 'large',
          },
          style: {
            textAlign: 'center',
            padding: '20px',
          },
        },
      ],
    },
    {
      id: 'cart',
      title: '购物车',
      path: '/pages/cart',
      showInTabbar: true,
      components: [
        {
          id: 'text-3',
          type: 'Text',
          props: {
            text: '购物车是空的',
            size: 'large',
          },
          style: {
            textAlign: 'center',
            padding: '20px',
          },
        },
      ],
    },
    {
      id: 'user',
      title: '我的',
      path: '/pages/user',
      showInTabbar: true,
      components: [
        {
          id: 'text-4',
          type: 'Text',
          props: {
            text: '个人中心',
            size: 'large',
          },
          style: {
            textAlign: 'center',
            padding: '20px',
          },
        },
      ],
    },
  ],
});

const currentPageId = ref('home');

// 处理Tab切换
const handleTabChange = (pageId: string) => {
  currentPageId.value = pageId;
  console.log('切换到页面:', pageId);
};

// 处理导航
const handleNavigate = (path: string, pageId: string) => {
  console.log('导航到:', path, pageId);
};
</script>

<template>
  <div class="tabbar-layout-demo">
    <div class="demo-header">
      <h2>TabBar布局演示</h2>
      <p>这是一个完整的TabBar布局演示，展示了如何处理TabBar与页面的绑定关系</p>
    </div>
    
    <div class="demo-container">
      <div class="phone-mockup">
        <AppLayout
          :project-config="demoProjectConfig"
          :current-page-id="currentPageId"
          :design-mode="true"
          @tab-change="handleTabChange"
          @navigate="handleNavigate"
        />
      </div>
      
      <div class="demo-info">
        <h3>当前配置</h3>
        <div class="config-item">
          <strong>布局模式:</strong> {{ demoProjectConfig.layoutMode }}
        </div>
        <div class="config-item">
          <strong>当前页面:</strong> {{ currentPageId }}
        </div>
        <div class="config-item">
          <strong>TabBar位置:</strong> {{ demoProjectConfig.tabbarConfig?.position }}
        </div>
        <div class="config-item">
          <strong>Tab数量:</strong> {{ demoProjectConfig.tabbarConfig?.tabs.length }}
        </div>
        
        <h4>页面列表</h4>
        <ul class="page-list">
          <li 
            v-for="page in demoProjectConfig.pages" 
            :key="page.id"
            :class="{ active: page.id === currentPageId }"
          >
            {{ page.title }} ({{ page.id }})
            <span v-if="page.showInTabbar" class="tabbar-badge">TabBar</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<style scoped>
.tabbar-layout-demo {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h2 {
  color: #1890ff;
  margin-bottom: 10px;
}

.demo-container {
  display: flex;
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.phone-mockup {
  width: 375px;
  height: 667px;
  background-color: #000;
  border-radius: 20px;
  padding: 10px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.demo-info {
  flex: 1;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-item {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.page-list {
  list-style: none;
  padding: 0;
}

.page-list li {
  padding: 8px 12px;
  margin-bottom: 4px;
  background-color: #f8f9fa;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-list li.active {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}

.tabbar-badge {
  background-color: #52c41a;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
}
</style>
