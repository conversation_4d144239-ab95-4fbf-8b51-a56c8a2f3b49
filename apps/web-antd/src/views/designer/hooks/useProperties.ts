import type { Ref } from 'vue';

import { computed, h, reactive, watch } from 'vue';

import {
  Checkbox,
  DatePicker,
  Input,
  InputNumber,
  RadioButton,
  RadioGroup,
  Select,
  SelectOption,
  Slider,
  Switch,
  TimePicker,
} from 'ant-design-vue';

export interface PropSchema {
  key: string;
  label: string;
  type: string;
  defaultValue?: any;
  options?: Array<{ label: string; value: any }>;
  optionsProvider?: string;
  min?: number;
  max?: number;
  step?: number;
}

export function useProperties(
  selectedComponent: Ref<any>,
  componentRegistry?: any,
) {
  // 属性面板的表单模型
  const propsForm = reactive({
    model: {},
  });

  // 当选中组件变化时，更新属性表单
  watch(
    selectedComponent,
    (newVal) => {
      propsForm.model = newVal && newVal.props ? { ...newVal.props } : {};
    },
    { immediate: true },
  );

  // 获取动态选项
  function getDynamicOptions(optionsProvider: string) {
    if (!componentRegistry || !optionsProvider) {
      return [];
    }

    try {
      if (optionsProvider === 'getRegisteredPages') {
        const getPages = componentRegistry.getRegisteredPages;
        if (typeof getPages === 'function') {
          return getPages() || [];
        }
      } else if (optionsProvider === 'getRegisteredComponents') {
        const getComponents = componentRegistry.getRegisteredComponents;
        if (typeof getComponents === 'function') {
          return getComponents() || [];
        }
      }
      // 可以添加更多的选项提供器
    } catch (error) {
      console.error('获取动态选项失败:', error);
    }

    return [];
  }

  // 处理属性变化
  function handlePropChange(key, value) {
    if (selectedComponent.value && selectedComponent.value.props) {
      // 直接修改原对象以确保响应性，然后通过重新赋值触发响应
      selectedComponent.value.props[key] = value;

      // 通过创建新引用来确保响应性
      propsForm.model = { ...selectedComponent.value.props };
    }
  }

  // 根据属性模式渲染不同的编辑器组件
  function renderPropEditor(propSchema: PropSchema) {
    const { key, type, options, optionsProvider, min, max, step } = propSchema;

    // 获取动态选项
    let actualOptions: Array<{ label: string; value: any }> = [];
    if (optionsProvider) {
      actualOptions = getDynamicOptions(optionsProvider);
    } else if (options) {
      actualOptions = options;
    }

    // 使用computed属性来确保响应式
    const propValue = computed(() => propsForm.model[key]);

    switch (type) {
      case 'checkbox': {
        return h(Checkbox, {
          checked: propValue.value,
          'onUpdate:checked': (val) => handlePropChange(key, val),
        });
      }
      case 'date': {
        return h(DatePicker, {
          value: propValue.value,
          'onUpdate:value': (val) => handlePropChange(key, val),
          class: 'w-full',
        });
      }
      case 'input':
      case 'text': {
        return h(Input, {
          value: propValue.value,
          'onUpdate:value': (val) => handlePropChange(key, val),
        });
      }
      case 'number': {
        return h(InputNumber, {
          value: propValue.value,
          'onUpdate:value': (val) => handlePropChange(key, val),
          min: min === undefined ? Number.MIN_SAFE_INTEGER : min,
          max: max === undefined ? Number.MAX_SAFE_INTEGER : max,
          step: step || 1,
          class: 'w-full',
        });
      }
      case 'radio-group': {
        return h(
          RadioGroup,
          {
            value: propValue.value,
            'onUpdate:value': (val) => handlePropChange(key, val),
            optionType: 'button',
          },
          () =>
            actualOptions?.map((opt) =>
              h(
                RadioButton,
                { value: opt.value, key: opt.value },
                () => opt.label,
              ),
            ),
        );
      }
      case 'select': {
        return h(
          Select,
          {
            value: propValue.value,
            'onUpdate:value': (val) => handlePropChange(key, val),
            style: { width: '100%' },
          },
          () =>
            actualOptions?.map((opt) =>
              h(
                SelectOption,
                { value: opt.value, key: opt.value },
                () => opt.label,
              ),
            ),
        );
      }
      case 'slider': {
        return h(Slider, {
          value: propValue.value,
          'onUpdate:value': (val) => handlePropChange(key, val),
          min: min === undefined ? 0 : min,
          max: max === undefined ? 100 : max,
          step: step || 1,
        });
      }
      case 'switch': {
        // 使用key属性强制组件重新渲染，确保状态同步
        return h(Switch, {
          key: `switch-${key}-${Boolean(propValue.value)}`,
          checked: Boolean(propValue.value),
          onChange: (checked) => {
            handlePropChange(key, checked);
          },
        });
      }
      case 'textarea': {
        return h(Input.TextArea, {
          value: propValue.value,
          'onUpdate:value': (val) => handlePropChange(key, val),
          rows: 3,
        });
      }
      case 'time': {
        return h(TimePicker, {
          value: propValue.value,
          'onUpdate:value': (val) => handlePropChange(key, val),
          class: 'w-full',
        });
      }
      default: {
        return h(Input, {
          value: propValue.value,
          'onUpdate:value': (val) => handlePropChange(key, val),
        });
      }
    }
  }

  return {
    propsForm,
    handlePropChange,
    renderPropEditor,
  };
}
