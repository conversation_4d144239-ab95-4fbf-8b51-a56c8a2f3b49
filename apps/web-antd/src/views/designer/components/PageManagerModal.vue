<script setup lang="ts">
import { reactive } from 'vue';

import { Button, Divider, Input, message, Modal } from 'ant-design-vue';

const props = defineProps<{
  currentPageId: string;
  pageList: { id: string; path: string; title: string }[];
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'switchPage', pageId: string): void;
  (e: 'addPage', page: { id: string; path: string; title: string }): void;
  (e: 'deletePage', pageId: string): void;
}>();

// 新页面表单
const newPage = reactive({
  id: '',
  title: '',
  path: '',
});

// 添加页面
function addPage() {
  if (!newPage.id || !newPage.title || !newPage.path) {
    message.error('页面信息不完整');
    return;
  }

  // 检查ID是否已存在
  if (props.pageList.some((page) => page.id === newPage.id)) {
    message.error('页面ID已存在');
    return;
  }

  // 提交新页面
  emit('addPage', { ...newPage });

  // 重置表单
  newPage.id = '';
  newPage.title = '';
  newPage.path = '';

  message.success('页面添加成功');
}
</script>

<template>
  <Modal
    :visible="visible"
    title="页面管理"
    width="600px"
    @update:visible="(val) => $emit('update:visible', val)"
  >
    <div class="page-manager">
      <div class="page-list">
        <div class="page-list-header">
          <div class="page-header-item page-id">页面ID</div>
          <div class="page-header-item page-title">页面标题</div>
          <div class="page-header-item page-path">页面路径</div>
          <div class="page-header-item page-action">操作</div>
        </div>
        <div v-for="page in pageList" :key="page.id" class="page-item">
          <div class="page-item-col page-id">{{ page.id }}</div>
          <div class="page-item-col page-title">{{ page.title }}</div>
          <div class="page-item-col page-path">{{ page.path }}</div>
          <div class="page-item-col page-action">
            <Button
              size="small"
              type="primary"
              class="mr-2"
              :disabled="currentPageId === page.id"
              @click="
                $emit('switchPage', page.id);
                $emit('update:visible', false);
              "
            >
              编辑
            </Button>
            <Button size="small" danger @click="$emit('deletePage', page.id)">
              删除
            </Button>
          </div>
        </div>
      </div>

      <Divider>添加新页面</Divider>

      <div class="add-page-form">
        <div class="form-row">
          <div class="form-label">页面ID:</div>
          <div class="form-field">
            <Input v-model:value="newPage.id" placeholder="如: about" />
          </div>
        </div>
        <div class="form-row">
          <div class="form-label">页面标题:</div>
          <div class="form-field">
            <Input v-model:value="newPage.title" placeholder="如: 关于我们" />
          </div>
        </div>
        <div class="form-row">
          <div class="form-label">页面路径:</div>
          <div class="form-field">
            <Input
              v-model:value="newPage.path"
              placeholder="如: /pages/about"
            />
          </div>
        </div>
        <div class="form-row">
          <Button type="primary" @click="addPage">添加页面</Button>
        </div>
      </div>
    </div>

    <template #footer>
      <Button @click="$emit('update:visible', false)">关闭</Button>
    </template>
  </Modal>
</template>

<style scoped>
.page-manager {
  padding: 10px;
}

.page-list {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 20px;
}

.page-list-header {
  display: flex;
  background-color: #fafafa;
  font-weight: bold;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.page-header-item {
  padding: 0 10px;
}

.page-item {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.page-item:last-child {
  border-bottom: none;
}

.page-item-col {
  padding: 0 10px;
  display: flex;
  align-items: center;
}

.page-id {
  width: 20%;
}

.page-title {
  width: 25%;
}

.page-path {
  width: 40%;
}

.page-action {
  width: 15%;
}

.add-page-form {
  padding: 10px;
}

.form-row {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
}

.form-label {
  width: 80px;
  text-align: right;
  padding-right: 10px;
}

.form-field {
  flex: 1;
}
</style>
