<script setup lang="ts">
import { ref } from 'vue';

import { cloneDeep } from 'lodash-es';
import { nanoid } from 'nanoid';
import Draggable from 'vuedraggable';

// 简单的组件库
const componentsLibrary = ref([
  {
    id: 'btn1',
    type: 'button',
    label: '按钮1',
    defaultProps: { text: '按钮1' },
  },
  {
    id: 'btn2',
    type: 'button',
    label: '按钮2',
    defaultProps: { text: '按钮2' },
  },
  { id: 'txt1', type: 'text', label: '文本1', defaultProps: { text: '文本1' } },
]);

// 画布组件
const canvasItems = ref([]);

// 克隆处理函数
function handleClone(item) {
  console.log('克隆组件:', item);
  return {
    ...cloneDeep(item),
    id: nanoid(),
    props: { ...item.defaultProps },
  };
}

function log(msg, data) {
  console.log(msg, data);
}
</script>

<template>
  <div class="test-container">
    <h2>测试拖拽功能</h2>

    <div class="flex">
      <div class="component-list border p-4">
        <h3>组件列表</h3>
        <Draggable
          :list="componentsLibrary"
          :group="{ name: 'components', pull: 'clone', put: false }"
          :sort="false"
          :clone="handleClone"
          item-key="id"
          class="grid gap-2 p-2"
          @start="log('开始拖拽', $event)"
          @end="log('结束拖拽', $event)"
        >
          <template #item="{ element }">
            <div class="component-item border bg-gray-100 p-2">
              {{ element.label }}
            </div>
          </template>
        </Draggable>
      </div>

      <div class="canvas ml-4 min-h-52 flex-1 border p-4">
        <h3>画布区域</h3>
        <Draggable
          v-model="canvasItems"
          :group="{ name: 'components', pull: false, put: true }"
          item-key="id"
          class="min-h-40 border-2 border-dashed p-2"
          @change="log('画布变化', $event)"
        >
          <template #item="{ element }">
            <div class="canvas-item mb-2 border bg-blue-100 p-2">
              {{ element.label }} ({{ element.id }})
            </div>
          </template>
        </Draggable>

        <div class="mt-4">
          <pre>{{ JSON.stringify(canvasItems, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.test-container {
  padding: 20px;
}
</style>
