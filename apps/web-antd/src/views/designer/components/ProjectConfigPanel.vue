<script setup lang="ts">
import type { ProjectConfig, TabConfig } from '@vben/lowcode/core';
import { ProjectLayoutMode } from '@vben/lowcode/core';

import { computed, ref, watch } from 'vue';

import { 
  Button, 
  Card, 
  Divider, 
  Form, 
  FormItem, 
  Input, 
  Modal, 
  Radio, 
  RadioGroup, 
  Select, 
  SelectOption, 
  Space, 
  Switch, 
  Table, 
  Tag 
} from 'ant-design-vue';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons-vue';

interface Props {
  projectConfig: ProjectConfig;
  visible: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  'update:project-config': [config: ProjectConfig];
}>();

// 本地配置副本
const localConfig = ref<ProjectConfig>({ ...props.projectConfig });

// 监听props变化
watch(() => props.projectConfig, (newConfig) => {
  localConfig.value = { ...newConfig };
}, { deep: true });

// Tab编辑相关
const tabEditVisible = ref(false);
const editingTab = ref<TabConfig | null>(null);
const tabForm = ref({
  name: '',
  title: '',
  icon: '',
  pageId: '',
});

// 布局模式选项
const layoutModeOptions = [
  { label: '单页面模式', value: 'single-page' },
  { label: 'TabBar模式', value: 'tabbar' },
  { label: '自定义模式', value: 'custom' },
];

// 可用页面选项
const availablePages = computed(() => {
  return localConfig.value.pages.map(page => ({
    label: page.title,
    value: page.id,
  }));
});

// Tab表格列定义
const tabColumns = [
  { title: 'Tab名称', dataIndex: 'title', key: 'title' },
  { title: '绑定页面', dataIndex: 'pageId', key: 'pageId' },
  { title: '图标', dataIndex: 'icon', key: 'icon' },
  { title: '操作', key: 'action', width: 120 },
];

// 处理布局模式变化
const handleLayoutModeChange = (mode: ProjectLayoutMode) => {
  localConfig.value.layoutMode = mode;
  
  if (mode === 'tabbar' && !localConfig.value.tabbarConfig) {
    // 初始化TabBar配置
    localConfig.value.tabbarConfig = {
      position: 'bottom',
      fixed: true,
      tabs: [],
    };
  }
  
  if (mode === 'single-page') {
    // 清除TabBar配置
    localConfig.value.tabbarConfig = undefined;
    // 清除页面的TabBar标记
    localConfig.value.pages.forEach(page => {
      page.showInTabbar = false;
    });
  }
};

// 添加Tab
const addTab = () => {
  editingTab.value = null;
  tabForm.value = {
    name: '',
    title: '',
    icon: 'home-o',
    pageId: '',
  };
  tabEditVisible.value = true;
};

// 编辑Tab
const editTab = (tab: TabConfig) => {
  editingTab.value = tab;
  tabForm.value = {
    name: tab.name,
    title: tab.title,
    icon: tab.icon || '',
    pageId: tab.pageId,
  };
  tabEditVisible.value = true;
};

// 删除Tab
const deleteTab = (tabId: string) => {
  if (!localConfig.value.tabbarConfig) return;
  
  const index = localConfig.value.tabbarConfig.tabs.findIndex(t => t.id === tabId);
  if (index !== -1) {
    const tab = localConfig.value.tabbarConfig.tabs[index];
    
    // 取消页面的TabBar标记
    const page = localConfig.value.pages.find(p => p.id === tab.pageId);
    if (page) {
      page.showInTabbar = false;
    }
    
    localConfig.value.tabbarConfig.tabs.splice(index, 1);
  }
};

// 保存Tab
const saveTab = () => {
  if (!localConfig.value.tabbarConfig) return;
  
  if (editingTab.value) {
    // 编辑模式
    const index = localConfig.value.tabbarConfig.tabs.findIndex(t => t.id === editingTab.value!.id);
    if (index !== -1) {
      // 取消原页面的TabBar标记
      const oldPage = localConfig.value.pages.find(p => p.id === editingTab.value!.pageId);
      if (oldPage) {
        oldPage.showInTabbar = false;
      }
      
      // 更新Tab
      localConfig.value.tabbarConfig.tabs[index] = {
        ...editingTab.value,
        ...tabForm.value,
      };
    }
  } else {
    // 新增模式
    const newTab: TabConfig = {
      id: `tab-${Date.now()}`,
      ...tabForm.value,
    };
    localConfig.value.tabbarConfig.tabs.push(newTab);
  }
  
  // 标记新页面在TabBar中显示
  const page = localConfig.value.pages.find(p => p.id === tabForm.value.pageId);
  if (page) {
    page.showInTabbar = true;
  }
  
  tabEditVisible.value = false;
};

// 获取页面标题
const getPageTitle = (pageId: string) => {
  const page = localConfig.value.pages.find(p => p.id === pageId);
  return page?.title || pageId;
};

// 保存配置
const saveConfig = () => {
  emit('update:project-config', { ...localConfig.value });
  emit('update:visible', false);
};

// 取消
const cancel = () => {
  localConfig.value = { ...props.projectConfig };
  emit('update:visible', false);
};
</script>

<template>
  <Modal
    :visible="visible"
    title="项目配置"
    width="800px"
    @ok="saveConfig"
    @cancel="cancel"
  >
    <div class="project-config-panel">
      <!-- 基本信息 -->
      <Card title="基本信息" size="small" class="mb-4">
        <Form layout="vertical">
          <FormItem label="项目名称">
            <Input v-model:value="localConfig.name" placeholder="请输入项目名称" />
          </FormItem>
        </Form>
      </Card>
      
      <!-- 布局配置 -->
      <Card title="布局配置" size="small" class="mb-4">
        <Form layout="vertical">
          <FormItem label="布局模式">
            <RadioGroup 
              :value="localConfig.layoutMode" 
              @change="e => handleLayoutModeChange(e.target.value)"
            >
              <Radio 
                v-for="option in layoutModeOptions" 
                :key="option.value" 
                :value="option.value"
              >
                {{ option.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Form>
      </Card>
      
      <!-- TabBar配置 -->
      <Card 
        v-if="localConfig.layoutMode === 'tabbar'" 
        title="TabBar配置" 
        size="small" 
        class="mb-4"
      >
        <Form layout="vertical">
          <FormItem label="TabBar位置">
            <Select v-model:value="localConfig.tabbarConfig!.position">
              <SelectOption value="bottom">底部</SelectOption>
              <SelectOption value="top">顶部</SelectOption>
            </Select>
          </FormItem>
          
          <FormItem label="固定位置">
            <Switch v-model:checked="localConfig.tabbarConfig!.fixed" />
          </FormItem>
        </Form>
        
        <Divider>Tab配置</Divider>
        
        <div class="mb-3">
          <Button type="primary" @click="addTab">
            <PlusOutlined />
            添加Tab
          </Button>
        </div>
        
        <Table 
          :dataSource="localConfig.tabbarConfig?.tabs || []" 
          :columns="tabColumns"
          :pagination="false"
          size="small"
          rowKey="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'pageId'">
              <Tag color="blue">{{ getPageTitle(record.pageId) }}</Tag>
            </template>
            <template v-else-if="column.key === 'icon'">
              <span v-if="record.icon">{{ record.icon }}</span>
              <span v-else class="text-gray-400">无</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <Space>
                <Button size="small" @click="editTab(record)">
                  <EditOutlined />
                </Button>
                <Button size="small" danger @click="deleteTab(record.id)">
                  <DeleteOutlined />
                </Button>
              </Space>
            </template>
          </template>
        </Table>
      </Card>
    </div>
    
    <!-- Tab编辑弹窗 -->
    <Modal
      v-model:visible="tabEditVisible"
      :title="editingTab ? '编辑Tab' : '添加Tab'"
      @ok="saveTab"
    >
      <Form layout="vertical">
        <FormItem label="Tab名称" required>
          <Input v-model:value="tabForm.name" placeholder="请输入Tab名称（英文）" />
        </FormItem>
        
        <FormItem label="显示标题" required>
          <Input v-model:value="tabForm.title" placeholder="请输入显示标题" />
        </FormItem>
        
        <FormItem label="绑定页面" required>
          <Select v-model:value="tabForm.pageId" placeholder="请选择绑定的页面">
            <SelectOption 
              v-for="page in availablePages" 
              :key="page.value" 
              :value="page.value"
            >
              {{ page.label }}
            </SelectOption>
          </Select>
        </FormItem>
        
        <FormItem label="图标">
          <Input v-model:value="tabForm.icon" placeholder="请输入图标名称" />
        </FormItem>
      </Form>
    </Modal>
  </Modal>
</template>

<style scoped>
.project-config-panel {
  max-height: 600px;
  overflow-y: auto;
}
</style>
