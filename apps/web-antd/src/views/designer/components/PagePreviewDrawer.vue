<script setup lang="ts">
import { ComponentRenderer } from '@vben/lowcode/core';

import { Drawer, Empty, Tag } from 'ant-design-vue';

defineProps<{
  canvasComponents: any[];
  currentPageId: string;
  pageList: { id: string; path: string; title: string }[];
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'switch-page', pageId: string): void;
}>();
</script>

<template>
  <Drawer
    :visible="visible"
    title="页面预览"
    width="390"
    placement="right"
    @update:visible="(val) => $emit('update:visible', val)"
  >
    <div class="preview-container">
      <div class="preview-header">
        <span
          >当前预览:
          {{ pageList.find((p) => p.id === currentPageId)?.title }}</span
        >
      </div>
      <div class="preview-tabs">
        <div class="preview-tabs-inner">
          <Tag
            v-for="page in pageList"
            :key="page.id"
            :color="currentPageId === page.id ? 'blue' : 'default'"
            class="preview-page-tag"
            @click="$emit('switch-page', page.id)"
          >
            {{ page.title }}
          </Tag>
        </div>
      </div>
      <div class="preview-phone">
        <div class="phone-header"></div>
        <div class="phone-content">
          <div
            v-for="comp in canvasComponents"
            :key="comp.id"
            class="preview-component"
          >
            <ComponentRenderer
              :type="comp.type"
              :component-props="comp.props"
              :style="comp.style"
            />
          </div>

          <Empty v-if="canvasComponents.length === 0" description="暂无内容" />
        </div>
        <div class="phone-footer"></div>
      </div>
    </div>
  </Drawer>
</template>

<style scoped>
.preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.preview-tabs {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
  overflow-x: auto;
  white-space: nowrap;
}

.preview-tabs-inner {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  padding-bottom: 4px;
}

.preview-page-tag {
  cursor: pointer;
  margin: 0;
  flex-shrink: 0;
}

.preview-phone {
  width: 320px;
  height: auto;
  max-height: calc(100% - 120px);
  margin: 10px auto;
  border: 10px solid #222;
  border-radius: 30px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.phone-header {
  height: 50px;
  background-color: #222;
  position: relative;
}

.phone-header:after {
  content: '';
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 20px;
  background-color: #333;
  border-radius: 20px;
}

.phone-content {
  flex: 1;
  background-color: white;
  overflow-y: auto;
  padding: 16px;
  min-height: 300px;
}

.phone-footer {
  height: 40px;
  background-color: #222;
}

.preview-component {
  margin-bottom: 12px;
  border-bottom: 1px dashed #f0f0f0;
  padding-bottom: 12px;
}
</style>
