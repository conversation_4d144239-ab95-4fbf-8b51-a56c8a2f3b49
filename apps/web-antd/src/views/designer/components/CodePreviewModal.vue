<script setup lang="ts">
import { Modal } from 'ant-design-vue';

defineProps<{
  code: string;
  visible: boolean;
}>();

defineEmits<{
  (e: 'update:visible', visible: boolean): void;
}>();
</script>

<template>
  <Modal
    :visible="visible"
    title="组件代码"
    width="800px"
    :footer="null"
    @update:visible="(val) => $emit('update:visible', val)"
  >
    <pre class="code-preview">{{ code }}</pre>
  </Modal>
</template>

<style scoped>
.code-preview {
  background-color: #001529;
  color: #fff;
  padding: 16px;
  border-radius: 4px;
  max-height: 500px;
  overflow: auto;
}
</style>
