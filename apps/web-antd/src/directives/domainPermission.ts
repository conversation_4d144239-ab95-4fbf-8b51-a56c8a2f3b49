import type { App, Directive, DirectiveBinding } from 'vue';

import { useUserStore } from '@vben/stores';

interface DomainPermissionValue {
  authority?: number; // 权限等级，1表示管理员
  domains?: string[]; // 允许访问的域名列表（可选，会覆盖全局配置）
}

// 全局配置
const config = {
  // 默认允许的域名列表
  allowedDomains: [
    // 生产环境域名
    'ml.liangaiyun.com',
    '*.your-domain.com',
    'admin.your-domain.com',
    'api.your-domain.com',
    // 测试环境域名
    'test.your-domain.com',
    'test-admin.your-domain.com',
    // 开发环境域名
    'localhost',
    '127.0.0.1',
    'dev.local',
    // '',
    // 添加更多允许的域名
  ],
  // 是否在开发环境下禁用域名检查
  disableInDev: false,
};

/**
 * 检查域名是否匹配
 * @param domain 要检查的域名
 * @param currentDomain 当前域名
 * @returns 是否匹配
 */
function isDomainMatch(domain: string, currentDomain: string): boolean {
  // 支持通配符匹配，例如 *.example.com
  if (domain.startsWith('*.')) {
    const suffix = domain.slice(1); // 从 '*.example.com' 得到 '.example.com'
    return currentDomain.endsWith(suffix);
  }
  return currentDomain === domain;
}

/**
 * 检测是否为开发环境
 */
function isDevelopment(): boolean {
  // 使用location检测开发环境，避免使用process.env
  return (
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname.includes('.local')
  );
}

// 域名权限指令
export const domainPermission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const value = binding.value as DomainPermissionValue | number;
    const userStore = useUserStore();
    const authority = userStore.userInfo?.authority;
    const currentDomain = window.location.hostname;

    // 获取当前应该检查的域名列表
    const domainsToCheck =
      typeof value === 'number'
        ? config.allowedDomains
        : value.domains || config.allowedDomains;

    // 检查域名是否匹配
    const isDev = isDevelopment();
    const domainMatches =
      isDev && config.disableInDev
        ? true
        : domainsToCheck.some((domain) => isDomainMatch(domain, currentDomain));

    // 域名不匹配，直接跳过此指令，元素保持显示
    if (!domainMatches) {
      return;
    }

    // 域名匹配后，才进行权限检查

    // 处理数字形式的权限值
    if (typeof value === 'number') {
      const requiredAuthority = value;

      // 如果用户是管理员或不需要管理员权限，保留元素
      if (authority === 1 || requiredAuthority !== 1) {
        return;
      }

      // 需要管理员权限但用户不是管理员，移除元素
      el.remove();
      return;
    }

    // 处理对象形式的权限值
    const requiredAuthority = value.authority || 0;

    // 如果用户是管理员或不需要管理员权限，保留元素
    if (authority === 1 || requiredAuthority !== 1) {
      return;
    }

    // 需要管理员权限但用户不是管理员，移除元素
    el.remove();
  },
};

// 注册指令
export function setupDomainPermissionDirective(app: App) {
  app.directive('domain-permission', domainPermission);
}
