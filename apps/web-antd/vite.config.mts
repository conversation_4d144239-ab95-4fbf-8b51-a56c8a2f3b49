/* eslint-disable n/prefer-global/process */
/* eslint-disable no-console */
import { defineConfig } from '@vben/vite-config';

import { loadEnv } from 'vite';

import { routeModulesTransformPlugin } from './vite-plugin/route-modules-transform';
import { versionFilterPlugin } from './vite-plugin/version-filter';

// 版本配置管理器 - 方便统一管理不同版本的配置
const versionConfigs = {
  // 旗舰版配置
  premium: {
    excludePaths: [],
    excludeRouteModules: ['forum.ts'],
    excludeRouteNames: [],
    // 其他特定于旗舰版的配置...
  },

  // 标准版配置
  standard: {
    // 要排除的文件路径（构建时排除）
    excludePaths: [
      // 'views/channel/card-channel', // 只排除卡片通道相关组件
      // 'router/routes/modules/channel.ts', // 不再排除整个路由文件
      // 'views/recharge', // 充值相关页面
      // 'views/risk', // 风控相关页面
      // 'views/pool', // 资金池相关页面
      // 'views/shop', // 商城相关页面
      // 'views/miniapp', // 小程序相关页面
    ],
    // 要排除的路由模块（运行时排除）
    excludeRouteModules: [
      // 'recharge.ts',
      // 'risk.ts',
      // 'shop.ts',
      // 'miniapp.ts',
      // 'pool.ts',
      'forum.ts',
    ],
    // 要排除的具体路由（通过路由名称匹配）
    excludeRouteNames: [
      'SystemSMS',
      // 'DougongAggregate',
      // 'DougongHuifu',
      // 'HuolianMerchant',
      // 'XiaomageMerchant',
    ],
    // 其他特定于标准版的配置...
  },
};

export default defineConfig(async ({ command, mode }) => {
  // 从环境变量获取版本类型
  const env = loadEnv(mode, process.cwd());
  const versionType =
    process.env.VITE_VERSION_TYPE || env.VITE_VERSION_TYPE || 'standard';
  console.log(`构建模式: ${mode}, 命令: ${command}`);

  // 获取当前版本的配置
  const currentConfig =
    versionConfigs[versionType === 'premium' ? 'premium' : 'standard'];

  // 输出当前版本类型，方便开发时确认
  console.log(
    `\n🚀 当前激活版本: ${versionType === 'premium' ? '旗舰版' : '普通版'} (${versionType})\n`,
  );
  console.log(`🔍 排除路径: ${JSON.stringify(currentConfig.excludePaths)}\n`);
  console.log(
    `🔍 排除路由模块: ${JSON.stringify(currentConfig.excludeRouteModules)}\n`,
  );

  console.log(
    `🔍 排除路由名称: ${JSON.stringify(currentConfig.excludeRouteNames)}\n`,
  );

  const isProduction = command === 'build';

  // 添加路由模块路径到排除路径列表，确保视图和路由文件都被排除
  const allExcludePaths = [...currentConfig.excludePaths];

  // 只有在标准版时才添加路由模块路径
  if (versionType === 'standard') {
    currentConfig.excludeRouteModules.forEach((module) => {
      allExcludePaths.push(`router/routes/modules/${module}`);
    });
  }

  return {
    application: {},
    vite: {
      // 仅修改define部分
      define: {
        __APP_VERSION_TYPE__: JSON.stringify(versionType),
        // 将排除的路由模块和名称注入为全局变量，以JSON字符串格式
        __PREMIUM_EXCLUDE_ROUTE_MODULES__: JSON.stringify(
          versionType === 'premium' ? currentConfig.excludeRouteModules : [],
        ),
        __PREMIUM_EXCLUDE_ROUTE_NAMES__: JSON.stringify(
          versionType === 'premium' ? currentConfig.excludeRouteNames : [],
        ),
        __STANDARD_EXCLUDE_ROUTE_MODULES__: JSON.stringify(
          versionType === 'standard' ? currentConfig.excludeRouteModules : [],
        ),
        __STANDARD_EXCLUDE_ROUTE_NAMES__: JSON.stringify(
          versionType === 'standard' ? currentConfig.excludeRouteNames : [],
        ),
      },
      // 确保version-filter插件尽早运行
      optimizeDeps: {
        exclude: ['vue-demi'],
      },
      plugins: [
        // 版本过滤插件：用于排除视图文件和路由文件
        versionFilterPlugin({
          isProduction,
          premiumExcludes:
            versionType === 'premium' ? currentConfig.excludePaths : [],
          standardExcludes: versionType === 'standard' ? allExcludePaths : [],
          versionType: versionType as 'premium' | 'standard',
        }),
        // 路由模块转换插件：用于清空特定路由模块或移除特定路由
        // routeModulesTransformPlugin({
        //   excludeModules:
        //     versionType === 'standard' ? currentConfig.excludeRouteModules : [],
        //   excludeRouteNames:
        //     versionType === 'standard' ? currentConfig.excludeRouteNames : [], // 启用路由名称排除功能
        // }),
        // 修改 routeModulesTransformPlugin 配置部分
        routeModulesTransformPlugin({
          // 移除条件判断，直接使用当前版本的配置
          excludeModules: currentConfig.excludeRouteModules,
          excludeRouteNames: currentConfig.excludeRouteNames,
        }),
        // 添加页面生成器插件 (仅在开发环境)
        // 修改为无条件添加插件
      ],
      server: {
        proxy: {
          '/api': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, ''),
            // target: 'http://365.junda.xyz',
            target: 'https://cmp.tqzhkj.com',
            // target: 'http://iot.jhtxiot.cn',
            // target: 'http://cmp.ppwl.tw.cn/',
            // target: 'http://cmp.xhwl.net.cn',
            // target: 'http://www.fangxingtxkj.com',
            ws: true,
          },
          '/py/media/static': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/py\/media\/static/, ''),
            // target: 'http://365.junda.xyz',
            target: 'https://cmp.tqzhkj.com',
            // target: 'http://iot.jhtxiot.cn',
            // target: 'http://cmp.ppwl.tw.cn/',
            // target: 'http://cmp.xhwl.net.cn/',
            // target: 'http://www.fangxingtxkj.com',
          },
        },
      },
    },
  };
});
