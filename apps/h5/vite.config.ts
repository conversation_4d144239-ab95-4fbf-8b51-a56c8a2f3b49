import { resolve } from 'node:path';
import { fileURLToPath } from 'node:url';

import vue from '@vitejs/plugin-vue';
import { defineConfig } from 'vite';

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(fileURLToPath(new URL('src', import.meta.url))),
    },
  },
  server: {
    port: 3100,
    host: true,
  },
  optimizeDeps: {
    include: ['@vben/lowcode-core', '@vben/lowcode-components'],
  },
});
