{"extends": "../../internal/tsconfig/base.json", "compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["ESNext", "DOM"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "strict": true, "sourceMap": true, "esModuleInterop": true, "skipLibCheck": true}, "references": [{"path": "./tsconfig.node.json"}], "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]}