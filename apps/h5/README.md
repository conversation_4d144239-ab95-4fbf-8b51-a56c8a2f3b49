# H5渲染引擎开发计划（详细版）

## 1. 项目概述

### 1.1 项目背景
H5渲染引擎是低代码平台的核心渲染模块，负责将低代码平台生成的JSON配置转换为可交互的H5页面。作为Monorepo项目的一部分，需要与其他模块（如低代码编辑器）保持良好的协同。该引擎将支持多样化的业务场景，包括表单提交、数据展示、流程处理等，并能适应不同终端设备。

### 1.2 功能定位
- **渲染引擎**：高性能解析JSON配置，实现像素级还原设计图
- **组件加载**：支持动态组件注册、异步加载与预加载策略
- **路由管理**：处理多页面应用的路由，支持多种路由模式与过渡效果
- **状态管理**：集中管理全局状态、页面状态和组件状态
- **主题系统**：支持动态主题切换，满足不同品牌定制需求
- **数据处理**：提供数据获取、处理和双向绑定能力
- **多端适配**：响应式设计，适配不同尺寸的移动设备

### 1.3 项目目标
1. **高性能**：首屏加载时间<2秒，页面切换<300ms
2. **高扩展性**：支持自定义组件便捷接入
3. **高兼容性**：支持Android 5.0+/iOS 10.0+主流浏览器
4. **易维护**：代码模块化，测试覆盖率>80%
5. **轻量级**：核心库体积<100KB (gzipped)

## 2. 技术架构

### 2.1 技术选型详细说明
- **Vue 3**：采用Composition API提高代码复用性和可维护性
  - 使用defineCustomElement构建独立组件
  - 利用Suspense处理异步组件加载
  - 基于Teleport实现模态框等全局组件
  
- **TypeScript**：提供类型安全和代码自文档能力
  - 严格的类型检查（strict: true）
  - 使用类型推导减少冗余代码
  - 利用泛型提高代码复用性

- **Vite**：快速的开发和构建工具
  - 配置模块联邦实现组件共享
  - 优化按需加载策略
  - 自定义插件处理组件预编译

- **Pinia**：轻量级状态管理库
  - 分模块管理不同领域状态
  - 利用持久化插件实现状态存储
  - 实现状态变更的追踪和调试

- **Vue Router**：路由管理
  - 实现动态路由生成
  - 支持路由级别权限控制
  - 管理页面切换动画和过渡效果

- **Vant**：移动端UI组件库
  - 按需加载减少包体积
  - 配置主题变量实现定制外观
  - 扩展组件满足业务需求

- **Axios**：网络请求处理
  - 配置请求/响应拦截器
  - 实现请求缓存和重试机制
  - 处理错误和异常情况

### 2.2 详细目录结构
```
apps/h5/
├── public/ # 静态资源
│ ├── favicon.ico
│ └── static/ # 静态资源文件夹
├── src/
│ ├── components/ # 组件目录
│ │ ├── business/ # 业务组件
│ │ │ ├── form/ # 表单类业务组件
│ │ │ ├── chart/ # 图表类业务组件
│ │ │ ├── list/ # 列表类业务组件
│ │ │ └── card/ # 卡片类业务组件
│ │ └── common/ # 通用功能组件
│ │ ├── feedback/ # 反馈类组件（Toast、Modal等）
│ │ ├── navigation/ # 导航类组件
│ │ └── display/ # 展示类组件
│ ├── core/ # 核心渲染逻辑
│ │ ├── renderer/ # 渲染引擎
│ │ │ ├── index.ts # 渲染入口
│ │ │ ├── dom.ts # DOM操作封装
│ │ │ └── lifecycle.ts # 生命周期管理
│ │ ├── parser/ # JSON解析器
│ │ │ ├── index.ts # 解析入口
│ │ │ ├── schema.ts # Schema定义与验证
│ │ │ └── transformer.ts # 转换器
│ │ ├── loader/ # 组件加载器
│ │ │ ├── index.ts # 加载入口
│ │ │ ├── async.ts # 异步加载实现
│ │ │ └── registry.ts # 组件注册机制
│ │ └── event/ # 事件系统
│ │ ├── index.ts # 事件入口
│ │ ├── bus.ts # 事件总线
│ │ └── handlers.ts # 事件处理器
│ ├── layouts/ # 布局组件
│ │ ├── default/ # 默认布局（带Tabbar）
│ │ │ ├── index.vue
│ │ │ ├── TabBar.vue
│ │ │ └── Header.vue
│ │ └── empty/ # 空布局（无Tabbar）
│ │ ├── index.vue
│ │ └── Header.vue
│ ├── pages/ # 页面目录
│ │ ├── index.vue # 入口页面
│ │ ├── render.vue # 通用渲染页
│ │ └── error.vue # 错误页面
│ ├── router/ # 路由配置
│ │ ├── index.ts # 路由入口
│ │ ├── routes.ts # 路由定义
│ │ └── guards.ts # 路由守卫
│ ├── store/ # 状态管理
│ │ ├── index.ts # 状态管理入口
│ │ ├── app.ts # 应用状态
│ │ ├── page.ts # 页面状态
│ │ └── component.ts # 组件状态
│ ├── styles/ # 样式文件
│ │ ├── index.less # 主样式入口
│ │ ├── variables.less # 变量定义
│ │ ├── themes/ # 主题定义
│ │ ├── animations/ # 动画效果
│ │ └── mixins/ # 混合器
│ ├── utils/ # 工具函数
│ │ ├── request.ts # 网络请求
│ │ ├── storage.ts # 存储管理
│ │ ├── validator.ts # 校验工具
│ │ └── formatter.ts # 格式化工具
│ ├── hooks/ # 自定义Hooks
│ │ ├── useRenderer.ts # 渲染器Hook
│ │ ├── useComponent.ts # 组件Hook
│ │ ├── useTheme.ts # 主题Hook
│ │ └── useDevice.ts # 设备信息Hook
│ ├── plugins/ # 插件
│ │ ├── i18n.ts # 国际化
│ │ ├── analytics.ts # 埋点分析
│ │ └── error-handler.ts # 错误处理
│ ├── constants/ # 常量定义
│ │ ├── events.ts # 事件常量
│ │ ├── storage-keys.ts # 存储键名
│ │ └── error-codes.ts # 错误码
│ ├── types/ # 类型定义
│ │ ├── component.d.ts # 组件类型
│ │ ├── config.d.ts # 配置类型
│ │ ├── event.d.ts # 事件类型
│ │ └── api.d.ts # API类型
│ ├── App.vue # 应用根组件
│ ├── main.ts # 应用入口
│ └── shims-vue.d.ts # Vue类型定义
├── tests/ # 测试目录
│ ├── unit/ # 单元测试
│ ├── integration/ # 集成测试
│ └── e2e/ # 端到端测试
├── mock/ # 数据模拟
├── .eslintrc.js # ESLint配置
├── .prettierrc.js # Prettier配置
├── tsconfig.json # TypeScript配置
├── vite.config.ts # Vite配置
└── package.json # 项目配置
```

## 3. 核心功能模块详细设计

### 3.1 渲染引擎（core/renderer）

#### 3.1.1 渲染流程
1. 接收JSON配置
2. Schema验证
3. 解析组件树
4. 组件实例化
5. 属性绑定
6. 事件绑定
7. 生命周期处理
8. DOM渲染
9. 后处理

#### 3.1.2 JSON配置解析器
- **Schema验证器**：基于ajv实现，支持自定义校验规则
- **配置标准化**：处理默认值和兼容性问题
- **引用解析**：处理组件间的引用关系
- **条件渲染**：支持if/else/switch等条件逻辑
- **循环渲染**：支持数组遍历和动态列表

```typescript
// Schema验证示例
export function validateSchema(config: any): Result<PageConfig> {
  const validator = new Ajv({ allErrors: true });
  const validate = validator.compile(pageConfigSchema);
  const valid = validate(config);
  
  if (!valid) {
    return {
      success: false,
      errors: validator.errors || [],
      data: null
    };
  }
  
  return {
    success: true,
    errors: [],
    data: config as PageConfig
  };
}
```

#### 3.1.3 组件树构建器
- **虚拟DOM构建**：生成VDOM树结构
- **组件依赖解析**：处理组件间父子关系
- **插槽处理**：支持命名插槽和默认插槽
- **条件编译**：根据环境变量处理不同版本

```typescript
// 组件树构建示例
export function buildComponentTree(config: ComponentConfig[]): VNode[] {
  return config.map(item => {
    // 动态获取组件
    const Component = getComponent(item.type);
    
    // 构建props
    const props = transformProps(item.props);
    
    // 处理子节点
    const children = item.children 
      ? buildComponentTree(item.children)
      : [];
    
    // 处理事件
    const events = transformEvents(item.events);
    
    // 生成VNode
    return h(Component, {
      ...props,
      ...events,
      key: item.id
    }, children.length > 0 ? () => children : null);
  });
}
```

#### 3.1.4 属性解析与绑定
- **动态属性**：支持表达式计算
- **数据绑定**：支持双向数据绑定
- **计算属性**：支持组件级计算属性
- **属性监听**：监听属性变化并触发回调

```typescript
// 属性绑定示例
function bindProps(component: any, props: Record<string, any>, context: RenderContext): void {
  Object.entries(props).forEach(([key, value]) => {
    if (isBindingExpression(value)) {
      // 处理绑定表达式
      createBinding(component, key, value, context);
    } else if (isComputedExpression(value)) {
      // 处理计算表达式
      createComputed(component, key, value, context);
    } else {
      // 静态属性直接赋值
      component[key] = value;
    }
  });
}
```

#### 3.1.5 事件系统处理
- **标准事件绑定**：click, input, change等
- **自定义事件**：支持组件间的事件通信
- **事件代理**：使用事件委托优化性能
- **全局事件**：支持跨组件的事件通信

```typescript
// 事件绑定示例
function bindEvents(component: any, events: Record<string, any>, context: RenderContext): void {
  Object.entries(events).forEach(([eventName, handler]) => {
    if (typeof handler === 'string') {
      // 字符串处理器，解析为函数
      const fn = parseEventHandler(handler, context);
      component.on(eventName, (...args: any[]) => fn(...args));
    } else if (typeof handler === 'object') {
      // 配置对象处理器
      const { action, params } = handler;
      const fn = getActionHandler(action, params, context);
      component.on(eventName, (...args: any[]) => fn(...args));
    } else if (typeof handler === 'function') {
      // 直接函数处理器
      component.on(eventName, handler);
    }
  });
}
```

#### 3.1.6 动态样式处理
- **样式解析**：支持内联样式和类名
- **主题变量**：支持变量替换
- **响应式样式**：基于媒体查询的样式处理
- **动画处理**：支持过渡和动画效果

```typescript
// 样式处理示例
function processStyles(styles: Record<string, any>, theme: Theme): CSSProperties {
  const result: CSSProperties = {};
  
  // 处理标准样式属性
  Object.entries(styles).forEach(([key, value]) => {
    if (typeof value === 'string' && value.includes('$')) {
      // 替换主题变量
      result[key] = replaceThemeVariables(value, theme);
    } else {
      result[key] = value;
    }
  });
  
  // 处理响应式样式
  if (styles.responsive) {
    Object.entries(styles.responsive).forEach(([breakpoint, breakpointStyles]) => {
      // 将响应式样式转换为媒体查询
      const mediaQuery = getMediaQuery(breakpoint);
      if (mediaQuery) {
        result[mediaQuery] = processStyles(breakpointStyles, theme);
      }
    });
  }
  
  return result;
}
```

### 3.2 组件系统（components）

#### 3.2.1 基础组件库
- **表单组件**
  - `Input`: 文本输入，支持多种类型
  - `Select`: 选择器，支持单选和多选
  - `Checkbox`: 复选框，支持组合使用
  - `Radio`: 单选按钮
  - `DatePicker`: 日期选择器
  - `Upload`: 文件上传
  - `Form`: 表单容器，支持校验

- **展示组件**
  - `Text`: 文本展示，支持富文本
  - `Image`: 图片展示，支持懒加载
  - `Icon`: 图标组件，支持多种图标库
  - `Avatar`: 头像组件
  - `Badge`: 徽章组件
  - `Card`: 卡片组件
  - `Collapse`: 折叠面板

- **容器组件**
  - `List`: 列表容器，支持虚拟滚动
  - `Grid`: 网格容器
  - `Tabs`: 标签页容器
  - `Carousel`: 轮播容器
  - `Modal`: 模态框容器
  - `Drawer`: 抽屉容器
  - `PopOver`: 气泡容器

- **功能组件**
  - `Loading`: 加载提示
  - `Toast`: 轻提示
  - `InfiniteScroll`: 无限滚动
  - `PullToRefresh`: 下拉刷新
  - `Skeleton`: 骨架屏
  - `ErrorBoundary`: 错误边界

#### 3.2.2 组件注册机制
```typescript
// 组件注册系统
export class ComponentRegistry {
  private static components: Map<string, ComponentConfig> = new Map();
  
  // 注册组件
  static register(name: string, component: any, meta: ComponentMeta): void {
    if (this.components.has(name)) {
      console.warn(`组件 ${name} 已经注册，将被覆盖`);
    }
    
    this.components.set(name, {
      component,
      meta,
      timestamp: Date.now()
    });
  }
  
  // 批量注册
  static registerBatch(components: Record<string, any>, metas: Record<string, ComponentMeta>): void {
    Object.entries(components).forEach(([name, component]) => {
      this.register(name, component, metas[name] || {});
    });
  }
  
  // 获取组件
  static getComponent(name: string): any {
    const config = this.components.get(name);
    if (!config) {
      console.error(`组件 ${name} 未注册`);
      // 返回错误占位符组件
      return this.components.get('ErrorComponent')?.component;
    }
    return config.component;
  }
  
  // 获取组件元数据
  static getComponentMeta(name: string): ComponentMeta | null {
    return this.components.get(name)?.meta || null;
  }
  
  // 获取所有已注册组件
  static getAllComponents(): [string, ComponentConfig][] {
    return Array.from(this.components.entries());
  }
  
  // 检查组件是否已注册
  static has(name: string): boolean {
    return this.components.has(name);
  }
  
  // 移除组件
  static unregister(name: string): boolean {
    return this.components.delete(name);
  }
}
```

#### 3.2.3 业务组件系统
- **组件元数据定义**：包含属性、事件和插槽信息
- **组件版本管理**：支持多版本并存和兼容性处理
- **组件热更新**：支持运行时更新组件定义
- **组件依赖管理**：处理组件间的依赖关系

```typescript
// 业务组件注册示例
@Component({
  name: 'business-card',
  version: '1.0.0',
  dependencies: ['text', 'image']
})
export class BusinessCard extends BaseComponent {
  @Prop({ type: String, required: true })
  title!: string;
  
  @Prop({ type: String })
  subtitle?: string;
  
  @Prop({ type: String })
  imageUrl?: string;
  
  @Prop({ type: Array, default: () => [] })
  actions!: ActionItem[];
  
  @Event()
  onClick(item: any): void {
    this.$emit('click', item);
  }
  
  // 组件生命周期
  created(): void {
    console.log('BusinessCard created');
  }
  
  mounted(): void {
    console.log('BusinessCard mounted');
  }
}

// 注册组件
ComponentRegistry.register('business-card', BusinessCard, {
  props: [
    { name: 'title', type: 'string', required: true, description: '卡片标题' },
    { name: 'subtitle', type: 'string', description: '卡片副标题' },
    { name: 'imageUrl', type: 'string', description: '卡片图片URL' },
    { name: 'actions', type: 'array', description: '卡片操作按钮' }
  ],
  events: [
    { name: 'click', params: ['item'], description: '点击事件' }
  ],
  slots: [
    { name: 'default', description: '默认插槽' },
    { name: 'footer', description: '底部插槽' }
  ]
});
```

#### 3.2.4 组件生命周期管理
```typescript
// 生命周期管理
export class ComponentLifecycle {
  // 组件实例映射
  private static instances: Map<string, ComponentInstance> = new Map();
  
  // 注册实例
  static registerInstance(id: string, instance: ComponentInstance): void {
    this.instances.set(id, instance);
  }
  
  // 调用生命周期钩子
  static callHook(id: string, hook: LifecycleHook, ...args: any[]): void {
    const instance = this.instances.get(id);
    if (instance && typeof instance[hook] === 'function') {
      instance[hook](...args);
    }
  }
  
  // 销毁实例
  static destroyInstance(id: string): void {
    const instance = this.instances.get(id);
    if (instance) {
      // 调用销毁钩子
      this.callHook(id, 'beforeDestroy');
      // 清理资源
      if (instance.cleanup && typeof instance.cleanup === 'function') {
        instance.cleanup();
      }
      this.instances.delete(id);
      this.callHook(id, 'destroyed');
    }
  }
  
  // 获取所有实例
  static getAllInstances(): Map<string, ComponentInstance> {
    return new Map(this.instances);
  }
}
```

#### 3.2.5 组件间通信机制
- **Props Down**：通过属性向下传递数据
- **Events Up**：通过事件向上传递数据
- **Provide/Inject**：跨层级传递数据
- **Event Bus**：全局事件总线
- **Store**：共享状态管理

```typescript
// 事件总线
export class EventBus {
  private static events: Map<string, Function[]> = new Map();
  
  // 注册事件监听
  static on(event: string, callback: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)?.push(callback);
  }
  
  // 触发事件
  static emit(event: string, ...args: any[]): void {
    const handlers = this.events.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args);
        } catch (error) {
          console.error(`事件处理器异常: ${event}`, error);
        }
      });
    }
  }
  
  // 移除事件监听
  static off(event: string, callback?: Function): void {
    if (!callback) {
      // 移除所有监听器
      this.events.delete(event);
    } else {
      // 移除特定监听器
      const handlers = this.events.get(event);
      if (handlers) {
        const index = handlers.indexOf(callback);
        if (index !== -1) {
          handlers.splice(index, 1);
        }
        if (handlers.length === 0) {
          this.events.delete(event);
        }
      }
    }
  }
  
  // 一次性事件监听
  static once(event: string, callback: Function): void {
    const onceHandler = (...args: any[]) => {
      callback(...args);
      this.off(event, onceHandler);
    };
    this.on(event, onceHandler);
  }
}
```

### 3.3 路由系统（router）

#### 3.3.1 动态路由注册
```typescript
// 路由配置生成
export function generateRoutes(pageConfigs: PageConfig[]): RouteRecordRaw[] {
  return pageConfigs.map(page => ({
    path: page.path,
    name: page.name || `page-${page.id}`,
    component: () => import('../pages/render.vue'),
    props: {
      pageConfig: page
    },
    meta: {
      title: page.title,
      requiresAuth: page.requiresAuth || false,
      layout: page.layout || 'default',
      keepAlive: page.keepAlive || false,
      tabbar: page.showTabbar !== false, // 默认显示tabbar
      transition: page.transition || 'fade'
    }
  }));
}

// 动态添加路由
export function addDynamicRoutes(router: Router, routes: RouteRecordRaw[]): void {
  routes.forEach(route => {
    if (!router.hasRoute(route.name as string)) {
      router.addRoute(route);
    }
  });
}
```

#### 3.3.2 路由守卫配置
```typescript
// 路由守卫
export function setupRouterGuards(router: Router, store: Store): void {
  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {
    // 显示加载进度条
    NProgress.start();
    
    // 设置页面标题
    document.title = to.meta.title || '应用名称';
    
    // 权限控制
    if (to.meta.requiresAuth && !store.state.user.isLoggedIn) {
      // 保存目标路由
      store.commit('app/SET_REDIRECT', to.fullPath);
      next({ name: 'login' });
      return;
    }
    
    // 加载页面配置
    if (to.name === 'dynamic-page') {
      try {
        await store.dispatch('page/loadPageConfig', to.params.pageId);
      } catch (error) {
        next({ name: 'error', params: { code: 404 } });
        return;
      }
    }
    
    next();
  });

  // 全局后置钩子
  router.afterEach(() => {
    // 关闭加载进度条
    NProgress.done();
  });
  
  // 错误处理
  router.onError((error) => {
    console.error('路由错误', error);
    NProgress.done();
  });
}
```

#### 3.3.3 页面切换动画
```vue
<template>
  <RouterView v-slot="{ Component, route }">
    <transition
      :name="route.meta.transition || 'fade'"
      :mode="route.meta.transitionMode || 'out-in'"
      @before-enter="onTransitionStart"
      @after-leave="onTransitionEnd"
    >
      <keep-alive v-if="route.meta.keepAlive">
        <component :is="Component" :key="route.path" />
      </keep-alive>
      <component v-else :is="Component" :key="route.path" />
    </transition>
  </RouterView>
</template>

<script setup>
import { useAppStore } from '../store/app';

const appStore = useAppStore();

function onTransitionStart() {
  appStore.setTransitioning(true);
}

function onTransitionEnd() {
  appStore.setTransitioning(false);
}
</script>

<style lang="less">
// 预定义过渡效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: transform 0.3s ease;
}

.slide-left-enter-from,
.slide-right-leave-to {
  transform: translateX(100%);
}

.slide-left-leave-to,
.slide-right-enter-from {
  transform: translateX(-100%);
}
</style>
```

#### 3.3.4 路由参数处理
```typescript
// 路由参数处理
export function parseRouteParams(route: RouteLocationNormalized): Record<string, any> {
  const params: Record<string, any> = {
    ...route.params,
    ...route.query
  };
  
  // 处理特殊类型参数
  Object.entries(params).forEach(([key, value]) => {
    if (typeof value === 'string') {
      // 尝试解析JSON
      if (value.startsWith('{') || value.startsWith('[')) {
        try {
          params[key] = JSON.parse(value);
        } catch (e) {
          // 保持原值
        }
      }
      // 处理布尔值
      else if (value === 'true') {
        params[key] = true;
      }
      else if (value === 'false') {
        params[key] = false;
      }
      // 处理数字
      else if (/^-?\d+(\.\d+)?$/.test(value)) {
        params[key] = Number(value);
      }
    }
  });
  
  return params;
}
```

### 3.4 布局系统（layouts）

#### 3.4.1 Tabbar布局
```vue
<!-- layouts/default/TabBar.vue -->
<template>
  <div 
    class="tabbar-container" 
    :class="{ 'tabbar-hidden': hidden }"
  >
    <div 
      v-for="(item, index) in tabbarItems" 
      :key="item.key"
      class="tabbar-item"
      :class="{ active: isActive(item) }"
      @click="handleTabClick(item)"
    >
      <div class="tabbar-icon">
        <component :is="getIconComponent(item)" />
      </div>
      <div class="tabbar-text">{{ item.text }}</div>
      <div v-if="item.badge" class="tabbar-badge">
        {{ item.badge }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAppStore } from '../../store/app';

const props = defineProps({
  items: {
    type: Array,
    default: () => []
  }
});

const router = useRouter();
const route = useRoute();
const appStore = useAppStore();

// 获取Tabbar项配置
const tabbarItems = computed(() => {
  return props.items.length > 0 
    ? props.items 
    : appStore.tabbarConfig.items;
});

// 判断Tab是否激活
const isActive = (item: any) => {
  if (item.match) {
    // 支持正则匹配
    return new RegExp(item.match).test(route.path);
  }
  return route.path === item.path;
};

// 处理Tab点击
const handleTabClick = (item: any) => {
  if (item.path) {
    router.push(item.path);
  }
  if (item.action) {
    appStore.executeAction(item.action, item.actionParams);
  }
};

// 获取图标组件
const getIconComponent = (item: any) => {
  return item.active && isActive(item) 
    ? item.activeIcon 
    : item.icon;
};

// 控制显示隐藏
const hidden = computed(() => {
  return !route.meta.tabbar;
});
</script>

<style lang="less" scoped>
.tabbar-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 50px;
  background-color: #fff;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.03);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  transition: transform 0.3s ease;
  
  &.tabbar-hidden {
    transform: translateY(100%);
  }
}

.tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  transition: all 0.3s;
  
  &.active {
    .tabbar-icon {
      color: var(--primary-color);
    }
    
    .tabbar-text {
      color: var(--primary-color);
      font-weight: 500;
    }
  }
}

.tabbar-icon {
  font-size: 20px;
  height: 24px;
  color: #999;
}

.tabbar-text {
  font-size: 12px;
  margin-top: 2px;
  color: #999;
}

.tabbar-badge {
  position: absolute;
  top: 4px;
  background-color: var(--danger-color);
  color: white;
  font-size: 10px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  padding: 0 4px;
}
</style>
```

#### 3.4.2 布局切换机制
```vue
<!-- App.vue -->
<template>
  <div class="app-container">
    <!-- 使用动态布局 -->
    <component
      :is="currentLayout"
      :page-config="currentPageConfig"
    >
      <router-view />
    </component>
    
    <!-- 全局组件 -->
    <GlobalLoading />
    <GlobalMessage />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { usePageStore } from './store/page';

// 导入所有布局组件
import DefaultLayout from './layouts/default/index.vue';
import EmptyLayout from './layouts/empty/index.vue';

const route = useRoute();
const pageStore = usePageStore();

// 布局映射
const layouts = {
  'default': DefaultLayout,
  'empty': EmptyLayout
};

// 当前布局
const currentLayout = computed(() => {
  const layoutName = route.meta.layout || 'default';
  return layouts[layoutName] || layouts.default;
});

// 当前页面配置
const currentPageConfig = computed(() => {
  return pageStore.currentPage;
});
</script>
```

### 3.5 状态管理（store）

#### 3.5.1 全局状态管理
```typescript
// store/app.ts
import { defineStore } from 'pinia';
import { TabbarConfig, Theme } from '../types';

interface AppState {
  appReady: boolean;
  appVersion: string;
  theme: string;
  themeConfig: Record<string, Theme>;
  tabbarConfig: TabbarConfig;
  transitioning: boolean;
  redirect: string | null;
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    appReady: false,
    appVersion: process.env.APP_VERSION || '1.0.0',
    theme: 'light',
    themeConfig: {
      light: {
        primaryColor: '#1890ff',
        textColor: '#333333',
        backgroundColor: '#ffffff'
      },
      dark: {
        primaryColor: '#177ddc',
        textColor: '#ffffff',
        backgroundColor: '#141414'
      }
    },
    tabbarConfig: {
      show: true,
      items: []
    },
        transitioning: false,
    redirect: null,
  }),
  
  getters: {
    currentTheme: (state) => state.themeConfig[state.theme] || state.themeConfig.light,
    hasTabbar: (state) => state.tabbarConfig.show && state.tabbarConfig.items.length > 0,
  },
  
  actions: {
    initApp() {
      // 从本地存储加载配置
      this.loadSettings();
      // 设置主题
      this.applyTheme(this.theme);
      // 标记应用就绪
      this.appReady = true;
    },
    
    setTheme(theme: string) {
      if (this.themeConfig[theme]) {
        this.theme = theme;
        this.applyTheme(theme);
        // 保存到本地存储
        localStorage.setItem('app_theme', theme);
      }
    },
    
    applyTheme(theme: string) {
      const themeVars = this.themeConfig[theme];
      if (themeVars) {
        Object.entries(themeVars).forEach(([key, value]) => {
          document.documentElement.style.setProperty(`--${key}`, value);
        });
        
        // 添加主题类名到body
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${theme}`);
      }
    },
    
    setTabbarConfig(config: Partial<TabbarConfig>) {
      this.tabbarConfig = {
        ...this.tabbarConfig,
        ...config
      };
    },
    
    setTransitioning(status: boolean) {
      this.transitioning = status;
    },
    
    setRedirect(path: string | null) {
      this.redirect = path;
    },
    
    loadSettings() {
      // 从本地存储加载设置
      const theme = localStorage.getItem('app_theme');
      if (theme && this.themeConfig[theme]) {
        this.theme = theme;
      }
      
      // 加载其他设置...
    },
    
    executeAction(action: string, params?: any) {
      // 执行全局动作
      switch (action) {
        case 'toggleTheme':
          const newTheme = this.theme === 'light' ? 'dark' : 'light';
          this.setTheme(newTheme);
          break;
        
        case 'logout':
          // 调用登出逻辑...
          break;
          
        // 其他动作...
        
        default:
          console.warn(`未知动作: ${action}`);
      }
    }
  },
});
```


#### 3.5.2 页面状态管理
```typescript
// store/page.ts
import { defineStore } from 'pinia';
import { fetchPageConfig } from '../api/page';
import { PageConfig } from '../types';

interface PageState {
  currentPageId: string | null;
  pageConfigs: Record<string, PageConfig>;
  currentPageData: Record<string, any>;
  loading: boolean;
  error: Error | null;
}

export const usePageStore = defineStore('page', {
  state: (): PageState => ({
    currentPageId: null,
    pageConfigs: {},
    currentPageData: {},
    loading: false,
    error: null,
  }),
  
  getters: {
    currentPage: (state) => state.currentPageId ? state.pageConfigs[state.currentPageId] : null,
    pageData: (state) => state.currentPageData,
  },
  
  actions: {
    async loadPageConfig(pageId: string) {
      // 如果已经加载过，直接使用缓存
      if (this.pageConfigs[pageId]) {
        this.currentPageId = pageId;
        return this.pageConfigs[pageId];
      }
      
      // 否则从API加载
      this.loading = true;
      this.error = null;
      
      try {
        const config = await fetchPageConfig(pageId);
        
        // 存储配置
        this.pageConfigs[pageId] = config;
        this.currentPageId = pageId;
        
        // 初始化页面数据
        this.currentPageData = this.initPageData(config);
        
        return config;
      } catch (error) {
        this.error = error as Error;
        throw error;
      } finally {
        this.loading = false;
      }
    },
    
    initPageData(config: PageConfig): Record<string, any> {
      // 从配置中提取初始数据
      const initialData: Record<string, any> = {};
      
      // 处理页面级数据
      if (config.data) {
        Object.entries(config.data).forEach(([key, value]) => {
          initialData[key] = value;
        });
      }
      
      return initialData;
    },
    
    updatePageData(key: string, value: any) {
      this.currentPageData[key] = value;
    },
    
    resetPageData() {
      if (this.currentPageId && this.pageConfigs[this.currentPageId]) {
        this.currentPageData = this.initPageData(this.pageConfigs[this.currentPageId]);
      } else {
        this.currentPageData = {};
      }
    },
    
    clearPageCache(pageId?: string) {
      if (pageId) {
        delete this.pageConfigs[pageId];
      } else {
        // 清除所有页面配置缓存
        this.pageConfigs = {};
      }
    }
  },
});
```

#### 3.5.3 组件状态管理
```typescript
// store/component.ts
import { defineStore } from 'pinia';
import { ComponentState } from '../types';

interface ComponentStoreState {
  components: Record<string, ComponentState>;
  activeComponents: string[];
}

export const useComponentStore = defineStore('component', {
  state: (): ComponentStoreState => ({
    components: {},
    activeComponents: [],
  }),
  
  getters: {
    getComponentState: (state) => (id: string) => state.components[id] || null,
    isComponentActive: (state) => (id: string) => state.activeComponents.includes(id),
  },
  
  actions: {
    registerComponent(id: string, initialState: Partial<ComponentState> = {}) {
      this.components[id] = {
        visible: true,
        disabled: false,
        loading: false,
        error: null,
        data: {},
        ...initialState,
      };
    },
    
    activateComponent(id: string) {
      if (!this.activeComponents.includes(id)) {
        this.activeComponents.push(id);
      }
    },
    
    deactivateComponent(id: string) {
      const index = this.activeComponents.indexOf(id);
      if (index !== -1) {
        this.activeComponents.splice(index, 1);
      }
    },
    
    updateComponentState(id: string, state: Partial<ComponentState>) {
      if (this.components[id]) {
        this.components[id] = {
          ...this.components[id],
          ...state,
        };
      } else {
        // 如果组件未注册，先注册再更新
        this.registerComponent(id, state);
      }
    },
    
    setComponentLoading(id: string, loading: boolean) {
      if (this.components[id]) {
        this.components[id].loading = loading;
      }
    },
    
    setComponentError(id: string, error: Error | null) {
      if (this.components[id]) {
        this.components[id].error = error;
      }
    },
    
    setComponentVisible(id: string, visible: boolean) {
      if (this.components[id]) {
        this.components[id].visible = visible;
      }
    },
    
    setComponentDisabled(id: string, disabled: boolean) {
      if (this.components[id]) {
        this.components[id].disabled = disabled;
      }
    },
    
    updateComponentData(id: string, data: Record<string, any>) {
      if (this.components[id]) {
        this.components[id].data = {
          ...this.components[id].data,
          ...data,
        };
      }
    },
    
    removeComponent(id: string) {
      delete this.components[id];
      this.deactivateComponent(id);
    },
    
    resetComponentState(id: string) {
      if (this.components[id]) {
        this.components[id] = {
          visible: true,
          disabled: false,
          loading: false,
          error: null,
          data: {},
        };
      }
    },
  },
});

```
