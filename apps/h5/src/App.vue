<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { registerAllComponents } from '@vben/lowcode-components';
import { ComponentRenderer, setupComponents } from '@vben/lowcode-core';

// 注册所有组件
registerAllComponents(setupComponents);

// 页面 schema
const pageSchema = ref<any>(null);

// 加载页面 schema
async function loadPageSchema() {
  try {
    // 这里应该是从 API 获取页面 schema，这里使用模拟数据
    const response = await fetch('/mock-schema.json');
    pageSchema.value = await response.json();
  } catch {
    // 使用默认 schema
    pageSchema.value = getDefaultSchema();
  }
}

// 获取默认 schema
function getDefaultSchema() {
  return {
    type: 'text',
    props: {
      text: '物联网卡管理系统',
      type: 'h1',
      style: {
        textAlign: 'center',
        margin: '20px 0',
        padding: '20px',
      },
    },
  };
}

// 组件映射函数
function getComponent(schema: any) {
  return schema ? ComponentRenderer : null;
}

// 组件挂载时加载页面 schema
onMounted(() => {
  loadPageSchema();
});
</script>

<template>
  <component
    v-if="pageSchema"
    :is="getComponent(pageSchema)"
    :type="pageSchema.type"
    :component-props="pageSchema.props"
    :children="pageSchema.children"
    id="root"
  />
  <div v-else class="loading">加载中...</div>
</template>

<style scoped>
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #666;
}
</style>
