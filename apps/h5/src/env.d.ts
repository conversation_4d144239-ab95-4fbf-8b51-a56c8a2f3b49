// / <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue';
  // eslint-disable-next-line @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

// 声明 @vben/lowcode-core 模块
declare module '@vben/lowcode-core' {
  import { Component } from 'vue';

  export function setupComponents(components: any[]): void;
  export const ComponentRenderer: Component;
}

// 声明 @vben/lowcode-components 模块
declare module '@vben/lowcode-components' {
  export function registerAllComponents(
    setupFn: (components: any[]) => void,
  ): void;
  export const AllComponents: any[];
}
